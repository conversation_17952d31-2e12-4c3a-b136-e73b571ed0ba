// Imports
import { Document } from 'mongoose';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { DataType } from 'sequelize-typescript';

export type AALogsDocument = AALogs & Document;

@Schema({ strict: false })
export class AALogs {
  @Prop({ required: true })
  source: string;

  @Prop({ required: false })
  userId: string;

  @Prop({ required: false })
  loanId: string;

  @Prop({ required: true })
  createdAt: string;

  @Prop({ required: false })
  type: string;

  @Prop({ required: false })
  subType: string;

  @Prop({ required: false, type: DataType.JSONB })
  data: any;
}

export const AALogsSchema = SchemaFactory.createForClass(AALogs);
