import {
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { LeadTrackingFileEntity } from './leadTrackingFile.entity';

@Table({})
export class LeadTrackingEntity extends Model<LeadTrackingEntity> {
  @Column({
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
    type: DataType.UUID,
  })
  id: string;

  @Column({
    type: DataType.SMALLINT,
    comment:
      '0 = Accepted, 1 = In Process, 2 = Rejected, 3 = Disbursed, 4 = Existing',
    // OLD : 0 = rejected, 1 = accepted, 2 = converted
  })
  leadStatus: number;

  @Column({
    type: DataType.SMALLINT,
    comment:
      '0 = Finance Buddha, 1 = <PERSON><PERSON>Fi, 2 = SwitchMyLoan, 3 = LoanTap, 4 = Scaller Boat Finnovation LLPs, 5 = GoCredit',
  })
  leadSource: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0 = SALARIED, 1 = SELF EMPLOYED',
  })
  jobType: number;

  @Column({
    type: DataType.SMALLINT,
  })
  cibilScore: number;

  @Column({
    type: DataType.SMALLINT,
  })
  age: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0 = MALE, 1 = FEMALE',
  })
  gender: number;

  @Column({
    type: DataType.DATEONLY,
  })
  dob: string;

  @Column({
    type: DataType.DATE,
  })
  disbursementDate: Date;

  // If file lead
  @ForeignKey(() => LeadTrackingFileEntity)
  @Column({
    type: DataType.INTEGER,
  })
  leadFileId: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  salary: number;

  @Column({
    type: DataType.INTEGER,
  })
  pincode: number;

  @Column({
    type: DataType.TEXT,
  })
  fullName: string;

  @Column({
    type: DataType.TEXT,
  })
  email: string;

  @Column({
    type: DataType.TEXT,
  })
  city: string;

  @Column({
    type: DataType.TEXT,
  })
  state: string;

  @Column({
    type: DataType.TEXT,
  })
  rejectReasons: string;

  @Column({
    type: DataType.TEXT,
  })
  phone: string;

  @Column({
    type: DataType.TEXT,
  })
  hashPhone: string;

  @Column({
    type: DataType.TEXT,
  })
  pan: string;

  @Column({
    type: DataType.TEXT,
  })
  hashPAN: string;

  @Column({
    type: DataType.DOUBLE,
    allowNull: true,
  })
  disbursement_amount: number;

  @Column({
    type: DataType.BOOLEAN,
  })
  isMigrated: boolean;
}
