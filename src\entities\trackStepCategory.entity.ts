import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { TrackErrorMetrics } from './trackErrorsMetrics.entity';
import { TrackUserAttempts } from './trackUserAttempts.entity';

@Table({})
export class TrackStepCategory extends Model<TrackStepCategory> {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    allowNull: false,
  })
  id: number;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  stepName: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
  })
  status: boolean;

  @HasMany(() => TrackErrorMetrics)
  trackErrorData: TrackErrorMetrics[];

  @HasMany(() => TrackUserAttempts)
  trackAttemptData: TrackUserAttempts[];
}
