// Imports
import { Table, Column, Model, DataType } from 'sequelize-typescript';

@Table({ timestamps: false })
export class LoanDetails extends Model<LoanDetails> {
  @Column({
    allowNull: false,
    primaryKey: true,
    type: DataType.INTEGER,
  })
  loanId: string;

  @Column({
    allowNull: false,
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    allowNull: false,
    defaultValue: 0,
    type: DataType.SMALLINT,
  })
  completed_loans: number;

  @Column({
    allowNull: false,
    comment: '0 -> Active, 1 -> Complete',
    type: DataType.SMALLINT,
  })
  loanStatus: number;

  @Column({
    allowNull: false,
    defaultValue: 0,
    type: DataType.DOUBLE,
  })
  approved_amount: number;

  @Column({
    allowNull: false,
    defaultValue: 0,
    type: DataType.STRING(10),
  })
  disbursed_date: string;

  @Column({
    allowNull: false,
    comment: 'Loan completion date',
    defaultValue: '-1',
    type: DataType.STRING(10),
  })
  closing_date: string;

  @Column({
    allowNull: false,
    defaultValue: 0,
    type: DataType.DOUBLE,
  })
  expected_principal: number;

  @Column({
    allowNull: false,
    defaultValue: 0,
    type: DataType.DOUBLE,
  })
  paid_principal: number;

  @Column({
    allowNull: false,
    defaultValue: 0,
    type: DataType.DOUBLE,
  })
  expected_interest: number;

  @Column({
    allowNull: false,
    defaultValue: 0,
    type: DataType.DOUBLE,
  })
  paid_interest: number;

  @Column({
    allowNull: false,
    defaultValue: -1,
    type: DataType.SMALLINT,
  })
  cibil_score: number;

  @Column({
    allowNull: false,
    defaultValue: -1,
    type: DataType.SMALLINT,
  })
  pl_score: number;

  @Column({
    allowNull: false,
    defaultValue: -1,
    type: DataType.SMALLINT,
  })
  pl_accounts: number;

  @Column({
    allowNull: false,
    defaultValue: -1,
    type: DataType.DOUBLE,
  })
  pl_outstanding: number;

  @Column({
    allowNull: false,
    defaultValue: -1,
    type: DataType.SMALLINT,
  })
  inquiry_last_30_days: number;

  @Column({
    allowNull: false,
    defaultValue: -1,
    type: DataType.DOUBLE,
  })
  verified_salary: number;

  @Column({
    allowNull: false,
    defaultValue: 'Unknown',
    type: DataType.STRING(56),
  })
  aadhaarState: string;

  @Column({
    allowNull: false,
    comment:
      '-1 -> None, 1 -> FINVU, 2 -> BankingPro, 3 -> Netbanking, 4 -> CAMS',
    defaultValue: -1,
    type: DataType.SMALLINT,
  })
  transactions_source: number;

  @Column({
    allowNull: false,
    defaultValue: true,
    type: DataType.BOOLEAN,
  })
  lastIndex: boolean;

  @Column({
    allowNull: false,
    defaultValue: 0,
    type: DataType.INTEGER,
  })
  max_dpd: number;

  @Column({
    allowNull: false,
    comment:
      '0 -> Pre paid, 1 -> On time, 2 -> Delay, 3 -> Default, 4 -> Upcoming',
    defaultValue: -1,
    type: DataType.SMALLINT,
  })
  repayment_status: number;
}
