// Imports
import { Injectable } from '@nestjs/common';
import { EXPERIAN_CONFIG } from 'src/constants/globals';
import { RedisService } from 'src/redis/redis.service';
import { APIService } from 'src/utils/api.service';
import { k500Error } from 'src/constants/misc';
import {
  k422ErrorMessage,
  kInvalidParamValue,
  kParamMissing,
} from 'src/constants/responses';
import { EnvConfig } from 'src/configs/env.config';
import { RedisKeys } from 'src/constants/objects';

@Injectable()
export class ExperianThirdParty {
  constructor(
    //Utils
    private readonly api: APIService,
    // Database
    private readonly redisService: RedisService,
  ) {}

  // Function to get the access token
  async getAccessToken(): Promise<string> {
    const url = EXPERIAN_CONFIG.EXPERIAN_BASE_URL + 'oauth2/v1/token';
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
    };

    const key = 'EXPERIAN_TOKEN';
    const accessToken = await this.redisService.get(key);
    if (accessToken) return accessToken;

    const body = new URLSearchParams({
      username: EXPERIAN_CONFIG.EXPERIAN_MEMBER_USERNAME,
      password: EXPERIAN_CONFIG.EXPERIAN_MEMBER_PASS,
      client_id: EXPERIAN_CONFIG.EXPERIAN_MEMBER_CLIENTID,
      client_secret: EXPERIAN_CONFIG.EXPERIAN_MEMBER_CLIENT_SECRET,
    });

    const response = await this.api.post(
      url,
      body.toString(),
      headers,
      null,
      null,
      true,
    );

    if (response && response.access_token) {
      // set redis expired time according to token expired time
      await this.redisService.set(
        key,
        response.access_token,
        response?.expires_in,
      );
      return response.access_token;
    }
    throw new Error('Failed to fetch access token');
  }

  // Function to get Enhanced Match data
  async getEnhancedMatchData(reqData) {
    const token = await this.getAccessToken();

    const url =
      EXPERIAN_CONFIG.EXPERIAN_BASE_URL + 'ecs/ecv-api/v1/enhanced-match';

    const headers = {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/x-www-form-urlencoded',
    };

    let result = await this.api.post(
      url,
      reqData.toString(),
      headers,
      null,
      null,
      true,
    );
    if (result == k500Error) throw new Error();
    return result;
  }

  async updateExperianMockResponse(reqData) {
    // Params validation
    const panNumber = reqData.panNumber;
    if (!panNumber) return kParamMissing('panNumber');
    const responseData = reqData.responseData;
    if (!responseData) return kParamMissing('responseData');
    if (!EnvConfig.mock.panNumbers.includes(panNumber))
      return kInvalidParamValue('panNumber');

    let mockData = await this.redisService.getKeyDetails(
      RedisKeys.MOCK_EXPERIAN_DATA,
    );
    if (!mockData) return k422ErrorMessage('No mock data available');
    if (typeof mockData == 'string') mockData = JSON.parse(mockData);
    if (typeof mockData == 'string') mockData = JSON.parse(mockData);

    mockData[panNumber] = responseData;
    await this.redisService.updateKeyDetails(
      RedisKeys.MOCK_EXPERIAN_DATA,
      JSON.stringify(mockData),
    );

    return {};
  }
}
