import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { k500Error } from 'src/constants/misc';
import { k422ErrorMessage, kParamMissing } from 'src/constants/responses';
import {
  kAadhaarAlreadyExist,
  kAddAadhareNumberRoute,
  kBankingRoute,
  kDisburementRoute,
  kEnterValidAadhaarNumber,
  kEsignRoute,
  kFinalVerificationRoute,
  kKycButton,
  kKycComplete,
  kKycFailed,
  kKycProcessStep,
  kMandateRoute,
  kNotEligibleRoute,
  kReApplyRoute,
  kRegistrationRoute,
  kSelectLoanAmountRoute,
  kUserNotExists,
  routeStepperStep,
} from 'src/constants/strings';
import { registeredUsers } from 'src/entities/user.entity';
import { CryptService } from 'src/utils/crypt.service';
import { FileService } from 'src/utils/file.service';
import * as fs from 'fs';
import { nExtractData, nWAStatementFlowStatus } from 'src/constants/network';
import { APIService } from 'src/utils/api.service';
import * as FormData from 'form-data';
import { UserServiceV4 } from 'src/v4/user/user.service.v4';
import { MasterRepository } from 'src/repositories/master.repository';
import { BankingSharedService } from '../banking.service';
import {
  kAadhaarAttempts,
  kBankingProHeaders,
  kResponseType,
  MICRO_ALERT_TOPIC,
} from 'src/constants/objects';
import { Experiment } from 'src/entities/experiment_schema';
import { KafkaService } from 'src/microservice/kafka/kafka.service';
import { DynamicEntity } from 'src/entities/dynamic.entity';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { MsgEntity } from 'src/entities/msg.entity';
import { CommonService } from 'src/utils/common.service';
import { KycServiceV4 } from 'src/v4/kyc/kyc.service.v4';
import { KYCEntity } from 'src/entities/kyc.entity';
import { Op } from 'sequelize';
import { TypeService } from 'src/utils/type.service';
import { WhatsAppService } from 'src/thirdParty/whatsApp/whatsApp.service';
import { RedisService } from 'src/redis/redis.service';
import { NUMBERS } from 'src/constants/numbers';
import { CommonSharedService } from '../common.shared.service';

@Injectable()
export class WhatsappLoanFlowService {
  constructor(
    private readonly repoManager: RepositoryManager,
    private readonly cryptService: CryptService,
    private readonly fileService: FileService,
    private readonly apiService: APIService,
    @Inject(forwardRef(() => UserServiceV4))
    private readonly userServiceV4: UserServiceV4,
    private readonly masterRepo: MasterRepository,
    private readonly bankingSharedService: BankingSharedService,
    private readonly kafkaService: KafkaService,
    private readonly commonService: CommonService,
    private readonly kycService: KycServiceV4,
    private readonly typeService: TypeService,
    private readonly whatsaAppService: WhatsAppService,
    private readonly redisServie: RedisService,
    private readonly commonSharedService: CommonSharedService,
  ) {}

  //#region  check existing user
  async userExists(mobileNumber) {
    mobileNumber = this.cryptService.getMD5Hash(mobileNumber);
    const userAttr = [
      'id',
      'isBlacklist',
      'stage',
      'appType',
      'kycId',
      'isDeleted',
    ];
    const userOptions = {
      where: { hashPhone: mobileNumber },
    };
    const userData = await this.repoManager.getRowWhereData(
      registeredUsers,
      userAttr,
      userOptions,
    );
    if (userData == k500Error) throw new Error();
    if (!userData) return k422ErrorMessage(kUserNotExists);
    return userData;
  }
  //#endregion

  async stageCheck(userData) {
    let routeResponse = await this.userServiceV4.routeDetails({
      id: userData?.id,
    });
    return routeResponse;
  }

  checkStepsStepProcess(
    stepperInfo,
    currentRoute,
    stepName = '',
    userData: any = null,
  ) {
    const prepareResponse: any = {};
    if (!stepperInfo) {
      prepareResponse.route = currentRoute;
      prepareResponse.pending = true;
      if (userData?.stage == 1) {
        prepareResponse.stage = routeStepperStep[currentRoute]?.PENDING;
      } else prepareResponse.stage = routeStepperStep[currentRoute]?.APPROVED;
      return prepareResponse;
    }
    const isVerified = stepperInfo.find((row) => row.name == stepName);
    if (isVerified?.description == 'Successful') {
      prepareResponse.route = currentRoute;
      prepareResponse.completed = true;
      prepareResponse.stage = routeStepperStep[currentRoute]?.APPROVED;
    }
    if (isVerified?.underVerification) {
      prepareResponse.route = currentRoute;
      prepareResponse.completed = true;
      prepareResponse.stage =
        routeStepperStep[currentRoute]?.UNDER_VERIFICATION;
    }
    if (isVerified?.isPending) {
      prepareResponse.route = currentRoute;
      prepareResponse.pending = true;
      prepareResponse.stage = routeStepperStep[currentRoute]?.PENDING;
    }
    return prepareResponse;
  }

  //#region check user verfication status....
  async userIsAlreadyInVerification(userRouteResponse) {
    const userCurrentRoute = userRouteResponse.continueRoute;
    let prepareResponse: any = { route: userCurrentRoute };
    try {
      if (userCurrentRoute == kNotEligibleRoute) {
        prepareResponse.stage = routeStepperStep.NO_ROUTE;
      }
      //check if user has alredy done pre approval steps
      else {
        const stepperInfo = userRouteResponse?.userData?.stepperInfo;
        let responseData: any = {};
        if (stepperInfo) {
          responseData = this.checkStepsStepProcess(
            stepperInfo,
            kFinalVerificationRoute,
            'Final verification',
          ); //check if final verification not done
          if (!responseData?.completed) {
            responseData = this.checkStepsStepProcess(
              stepperInfo,
              kSelectLoanAmountRoute,
              'Loan offer',
            );
            if (!responseData?.pending)
              responseData = this.checkStepsStepProcess(
                stepperInfo,
                kMandateRoute,
                'E-Mandate',
              );
            if (!responseData?.pending)
              responseData = this.checkStepsStepProcess(
                stepperInfo,
                kEsignRoute,
                'E-Sign',
              );
            if (!responseData?.pending)
              responseData = this.checkStepsStepProcess(
                stepperInfo,
                kDisburementRoute,
                'Disbursement',
              );
          }
        } else {
          if (!userCurrentRoute) {
            responseData = {
              route: kRegistrationRoute,
              stage: routeStepperStep[kRegistrationRoute].PENDING,
            };
          } else {
            //check if user has already done the pending steps
            responseData = this.checkStepsStepProcess(
              stepperInfo,
              userCurrentRoute,
            );
          }
        }
        prepareResponse = { ...prepareResponse, ...responseData };
      }
    } catch (errror) {}
    return prepareResponse;
  }
  //#endregion

  //#region Submit step details....
  async submitTheStepDetails(
    reqData,
    submitStepRoute,
    userRouteResponse,
    userData,
    name = '',
    is_strick_match: boolean = false,
  ) {
    let prepareResponse: any = {};
    const expirimentResponse: any = { ...reqData };
    let responseData: any = await this.userIsAlreadyInVerification(
      userRouteResponse,
    );
    const stepperInfo = userRouteResponse?.userData?.stepperInfo;
    if (is_strick_match) {
      if (submitStepRoute == userRouteResponse.continueRoute)
        prepareResponse = {
          route: submitStepRoute,
          stage: routeStepperStep[submitStepRoute].PENDING,
        };
      else {
        prepareResponse = {
          route: responseData.route,
          stage: routeStepperStep.NO_ROUTE,
        };
      }
    }
    //check already has done the steps
    else if (
      !responseData?.pending &&
      !responseData?.completed &&
      responseData.route != kNotEligibleRoute
    ) {
      responseData = this.checkStepsStepProcess(
        stepperInfo,
        submitStepRoute,
        name,
        userData,
      );
      if (Object.keys(responseData).length == 0)
        prepareResponse = {
          route: submitStepRoute,
          stage: routeStepperStep[submitStepRoute].PENDING,
        };
      else prepareResponse = { ...prepareResponse, ...responseData };
    } else prepareResponse = { ...prepareResponse, ...responseData };

    return { whatsappBody: prepareResponse, expirimentResponse };
  }
  //#endregion

  //#region submit details
  async submitDataForLoan(reqData) {
    let mobileNumber = reqData.mobileNumber;
    if (!mobileNumber) return kParamMissing('mobileNumber');
    const userData = await this.userExists(mobileNumber);
    if (!userData) return {};
    //Check pre approved
    const stepResponse = await this.stageCheck(userData);
    const currentStep = stepResponse?.continueRoute;
    let responsePayload: any = {};
    //check for bank statement
    responsePayload = await this.submitTheStepDetails(
      reqData,
      kBankingRoute,
      stepResponse,
      userData,
      'Salary verification',
    );
    //prepare whatsapp send body
    const whatsAppPayload = responsePayload.whatsappBody;
    const currentWPRoute = whatsAppPayload.route;
    const currentWPStage = whatsAppPayload.stage;
    //get stage according route
    const stageStatus = routeStepperStep[currentWPRoute];
    //check statement
    if (
      currentWPRoute == kBankingRoute &&
      currentWPStage != stageStatus.APPROVED &&
      currentWPStage != stageStatus.UNDER_VERIFICATION
    )
      responsePayload = await this.checkBankStatementApproval(
        currentStep,
        reqData,
        userData,
      );
    //replay to whatsapp
    this.checkAndReplyWhatsApp(
      responsePayload,
      reqData,
      userData,
      'PRE_APPROVAL_V1',
    ).catch((error) => {});

    return { success: true };
  }
  //#endregion

  //#region checking bank statement....
  async checkBankStatementApproval(routeInfo, reqData, userData) {
    if (routeInfo != kBankingRoute && routeInfo != kReApplyRoute) {
    }
    const preapareResponse: any = { route: kBankingRoute, is_valid: true };
    const expirimentResponse: any = {
      bankCode: reqData.bankCode,
      salary: reqData?.salary ?? 0,
      password: reqData.password,
    };
    try {
      const file = reqData.file;
      const bankCode = reqData.bankCode;
      const salary = +(reqData?.salary ?? 0);
      if (!file) return kParamMissing('file');
      if (!bankCode) return kParamMissing('bankCode');
      if (!salary) return kParamMissing('salary');
      const uploadFileName = file?.filename;
      const fileUrl = await this.fileService.uploadFile(uploadFileName);
      expirimentResponse.fileUrl = fileUrl;
      // stage 3, 4
      const filePath = await this.fileService.urlToBuffer(fileUrl, true, 'pdf');
      const fileContent = await fs.readFileSync(filePath);
      await this.fileService.removeFile(filePath);
      const fileName = uploadFileName
        ?.split('/')
        ?.slice(-1)[0]
        .replace('.pdf', '');

      const formData = new FormData();
      formData.append('bankCode', reqData.bankCode);
      formData.append('pdfFile', fileContent, fileName + '.pdf');
      if (reqData.password) formData.append('password', reqData.password);

      let bankingData = await this.apiService.post(
        nExtractData,
        formData,
        {
          appId: kBankingProHeaders.appId,
          secretKey: kBankingProHeaders.secretKey,
        },
        null,
        null,
        null,
      );
      if (bankingData == k500Error) return preapareResponse;
      else if (bankingData?.valid == false) {
        preapareResponse.is_valid = false;
        preapareResponse.stage =
          routeStepperStep[kBankingRoute].STAETMENT_NOT_VALID;
        return preapareResponse;
      }
      const parsedStatement = JSON.parse(bankingData?.filePath);
      const transactions = parsedStatement?.transactions ?? [];
      expirimentResponse.data = {
        accountDetails: bankingData.accountDetails,
        transactions: transactions,
      };
      const is_valid_transaction =
        await this.bankingSharedService.checkHavingValidTransactionsForLoan(
          transactions,
        );

      if (!is_valid_transaction) {
        preapareResponse.is_valid = false;
        preapareResponse.stage =
          routeStepperStep[kBankingRoute].TRANSACTION_MISSING;
        return preapareResponse;
      }
      //get master data
      const masterData = await this.masterRepo.getRowWhereData(
        ['id', 'otherInfo'],
        {
          where: { userId: userData?.id },
          order: [['id', 'desc']],
        },
      );
      if (masterData == k500Error) throw new Error();
      const userEnteredSalary = salary ?? masterData?.otherInfo?.salaryInfo;
      const prepareObj = {
        accountDetails: bankingData?.accountDetails
          ? bankingData?.accountDetails
          : null,
        salaryDate: new Date().getDate(),
        userId: userData?.id,
        salary: userEnteredSalary,
        bank: bankingData?.accountDetails?.bank,
        isFunctionFor: 'PRE_APPROVEL',
        transactions,
      };
      const eligibleResponse: any =
        await this.bankingSharedService.checkLatestTransactions(prepareObj);
      if (eligibleResponse?.isEligible == false) {
        preapareResponse.is_valid = false;
        const internlResponse = eligibleResponse?.response;
        if (internlResponse?.salaryVerification == '4') {
          preapareResponse.stage =
            routeStepperStep[kBankingRoute].TRANSACTION_MISSING;
          preapareResponse.missingMonth =
            internlResponse?.message?.dataOfMonth ?? [];
        } else
          preapareResponse.stage = routeStepperStep[kBankingRoute].NOT_ELIGIBLE;
      } else if (eligibleResponse?.isEligible) {
        preapareResponse.stage = routeStepperStep[kBankingRoute].APPROVED;
      }
    } catch (error) {}
    return {
      whatsappBody: preapareResponse,
      expirimentResponse,
    };
  }
  //#endregion

  //#region check and reply to whatsapp....
  async checkAndReplyWhatsApp(response: any, reqData, userData, type) {
    const body = {
      number: reqData?.mobileNumber,
      ...response?.whatsappBody,
    };
    let replyRes = await this.apiService.post(
      nWAStatementFlowStatus,
      body,
      null,
      null,
      null,
      true,
    );
    const experimentResponse = response?.experimentResponse ?? {};
    experimentResponse.response = replyRes;

    const creationData = {
      type: type,
      value: JSON.stringify(experimentResponse),
      userId: userData.id,
    };
    try {
      const createDynamicData = {
        userId: userData.id,
        data: { ...body, type, ...experimentResponse },
      };
      await this.repoManager.createRowData(DynamicEntity, createDynamicData);
    } catch (err) {}

    try {
      await this.repoManager.createRowData(Experiment, creationData);
    } catch (err) {}
    if (replyRes?.data?.response) {
      const payload = {
        toNumber: reqData.mobileNumber,
        appType: userData?.loanData?.appType ?? userData?.appType,
        message: replyRes?.data?.response,
      };
      await this.kafkaService.send(MICRO_ALERT_TOPIC.SEND_WA_TEXT_MSG, payload);
      return replyRes?.data;
    }
    return {};
  }
  //#endregion

  //#region KYC through WhatsApp....
  async processKYCRequest(reqData: any) {
    if (!reqData) return;

    // Extracting data from request....
    const { message, phone, userInput } = this.extractReqData(reqData);

    // Fetching user data and message data....
    const userData = await this.userExists(phone);
    if (
      userData?.message?.includes(kUserNotExists) ||
      userData?.isBlacklist == 1 ||
      userData?.isDeleted == 1 ||
      userData?.appType == 0
    )
      return;

    const userMsgData = await this.getMsgRow(phone);
    if (!userMsgData || userMsgData == k500Error) return;

    // Checking user stage....
    const { whatsappBody, stageStatus } = await this.getUserStage(
      reqData,
      userData,
    );

    // Processing KYC flow....
    const updatedPayload = await this.processKYCFlow(
      message,
      phone,
      userInput,
      userData,
      userMsgData,
      whatsappBody,
      stageStatus,
    );

    if (!updatedPayload || !updatedPayload?.stage) return;

    // Updating message data and sending reply to WhatsApp....
    await this.repoManager.updateRowData(
      MsgEntity,
      { subStep: updatedPayload?.stage, processing_steps: kKycProcessStep },
      reqData?.messageDBId,
    );

    if (whatsappBody?.stage) whatsappBody.stage = updatedPayload?.stage;
    if (updatedPayload?.callMl) {
      const replyResponse = await this.checkAndReplyWhatsApp(
        { whatsappBody },
        { mobileNumber: phone },
        userData,
        'KYC',
      );

      // Saving ML message response....
      if (replyResponse?.response)
        await this.saveMsgRecord(
          stageStatus.ML_RESPONSE,
          phone,
          replyResponse?.response,
          updatedPayload?.subType,
        );
    }
  }

  extractReqData(reqData) {
    const message = reqData?.msg;
    const phone = this.commonService.removePrefixFromMobile(message?.from);
    const userInput = message?.button?.text || message?.text?.body;

    return { message, phone, userInput };
  }

  async getUserStage(reqData, userData) {
    const userRoute = await this.stageCheck(userData);

    const stepDetails = await this.submitTheStepDetails(
      reqData,
      kAddAadhareNumberRoute,
      userRoute,
      userData,
      'KYC Verification',
      true,
    );

    return {
      whatsappBody: stepDetails?.whatsappBody,
      stageStatus: routeStepperStep[stepDetails?.whatsappBody?.route],
    };
  }

  async processKYCFlow(
    message,
    phone,
    userInput,
    userData,
    msgData,
    whatsappPayload,
    stageStatus,
  ) {
    const userId = userData?.id;
    const currentStep = msgData?.subStep;
    const isTextOrButton = ['text', 'button'].includes(message?.type);

    // Check if user has already completed the KYC process....
    if (whatsappPayload?.route != kAddAadhareNumberRoute) {
      await this.sendWhatsAppTemplate(userId, kKycComplete);
      return { stage: stageStatus.APPROVED };
    }

    // Check the user input and initiate the KYC process....
    return isTextOrButton
      ? await this.InitiateKYCProcess(
          phone,
          userInput,
          userData,
          currentStep,
          stageStatus,
        )
      : // Otherwise send invalid response....
        {
          stage: stageStatus.INVALID_TYPE,
          subType: kResponseType.IMAGE_OR_DOCUMENT,
          callMl: true,
        };
  }

  async InitiateKYCProcess(phone, userInput, user, currentStep, stageStatus) {
    const redisKey = `${user?.id}-AADHAAR_ATTEMPTS_IN_WA`;
    let updatedPayload: any = {};

    // Check if user has clicked on the KYC button....
    if (userInput == kKycButton && !currentStep) {
      updatedPayload.stage = stageStatus.PENDING;
      updatedPayload.subType = kResponseType.BUTTON;
      updatedPayload.callMl = true;
    }

    // Check if user has entered the Aadhaar number....
    const aadhaarSteps = [stageStatus.PENDING, stageStatus.INVALID_AADHAAR];
    if (aadhaarSteps.includes(currentStep)) {
      updatedPayload = await this.validateAadhaarRequest(
        user?.id,
        userInput,
        stageStatus,
        redisKey,
      );
      return updatedPayload;
    }

    // Check if user has entered the OTP....
    const aadhaarOtpSteps = [stageStatus.REQUEST_OTP, stageStatus.INVALID_OTP];
    if (aadhaarOtpSteps.includes(currentStep))
      updatedPayload = await this.validateAadhaarOtp(
        user,
        userInput,
        stageStatus,
        phone,
        redisKey,
      );
    return updatedPayload;
  }

  async validateAadhaarRequest(userId, aadhaarNumber, status, redisKey) {
    const redisData = await this.getRedisData(redisKey);
    const isAadhaar = /^\d{12}$/.test(aadhaarNumber?.replace(/\s/g, ''));

    // Check if Aadhaar number is valid....
    let aadhaarResponse = null;

    if (isAadhaar) {
      if (redisData?.[kAadhaarAttempts.AADHAAR_INVALID] == 3) {
        return await this.handleAttempts(
          userId,
          redisKey,
          redisData,
          kAadhaarAttempts.AADHAAR_INVALID,
          status.INVALID_AADHAAR,
        );
      }

      aadhaarResponse = await this.kycService.aadhaarOtpRequest({
        userId,
        aadhaarNumber,
        reqFromWA: true,
      });

      if (aadhaarResponse?.reference_id) {
        redisData[kAadhaarAttempts.OTP_EXPIRE] = Date.now() + 10 * 60 * 1000;
        await this.setRedisData(redisKey, redisData);
        return { stage: status.REQUEST_OTP, callMl: true };
      }

      if (!aadhaarResponse?.reference_id) {
        if (
          aadhaarResponse?.message?.includes('Invalid ID') ||
          aadhaarResponse?.message?.includes(kEnterValidAadhaarNumber) ||
          aadhaarResponse?.message?.includes(kAadhaarAlreadyExist)
        ) {
          return await this.handleAttempts(
            userId,
            redisKey,
            redisData,
            kAadhaarAttempts.AADHAAR_INVALID,
            status.INVALID_AADHAAR,
          );
        } else {
          await this.sendWhatsAppTemplate(userId, kKycFailed);
          return { stage: status.UNKNOWN_ERROR };
        }
      }
    } else {
      return await this.handleAttempts(
        userId,
        redisKey,
        redisData,
        kAadhaarAttempts.AADHAAR_INVALID,
        status.INVALID_AADHAAR,
      );
    }
  }

  async validateAadhaarOtp(userData, aadhaarOtp, status, from, redisKey) {
    const redisData = await this.getRedisData(redisKey);
    const isOtp = /^\d{6}$/.test(aadhaarOtp?.replace(/\s/g, ''));

    let otpResponse = null;
    // Check if OTP is valid....

    if (isOtp) {
      if (redisData?.[kAadhaarAttempts.OTP_EXPIRE] < Date.now()) {
        await this.sendWhatsAppTemplate(userData?.id, kKycFailed);
        return { stage: status.OTP_EXPIRE };
      }

      if (redisData?.[kAadhaarAttempts.OTP_INVALID] == 3) {
        return await this.handleAttempts(
          userData?.id,
          redisKey,
          redisData,
          kAadhaarAttempts.OTP_INVALID,
          status.INVALID_OTP,
        );
      }

      const pendingAadhar = await this.getLatestAadhaar(
        from,
        status.REQUEST_OTP,
      );
      if (!pendingAadhar) return { stage: status.PENDING };

      const kycData = await this.getKycData(userData?.kycId);

      otpResponse = await this.kycService.validatemAadhaar({
        aadhaarNumber: pendingAadhar?.textContent,
        otp: aadhaarOtp,
        userId: userData?.id,
        reference_id: kycData.aadhaarReferenceId,
      });

      if (!(otpResponse?.valid == false && otpResponse?.message)) {
        await this.sendWhatsAppTemplate(userData?.id, kKycComplete);
        return { stage: status.APPROVED };
      } else if (
        !otpResponse?.valid &&
        otpResponse?.message?.includes('Invalid OTP')
      ) {
        return await this.handleAttempts(
          userData?.id,
          redisKey,
          redisData,
          kAadhaarAttempts.OTP_INVALID,
          status.INVALID_OTP,
        );
      } else if (
        !otpResponse?.valid &&
        !otpResponse?.message?.includes('Invalid OTP')
      ) {
        await this.sendWhatsAppTemplate(userData?.id, kKycFailed);
        return { stage: status.UNKNOWN_ERROR };
      }
    } else {
      return await this.handleAttempts(
        userData?.id,
        redisKey,
        redisData,
        kAadhaarAttempts.OTP_INVALID,
        status.INVALID_OTP,
      );
    }
  }

  async getRedisData(key) {
    return (
      JSON.parse(await this.redisServie.get(key)) || {
        aadhaarFails: 0,
        otpFails: 0,
        otpExpiry: null,
      }
    );
  }

  async setRedisData(key, data) {
    await this.redisServie.set(
      key,
      JSON.stringify(data),
      NUMBERS.SEVEN_DAYS_IN_SECONDS,
    );
  }

  async handleAttempts(userId, key, data, field, stage) {
    data[field]++;
    if (data[field] > 3) {
      await this.sendWhatsAppTemplate(userId, kKycFailed);
      return { stage };
    } else {
      await this.setRedisData(key, data);
      return { stage, callMl: true };
    }
  }

  async getLatestAadhaar(from, subStep) {
    return await this.repoManager.getRowWhereData(
      MsgEntity,
      ['id', 'textContent', 'createdAt'],
      {
        where: {
          from,
          processing_steps: kKycProcessStep,
          subStep,
        },
        order: [['createdAt', 'DESC']],
      },
    );
  }

  async getKycData(id) {
    return await this.repoManager.getRowWhereData(
      KYCEntity,
      [
        'id',
        'maskedAadhaar',
        'aadhaarStatus',
        'aadhaarReferenceId',
        'attemptData',
      ],
      {
        where: { id },
      },
    );
  }

  async getMsgRow(from) {
    const currentDate = new Date().toString();
    const dateRange = this.typeService.getUTCDateRange(
      currentDate,
      currentDate,
    );
    const attributes = [
      'from',
      'textContent',
      'type',
      'subType',
      'processing_steps',
      'subStep',
      'createdAt',
    ];
    const queryOptions = {
      where: {
        from,
        processing_steps: kKycProcessStep,
        subStep: {
          [Op.or]: [
            { [Op.is]: null }, // Allow NULL values
            {
              [Op.notIn]: [
                kResponseType.ML_RESPONSE,
                kResponseType.INVALID_TYPE,
              ],
            },
          ],
        },
        createdAt: {
          [Op.and]: {
            [Op.gte]: dateRange.fromDate,
            [Op.lte]: dateRange.endDate,
          },
        },
      },
      order: [['createdAt', 'DESC']],
    };

    return await this.repoManager.getRowWhereData(
      MsgEntity,
      attributes,
      queryOptions,
    );
  }

  async saveMsgRecord(
    subStep,
    from,
    textContent,
    subType = kResponseType.TEXT,
  ) {
    const msgData = {
      id: `${this.cryptService.getMD5Hash(
        from,
      )}${new Date().getMilliseconds()}`,
      from,
      type: 1,
      subType,
      subStep,
      textContent,
      processing_steps: kKycProcessStep,
    };
    await this.repoManager.createRowData(MsgEntity, msgData);
  }

  async sendWhatsAppTemplate(userId, title) {
    if (!userId) return;
    const userData = await this.repoManager.getRowWhereData(
      registeredUsers,
      ['fullName', 'email', 'phone', 'hashPhone'],
      { where: { id: userId } },
    );

    const hashPhones = [userData?.hashPhone];
    const nonWhatsAppHashPhone =
      await this.whatsaAppService.getNonWhatsAppUsers(hashPhones);

    if (nonWhatsAppHashPhone.includes(userData?.hashPhone)) return;
    const payload = {
      userId,
      customerName: userData?.fullName ?? 'User',
      email: userData?.email,
      number: this.cryptService.decryptPhone(userData?.phone),
      title,
      requestData: title,
    };
    await this.whatsaAppService.sendWhatsAppMessageMicroService(payload);
  }

  //#endregion

  //#region  call back for kyc from whatsapp
  async callBackDataForWhatsapp(reqData) {
    try {
      const msg = reqData?.data?.msg;
      if (msg?.text?.body == 'No' || msg?.button?.text == 'No') return;
      await this.processKYCRequest(reqData?.data);
      return {};
    } catch (error) {}
  }
  //#endregion

  // //#region process loan flow
  // processLoanFlow() {}
  // //#endregion
}
