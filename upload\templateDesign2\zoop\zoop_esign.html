<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Your Site</title>
  </head>

  <body>
    <!-- And add the code below into the body of your HTML -->
    <div id="zoop-gateway-model">
      <div id="zoop-model-content"></div>
    </div>
    <script>
      (() => {
        const o = Object.freeze({
          POPUP: 'POPUP',
          TAB: 'TAB',
          REDIRECT: 'REDIRECT',
        });
        !(function () {
          var e = {
              staging: 'https://preprod.aadhaarapi.com',
              production: 'https://prod.aadhaarapi.com',
              bsaURL: 'https://bsa.aadhaarapi.com',
              itdURL: 'https://itd.zoop.one',
              itdProdURL: 'https://itd.zoop.one',
              itdStagingURL: 'https://itd-staging.zoop.one',
              eSignV4URL: 'https://esign.zoop.plus',
              digilockerV1URL: 'https://gateway.zoop.one/digilocker/v1',
              livenessV1URL: 'https://gateway.zoop.one/liveness',
              studentSDKV1URL:
                'https://gateway.zoop.one/student-verification-sdk/request_id',
              url: 'https://prod.aadhaarapi.com',
              zoopModel: window.document.getElementById('zoop-gateway-model'),
              zoopWindow: null,
            },
            t = {
              zoopGateWayModel: {
                display: 'none',
                position: 'fixed',
                'z-index': 1,
                left: 0,
                top: 0,
                width: '100%',
                height: '100%',
                overflow: 'auto',
                'background-color': 'rgba(0, 0, 0, 0.4)',
              },
              zoopModelContent: {
                'border-radius': '10px',
                'background-color': '#fefefe',
                'margin-top': '50px',
                'margin-bottom': 'auto',
                'margin-left': 'auto',
                'margin-right': 'auto',
                padding: ' -1px',
                width: '700px',
                height: '675px',
              },
              iframe: {
                'border-radius': 'inherit',
                margin: '0px',
                padding: '0px',
                border: 'none',
              },
            },
            n = {
              options: e,
              styles: t,
              esignGatewayOptions: {
                gateway_url: '',
                transaction_id: '',
                company_display_name: '',
                color_bg: '0FACF3',
                color_ft: 'FFFFFF',
                logo_url: '',
                otp_mode: 'y',
                fp_mode: 'y',
                ir_mode: 'y',
                phone_auth: 'null',
                draggable_sign: 'y',
                google_sign: 'null',
                customer_email: '',
                customer_phone: '',
                show_download_btn: 'Y',
                mode: 'POPUP',
                zoomLevel: 2,
              },
              incomeTaxReturnsOptions: {
                txt_color: '202020',
                bg_color: 'f5f5f5',
                btn_color: '0075f3',
                btn_txt_color: 'ffffff',
                platform: 'web',
                sdk_version: '2',
                logo_url: '',
              },
              digilockerGatewayOption: {
                request_id: '',
                gatewayURL: e.digilockerV1URL,
                mode: o.TAB,
              },
              livenessGatewayOption: {
                request_id: '',
                gatewayURL: e.livenessV1URL,
                mode: o.REDIRECT,
              },
              studentSDKGatewayOption: {
                request_id: '',
                gatewayURL: e.studentSDKV1URL,
                mode: o.REDIRECT,
              },
              check: function (o, e) {
                return (
                  !!o.hasOwnProperty(e) &&
                  (!!n.isNullUndefinedOrEmpty(o[e]) ||
                    ('undefined' !== e && null !== e && 0 !== e.length))
                );
              },
              onError: function () {},
              onSuccess: function () {},
              isNullUndefinedOrEmpty: function (o) {
                return null == o || 0 === o.length;
              },
              setEnvironment: function (o) {
                switch (o) {
                  case 'production':
                    (n.options.url = n.options.production),
                      (n.options.itdURL = n.options.itdProdURL);
                    break;
                  case 'staging':
                    (n.options.url = n.options.staging),
                      (n.options.itdURL = n.options.itdStagingURL);
                }
              },
              setStyles: function (o, e, t) {
                let n;
                if ('class' == t) {
                  const e = document.getElementByClass(o);
                  n = e && e.style;
                } else if ('id' == t) {
                  const e = document.getElementById(o);
                  n = e && e.style;
                } else n = document.getElementsByTagName(o);
                if (n && Object.keys(e).length) for (var i in e) n[i] = e[i];
              },
              eSignGatewayInit: function (e) {
                return (
                  (n.esignGatewayOptions.company_display_name =
                    e.company_display_name),
                  (n.esignGatewayOptions.color_bg = n.check(e, 'color_bg')
                    ? e.color_bg
                    : n.esignGatewayOptions.color_bg),
                  (n.esignGatewayOptions.color_ft = n.check(e, 'color_ft')
                    ? e.color_ft
                    : n.esignGatewayOptions.color_ft),
                  (n.esignGatewayOptions.logo_url = n.check(e, 'logo_url')
                    ? e.logo_url
                    : n.esignGatewayOptions.logo_url),
                  (n.esignGatewayOptions.otp_mode = n.check(e, 'otp_mode')
                    ? e.otp_mode
                    : n.esignGatewayOptions.otp_mode),
                  (n.esignGatewayOptions.fp_mode = n.check(e, 'fp_mode')
                    ? e.fp_mode
                    : n.esignGatewayOptions.fp_mode),
                  (n.esignGatewayOptions.ir_mode = n.check(e, 'ir_mode')
                    ? e.ir_mode
                    : n.esignGatewayOptions.ir_mode),
                  (n.esignGatewayOptions.phone_auth = n.check(e, 'phone_auth')
                    ? e.phone_auth
                    : n.esignGatewayOptions.phone_auth),
                  (n.esignGatewayOptions.draggable_sign = n.check(
                    e,
                    'draggable_sign',
                  )
                    ? e.draggable_sign
                    : n.esignGatewayOptions.draggable_sign),
                  (n.esignGatewayOptions.google_sign = n.check(e, 'google_sign')
                    ? e.google_sign
                    : n.esignGatewayOptions.google_sign),
                  (n.esignGatewayOptions.customer_email = n.check(
                    e,
                    'customer_email',
                  )
                    ? e.customer_email
                    : n.esignGatewayOptions.customer_email),
                  (n.esignGatewayOptions.customer_phone = n.check(
                    e,
                    'customer_phone',
                  )
                    ? e.customer_phone
                    : n.esignGatewayOptions.customer_phone),
                  (n.esignGatewayOptions.show_download_btn = n.check(
                    e,
                    'show_download_btn',
                  )
                    ? e.show_download_btn
                    : n.esignGatewayOptions.show_download_btn),
                  (n.esignGatewayOptions.mode = n.check(e, 'mode')
                    ? e.mode
                    : n.esignGatewayOptions.mode),
                  (n.esignGatewayOptions.zoomLevel = n.check(e, 'zoomLevel')
                    ? Number.parseFloat(
                        0.8 +
                          0.2 *
                            (Math.max(
                              Math.min(Number.parseInt(e.zoomLevel), 7),
                              1,
                            ) -
                              1),
                      ).toFixed(1)
                    : n.esignGatewayOptions.zoomLevel),
                  e.mode && e.mode.toUpperCase() === o.TAB
                    ? (n.esignGatewayOptions.mode = o.TAB)
                    : e.mode && e.mode.toUpperCase() === o.REDIRECT
                    ? (n.esignGatewayOptions.mode = o.REDIRECT)
                    : (n.esignGatewayOptions.mode = o.POPUP),
                  (n.options.zoopModel =
                    window.document.getElementById('zoop-gateway-model')),
                  !0
                );
              },
              eSignGateway: function (e, t = 'v4') {
                console.log(e);
                if (n.isNullUndefinedOrEmpty(e))
                  throw new Error(
                    'Gateway Transaction Id is mandatory to initiate gateway.',
                  );
                if (
                  ((n.esignGatewayOptions.transaction_id = e),
                  'v3' == t &&
                    n.isNullUndefinedOrEmpty(
                      n.esignGatewayOptions.company_display_name,
                    ))
                )
                  throw new Error(
                    'Company Display Name is mandatory in gateway options.',
                  );
                switch (t) {
                  case 'v3':
                    n.esignGatewayOptions.gateway_url =
                      n.options.url + '/esign/gateway/v3';
                    break;
                  default:
                    (n.esignGatewayOptions.gateway_url =
                      n.options.eSignV4URL + '/v4/viewer'),
                      n.esignGatewayOptions.mode === o.POPUP &&
                        (n.esignGatewayOptions.mode = o.TAB);
                }
                let i =
                  n.esignGatewayOptions.gateway_url +
                  '/' +
                  n.esignGatewayOptions.transaction_id +
                  '?company_display_name=' +
                  n.esignGatewayOptions.company_display_name +
                  '&color_bg=' +
                  n.esignGatewayOptions.color_bg +
                  '&color_ft=' +
                  n.esignGatewayOptions.color_ft +
                  '&otp_mode=' +
                  n.esignGatewayOptions.otp_mode +
                  '&fp_mode=' +
                  n.esignGatewayOptions.fp_mode +
                  '&ir_mode=' +
                  n.esignGatewayOptions.ir_mode +
                  '&phone_auth=' +
                  n.esignGatewayOptions.phone_auth +
                  '&draggable_sign=' +
                  n.esignGatewayOptions.draggable_sign +
                  '&google_sign=' +
                  n.esignGatewayOptions.google_sign +
                  '&can_select_device=' +
                  n.esignGatewayOptions.device_selection_allowed +
                  '&phone=' +
                  n.esignGatewayOptions.customer_phone +
                  '&logo_url=' +
                  n.esignGatewayOptions.logo_url +
                  '&show_download_btn=' +
                  n.esignGatewayOptions.show_download_btn +
                  '&mode=' +
                  n.esignGatewayOptions.mode +
                  '&zoom_level=' +
                  n.esignGatewayOptions.zoomLevel +
                  '&v=4.2.0';
                n.esignGatewayOptions.mode === o.TAB
                  ? null == n.options.zoopWindow || n.options.zoopWindow.closed
                    ? (n.options.zoopWindow = window.open(
                        encodeURI(i),
                        '_blank',
                      ))
                    : n.options.zoopWindow.focus()
                  : n.esignGatewayOptions.mode === o.REDIRECT
                  ? (window.location = encodeURI(i))
                  : n.esignGatewayOptions.mode === o.POPUP &&
                    ((window.document.getElementById(
                      'zoop-model-content',
                    ).innerHTML =
                      '<iframe id="zoop-gateway-iframe" height="100%" width="100%" src="' +
                      encodeURI(i) +
                      '"></iframe>'),
                    (n.options.zoopModel.style.display = 'block'));
              },
              initBsaGateway: function (o) {
                n.bankStatementAnalysis = {
                  url: `${n.options.bsaURL}/?session_id=${o}&platform=web&sdk_v=2`,
                };
              },
              openBsaGateway: function () {
                window.location = n.bankStatementAnalysis.url;
              },
              initItdGateway: function (o = {}) {
                (n.incomeTaxReturnsOptions.txt_color = n.check(o, 'txt_color')
                  ? o.txt_color
                  : n.incomeTaxReturnsOptions.txt_color),
                  (n.incomeTaxReturnsOptions.bg_color = n.check(o, 'bg_color')
                    ? o.bg_color
                    : n.incomeTaxReturnsOptions.bg_color),
                  (n.incomeTaxReturnsOptions.btn_color = n.check(o, 'btn_color')
                    ? o.btn_color
                    : n.incomeTaxReturnsOptions.btn_color),
                  (n.incomeTaxReturnsOptions.btn_txt_color = n.check(
                    o,
                    'btn_txt_color',
                  )
                    ? o.btn_txt_color
                    : n.incomeTaxReturnsOptions.btn_txt_color),
                  (n.incomeTaxReturnsOptions.logo_url = n.check(o, 'logo_url')
                    ? o.logo_url
                    : n.incomeTaxReturnsOptions.logo_url);
              },
              openItdGateway: function (o) {
                if (n.isNullUndefinedOrEmpty(o))
                  throw new Error(
                    'Gateway Transaction Id is mandatory to initiate gateway.',
                  );
                let e = `${n.options.itdURL}/?session_id=${o}&txt_color=${n.incomeTaxReturnsOptions.txt_color}&bg_color=${n.incomeTaxReturnsOptions.bg_color}&btn_color=${n.incomeTaxReturnsOptions.btn_color}&btn_txt_color=${n.incomeTaxReturnsOptions.btn_txt_color}&logo_url=${n.incomeTaxReturnsOptions.logo_url}&platform=${n.incomeTaxReturnsOptions.platform}&sdk_version=${n.incomeTaxReturnsOptions.sdk_version}`;
                (window.document.getElementById(
                  'zoop-model-content',
                ).innerHTML =
                  '<iframe id="zoop-gateway-iframe" height="100%" width="100%" src="' +
                  encodeURI(e) +
                  '"></iframe>'),
                  (n.options.zoopModel.style.display = 'block');
              },
              initDigilockerGateway: function (e = {}) {
                e.mode &&
                  e.mode.toUpperCase() === o.REDIRECT &&
                  (n.digilockerGatewayOption.mode = o.REDIRECT);
              },
              openDigilockerGateway: function (e) {
                n.digilockerGatewayOption.request_id = e;
                const t = `${n.digilockerGatewayOption.gatewayURL}/start/${n.digilockerGatewayOption.request_id}?mode=${n.digilockerGatewayOption.mode}`;
                n.digilockerGatewayOption.mode === o.REDIRECT &&
                  (window.location = encodeURI(t)),
                  n.digilockerGatewayOption.mode === o.TAB &&
                    (null == n.options.zoopWindow || n.options.zoopWindow.closed
                      ? (n.options.zoopWindow = window.open(
                          encodeURI(t),
                          '_blank',
                        ))
                      : n.options.zoopWindow.focus());
              },
              initLivenessGateway: function (o = {}) {
                n.livenessGatewayOption.mode = o.mode;
              },
              openLivenessGateway: function (e) {
                n.livenessGatewayOption.request_id = e;
                const t = `${n.livenessGatewayOption.gatewayURL}/${n.livenessGatewayOption.request_id}?mode=${n.livenessGatewayOption.mode}`;
                if (n.livenessGatewayOption.mode !== o.REDIRECT)
                  throw new Error('only REDIRECT mode is supported');
                window.location = encodeURI(t);
              },
              initStudentSDKGateway: function (o = {}) {
                n.studentSDKGatewayOption.mode = o.mode;
              },
              openStudentSDKGateway: function (e) {
                n.studentSDKGatewayOption.request_id = e;
                const t = `${n.studentSDKGatewayOption.gatewayURL}/${n.studentSDKGatewayOption.request_id}/`;
                if (n.studentSDKGatewayOption.mode !== o.REDIRECT)
                  throw new Error('only REDIRECT mode is supported');
                window.location = encodeURI(t);
              },
            };
          const i = {
            close: () => {},
            'consent-denied': () => {},
            'otp-error': () => {},
            'gateway-error': () => {},
            'esign-result': () => {},
            'esign-success': () => {},
            'esign-error': () => {},
            'itd-error': () => {},
            'itd-consent-denied': () => {},
            'itd-gateway-terminated': () => {},
            'itd-success': () => {},
            'digilocker-error': () => {},
            'digilocker-success': () => {},
            'liveness-success': () => {},
            'liveness-failure': () => {},
            'liveness-timeout': () => {},
            'liveness-error': () => {},
            'liveness-internal-server-error': () => {},
            'liveness-invalid-reqid': () => {},
            'liveness-session-expired': () => {},
          };
          (n.on = function (o = '', e = () => {}) {
            if ('string' != typeof o)
              throw new Error('Event name must be a string.');
            if ('function' != typeof e)
              throw new Error('Callback must be a function.');
            Object.keys(i).includes(o)
              ? (i[o] = e)
              : console.warn(`No event found named ${o}`);
          }),
            (n.emit = function (o, e) {
              i[o](e);
            }),
            (n.dispatchEvent = function (o) {
              let e;
              if (
                ![
                  n.options.url,
                  n.options.production,
                  n.options.staging,
                  n.options.bsaURL,
                  n.options.itdURL,
                  n.options.eSignV4URL,
                  n.options.digilockerV1URL,
                  n.options.livenessV1URL,
                ].some((e) => e.startsWith(o.origin))
              )
                return console.log('Message is not from Zoop Gateway');
              if (o.data && ((e = o.data), e.hasOwnProperty('action')))
                switch (e.action) {
                  case 'close':
                  case 'consent-denied':
                  case 'otp-error':
                  case 'gateway-error':
                  case 'esign-result':
                  case 'esign-success':
                  case 'esign-error':
                  case 'itd-error':
                  case 'itd-consent-denied':
                  case 'itd-gateway-terminated':
                  case 'itd-success':
                  case 'digilocker-error':
                  case 'digilocker-success':
                  case 'liveness-failure':
                  case 'liveness-timeout':
                  case 'liveness-error':
                  case 'liveness-internal-server-error':
                  case 'liveness-invalid-reqid':
                  case 'liveness-session-expired':
                    return (
                      (e.payload =
                        'string' == typeof e.payload
                          ? JSON.parse(e.payload)
                          : e.payload),
                      (n.options.zoopModel.style.display = 'none'),
                      (window.document.getElementById(
                        'zoop-model-content',
                      ).innerHTML = ''),
                      n.options.zoopWindow &&
                        !n.options.zoopWindow.closed &&
                        n.options.zoopWindow.close(),
                      void n.emit(e.action, e)
                    );
                  default:
                    console.warn('Unsupported event: ', e.action);
                }
            }),
            n.setStyles('zoop-gateway-model', t.zoopGateWayModel, 'id'),
            n.setStyles('zoop-model-content', t.zoopModelContent, 'id'),
            window.addEventListener('message', n.dispatchEvent, !1),
            (window.zoop = n);
        })();
      })();
    </script>
    <script type="application/javascript">
      function openGateway() {
        zoop.eSignGatewayInit({ mode: 'REDIRECT', zoomLevel: 1 });
        zoop.eSignGateway('##ID##');
      }
      openGateway();
    </script>
  </body>
</html>
