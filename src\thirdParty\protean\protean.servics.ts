// Imports
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { APIService } from 'src/utils/api.service';
import { CryptService } from 'src/utils/crypt.service';
import { TypeService } from 'src/utils/type.service';
import {
  kParamMissing,
  kNoDataFound,
  kInternalError,
  kInvalidParamValue,
} from 'src/constants/responses';
import { k500Error } from 'src/constants/misc';
import {
  kProteanGetAccessTokenURL,
  kProteanEPFUANLookupURL,
  kProteanEPFUANValidationURL,
  kProteanCompanyLookUpURL,
  kProteanCompanySearchLiteURL,
  kProteanCompanyMasterDataURL,
  kProteanEPFUANValidationS_URL,
} from 'src/constants/network';
import { EnvConfig } from 'src/configs/env.config';
import { v4 as uuidv4 } from 'uuid';
import { CompanyRepository } from 'src/repositories/google.company.repository';
import { Op } from 'sequelize';
import { validAppTypeValues } from 'src/constants/globals';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { FetchDataEntity } from 'src/entities/fetchData.entity';
import { DateService } from 'src/utils/date.service';
import { EmploymentSharedService } from 'src/shared/employment.shared.service';

@Injectable()
export class ProteanService {
  constructor(
    private readonly api: APIService,
    private readonly cryptService: CryptService,
    private readonly companyRepo: CompanyRepository,
    private readonly typeService: TypeService,
    private readonly dateService: DateService,
    @Inject(forwardRef(() => EmploymentSharedService))
    private readonly employmentSharedService: EmploymentSharedService,
    private readonly repoManager: RepositoryManager,
  ) {}

  async generateAccessToken(reqData) {
    const apiKey = reqData?.apiKey;
    const secretKey = reqData?.secretKey;
    if (!apiKey) return kParamMissing('apiKey');
    if (!secretKey) return kParamMissing('secretKey');
    const url = kProteanGetAccessTokenURL;
    const auth = {
      username: apiKey,
      password: secretKey,
    };
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
    };
    const body = {
      grant_type: 'client_credentials',
    };
    const data = await this.api.post(url, body, headers, auth);
    if (data == k500Error) return kInternalError;
    if (!data.access_token) return kNoDataFound;
    return data.access_token;
  }

  async requestToProtean(reqData) {
    const url = reqData?.url;
    if (!url) return kParamMissing('url');
    const appType = reqData?.appType;
    const jsonObject = reqData?.jsonObject;
    if (!jsonObject) return kParamMissing('jsonObject');
    if (typeof jsonObject === 'object') jsonObject.consent = 'Y';
    else return kInvalidParamValue('jsonObject');
    if (!validAppTypeValues.includes(appType))
      return kInvalidParamValue('appType');
    const apiKey = EnvConfig.protean[appType]['uan1']['apiKey'];
    const secretKey = EnvConfig.protean[appType]['uan1']['secretKey'];
    const accessToken = await this.generateAccessToken({ apiKey, secretKey });
    if (accessToken.message) return kInternalError;
    const headers = {
      Authorization: `Bearer ${accessToken}`,
      apiKey,
    };
    const body = this.getCommandBodyFormatForProtean(jsonObject, appType);
    const response = await this.api.requestPost(url, body, headers);
    if (response == k500Error || response?.error_code) return kInternalError;
    const decryptedKey = this.cryptService.getDecryptedKeyForProtean(
      response.symmetricKey,
      appType,
    );
    let data: any = this.cryptService.getDecryptDataForProtean(
      response.data,
      decryptedKey,
    );
    data = data ? JSON.parse(data) : {};
    if (data?.error_code || !data?.result) return kInternalError;
    return { result: data.result, response: JSON.stringify(data) };
  }

  async uanLookup(reqData: any) {
    const mobileNo = reqData?.mobileNo;
    const appType = reqData?.appType;
    if (!mobileNo) return kParamMissing('mobileNo');
    if (typeof mobileNo != 'string') return kInvalidParamValue('mobileNo');
    if (!validAppTypeValues.includes(appType))
      return kInvalidParamValue('appType');
    const url = kProteanEPFUANLookupURL;
    const jsonObject = {
      mobile: mobileNo,
      consent: 'Y',
    };
    const apiKey = EnvConfig.protean[appType]['uan1']['apiKey'];
    const secretKey = EnvConfig.protean[appType]['uan1']['secretKey'];
    const accessToken = await this.generateAccessToken({ apiKey, secretKey });
    if (accessToken.message) return kInternalError;

    const headers = {
      Authorization: `Bearer ${accessToken}`,
      apiKey,
    };
    const body = this.getCommandBodyFormatForProtean(jsonObject, appType);
    const response = await this.api.requestPost(url, body, headers);

    const logTrackData = {
      phone: this.cryptService.encryptPhone(mobileNo),
      hashPhone: this.cryptService.getMD5Hash(mobileNo),
      result: null,
      requestData: JSON.stringify(jsonObject),
      response: null,
      type: 'PROTEAN_UAN_LOOKUP',
    };

    if (response == k500Error || response?.error_code) {
      logTrackData.response = JSON.stringify(response);
      await this.repoManager.createRowData(FetchDataEntity, logTrackData);
      return kInternalError;
    }
    const decryptedKey = this.cryptService.getDecryptedKeyForProtean(
      response.symmetricKey,
      appType,
    );
    let data: any = this.cryptService.getDecryptDataForProtean(
      response.data,
      decryptedKey,
    );
    data = data ? JSON.parse(data) : {};

    logTrackData.response = JSON.stringify(data);
    await this.repoManager.createRowData(FetchDataEntity, logTrackData);

    if (data?.error_code || !data?.result) return kInternalError;
    if (Array.isArray(data?.result?.uan)) return data?.result;
    return { uan: [] };
  }

  async uanValidation(reqData: any) {
    const uanNumber = reqData?.uanNumber;
    const appType = reqData?.appType;

    if (!uanNumber) return kParamMissing('uanNumber');
    if (typeof uanNumber != 'string') return kInvalidParamValue('uanNumber');
    if (!validAppTypeValues.includes(appType))
      return kInvalidParamValue('appType');
    const url = kProteanEPFUANValidationURL;
    const jsonObject = {
      uan: uanNumber,
      consent: 'Y',
    };
    const apiKey = EnvConfig.protean[appType]['uan1']['apiKey'];
    const secretKey = EnvConfig.protean[appType]['uan1']['secretKey'];
    const accessToken = await this.generateAccessToken({ apiKey, secretKey });

    if (accessToken.message) return kInternalError;
    const headers = {
      Authorization: `Bearer ${accessToken}`,
      apiKey,
    };
    const body = this.getCommandBodyFormatForProtean(jsonObject, appType);

    const response = await this.api.requestPost(url, body, headers);

    const logTrackData = {
      phone: null,
      hashPhone: null,
      result: null,
      requestData: JSON.stringify(jsonObject),
      response: null,
      type: 'PROTEAN_UAN_VALIDATION',
    };

    if (response == k500Error || response?.error_code) {
      logTrackData.response = JSON.stringify(response);
      await this.repoManager.createRowData(FetchDataEntity, logTrackData);
      return kInternalError;
    }
    const decryptedKey = this.cryptService.getDecryptedKeyForProtean(
      response.symmetricKey,
      appType,
    );

    let data: any = this.cryptService.getDecryptDataForProtean(
      response.data,
      decryptedKey,
    );

    data = data ? JSON.parse(data) : {};
    logTrackData.response = JSON.stringify(data);
    await this.repoManager.createRowData(FetchDataEntity, logTrackData);

    if (data?.error_code || !data?.result) return kInternalError;
    return data.result;
  }

  async uanValidationS(reqData: any) {
    const uanNumber = reqData?.uanNumber;
    const appType = reqData?.appType;
    if (!uanNumber) return kParamMissing('uanNumber');
    if (typeof uanNumber != 'string') return kInvalidParamValue('uanNumber');
    if (!validAppTypeValues.includes(appType))
      return kInvalidParamValue('appType');
    const url = kProteanEPFUANValidationS_URL;
    const jsonObject = {
      uan: uanNumber,
    };
    const apiKey = EnvConfig.protean[appType]['uan2']['apiKey'];
    const secretKey = EnvConfig.protean[appType]['uan2']['secretKey'];
    const accessToken = await this.generateAccessToken({ apiKey, secretKey });

    if (accessToken.message) return kInternalError;
    const headers = {
      Authorization: `Bearer ${accessToken}`,
      apiKey,
    };
    const body = this.getCommandBodyFormatForProtean(jsonObject, appType);

    const response = await this.api.requestPost(url, body, headers);
    const logTrackData = {
      phone: null,
      hashPhone: null,
      result: null,
      requestData: JSON.stringify(jsonObject),
      response: null,
      type: 'PROTEAN_UAN_VALIDATION_S',
    };

    if (response == k500Error || response?.error_code) {
      logTrackData.response = JSON.stringify(response);
      await this.repoManager.createRowData(FetchDataEntity, logTrackData);
      return kInternalError;
    }

    const decryptedKey = this.cryptService.getDecryptedKeyForProtean(
      response.symmetricKey,
      appType,
    );

    let data: any = this.cryptService.getDecryptDataForProtean(
      response.data,
      decryptedKey,
    );
    data = data ? JSON.parse(data) : {};

    logTrackData.response = JSON.stringify(data);
    await this.repoManager.createRowData(FetchDataEntity, logTrackData);

    if (data?.error_code || !data?.result) return kInternalError;
    if (Array.isArray(data.result)) return data.result;
    return [];
  }

  async getCompanyListByLite(reqData: any) {
    const companyName = reqData?.companyName;
    const appType = reqData?.appType;
    if (!companyName) return kParamMissing('companyName');
    if (!validAppTypeValues.includes(appType))
      return kInvalidParamValue('appType');

    const url = kProteanCompanySearchLiteURL;
    const jsonObject = {
      companyName: companyName,
      consent: 'Y',
    };
    const apiKey = EnvConfig.protean[appType]['company']['apiKey'];
    const secretKey = EnvConfig.protean[appType]['company']['secretKey'];
    const accessToken = await this.generateAccessToken({ apiKey, secretKey });
    if (accessToken.message) return kInternalError;
    const headers = {
      Authorization: `Bearer ${accessToken}`,
      apiKey,
    };

    const body = this.getCommandBodyFormatForProtean(jsonObject, appType);

    const response = await this.api.requestPost(url, body, headers);

    const logTrackData = {
      phone: null,
      hashPhone: null,
      result: null,
      requestData: JSON.stringify(jsonObject),
      response: null,
      type: 'PROTEAN_COMPANY_SEARCH_LITE',
    };

    if (response == k500Error || response?.error_code) {
      logTrackData.response = JSON.stringify(response);
      await this.repoManager.createRowData(FetchDataEntity, logTrackData);
      return kInternalError;
    }
    const decryptedKey = this.cryptService.getDecryptedKeyForProtean(
      response.symmetricKey,
      appType,
    );

    let data: any = this.cryptService.getDecryptDataForProtean(
      response.data,
      decryptedKey,
    );
    data = data ? JSON.parse(data) : {};

    logTrackData.response = JSON.stringify(data);
    await this.repoManager.createRowData(FetchDataEntity, logTrackData);

    if (data?.error_code || !data?.result) return kInternalError;
    if (Array.isArray(data.result)) return data.result;
    return [];
  }

  async getCompanyListByLookup(reqData: any) {
    const companyName = reqData?.companyName;
    const appType = reqData?.appType;
    if (!companyName) return kParamMissing('companyName');
    if (!validAppTypeValues.includes(appType))
      return kInvalidParamValue('appType');

    const url = kProteanCompanyLookUpURL;
    const jsonObject = {
      company_name: companyName,
      consent: 'Y',
    };
    const apiKey = EnvConfig.protean[appType]['company']['apiKey'];
    const secretKey = EnvConfig.protean[appType]['company']['secretKey'];
    const accessToken = await this.generateAccessToken({ apiKey, secretKey });
    if (accessToken.message) return kInternalError;
    const headers = {
      Authorization: `Bearer ${accessToken}`,
      apiKey,
    };
    const body = this.getCommandBodyFormatForProtean(jsonObject, appType);
    const response = await this.api.requestPost(url, body, headers);
    const logTrackData = {
      phone: null,
      hashPhone: null,
      result: null,
      requestData: JSON.stringify(jsonObject),
      response: null,
      type: 'PROTEAN_COMPANY_LOOKUP',
    };

    if (response == k500Error || response?.error_code) {
      logTrackData.response = JSON.stringify(response);
      await this.repoManager.createRowData(FetchDataEntity, logTrackData);
      return kInternalError;
    }

    const decryptedKey = this.cryptService.getDecryptedKeyForProtean(
      response.symmetricKey,
      appType,
    );

    let data: any = this.cryptService.getDecryptDataForProtean(
      response.data,
      decryptedKey,
    );
    data = data ? JSON.parse(data) : {};

    logTrackData.response = JSON.stringify(data);
    await this.repoManager.createRowData(FetchDataEntity, logTrackData);

    if (data?.error_code || !data?.result) return kInternalError;
    if (Array.isArray(data.result)) return data.result;
    return [];
  }

  async companyDetailsLookUp(reqData: any) {
    const cin = reqData?.cin;
    const appType = reqData?.appType;
    if (!cin) return kParamMissing('cin');
    if (!validAppTypeValues.includes(appType))
      return kInvalidParamValue('appType');

    const url = kProteanCompanyMasterDataURL;
    const jsonObject = {
      cin,
      consent: 'Y',
    };
    const apiKey = EnvConfig.protean[appType]['company']['apiKey'];
    const secretKey = EnvConfig.protean[appType]['company']['secretKey'];
    const accessToken = await this.generateAccessToken({ apiKey, secretKey });
    if (accessToken.message) return kInternalError;
    const headers = {
      Authorization: `Bearer ${accessToken}`,
      apiKey,
    };
    const body = this.getCommandBodyFormatForProtean(jsonObject, appType);

    const response = await this.api.requestPost(url, body, headers);

    const logTrackData = {
      phone: null,
      hashPhone: null,
      result: null,
      requestData: JSON.stringify(jsonObject),
      response: null,
      type: 'PROTEAN_COMPANY_MCA',
    };

    if (response == k500Error || response?.error_code) {
      logTrackData.response = JSON.stringify(response);
      await this.repoManager.createRowData(FetchDataEntity, logTrackData);
      return kInternalError;
    }
    const decryptedKey = this.cryptService.getDecryptedKeyForProtean(
      response.symmetricKey,
      appType,
    );

    let data: any = this.cryptService.getDecryptDataForProtean(
      response.data,
      decryptedKey,
    );
    data = data ? JSON.parse(data) : {};

    logTrackData.response = JSON.stringify(data);
    await this.repoManager.createRowData(FetchDataEntity, logTrackData);

    if (data?.error_code || !data?.result) return kInternalError;
    return data.result;
  }

  async searchCompany(reqData: any) {
    let searchStr = reqData?.searchStr;
    if (!searchStr) return kParamMissing('searchStr');
    const appType = reqData?.appType;
    if (!validAppTypeValues.includes(appType))
      return kInvalidParamValue('appType');

    searchStr = searchStr?.trim();
    if (!searchStr || searchStr?.length <= 2) return [];
    searchStr = searchStr.toUpperCase();
    searchStr = searchStr?.replaceAll('\n', ' ')?.toUpperCase();

    let companyList: any = [];

    // Check in database
    companyList = await this.getCompanyDataLocally(searchStr);
    if (companyList.message) return kInternalError;
    companyList = companyList.map((company) => company.companyName);
    if (companyList.length > 0) return companyList;

    //change lookup fun with lite if like to use lite api
    const companysData = await this.getCompanyListByLookup({
      companyName: searchStr,
      appType,
    });
    if (companysData.message) return kInternalError;
    if (!companysData.length) return companysData;

    const isCompanySaved = await this.saveCompanyInLocally(companysData);
    if (!isCompanySaved) return kInternalError;
    companyList = companysData.map((company) => {
      const companyName = (company?.companyName ?? company?.entityName)
        ?.trim()
        ?.toUpperCase();
      return companyName;
    });
    return companyList;
  }

  async getCompanyDataLocally(searchStr) {
    const attributes = ['id', 'companyName', 'CIN', 'companyDetails', 'source'];
    const options = {
      where: {
        companyName: {
          [Op.like]: searchStr + '%',
        },
      },
    };
    let companyList = await this.companyRepo.getTableWhereData(
      attributes,
      options,
    );
    if (companyList == k500Error) return kInternalError;
    return companyList;
  }

  async saveCompanyInLocally(companyList) {
    let companyArrFromProtean = companyList.map((company) => {
      const companyName = (company.companyName ?? company.entityName)
        ?.trim()
        ?.toUpperCase();
      const CIN = company?.companyID ?? company?.cin;
      return { companyName, CIN, source: 'PROTEAN' };
    });

    let companyNameArrFromProtean = companyArrFromProtean.map(
      (el) => el.companyName,
    );
    companyNameArrFromProtean = [...new Set(companyNameArrFromProtean)];
    const companyFromDb = await this.companyRepo.getTableWhereData(
      ['companyName'],
      { where: { companyName: companyNameArrFromProtean } },
    );
    if (companyFromDb == k500Error) return false;
    const companyNameArrFromDb = companyFromDb.map((el) => el.companyName);
    const uniqueCompanyNameArr = companyNameArrFromProtean.filter(
      (el) => !companyNameArrFromDb.includes(el),
    );
    const uniqueCompanyArr = [];
    companyArrFromProtean.forEach((company) => {
      const canAdd =
        uniqueCompanyNameArr.includes(company.companyName) &&
        !uniqueCompanyArr.find((el) => el.companyName == company.companyName);
      if (canAdd) uniqueCompanyArr.push(company);
    });
    const isBulkCreted = await this.companyRepo.bulkCreate(uniqueCompanyArr);
    if (isBulkCreted == k500Error) return false;
    return true;
  }

  async getCompanyDataFromProtean(reqData) {
    let companyName = reqData?.companyName;
    const appType = reqData?.appType;
    if (!companyName) return kParamMissing('searchStr');
    if (!validAppTypeValues.includes(appType))
      return kInvalidParamValue('appType');

    companyName = companyName?.trim();
    if (!companyName || companyName?.length <= 2) return [];
    companyName = companyName.toUpperCase();

    const allCompanyData = await this.getCompanyDataLocally(companyName);
    if (allCompanyData == k500Error) return kInternalError;

    const companyListDataNew: any = [];
    let company: any;
    for (let i = 0; i < allCompanyData.length; i++) {
      const ele = allCompanyData[i];
      if (ele?.companyDetails && ele?.CIN && ele?.companyName == companyName)
        companyListDataNew.push(ele);
    }

    if (companyListDataNew.length > 0) {
      return companyListDataNew;
    } else if (companyListDataNew.length == 0) {
      const find = allCompanyData.find((el) => el.companyName == companyName);

      if (!find || !find?.CIN) {
        //change lookup fun with lite if like to use lite api
        const companysData = await this.getCompanyListByLookup({
          companyName,
          appType,
        });
        if (companysData.message) return kInternalError;
        for (let index = 0; index < companysData.length; index++) {
          company = companysData[index];
          const companyNameFromProtean =
            company.companyName?.toUpperCase() ??
            company.entityName?.toUpperCase();
          const cinFromProtean = company?.companyID ?? company?.cin;
          if (companyNameFromProtean == companyName) {
            company = {
              companyName: companyNameFromProtean,
              CIN: cinFromProtean,
            };
            break;
          }
        }
      }
      const companyDetails = await this.companyDetailsLookUp({
        cin: company?.CIN ?? find?.CIN,
        appType,
      });
      if (companyDetails.message) return kInternalError;

      let establishedDate = companyDetails['Date_of_Incorporation']?.length
        ? companyDetails['Date_of_Incorporation']
        : new Date();
      if (typeof establishedDate == 'string') {
        establishedDate = establishedDate?.split('/')?.reverse()?.join('-');
        establishedDate = new Date(establishedDate);
      }
      const updatedCompanyData: any = {
        companyDetails,
        CIN: company?.CIN ?? find?.CIN,
        establishedDate,
        forMigration: null,
        source: 'PROTEAN',
      };
      if (find) {
        await this.companyRepo.updateRowData(updatedCompanyData, find.id);
        company = {
          ...find,
          ...updatedCompanyData,
        };
      } else {
        updatedCompanyData.companyName = company.companyName;
        company = await this.companyRepo.createRowData(updatedCompanyData);
      }
      companyListDataNew.push(company);
      return companyListDataNew;
    }
  }

  private getCommandBodyFormatForProtean(jsonObject, appType) {
    return {
      symmetricKey: this.cryptService.getEncryptedKeyForProtean(appType),
      data: this.cryptService.getEncryptDataForProtean(jsonObject, appType),
      hash: this.cryptService.getHashForProtean(jsonObject, appType),
      version: '1.0.0',
      timestamp: `${new Date().toISOString()}`,
      requestId: uuidv4(),
    };
  }

  async uanDetails(reqData: any) {
    const mobileNo = reqData?.mobileNo;
    const appType = reqData?.appType;
    if (!mobileNo) return kParamMissing('mobileNo');
    if (typeof mobileNo != 'string') return kInvalidParamValue('mobileNo');
    if (!validAppTypeValues.includes(appType))
      return kInvalidParamValue('appType');
    const data = await this.employmentSharedService.getUanDetails({
      ...reqData,
      typeOfService: 1,
    });
    return data;
  }

  async getEPFODataUsingUanValidation(uanList: Array<String>, appType: any) {
    const finalEmploymentData: any = {};
    const employers = [];
    //get employment details
    for (let index = 0; index < uanList.length; index++) {
      const uan = uanList[index];
      const uanValidationData = await this.uanValidation({
        uanNumber: uan,
        appType,
      });
      if (uanValidationData?.message) return uanValidationData;
      const employer = uanValidationData?.employers ?? [];
      employers.push(...employer);
      //add personalDetails in final response data
      if (
        !finalEmploymentData?.personalDetails &&
        uanValidationData?.personalDetails?.name?.trim()
      )
        finalEmploymentData.personalDetails = uanValidationData.personalDetails;
    }

    //format final response data
    let lastEmployer = null,
      minimumWorkExperienceInMonths = null,
      latestJoiningDate = null;

    //get lastEmployer and latestJoiningDate and minimumWorkExperienceInMonths
    for (const emp of employers) {
      const startDate = emp?.startMonthYear
        ? this.typeService.parseDate(emp.startMonthYear)
        : null;
      const endDate = emp?.lastMonthYear
        ? this.typeService.parseDate(emp.lastMonthYear)
        : null;
      //change latestJoiningDate and lastEmployer if need
      if (startDate && (!lastEmployer || startDate > latestJoiningDate)) {
        latestJoiningDate = startDate;
        lastEmployer = emp;
      }
      //add total minimumWorkExperienceInMonths
      if (startDate && endDate)
        minimumWorkExperienceInMonths += this.typeService.dateDifference(
          startDate,
          endDate,
          'Month',
        );
    }

    let vintageInMonths = null,
      exitReason = null,
      joiningDate = null,
      lastPFDate = null;
    if (lastEmployer) {
      joiningDate = lastEmployer?.startMonthYear
        ? this.typeService.parseDate(lastEmployer?.startMonthYear)
        : null;
      lastPFDate = lastEmployer?.lastMonthYear
        ? this.typeService.parseDate(lastEmployer?.lastMonthYear)
        : null;
      if (joiningDate && lastPFDate)
        vintageInMonths = this.typeService.dateDifference(
          joiningDate,
          lastPFDate,
          'Month',
        );
      exitReason = lastEmployer?.exitReason?.trim()?.length
        ? lastEmployer?.exitReason?.trim()
        : null;
    }

    finalEmploymentData.employers = employers;
    finalEmploymentData.summary = {
      minimumWorkExperienceInMonths,
      lastEmployer: {
        employerName: lastEmployer?.establishmentName?.trim()?.length
          ? lastEmployer?.establishmentName?.trim()
          : null,
        startMonthYear: lastEmployer?.startMonthYear ?? null,
        lastMonthYear: lastEmployer?.lastMonthYear ?? null,
        vintageInMonths,
      },
    };

    const data = {
      typeOfService: 1,
      tenureOfEmployment:
        finalEmploymentData?.summary?.lastEmployer?.vintageInMonths,
      totalTenureOfEmployment:
        finalEmploymentData?.summary?.minimumWorkExperienceInMonths,
      joiningDate: joiningDate
        ? this.typeService.getGlobalDate(joiningDate)
        : null,
      lastPFDate: lastPFDate
        ? this.typeService.getGlobalDate(lastPFDate)
        : null,
      exitDate: null,
      latestCompanyName:
        finalEmploymentData?.summary?.lastEmployer?.employerName?.trim()?.length
          ? finalEmploymentData?.summary?.lastEmployer?.employerName
              ?.trim()
              ?.toUpperCase()
          : null,
      employeeName: finalEmploymentData?.personalDetails?.name?.trim()
        ? finalEmploymentData?.personalDetails?.name?.trim()?.toUpperCase()
        : null,
      exitReason,
      uanList,
      allEmploymentData: JSON.stringify(finalEmploymentData),
    };
    return data;
  }

  async getEPFODataUsingUanValidationS(uanList: Array<String>, appType: any) {
    const employers = [];
    //get employment details
    for (let index = 0; index < uanList.length; index++) {
      const uan = uanList[index];
      const uanValidationData = await this.uanValidationS({
        uanNumber: uan,
        appType,
      });
      if (uanValidationData?.message) return uanValidationData;
      const employer = uanValidationData ?? [];
      employers.push(...employer);
    }

    //format final response data
    let lastEmployer = null,
      latestJoiningDate = null,
      exitDate = null,
      totalTenureOfEmployment = null,
      tenureOfEmployment = null;
    for (const emp of employers) {
      const startDate =
        emp?.dateOfJoining?.length == 11
          ? this.dateService.anyToDate(emp?.dateOfJoining)
          : null;
      //change lastEmployer if need
      if (startDate && (!lastEmployer || startDate > latestJoiningDate)) {
        lastEmployer = emp;
        latestJoiningDate = startDate;
      }
      if (emp?.tenureOfEmployment > 0)
        totalTenureOfEmployment += emp?.tenureOfEmployment;
    }
    //add exitDate and tenureOfEmployment
    if (lastEmployer) {
      exitDate =
        lastEmployer?.dateOfExit?.length == 11
          ? this.dateService.anyToDate(lastEmployer?.dateOfExit)
          : null;
      exitDate = exitDate ? this.typeService.getGlobalDate(exitDate) : null;
      if (lastEmployer?.tenureOfEmployment > 0)
        tenureOfEmployment = lastEmployer?.tenureOfEmployment;
    }

    const data = {
      typeOfService: 1,
      tenureOfEmployment,
      totalTenureOfEmployment,
      joiningDate: latestJoiningDate
        ? this.typeService.getGlobalDate(latestJoiningDate)
        : null,
      lastPFDate: null,
      exitDate,
      latestCompanyName: lastEmployer?.establishmentName?.trim()?.length
        ? lastEmployer?.establishmentName?.trim()?.toUpperCase()
        : null,
      employeeName: lastEmployer?.name?.trim()?.length
        ? lastEmployer?.name?.trim()?.toUpperCase()
        : null,
      exitReason: null,
      uanList,
      allEmploymentData: JSON.stringify(employers),
    };
    return data;
  }
}
