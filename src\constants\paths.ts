export const CRYPT_PATH = {
  authPrivateKey: 'secrets/auth_private.pem',
  authPublicKey: 'secrets/auth_public.pem',
  proteanLspAuthPublicKey: 'secrets/protean_lsp_auth_public.pem',
  proteanLspAuthPrivateKey: 'secrets/protean_lsp_auth_private.pem',
  proteanNbfcAuthPublicKey: 'secrets/protean_nbfc_auth_public.pem',
  proteanNbfcAuthPrivateKey: 'secrets/protean_nbfc_auth_private.pem',
  oraclePublicKey: 'secrets/oracle_public_key.pem',
  oraclePrivateKey: 'secrets/oracle_private_key.pem',
  miplExceptionLoans: 'secrets/miplExceptionLoan.json',
  yesBankPublicKey: 'secrets/YBL_Public_Key_PROD.cer',
  defaulters: 'secrets/defaulters.json',
  inActiveDefaulters: 'secrets/In Active Defaulters Json.json',
  loanData: 'secrets/loanData.json',
  emiData: 'secrets/emiData.json',
  refundCreditData: 'secrets/refundCreditData.json',
  activeUsers: 'secrets/Active_users_loan.json',
  activeOntimeUsers: 'secrets/activeOntimeLoans.json',
  completedLoanCreditData: 'secrets/completedLoan_creditData.json',
  bankTranserCreditData: 'secrets/bankTransferCreditData.json',
};
