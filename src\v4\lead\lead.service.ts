// Imports
import { Injectable } from '@nestjs/common';
import { k500Error } from 'src/constants/misc';
import { nTagTransactions } from 'src/constants/network';
import { Experiment } from 'src/entities/experiment_schema';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { APIService } from 'src/utils/api.service';
import { FileService } from 'src/utils/file.service';

@Injectable()
export class LeadServiceV4 {
    constructor(
        private readonly api: APIService,
        private readonly fileService: FileService,
        private readonly repo: RepositoryManager,
    ) { }

    async expressEligibility(reqData) {
        new Promise(async (resolve, _) => {
            const creationData = {
                type: 'EXPRESS_ELIGIBLITY',
                value: JSON.stringify(reqData),
                userId: '-',
            };
            await this.repo.createRowData(Experiment, creationData);
            resolve({});
        }).catch((err) => { });

        const response = reqData.body ?? {};
        const accDetails = response?.accountDetails ?? {};
        const inputs = response?.inputs ?? {};
        const filePath = JSON.parse(response?.filePath ?? '{}');
        const transactions = filePath?.transactions ?? [];
        const row_file = filePath?.row_file ?? '';
        let fileUrl = '-';

        delete response.inputs;
        delete response?.accountDetails;

        if (row_file) {
            const buffer_data = Buffer.from(row_file, 'base64');
            fileUrl = await this.fileService.base64ToFileURL(buffer_data);
        }

        const preparedData = {
            statements: transactions,
            userName: '-',
            companyName: '-',
            salary: inputs?.netSalary ? +inputs.netSalary : 35000,
        };
        
        preparedData['callerApi'] = 'ExpressFlow';

        const tagResponse = await this.api.post(nTagTransactions, preparedData);
        if (tagResponse == k500Error) throw new Error();

        const transList = tagResponse.data.transactions ?? [];
        let loanCredits = 0;
        let salaryCredits = 0;
        let otherIncomeCredits = 0;
        for (let index = 0; index < transList.length; index++) {
            const transData = transList[index];
            const category = transData.prediction1?.toLowerCase();
            const isCredit = transData?.type == 'CREDIT';

            if (!isCredit) continue;
            if (['application', 'upi', 'interest', 'other'].includes(category))
                continue;

            if (category == 'loan') loanCredits++;
            else if (category == 'salary') salaryCredits++;
            else otherIncomeCredits++;
        }

        const finalizedData = {
            accDetails,
            fileUrl,
            inputs,
            loanCredits,
            salaryCredits,
            otherIncomeCredits,
        };

        new Promise(async (resolve, _) => {
            const creationData = {
                type: 'EXPRESS_ELIGIBLITY_RESULT',
                value: JSON.stringify(finalizedData),
                userId: '-',
            };
            await this.repo.createRowData(Experiment, creationData);
            resolve({});
        }).catch((err) => { });

        return {};
    }
}
