// Imports
import { Inject, Injectable, forwardRef } from '@nestjs/common';
import {
  MICRO_ALERT_TOPIC,
  UserStage,
  WHATSAPP_SERVICE,
} from 'src/constants/objects';
import {
  k422ErrorMessage,
  kInternalError,
  kParamMissing,
} from 'src/constants/responses';
import { k500Error } from 'src/constants/misc';
import {
  DAYS_OF_CHECK_WHATSAPP_NUMBER,
  gIsPROD,
  promotionalWhatsAppTemplates,
  SYSTEM_ADMIN_ID,
  templateDesign,
  UAT_PHONE_NUMBER,
} from 'src/constants/globals';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { CryptService } from 'src/utils/crypt.service';
import { UserRepository } from 'src/repositories/user.repository';
import { LoanRepository } from 'src/repositories/loan.repository';
import { KafkaService } from 'src/microservice/kafka/kafka.service';
import { Op } from 'sequelize';
import { MasterEntity } from 'src/entities/master.entity';
import { TypeService } from 'src/utils/type.service';
import { DateService } from 'src/utils/date.service';
import {
  kKycProcessStep,
  kKycButton,
  kNoDataFound,
  kUserNotExists,
  kLoanAcceptTitle,
  kLoanEMandateTitle,
  kLoanESignTitle,
  kReviewLink,
  kPaymentLinkTitle,
} from 'src/constants/strings';
import { MailTrackerRepository } from 'src/repositories/mail.tracker.repository';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { MsgEntity } from 'src/entities/msg.entity';
import { HashPhoneEntity } from 'src/entities/hashPhone.entity';
import { EnvConfig } from 'src/configs/env.config';
import { APIService } from 'src/utils/api.service';
import { nCheckWhatsAppNumber, isUserOnWhatsApp } from 'src/constants/network';
import { registeredUsers } from 'src/entities/user.entity';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { WhatsAppCheck } from 'src/entities/whatsapp.check.schema';
import { FileService } from 'src/utils/file.service';
import { ErrorContextService } from 'src/utils/error.context.service';
import { RedisService } from 'src/redis/redis.service';

@Injectable()
export class WhatsAppService {
  constructor(
    @Inject(forwardRef(() => CommonSharedService))
    private readonly sharedCommonService: CommonSharedService,
    private readonly cryptService: CryptService,
    private readonly userRepo: UserRepository,
    private readonly loanRepo: LoanRepository,
    private readonly kafkaService: KafkaService,
    private readonly typeService: TypeService,
    private readonly dateServices: DateService,
    private readonly repo: RepositoryManager,
    // @Inject(forwardRef(() => APIService))
    private readonly api: APIService,
    @Inject(forwardRef(() => FileService))
    private readonly fileService: FileService,
    private readonly errorContextService: ErrorContextService,
    private readonly redisService: RedisService,
    @InjectModel(WhatsAppCheck.name, EnvConfig.database.mongodb.databaseName)
    private readonly WhatsAppCheckModel: Model<WhatsAppCheck>,
  ) {}

  async sendWhatsAppMessageMicroService(reqData) {
    try {
      let sentBy: string;

      let {
        appType,
        loanId,
        userId,
        adminId,
        title,
        number,
        needRes = false,
      } = reqData;

      // promotional excpetion User
      if (promotionalWhatsAppTemplates.includes(title)) {
        let encryptedData;
        if (!isNaN(number)) {
          encryptedData = await this.cryptService.encryptPhone(number);
          encryptedData = encryptedData.split('===')[1];
        }
        // const userIdWhere = userId && { id: userId };
        const userData = await this.userRepo.getRowWhereData(
          ['id', 'allowedPromotionalContent', 'isBlacklist'],
          {
            where: {
              phone: {
                [Op.like]: encryptedData ? '%' + encryptedData + '%' : null,
              },
            },
            include: [
              {
                model: MasterEntity,
                attributes: ['id', 'coolOffData'],
                required: false,
              },
            ],
          },
        );
        if (!userData || userData === k500Error) return kInternalError;

        const currentDate = this.typeService.getGlobalDate(new Date());
        const coolOffEndsDate = this.typeService.getGlobalDate(
          userData?.masterData?.coolOffData?.coolOffEndsOn,
        );
        const isUserCooledOff = currentDate < coolOffEndsDate;
        if (
          String(userData.allowedPromotionalContent) == 'false' ||
          userData.isBlacklist == '1' ||
          isUserCooledOff
        )
          return false;
      }

      adminId = adminId ?? SYSTEM_ADMIN_ID;
      if (appType == null) {
        if (templateDesign == '1') {
          if (loanId) {
            const loanAtr = ['appType'];
            const loanOptions = { where: { id: loanId } };
            const loanData = await this.loanRepo.getRowWhereData(
              loanAtr,
              loanOptions,
            );
            if (loanData === k500Error) return kInternalError;
            appType = loanData?.appType;
          } else {
            const userAtr = ['appType'];
            const userOptions = { where: { id: userId } };
            const userData = await this.userRepo.getRowWhereData(
              userAtr,
              userOptions,
            );
            if (userData === k500Error) return kInternalError;
            appType = userData?.appType;
          }
        }
        if (templateDesign == '0') appType = 0;
      }

      if (adminId && adminId != SYSTEM_ADMIN_ID) {
        sentBy = (await this.sharedCommonService.getAdminData(adminId))
          ?.fullName;
      }

      // Set whatsapp service
      const msg91Template = [
        'Legal Notice',
        'Summons',
        'Disbursement',
        kLoanAcceptTitle,
        kLoanEMandateTitle,
        kLoanESignTitle,
        kReviewLink,
        kPaymentLinkTitle,
      ];
      let service = WHATSAPP_SERVICE.COMBOT;
      if (msg91Template.includes(title)) service = WHATSAPP_SERVICE.MSG91;
      const payload = {
        ...reqData,
        userId,
        loanId,
        adminId,
        appType,
        sentBy,
        service,
      };

      const microApi = () =>
        this.kafkaService.send(
          MICRO_ALERT_TOPIC.SEND_WHATSAPP_MESSAGE,
          payload,
        );
      // call api if need to return response
      if (needRes) return await microApi();
      microApi();
      return {};
    } catch (error) {
      return kInternalError;
    }
  }

  // webhook
  async storeWhatsAppWebhookResponse(body) {
    this.kafkaService.send(MICRO_ALERT_TOPIC.STORE_WHATSAPP_WEBHOOK, body);
    return {};
  }

  // send whatsapp campaign message
  async sendCampaignMessage(body) {
    let { userIds, title, appType, phone } = body;
    if (!title) return kParamMissing('title');
    if (!userIds && !phone) return kParamMissing('userIds or phone');
    let hashPhone;
    if (!userIds && phone) {
      hashPhone = [
        ...new Set(
          phone.map((el) => this.cryptService.getMD5Hash(el.toString())),
        ),
      ];
    } else userIds = [...new Set(userIds.map((ele) => ele))];

    if (hashPhone) {
      const hashPhoneData = await this.repo.getTableWhereData(
        HashPhoneEntity,
        ['userId'],
        { where: { hashPhone } },
      );
      if (hashPhoneData === k500Error) throw new Error();
      userIds = [...new Set(hashPhoneData.map((el) => el.userId))];
    }
    const userOption: any = { where: { id: userIds } };
    const userAtr = ['id', 'fullName', 'email', 'phone', 'hashPhone'];
    const userData = await this.userRepo.getTableWhereData(userAtr, userOption);

    if (userData === k500Error) return kInternalError;

    const payload = {
      userData,
      title,
      appType,
    };

    this.kafkaService.send(MICRO_ALERT_TOPIC.SEND_WHATSAPP_CAMPAIGN, payload);
    return {};
  }

  async sendKYCStageMessage(query?) {
    const userId = query?.userId;
    const date = new Date();
    const startDate = this.dateServices.getStartOfDay(date)?.startOfDay;
    const endDate = this.dateServices.getEndOfDay(date)?.endOfDay;
    // Define the current time - 10 minutes
    // const endDate = new Date(date.getTime() - 10 * 60 * 1000);
    const attr = [
      'id',
      'stage',
      'stageTime',
      'stageStatus',
      'fullName',
      'phone',
      'email',
      'hashPhone',
    ];
    const option: any = {
      where: {
        stage: UserStage.AADHAAR,
        stageStatus: -1,
        stageTime: {
          [Op.and]: {
            [Op.gte]: startDate,
            [Op.lt]: endDate,
          },
        },
      },
      order: [['createdAt', 'DESC']],
    };
    if (userId) option.where.id = userId;
    const userData = await this.userRepo.getTableWhereData(attr, option);

    if (userData === k500Error) return kInternalError;
    if (!userData || userData?.length === 0)
      return k422ErrorMessage(kNoDataFound);
    const todayMessages = await this.repo.getTableWhereData(
      MsgEntity,
      ['textContent', 'subType', 'reply', 'from'],
      {
        where: {
          textContent: kKycButton,
          subType: 3,
          createdAt: {
            [Op.and]: {
              [Op.gte]: startDate,
              [Op.lt]: endDate,
            },
          },
          processing_steps: kKycProcessStep,
        },
        order: [['createdAt', 'DESC']],
      },
    );

    let todayUser: any;
    if (todayMessages) {
      todayUser = new Set(todayMessages.map((row) => row.from));
    }

    const hashPhones = userData.map((item) => item?.hashPhone);
    const nonWhatsAppHashPhone = await this.getNonWhatsAppUsers(hashPhones);

    const length = userData.length;
    const approvedUser = [];
    for (let i = 0; i < length; i++) {
      const ele = userData[i];
      const userId = ele?.id;

      const customerName = ele?.fullName ?? 'User';
      const email = ele?.email;
      const number = this.cryptService.decryptPhone(ele?.phone);
      if (nonWhatsAppHashPhone.includes(ele?.hashPhone)) continue;
      if (todayUser && todayUser.has(number)) continue;
      const whatsappOption: any = {
        userId,
        customerName,
        email,
        number: number,
        title: kKycProcessStep,
        requestData: kKycProcessStep,
      };
      await this.sendWhatsAppMessageMicroService(whatsappOption);
      approvedUser.push(whatsappOption);
    }
    return approvedUser;
  }

  async updateTargetNumber(reqData) {
    try {
      const { targetList } = reqData;

      if (targetList.length == 0) return false;

      for (let i = 0; i < targetList.length; i++) {
        const ele = targetList[i];
        const form = ele.form;
        const uat_number = ele.uat_number;

        if (!uat_number || !form) continue;

        const payload = {
          form,
          uat_number,
        };

        this.kafkaService.send(
          MICRO_ALERT_TOPIC.REDIS_TARGET_LIST_UPDATE,
          payload,
        );
      }
    } catch (err) {
      return kInternalError;
    }
  }

  //#region Send stuckWATemplate (LOAN ACCEPT, E-MANDATE, E-SIGN)
  async sendUserStuckWhatsApp(query?) {
    const userId = query?.userId;

    // Get eligible user to send template....
    const users = await this.getEligibleUsers(userId);
    if (users === k500Error || !users?.length) return;

    const loanIds = users
      .filter((user) => user?.stage == UserStage.LOAN_ACCEPT)
      .map((user) => user?.lastLoanId);

    const loans = loanIds?.length ? await this.getLoanDetails(loanIds) : [];
    if (loans == k500Error) return kInternalError;

    return await this.sendWAPayload(users, loans);
  }

  async getEligibleUsers(userId) {
    const attr = [
      'id',
      'fullName',
      'email',
      'phone',
      'lastLoanId',
      'stage',
      'appType',
      'hashPhone',
    ];

    const option: any = {
      where: {
        stage: [UserStage.LOAN_ACCEPT, UserStage.ESIGN, UserStage.MANDATE],
        isDeleted: { [Op.ne]: 1 },
        isBlacklist: { [Op.ne]: '1' },
        stageStatus: -1,
      },
      order: [['createdAt', 'DESC']],
    };

    if (userId && userId != '') option.where.id = userId;
    return await this.userRepo.getTableWhereData(attr, option);
  }

  async getLoanDetails(loanIds) {
    return await this.loanRepo.getTableWhereData(
      ['id', 'userId', 'netApprovedAmount', 'netEmiData', 'eligibilityDetails'],
      {
        where: {
          id: loanIds,
        },
      },
    );
  }

  private async sendWAPayload(users, loanData) {
    const usersLength = users?.length;

    const sentMessages = {
      loanAccept: [],
      eMandate: [],
      eSign: [],
    };

    const hashPhones = users.map((item) => item?.hashPhone);
    const nonWhatsAppHashPhone = await this.getNonWhatsAppUsers(hashPhones);

    for (let i = 0; i < usersLength; i++) {
      const user = users[i];
      if (nonWhatsAppHashPhone.includes(user?.hashPhone)) continue;
      const number = this.cryptService.decryptPhone(user?.phone);
      let waPayload: any = {
        userId: user?.id,
        customerName: user?.fullName ?? 'User',
        email: user?.email,
        number,
        contactNumber: EnvConfig.number.helpContact,
        nbfcname: EnvConfig.nbfc.nbfcName,
        appType: user?.appType,
        loanId: user?.lastLoanId,
        needRes: true,
      };

      if (user?.stage == UserStage.LOAN_ACCEPT && loanData?.length != 0) {
        const loan = loanData.find((data) => data?.userId == user.id);
        const emiData = loan?.netEmiData?.[0]
          ? JSON.parse(loan?.netEmiData?.[0])
          : null;

        // Preparing WAPayload for loan accept
        waPayload.title = kLoanAcceptTitle;
        waPayload.requestData = kLoanAcceptTitle;
        waPayload.loanAmount = loan?.netApprovedAmount;
        waPayload.emiCount = loan?.netEmiData?.length;
        waPayload.interestRate = loan?.eligibilityDetails?.anummInterest;
        waPayload.emiAmount = emiData?.Emi;
        waPayload.emiDate = this.dateServices.dateToReadableFormat(
          emiData?.Date,
          'DD/MM/YYYY',
        ).readableStr;
        sentMessages.loanAccept.push(waPayload);
      }

      if (user?.stage == UserStage.MANDATE) {
        // Preparing WAPayload for loan eMandate
        waPayload.title = kLoanEMandateTitle;
        waPayload.requestData = kLoanEMandateTitle;
        sentMessages.eMandate.push(waPayload);
      }

      if (user?.stage == UserStage.ESIGN) {
        // Preparing WAPayload for loan eSign
        waPayload.title = kLoanESignTitle;
        waPayload.requestData = kLoanESignTitle;
        sentMessages.eSign.push(waPayload);
      }
      await this.sendWhatsAppMessageMicroService(waPayload);
    }
    return sentMessages;
  }

  async getNonWhatsAppUsers(hashPhones) {
    if (hashPhones.length == 0) return [];

    const attr = ['isWhatsApp', 'hashPhone'];
    const option = {
      where: { hashPhone: hashPhones, isWhatsApp: false },
      useMaster: false,
    };
    let data = await this.repo.getTableWhereData(HashPhoneEntity, attr, option);
    const nonWhatsAppHashPhone = data.map((item) => item.hashPhone);
    return nonWhatsAppHashPhone;
  }

  async cronJobCheckWhatsAppNumber(query) {
    const limit = query?.limit;
    const currDate = new Date();
    currDate.setDate(currDate.getDate() - DAYS_OF_CHECK_WHATSAPP_NUMBER);

    const userInclude = {
      model: registeredUsers,
      attributes: ['id', 'phone'],
    };
    const attr = ['userId'];
    let option: any = {
      where: {
        [Op.or]: [
          { lastWhatsAppCheckDate: null },
          { lastWhatsAppCheckDate: { [Op.gt]: currDate } },
        ],
      },
      include: [userInclude],
    };

    if (limit) {
      option.limit = limit;
    }

    const checkNumber = await this.repo.getTableWhereData(
      HashPhoneEntity,
      attr,
      option,
    );

    if (checkNumber == k500Error) return kInternalError;
    if (!checkNumber) return [];

    if (checkNumber) {
      let apiPromise = [];
      checkNumber.forEach((item) => {
        const userData = item?.userData;
        if (userData) {
          const number = this.cryptService.decryptPhone(userData.phone);
          const body = { number };
          const promise = this.api.post(nCheckWhatsAppNumber, body);
          apiPromise.push({ promise, userId: userData.id });
        }
      });

      const allRes = await Promise.allSettled([
        ...apiPromise.map((item) => item.promise),
      ]);

      const updatedArr = allRes.map((item, index) => ({
        ...item,
        userId: apiPromise?.[index]?.userId,
      }));

      const userIdsRegistered = updatedArr
        ?.filter(
          (item) =>
            item.status == 'fulfilled' && item?.value?.isRegistered == true,
        )
        .map((item) => item.userId);

      const unRegisteredUsers = updatedArr
        ?.filter(
          (item) =>
            item.status == 'fulfilled' && item?.value?.isRegistered == false,
        )
        .map((item) => item.userId);

      if (userIdsRegistered?.length > 0) {
        let updatedData = {
          isWhatsApp: true,
          lastWhatsAppCheckDate: new Date(),
        };
        let options = { where: { userId: userIdsRegistered } };
        await this.repo.updateRowWhereData(
          HashPhoneEntity,
          updatedData,
          options,
        );
      }
      if (unRegisteredUsers?.length > 0) {
        let updatedData = {
          isWhatsApp: false,
          lastWhatsAppCheckDate: new Date(),
        };
        let options = { where: { userId: unRegisteredUsers } };

        await this.repo.updateRowWhereData(
          HashPhoneEntity,
          updatedData,
          options,
        );
      }

      return { userIdsRegistered, unRegisteredUsers };
    }
    return [];
  }

  //#endregion

  // WhatsAppChecker
  private async checkWhatsAppStatus(number: string): Promise<boolean> {
    const response = await this.api.post(isUserOnWhatsApp, { number });
    if (response == k500Error || response?.isError) {
      return false;
    }
    return response?.isRegistered;
  }

  private async saveCheckResult(
    number: string,
    isOnWhatsApp: boolean,
    batchId?: string,
    adminId?: string,
    adminName?: string,
  ) {
    try {
      const result = {
        number,
        isOnWhatsApp,
        adminId,
        adminName,
        createdAt: new Date(),
      };

      if (this.WhatsAppCheckModel) {
        try {
          const whatsAppCheck = new this.WhatsAppCheckModel(result);
          await whatsAppCheck.save();
        } catch (error) {
          this.errorContextService.throwAndSetCtxErr(error);
        }
      }
      return result;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return null;
    }
  }

  async whatsAppChecker(body) {
    try {
      let number = body?.number;
      const file = body?.file;
      const adminId = body?.adminId;
      if (!adminId) return kParamMissing('adminId');
      if (!number && !file) return kParamMissing('number or file');
      if (file && !file?.path) return k422ErrorMessage('No file uploaded');
      const adminName = (await this.sharedCommonService.getAdminData(adminId))
        ?.fullName;
      // for single number check
      if (number) {
        if (number.length == 13 && number.startsWith('+91'))
          number = number.replace('+91', '');
        else if (number.length == 12 && number.startsWith('91'))
          number = number.replace('91', '');
        else if (number.length == 11 && number.startsWith('0'))
          number = number.replace('0', '');
        if (number?.length != 10)
          return k422ErrorMessage('Please enter correct 10 digit number');
        const isOnWhatsApp = await this.checkWhatsAppStatus(number);
        await this.saveCheckResult(
          number,
          isOnWhatsApp,
          null,
          adminId,
          adminName,
        );
        return {
          number,
          status: isOnWhatsApp ?? false,
        };
      }

      // for bulk number check
      const batchId = `batch_${Date.now()}_${Math.random()
        .toString(36)
        .slice(2, 10)}`;
      const excelData: any = await this.fileService.excelToArray(
        file.path,
        {},
        false,
        true,
      );
      await this.fileService.removeFile(file.path);
      if (excelData?.message) return excelData;
      if (!excelData || !excelData.length)
        return k422ErrorMessage('Upload valid excel file!');

      //check if the required column exists
      if (!excelData.some((row) => 'Number' in row || 'number' in row)) {
        return k422ErrorMessage(
          'Invalid Excel File - Missing required column "Number"',
        );
      }

      // check if the file has already been processed
      if (
        excelData.some((row) => 'isOnWhatsApp' in row || 'checkedAt' in row)
      ) {
        return k422ErrorMessage('Invalid Excel File - File already processed');
      }

      //remove duplicate phone numbers
      const seen = new Set(),
        uniqueExcelData = excelData.filter(
          (r) => !seen.has((r = String(r.Number || r.number))) && seen.add(r),
        );

      const fileName = file.originalname;

      const batchInfo = {
        batchId,
        fileName,
        status: 'In Progress',
        createdAt: new Date(),
        adminId,
        adminName,
      };
      let keyValue = [];
      const wKey = 'WHATSAPP_CHECK_DATA';
      let oldData = await this.redisService.get(wKey);
      if (oldData) keyValue = JSON.parse(oldData);
      keyValue.push(batchInfo);

      const midnightKeyExpire = this.typeService.midnightSeconds();
      await this.redisService.set(
        wKey,
        JSON.stringify(keyValue),
        midnightKeyExpire,
      );
      const data = { excelData: uniqueExcelData, batchId, adminId, adminName };
      this.checkBulkWhatsApp(data);
      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async checkBulkWhatsApp(data) {
    const excelData = data.excelData;
    const batchId = data?.batchId;
    const adminId = data?.adminId;
    const adminName = data?.adminName;
    const midnightKeyExpire = this.typeService.midnightSeconds();
    if (!excelData.length || !batchId || !adminId || !adminName) return {};
    const allResults = [];
    const mongoDbResults = [];

    for (let i = 0; i < excelData.length; i++) {
      const ele = excelData[i];
      let phone;
      if (ele.Number || ele.number) phone = String(ele.Number || ele.number);
      if (!phone) continue;
      if (phone.length == 13 && phone.startsWith('+91'))
        phone = phone.replace('+91', '');
      else if (phone.length == 12 && phone.startsWith('91'))
        phone = phone.replace('91', '');
      else if (phone.length == 11 && phone.startsWith('0'))
        phone = phone.replace('0', '');
      if (phone?.length != 10) continue;
      const isOnWhatsApp = await this.checkWhatsAppStatus(phone);
      mongoDbResults.push({
        number: phone,
        isOnWhatsApp,
        batchId,
        adminId,
        adminName,
        createdAt: new Date(),
      });

      const obj = {
        number: phone,
        isOnWhatsApp: isOnWhatsApp ? 'true' : 'false',
        checkedAt: this.typeService.dateTimeFormate(new Date()),
      };
      allResults.push(obj);
    }

    if (!mongoDbResults.length) return {};
    try {
      await this.WhatsAppCheckModel.insertMany(mongoDbResults);
    } catch (err) {
      this.errorContextService.throwAndSetCtxErr(err);
    }

    const rawExcelData = {
      sheets: ['WhatsApp Results'],
      data: [allResults],
      sheetName: `WhatsApp_Check_${batchId}.xlsx`,
    };
    const url = await this.fileService.objectToExcelURL(rawExcelData);
    if (url?.message) return url;
    const wKey = 'WHATSAPP_CHECK_DATA';
    let redisData = await this.redisService.get(wKey);
    redisData = redisData ? JSON.parse(redisData) : [];
    let inProcess = redisData.find((f: any) => f.batchId == batchId);
    inProcess.status = 'Completed';
    inProcess.downloadUrl = url;
    await this.redisService.set(
      wKey,
      JSON.stringify(redisData),
      midnightKeyExpire,
    );
    return {};
  }

  // getWhatsappCheckList
  async getWhatsappCheckList() {
    const wKey = 'WHATSAPP_CHECK_DATA';
    const checkList = await this.redisService.get(wKey);
    if (checkList) {
      const rows = checkList ? JSON.parse(checkList) : [];
      return { count: rows.length, rows };
    }
    return { count: 0, rows: [] };
  }
}
