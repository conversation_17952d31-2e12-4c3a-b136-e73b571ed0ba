// Imports
import { Body, Controller, Get, Post, Query, Res } from '@nestjs/common';
import { BefiscService } from './befisc.service';
import { kInternalError, kSuccessData } from 'src/constants/responses';

@Controller('thirdParty/befisc')
export class BefiscController {
  constructor(private readonly service: BefiscService) {}

  @Post('mobile-to-uan')
  async funMobileToUan(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.mobileToUan(body);
      if (data.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Post('uan-to-employment')
  async funUanToEmployment(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.uanToEmployment(body);
      if (data.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  // for user other number, email and address (Profile Basic Befisc)
  @Post('profileBasic')
  async funProfileBasicBefisc(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.profileBasic(body);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Post('uan-details')
  async funUANDetails(@Body() body, @Res() res) {
    try {
      const data : any = await this.service.uanDetails(body);
      if (data.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }
}
