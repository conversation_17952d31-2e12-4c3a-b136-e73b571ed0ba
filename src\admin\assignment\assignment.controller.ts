// Imports
import { Body, Controller, Get, Post, Query, Res } from '@nestjs/common';
import { kInternalError, kSuccessData } from 'src/constants/responses';
import { CAAssignmentService } from 'src/shared/assignment/caAssignCase.service';
import { ErrorContextService } from 'src/utils/error.context.service';

@Controller('admin/assignment')
export class AssignmentController {
  constructor(
    private readonly caAssignmentService: CAAssignmentService,
    private readonly errorContextService: ErrorContextService,
  ) {}

  @Get('getCAData')
  async funGetCAData(@Query() query, @Res() res) {
    try {
      const data: any = await this.caAssignmentService.getCAData(
        query,
      );
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Post('resetAllCACaseCountData')
  async funResetCAAdminWiseCaseCountData(@Res() res) {
    try {
      const data: any =
        await this.caAssignmentService.resetAllCACaseCountData();
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Post('syncCAShiftDataWhenShiftChange')
  async funSyncCAShiftDataWhenShiftChange(@Res() res) {
    try {
      const data: any =
        await this.caAssignmentService.syncCAShiftDataWhenShiftChange();
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Post('syncCAShiftData')
  async funSyncCAShiftData(@Res() res) {
    try {
      const data: any =
        await this.caAssignmentService.syncCAShiftData();
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }
}
