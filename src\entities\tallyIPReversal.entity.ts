// Imports
import { Column, DataType, Model, Table } from 'sequelize-typescript';

@Table({})
export class TallyIPReversal extends Model<TallyIPReversal> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
  })
  emiId: number;

  @Column({
    type: DataType.INTEGER,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  type: string;

  @Column({
    type: DataType.TEXT,
  })
  reversalData: string;

  @Column({
    type: DataType.INTEGER,
  })
  adminId: number;
}
