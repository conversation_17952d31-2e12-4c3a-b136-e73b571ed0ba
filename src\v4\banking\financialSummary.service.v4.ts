// Imports
import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { k500Error, topBanks } from 'src/constants/misc';
import { Op } from 'sequelize';
import { k422ErrorMessage, kParamMissing } from 'src/constants/responses';
import {
  kCAMS,
  kCapActive,
  kErrorMsgs,
  kfinvu,
  kNoDataFound,
} from 'src/constants/strings';
import { UserRepository } from 'src/repositories/user.repository';
import { BankingSharedService } from 'src/shared/banking.service';
import { CryptService } from 'src/utils/crypt.service';
import { TypeService } from 'src/utils/type.service';
import {
  aaField,
  accountAggregators,
  getAAWebDataForFinvu,
  kBankIcons,
  kBankingProHeaders,
  kCategoryIcons,
  kFinancialSummaryCloudStuff,
  kMonths,
  kMonthSortOrder,
  kTopCategoriesForTransactionsColorCodes,
  shortMonth,
} from 'src/constants/objects';
import { FinvuService } from 'src/thirdParty/finvu/finvu.service';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { PeriodicEntity } from 'src/entities/periodic.entity';
import { GET_COMPARE_ACCOUNT } from 'src/constants/network';
import { APIService } from 'src/utils/api.service';
import { RedisService } from 'src/redis/redis.service';
import { DateService } from 'src/utils/date.service';
import { BankListRepository } from 'src/repositories/bankList.repository';
import { SharedNotificationService } from 'src/shared/services/notification.service';
import { UserServiceV4 } from '../user/user.service.v4';
import { CamsServiceThirdParty } from 'src/thirdParty/cams/cams.service.thirdParty';
import { SPEND_ANALYTICS_AUTO_REFRESH } from 'src/constants/globals';

@Injectable()
export class FinancialSummaryServiceV4 {
  constructor(
    // Database
    private readonly repo: RepositoryManager,
    @Inject(forwardRef(() => FinvuService))
    private readonly finvuService: FinvuService,
    @Inject(forwardRef(() => CamsServiceThirdParty))
    private readonly camsService: CamsServiceThirdParty,
    private readonly cryptService: CryptService,
    private readonly typeService: TypeService,
    private readonly userRepo: UserRepository,
    private readonly api: APIService,
    private readonly redisService: RedisService,
    private readonly dateService: DateService,
    private readonly bankListRepo: BankListRepository,
    private readonly notificationService: SharedNotificationService,
    @Inject(forwardRef(() => UserServiceV4))
    private readonly userService: UserServiceV4,
    @Inject(forwardRef(() => BankingSharedService))
    private readonly bankingSharedService: BankingSharedService,
  ) {}

  // #region aaBankList Banks Available for AA
  async aaBankList() {
    const key = 'BANKS_LIST';
    let bankList = await this.redisService.getKeyDetails(key);
    if (bankList) bankList = JSON.parse(bankList);
    else {
      const attributes = [
        'aaMode',
        'bankCode',
        'fipName',
        'bankName',
        'image',
        'service',
        'bankImg',
        'lspBankImg',
      ];
      const options = {
        useMaster: false,
        where: { statusFlag: '1' },
        order: [['bankName', 'ASC']],
      };

      bankList = await this.bankListRepo.getTableWhereData(attributes, options);
      if (bankList == k500Error)
        throw new Error('Error in Getting Account Aggregator Banks:DB');
    }

    // To Show Banks With Active Account Aggregator Service. // Top Banks And Remaining Banks.
    const topBankWithAaService = [];
    const remainingBankWithAaService = [];
    let topBanksCount = bankList.filter(
      (bank) => bank.image != null && bank?.service?.aa == 2,
    );
    bankList.forEach((bank: any) => {
      if (topBanksCount.length < 7) bank.image = null;
      if (
        topBanks.includes(bank.bankCode) &&
        bank.image != null &&
        (bank?.service?.aa == 2 || bank?.service?.aa == 0)
      )
        topBankWithAaService.push(bank);
      else if (bank?.service?.aa == 2 || bank?.service?.aa == 0)
        remainingBankWithAaService.push(bank);
    });
    return [...topBankWithAaService, ...remainingBankWithAaService]; // Return Order: Top to Low
  }
  //#endregion

  // #region Submit Bank for Financial Summary
  async submitBankForFinancialSummary(reqData) {
    // User Data
    const userId = reqData?.userId;
    if (!userId) return kParamMissing('userId');
    const bankCode = reqData?.bankCode;
    if (!bankCode) return kParamMissing('bankCode');
    let findUser = await this.userRepo.getRowWhereData(
      ['phone', 'financialSummaryData'],
      {
        where: { id: userId },
      },
    );
    const banks = await this.aaBankList();
    const find = banks.find((bank) => bank?.bankCode == bankCode);
    const consentMode =
      find?.service?.aa == 0 ? kCAMS : find?.service?.aa == 2 ? kfinvu : null;
    if (!consentMode) return k422ErrorMessage('No Service Available');

    if (findUser == k500Error) throw new Error();
    let decryptedPhone = this.cryptService.decryptPhone(findUser.phone);

    // Preparing Data for Web view
    let invitationData;
    if (consentMode === kfinvu) {
      invitationData = await this.finvuService.inviteForAaForFinSummary({
        custId: decryptedPhone,
        financialSummaryData: findUser?.financialSummaryData ?? {},
        ...reqData,
      });
      if (invitationData.message) return invitationData;
    } else if (consentMode === kCAMS) {
      invitationData = await this.camsService.inviteForAaForFinSummary({
        decryptedPhone,
        fipName: find.fipName,
        bankCode: reqData?.bankCode,
        financialSummaryData: findUser?.financialSummaryData ?? {},
        ...reqData,
      });
      if (invitationData?.message) return invitationData;
    }

    // Preparing Web view for Finvu Journey
    const webviewData = getAAWebDataForFinvu(
      invitationData.url,
      reqData.userId,
      reqData.bankCode,
    );

    return {
      needUserInfo: true,
      webviewData,
    };
  }
  //#endregion

  // #region validateAAForFinancialSummary Similar to validateAA But Used Only For Financial Summary
  async validateAAForFinancialSummary(reqData) {
    // Validation -> parameters
    const userId = reqData.userId;
    if (!userId) return kParamMissing('userId');
    const bankCode = reqData.bankCode;
    if (!bankCode) return kParamMissing('bankCode');
    const otherBankAttr: any = {};

    // Fetch Data from User
    let findUser = await this.userRepo.getRowWhereData(
      ['phone', 'financialSummaryData'],
      {
        where: { id: reqData.userId },
      },
    );
    if (findUser == k500Error) throw new Error();

    // Preparing Phone and Consent Id
    const phone = this.cryptService.decryptPhone(findUser?.phone);
    const currentBankData = findUser?.financialSummaryData[bankCode] ?? [];
    const consentHandleId =
      currentBankData[currentBankData.length - 1]?.consentHandleId ?? '';

    // Validation -> Finvu
    const consentData: any = await this.finvuService.checkConsentStatus(
      consentHandleId,
      `${phone}@finvu`,
    );
    if (
      reqData?.typeOfDevice == '2' &&
      consentData?.consentId == null &&
      consentData?.consentStatus != 'REJECTED'
    )
      return {};
    const consentId = consentData?.consentId;

    if (consentData.consentStatus === 'ACCEPTED') {
      otherBankAttr.aaDataStatus = 1;
      otherBankAttr.consentId = consentData.consentId;
      otherBankAttr.consentStatus = consentData.consentStatus;
      otherBankAttr.consentResponse = JSON.stringify(consentData);
      // Request for periodic data
      otherBankAttr.sessionId = await this.finvuService.fetchDataRequest(
        phone,
        consentId,
        consentHandleId,
      );
      const createdData = await this.repo.createRowData(PeriodicEntity, {
        sessionId: otherBankAttr.sessionId,
        type: 6,
        userId,
        source: 1,
        data: {
          bankCode,
          purpose: 'FINANCIAL_SUMMARY',
          otherBankAttr: otherBankAttr,
        },
        status: 3,
        consentId,
      });
      if (createdData == k500Error) throw new Error();

      // Preparing Updated financialSummaryData
      let financialSummaryData = findUser.financialSummaryData;
      financialSummaryData[bankCode].forEach((ele) => {
        if (ele.consentHandleId == consentHandleId) {
          ele.consentStatus = 'ACCEPTED';
          ele.consentId = consentId;
          ele.sessionId = otherBankAttr.sessionId;
          ele.createdAt = new Date();
          ele.consentMode = kfinvu;
        }
      });
      const updateUser = await this.userRepo.updateRowData(
        { financialSummaryData },
        userId,
      );
      if (updateUser == k500Error) throw new Error();
      await this.userService.routeDetails({ id: userId });
    } else if (consentData.consentStatus === 'REJECTED') {
      // Update User's Consent If It's Rejected
      let financialSummaryData = findUser.financialSummaryData;
      financialSummaryData[bankCode].forEach((ele) => {
        if (ele.consentHandleId == consentHandleId)
          ele.consentStatus = 'REJECTED';
      });
      const updateUser = await this.userRepo.updateRowData(
        { financialSummaryData },
        reqData.userId,
      );
      if (updateUser == k500Error) throw new Error();
      return k422ErrorMessage(kErrorMsgs.AA_CONSENT_REJECTED);
    } else if (consentData.consentStatus === 'REVOKED') {
      // Preparing Updated financialSummaryData
      let financialSummaryData = findUser.financialSummaryData;
      financialSummaryData[bankCode].forEach((ele) => {
        if (ele.consentHandleId == consentHandleId)
          ele.consentStatus = 'REVOKED';
      });
      const updateUser = await this.userRepo.updateRowData(
        { financialSummaryData },
        reqData.userId,
      );
      if (updateUser == k500Error) throw new Error();
      return k422ErrorMessage('Your consent is revoked, Please try again');
    }

    return { needUserInfo: true };
  }
  //#endregion

  // #region Bank Data For Financial Summary
  async getDataForFinancialSummary(reqData) {
    if (!reqData?.userId) return kParamMissing('userId');

    // User Data
    let userAttr = ['phone', 'financialSummaryData'];
    let user = await this.userRepo.getRowWhereData(userAttr, {
      where: {
        id: reqData?.userId,
      },
    });
    if (user == k500Error) throw new Error();
    if (!user) return k422ErrorMessage(kNoDataFound);

    let phone = this.cryptService.decryptPhone(user?.phone);
    let accountAggregator = accountAggregators;
    let data: any = {
      canRefresh: false,
      aaField: aaField,
    };
    data.aaField.child.texts = [accountAggregator[0]];

    let arr = [];
    let totalBalance = 0;
    let nextUpdateDate: any;
    let todayDate = this.typeService.getGlobalDate(new Date()).getDate();
    let todayMonth = this.typeService.getGlobalDate(new Date()).getMonth();
    let updatedAtDate;
    let updatedAtMonth;
    let isBalanceUpdated = true;
    let lastUpdatedAt;

    const thisMonthDate = this.typeService.getGlobalDate(new Date());
    thisMonthDate.setDate(1);
    const nextMonthDate = new Date();
    nextMonthDate.setDate(1);
    nextMonthDate.setMonth(thisMonthDate.getMonth() + 1);
    let aaServices = [];

    // Loop Over the Banks User Added
    for (let key in user?.financialSummaryData) {
      let find = user?.financialSummaryData[key].filter(
        (f) => f.consentStatus == 'ACTIVE' && f.consentId,
      );
      let nextDate: any;
      // Loop Over the Accounts in Bank. (Accounts can Be Single or Multiple)
      for (let i = 0; i < find.length; i++) {
        try {
          let ele = find[i];
          if (ele?.consentMode == kfinvu || !ele?.consentMode)
            aaServices.push(kfinvu);
          if (ele?.consentMode == kCAMS) aaServices.push(kCAMS);
          let accountNumber = ele?.accountNumber;
          let accountBalance = ele?.accountBalance;
          let consentId = ele?.consentId;
          updatedAtDate = this.typeService
            .getGlobalDate(new Date(ele?.updatedAt))
            .getDate();
          updatedAtMonth = this.typeService
            .getGlobalDate(new Date(ele?.updatedAt))
            .getMonth();

          lastUpdatedAt = ele?.updatedAt;
          if (!consentId) {
            console.log('reqData', reqData, ele, user?.financialSummaryData);
          }
          const periodicData = await this.repo.getTableCountWhereData(
            PeriodicEntity,
            ['data', 'type', 'status'],
            {
              where: {
                type: 6,
                consentId,
                status: { [Op.or]: [1, 3] },
                createdAt: {
                  [Op.gte]: thisMonthDate,
                  [Op.lt]: nextMonthDate,
                },
              },
              order: [['createdAt', 'DESC']],
            },
          );
          if (periodicData == k500Error) throw new Error();
          let dataFetchCount = periodicData?.count;
          let refreshBalanceReqs = periodicData?.rows.filter(
            (r) =>
              r.status == 3 &&
              r.data.purpose == 'FINANCIAL_SUMMARY_BALANCE_REFRESH',
          );
          if (refreshBalanceReqs.length > 0) isBalanceUpdated = false;
          if (dataFetchCount < 2 && todayDate != updatedAtDate)
            data.canRefresh = true;

          // Fetch Data from Redis
          let redisKey =
            reqData?.userId +
            '_' +
            key +
            accountNumber +
            '_FINANCIAL_SUMMARY_DATA';
          let isDataFetched = false;
          if (!reqData?.isRefresh) {
            let cachedData = await this.redisService.get(redisKey);
            if (cachedData && typeof cachedData == 'string') {
              cachedData = JSON.parse(cachedData);
              totalBalance += cachedData?.balanceInt;
              delete cachedData?.balanceInt;
              arr.push(cachedData);
              isDataFetched = true;
            }
          }

          if (dataFetchCount >= 2) nextDate = new Date(ele?.updatedAt);

          // Fetching Data From Transactions Project
          if (!isDataFetched) {
            let accountNumber = key + phone + '_' + ele?.accountNumber;
            let url =
              GET_COMPARE_ACCOUNT +
              '?' +
              `salaryAccountId=${accountNumber}&callerApi=DataFinanceSummary`;
            if (reqData.endDate) url = url + `&endDate=${reqData.endDate}`;
            const header = kBankingProHeaders;
            const response = await this.api.requestGet(url, header);
            if (response == k500Error || response?.message) throw new Error();
            response.bankName = key;
            response.lastUpdateDate =
              'Last updated on ' +
              this.dateService.readableDate(ele?.updatedAt);
            response.accountNumber = ele?.accountNumber?.slice(-6);
            response.fullAccountNumber = ele?.accountNumber;
            response.bankBalance = accountBalance;
            // Preparing Data (Account Wise)
            let preparedData = this.prepareDataForFinancialSummary(response);
            totalBalance += preparedData?.balanceInt;
            this.redisService.set(redisKey, JSON.stringify(preparedData));
            delete preparedData?.balanceInt;
            arr.push(preparedData);
          }
          if (nextDate) {
            if (nextUpdateDate)
              nextUpdateDate =
                nextUpdateDate.getTime() < new Date(nextDate).getTime()
                  ? nextUpdateDate
                  : new Date(nextDate);
            else nextUpdateDate = new Date(nextDate);
          }
        } catch (error) {}
      }
    }

    // Icon for AA Service
    if (aaServices.includes(kfinvu) && aaServices.includes(kCAMS)) {
      data.aaField.child.texts = [...new Set(accountAggregator)];
      data.aaField.child.texts[0].text = 'RBI regulated Account Aggregators: ';
    } else if (aaServices.includes(kCAMS))
      data?.aaField?.child?.texts.push(accountAggregator[3]);
    else data?.aaField?.child?.texts.push(accountAggregator[1]);

    data.accountBalance =
      '₹' + this.typeService.amountNumberWithCommas(totalBalance);

    let nextUpdate = new Date(lastUpdatedAt);
    if (data.canRefresh) {
      nextUpdate.setDate(nextUpdate.getDate() + SPEND_ANALYTICS_AUTO_REFRESH);

      data.nextUpdateDate = `Next refresh on ${nextUpdate.getDate()} ${
        kMonths[nextUpdate.getMonth()]
      }`;
    } else
      data.nextUpdateDate = `Next refresh on 1st ${
        kMonths[nextUpdate.getMonth() + 1]
      }`;
    // Next Date When User Can Refresh Thier Balance
    if (nextUpdateDate) {
      let nextMonth = nextUpdateDate.getMonth() + 1;
      if (nextMonth - 1 == 11) nextMonth = 0;
      nextUpdateDate = this.dateService
        .readableDate(nextUpdateDate.toString())
        .split(' ');
      data.nextUpdateDate =
        nextMonth != new Date().getMonth()
          ? 'Next Refresh on 1st ' + kMonths[nextMonth].trim()
          : '';

      if (nextMonth <= new Date().getMonth()) {
        data.canRefresh = true;
        data.nextUpdateDate = '';
      }
    }
    if (
      updatedAtDate == todayDate &&
      updatedAtMonth == todayMonth &&
      data.canRefresh == false
    )
      data.nextUpdateDate = 'You can refresh balance tomorrow';
    data.isBalanceUpdated = isBalanceUpdated;
    data.bankAccounts = [...arr];
    data.showRefreshBtn = false;
    return data;
  }
  //#endregion

  //#region Prepare Data for Finanical Data
  prepareDataForFinancialSummary(data) {
    let obj: any = {};
    obj.balance =
      '₹' + this.typeService.amountNumberWithCommas(data?.bankBalance);
    data.bankName = data.bankName.replace(/_/g, ' ');
    obj.icon = kBankIcons[data.bankName.toLowerCase()] ?? kBankIcons.default;
    obj.balanceInt = +data?.bankBalance;
    obj.name = data.bankName;
    obj.lastUpdateDate = data.lastUpdateDate;
    obj.maskedNumber = data.accountNumber;
    obj.fullAccountNumber = data.fullAccountNumber;
    obj.accountBalanceData = {
      accountHistory: [],
      creditDebitData: [
        {
          color: '0xff3ABA97',
          title: 'Inflow',
        },
        {
          color: '0xffFF8D3E',
          title: 'Outflow',
        },
      ],
    };
    obj.transactionAnalysis = [];

    let transactionData = data?.transactionJson;
    let creditTransactionData = data?.summary?.credits?.monthlyDetails ?? [];
    let debitTransactionData = data?.summary?.debits?.monthlyDetails ?? [];

    let creditDebitData = {};
    let credit = 0;
    let debit = 0;

    // Loop to Prepare Credit Transaction Data
    for (let index = 0; index < creditTransactionData.length; index++) {
      const ele = creditTransactionData[index];
      if (creditDebitData.hasOwnProperty(ele?.monthYear)) {
        if (creditDebitData[ele?.monthYear].credit)
          creditDebitData[ele?.monthYear].credit += +(ele?.value).toFixed();
        else creditDebitData[ele?.monthYear].credit = +(ele?.value).toFixed();
      } else
        creditDebitData[ele?.monthYear] = { credit: +(ele?.value).toFixed() };
      credit += +(ele?.value).toFixed();
    }

    // Loop to Prepare Debit Transaction Data
    for (let index = 0; index < debitTransactionData.length; index++) {
      const ele = debitTransactionData[index];
      if (creditDebitData.hasOwnProperty(ele?.monthYear)) {
        if (creditDebitData[ele?.monthYear].debit)
          creditDebitData[ele?.monthYear].debit += +(ele?.value).toFixed();
        else creditDebitData[ele?.monthYear].debit = +(ele?.value).toFixed();
      } else
        creditDebitData[ele?.monthYear] = { debit: +(ele?.value).toFixed() };
      debit += +(ele?.value).toFixed();
    }

    // Preparing Account History Month Wise (Both Credit & Debit)
    for (let key in creditDebitData) {
      // Jan 2024 -> Jan 24
      let monthYear: any = key.split(' ');
      monthYear = monthYear[0] + ' ' + monthYear[1].slice(-2);
      obj.accountBalanceData.accountHistory.push({
        credit: creditDebitData[key].credit ?? 0,
        debit: creditDebitData[key].debit ?? 0,
        month: monthYear,
        space: 1200,
      });
    }

    obj.accountBalanceData.accountHistory.sort((a, b) => {
      const [monthA, yearA] = a.month.split(' ');
      const [monthB, yearB] = b.month.split(' ');
      return (
        parseInt(yearB) - parseInt(yearA) ||
        kMonthSortOrder[monthB] - kMonthSortOrder[monthA]
      );
    });

    // Inflow and Outflow Data --> Credit and Debit
    obj.accountBalanceData.creditDebitData[0]['amount'] =
      '₹' + this.typeService.amountNumberWithCommas(credit);
    obj.accountBalanceData.creditDebitData[1]['amount'] =
      '₹' + this.typeService.amountNumberWithCommas(debit);

    // Preparing Transaction Category Wise
    let arr = [];
    let totalAmount = 0;
    let todayDate = new Date();

    // Current Months Variables(For Expense Percentage Analysis)
    let currentMonthSpentAmtTillDate = 0;
    let thisMonthIndex = todayDate.getMonth() + 1;
    let thisDateIndex = todayDate.getDate();

    // Last Month Variables(For Expense Percentage Analysis)
    let lastMonthSpentAmtTillDate = 0;
    let lastMonthIndex = todayDate.getMonth();
    let lastDateIndex = todayDate.getDate();
    if (thisMonthIndex == 1) lastMonthIndex = 12;

    transactionData.sort((a, b) => b.id - a.id);
    for (let index = 0; index < transactionData.length; index++) {
      const ele = transactionData[index];
      let date = +ele?.dateTime.split('/')[0];
      let month = +ele?.dateTime.split('/')[1];
      // current month
      if (
        month === thisMonthIndex &&
        date <= thisDateIndex &&
        ele?.type == 'DEBIT'
      )
        currentMonthSpentAmtTillDate += ele?.amount;
      // last month
      else if (
        month === lastMonthIndex &&
        date <= lastDateIndex &&
        ele?.type == 'DEBIT'
      )
        lastMonthSpentAmtTillDate += ele?.amount;

      let find = arr.filter((t) => t.category == ele?.category);
      if (ele?.category == '-') continue;
      if (find.length != 0) {
        find[0].amount += ele?.amount;
        find[0].numberOfTransactions += 1;
        totalAmount += ele?.amount;
      } else {
        arr.push({
          numberOfTransactions: 1,
          amount: ele?.amount,
          category: ele?.category,
        });
        totalAmount += ele?.amount;
      }
    }

    // Expense Percentage for Analysis (Last Month vs Current Month)
    // If Today(11-12-24) Comparision (01-11-24 to 11-11-24) vs (01-12-24 to 11-12-24)
    let expensePercentage = 0;
    let expenseConclusion = '';
    // Last Month Expense > Current Month Expense
    if (lastMonthSpentAmtTillDate >= currentMonthSpentAmtTillDate) {
      expensePercentage =
        100 - (currentMonthSpentAmtTillDate / lastMonthSpentAmtTillDate) * 100;
      expenseConclusion = 'down';
    }
    // Current Month Expense > Last Month Expense
    else {
      expensePercentage =
        100 - (lastMonthSpentAmtTillDate / currentMonthSpentAmtTillDate) * 100;
      expenseConclusion = 'high';
    }
    expensePercentage = +expensePercentage.toFixed(2);

    obj.expenseTrack = {
      backgroundColor:
        expenseConclusion == 'down' ? '0xFFF1FFE6' : '0xffFFECF0',
      textColor: expenseConclusion == 'down' ? '0xFF3ABA97' : '0xFFFF4E60',
      icon:
        expenseConclusion == 'down'
          ? kFinancialSummaryCloudStuff['expenseDown']
          : kFinancialSummaryCloudStuff['expenseHigh'],
      title: `Expense ${expenseConclusion} ${expensePercentage}% since last month`,
    };
    if ((lastMonthSpentAmtTillDate ?? 0) == (currentMonthSpentAmtTillDate ?? 0))
      obj.expenseTrack.title = 'Expenses are neutral since last month';

    // Decorating Data for App
    arr = arr.sort((a, b) => b.amount - a.amount);
    let otherCategory: any = {
      numberOfTransactions: 0,
      amount: 0,
      category: 'Other',
      percentageData: 0,
      icon: kCategoryIcons['other'],
      color: kTopCategoriesForTransactionsColorCodes[9],
    };
    let categoriezedArray = [];
    for (let index = 0; index < arr.length; index++) {
      const ele = arr[index];
      if (index < 9 && ele?.category.toLowerCase() != 'other') {
        ele.percentage = +((ele?.amount / totalAmount) * 100).toFixed(2);
        ele.percentageData = ele?.percentage;
        ele.percentage = ele?.percentage < 1 ? '< 1%' : ele?.percentage + '%';
        ele.amount = '₹' + this.typeService.amountNumberWithCommas(ele?.amount);
        ele.icon = kCategoryIcons[(ele?.category).toLowerCase()];
        ele.category = ele?.category.replace(/_/g, ' ').toLowerCase();
        ele.category = ele?.category.replace(/\b\w/g, (char) =>
          char.toUpperCase(),
        );
        if (['Imps', 'Upi', 'Neft', 'Pos'].indexOf(ele?.category) != -1)
          ele.category = ele?.category.toUpperCase();
        ele.color = kTopCategoriesForTransactionsColorCodes[index];
        categoriezedArray.push(ele);
      } else if (ele?.category.toLowerCase() == 'other' || index >= 9) {
        otherCategory.amount += ele?.amount;
        otherCategory.numberOfTransactions += ele?.numberOfTransactions;
        otherCategory.percentageData += (ele?.amount / totalAmount) * 100;
      }
    }

    // Preparing Data for Other Category
    otherCategory.amount =
      '₹' + this.typeService.amountNumberWithCommas(otherCategory?.amount);
    otherCategory.percentageData = +(otherCategory?.percentageData).toFixed(2);
    otherCategory.percentage =
      otherCategory?.percentageData < 1
        ? '< 1%'
        : otherCategory?.percentageData + '%';
    obj.transactionAnalysis = [...categoriezedArray, otherCategory].sort(
      (a, b) => b.percentageData - a.percentageData,
    );

    return obj;
  }
  //#endregion

  // #region Transaction Bank wise With Filter
  async getTransactionForFinancialSummary(reqData) {
    if (!reqData?.userId) return kParamMissing('userId');
    let { bank, accountNumber, filter } = reqData;
    if (!bank || !accountNumber) return kParamMissing('bank or accountNumber');
    let isFromCallback = reqData?.fromCallback === true;

    // User Data
    let userAttr = ['phone'];
    let user = await this.userRepo.getRowWhereData(userAttr, {
      where: {
        id: reqData?.userId,
      },
    });
    if (user == k500Error) throw new Error();
    if (!user) return k422ErrorMessage(kNoDataFound);

    let phone = this.cryptService.decryptPhone(user?.phone);
    bank = bank.replace(/ /g, '_').toUpperCase();
    let redisKey = bank + phone + '_' + accountNumber;
    let allTransRedisKey = 'ALL_TRANS' + bank + phone + '_' + accountNumber;
    let preparedData = {};
    let reqInfo: any = {};

    // // Redis Ops.
    let isDataFetched = false;
    // All Transaction(Prepared) Fetcing form Redis
    if (!reqData?.filter) {
      let allTransCachedData = await this.redisService.get(allTransRedisKey);
      if (allTransCachedData && typeof allTransCachedData == 'string') {
        try {
          allTransCachedData = JSON.parse(allTransCachedData);
        } catch (error) {}
        preparedData = allTransCachedData;
        isDataFetched = true;
      }
    }
    // Fetching Raw Transactions from Redis
    if (!isDataFetched) {
      let cachedData = await this.redisService.get(redisKey);
      if (cachedData && typeof cachedData == 'string') {
        try {
          cachedData = JSON.parse(cachedData);
        } catch (error) {}
        preparedData = this.prepareTranForFinancialSummary(cachedData, filter);
        isDataFetched = true;
      }
    }
    // If Data Not Found form Redis - Fetching From Trans. Project and Preparing
    if (!isDataFetched || isFromCallback) {
      let accNumber = bank + phone + '_' + accountNumber;
      let url =
        GET_COMPARE_ACCOUNT +
        '?' +
        `salaryAccountId=${accNumber}&callerApi=TransactionFinanceSummary`;
      if (reqData.endDate) url = url + `&endDate=${reqData.endDate}`;
      const header = kBankingProHeaders;
      const response = await this.api.requestGet(url, header);
      if (response == k500Error || response?.message) throw new Error();

      reqInfo.lastBalance = response?.netBankingScore?.closeBalance ?? 0;
      reqInfo.salaryAmount = response?.summary?.salary?.average ?? 0;
      reqInfo.salaryCount = response?.summary?.salary?.total ?? 0;
      reqInfo.ecsCharge =
        response?.summary?.countOfCategory?.['ecs/chq return charges'] ?? 0;
      preparedData = this.prepareTranForFinancialSummary(response, filter);

      // Add Data to Redis
      this.redisService.set(redisKey, JSON.stringify(response));
      // Storing Full Trans.(Prepared) to Redis
      if (isFromCallback || !reqData?.filter)
        this.redisService.set(allTransRedisKey, JSON.stringify(preparedData));
    }
    if (isFromCallback) return reqInfo;
    return preparedData;
  }
  // #endregion

  // #region prepareTranForFinancialSummary
  prepareTranForFinancialSummary(data, filter) {
    // #1 Preparing Filter Options - If Filter Applied
    if (filter && typeof filter == 'string') filter = JSON.parse(filter);
    let monthFilter = [];
    let yearFilter = [];
    let typeFilter = 0;
    let id = 1;
    if (filter) {
      // Time Filters --> Month and Year
      if (filter.month) {
        monthFilter = [
          ...new Set(
            filter.month.map(
              (ele) => shortMonth.indexOf(ele.value.split(' ')[0]) + 1,
            ),
          ),
        ];
        yearFilter = [
          ...new Set(filter.month.map((ele) => ele.value.split(' ')[1])),
        ];
      }
      // Type Filters --> Credit And Debit
      if (filter.type && filter.type.length != 2) {
        if (filter.type[0].id == 1) typeFilter = 1;
        else if (filter.type[0].id == 2) typeFilter = 2;
      }
    }

    // #2 Preparing Static Data
    let transArr = [];
    let trans = data.transactionJson;
    let filters = {
      filterIcon: kFinancialSummaryCloudStuff['filterIcon'],
      filterTitle: 'Filter',
      clearFilterIcon: kFinancialSummaryCloudStuff['filterIcon'],
      clearFilterTitle: 'Clear',
      filterDescription: 'Filter your transactions',
      filtersList: [
        {
          key: 'month',
          type: 'Select month',
          options: [],
        },
        {
          key: 'type',
          type: 'Transaction type',
          options: [
            { id: 1, value: 'Credit' },
            { id: 2, value: 'Debit' },
          ],
        },
      ],
    };

    // #3 Iterating Over Transactions
    // Sorting Trans : New -> Old
    trans = trans.sort((a, b) => b.id - a.id);
    for (let index = 0; index < trans.length; index++) {
      const ele = trans[index];
      if (ele?.category == '-') continue;
      if (typeof ele?.amount == 'string')
        ele.amount = ele.amount.replace(/,/g, '');
      const month = shortMonth[+ele.dateTime.split('/')[1] - 1];
      const year = ele.dateTime.split('/')[2];

      // Preparing Filter Options
      if (
        filters.filtersList[0].options.findIndex(
          (obj) => obj.value === month + ' ' + year,
        ) == -1
      ) {
        filters.filtersList[0].options.push({
          id,
          value: month + ' ' + year,
        });
        id++;
      }

      // Preparing Data According to Filter(If Filter Applied)
      if (filter && monthFilter.length > 0 && yearFilter.length > 0) {
        const monthNumber = +ele.dateTime.split('/')[1];
        if (
          monthFilter.indexOf(monthNumber) == -1 &&
          yearFilter.indexOf(+year) == -1
        )
          continue;
      }
      if (filter && typeFilter) {
        if (typeFilter == 1 && ele.type == 'DEBIT') continue;
        if (typeFilter == 2 && ele.type == 'CREDIT') continue;
      }

      let dataObj = {
        date: ele.dateTime,
        transactionsData: [],
      };
      let today = new Date();
      let isSameDay =
        ele?.dateTime ===
        `${today.getDate()}/${today.getMonth() + 1}/${today.getFullYear()}`;
      if (isSameDay) dataObj.date = 'Today';
      let isCredited = ele.type == 'CREDIT';
      ele.amount = this.typeService.amountNumberWithCommas(ele?.amount);
      let icon = isCredited
        ? kFinancialSummaryCloudStuff['creditIcon']
        : kFinancialSummaryCloudStuff['debitIcon'];
      let color = isCredited ? '0xff3ABA97' : '0xffFF4E60';
      let backgroundColor = isCredited ? '0xffEBFCFF' : '0xffFFEEF0';

      ele.category = ele?.category.replace(/_/g, ' ').toLowerCase();
      ele.category = ele?.category.replace(/\b\w/g, (char) =>
        char.toUpperCase(),
      );
      if (['Imps', 'Upi', 'Neft', 'Pos'].indexOf(ele?.category) != -1)
        ele.category = ele?.category.toUpperCase();

      let description = ele?.description ?? '-';
      dataObj.transactionsData.push({
        isCredited,
        amount: '₹' + ele.amount,
        backgroundColor,
        color,
        icon,
        category: description,
      });
      transArr.push(dataObj);
    }

    // #4 Preparing Grouped Data Datewise
    let groupedData = transArr.reduce((acc, current) => {
      try {
        const existingDate = acc.find((item) => item.date === current.date);
        if (existingDate) {
          existingDate.transactionsData.push(...current.transactionsData);
        } else {
          acc.push({
            date: current.date,
            transactionsData: [...current.transactionsData],
          });
        }
        return acc;
      } catch (error) {}
    }, []);
    // Error Message If No Data Found for Applied Filter
    let filterError: any = {};
    if (groupedData.length == 0) {
      filterError.title = 'No transactions found!';
      filterError.subTitle = filter ? 'Try changing filters' : '';
      filterError.icon = kFinancialSummaryCloudStuff['transactionFilterIcon'];
    }
    return { filters, transactions: groupedData, filterError };
  }
  //#endregion

  //#region refreshAccountBalance
  async refreshAccountBalance(reqData) {
    if (!reqData?.userId) return kParamMissing('userId');

    // User Data
    let userAttr = ['phone', 'typeOfDevice', 'financialSummaryData'];
    let user = await this.userRepo.getRowWhereData(userAttr, {
      where: {
        id: reqData?.userId,
      },
    });
    if (user == k500Error) throw new Error();
    if (!user) return k422ErrorMessage(kNoDataFound);

    const financialSummaryData = user.financialSummaryData;
    const typeOfDevice = user?.typeOfDevice;
    const custId = this.cryptService.decryptPhone(user?.phone);
    const thisMonthDate = this.typeService.getGlobalDate(new Date());
    thisMonthDate.setDate(1);
    const nextMonthDate = new Date();
    nextMonthDate.setDate(1);
    nextMonthDate.setMonth(thisMonthDate.getMonth() + 1);
    for (let key in financialSummaryData) {
      let banks = financialSummaryData[key].filter(
        (b) => b.consentStatus == 'ACTIVE' && b.consentId,
      );
      for (let index = 0; index < banks.length; index++) {
        try {
          const ele = banks[index];
          let consentMode = 1;
          if (ele?.consentMode && ele?.consentMode == kCAMS) consentMode = 2;
          if (!ele.consentId) {
            console.log('reqData', reqData, ele, financialSummaryData);
          }
          const dataFetchCount = await this.repo.getCountsWhere(
            PeriodicEntity,
            {
              where: {
                type: 6,
                consentId: ele.consentId,
                status: 1,
                createdAt: {
                  [Op.gte]: thisMonthDate,
                  [Op.lt]: nextMonthDate,
                },
              },
              order: [['createdAt', 'DESC']],
            },
          );
          if (dataFetchCount == k500Error) throw new Error();
          if (dataFetchCount >= 3) continue;

          let sessionId: any = '';
          if (consentMode == 1) {
            const consentData: any = await this.finvuService.checkConsentStatus(
              ele?.consentHandleId,
              `${custId}@finvu`,
            );

            if (
              typeOfDevice == '2' &&
              consentData?.consentId == null &&
              consentData?.consentStatus != 'REJECTED'
            ) {
              continue;
            }
            if (consentData?.consentStatus !== 'ACCEPTED') continue;

            // Check consent expired or not
            const consentExpiry =
              await this.bankingSharedService.isFinvuConsentExpired(
                ele?.consentId,
              );
            if (consentExpiry) continue;

            sessionId = await this.finvuService.fetchDataRequest(
              custId,
              ele?.consentId,
              ele?.consentHandleId,
            );
            if (!sessionId) continue;
          } else if (consentMode == 2) {
            const consentResponse = await this.camsService.checkConsentStatus(
              ele?.consentHandleId,
              ele?.consentId,
            );
            if (consentResponse?.message) return consentResponse;
            if (consentResponse?.consentExpired == true) return {};

            const consentStatus =
              consentResponse?.consentDetails?.consentStatus;

            if (
              typeOfDevice == '2' &&
              consentResponse?.consentDetails?.consentId == null &&
              consentStatus != 'REJECTED'
            )
              continue;
            if (consentStatus !== kCapActive) continue;

            sessionId = await this.camsService.fetchDataForFinancialSummary(
              ele?.consentId,
              ele?.clientTxnId,
            );
            if (sessionId == false || sessionId?.message) continue;
          }

          const periodicEntry = await this.repo.createRowData(PeriodicEntity, {
            consentId: ele?.consentId,
            data: {
              purpose: 'FINANCIAL_SUMMARY_BALANCE_REFRESH',
              bankCode: key,
            },
            sessionId,
            source: consentMode,
            type: 6,
            status: 3,
            userId: reqData?.userId,
          });
          if (periodicEntry == k500Error) throw new Error();
        } catch (error) {}
      }
    }

    return {
      needUserInfo: true,
    };
  }
  //#endregion

  //#region Expire The Sessions Where the Finvu Response Not Arrived Even After the 10 Mins
  async expireFinvuSessions() {
    // Preparing 10m Ago Time
    let tenMinuteAgo = new Date();
    tenMinuteAgo.setMinutes(tenMinuteAgo.getMinutes() - 10);
    // DB Ops.
    const periodicData = await this.repo.getTableWhereData(
      PeriodicEntity,
      ['userId', 'consentId', 'sessionId', 'data'],
      {
        where: {
          source: { [Op.or]: [1, 2] },
          type: 6,
          status: { [Op.or]: [0, 3] },
          createdAt: {
            [Op.lt]: tenMinuteAgo,
          },
        },
        order: [['createdAt', 'DESC']],
      },
    );
    if (periodicData == k500Error) throw new Error();

    // Preparing Session Ids
    let sessionIds = periodicData.map((a) => a.sessionId);
    let userIds = [...new Set(periodicData.map((a) => a.userId))];
    let updatePeriodicData = await this.repo.updateData(
      PeriodicEntity,
      { status: 2 },
      { sessionId: { [Op.in]: sessionIds } },
    );
    if (updatePeriodicData == k500Error) throw new Error();

    const users = await this.userRepo.getTableWhereData(['fcmToken'], {
      where: {
        id: userIds,
      },
    });
    if (users == k500Error) throw new Error();

    // Calling Route Details
    for (let index = 0; index < userIds.length; index++) {
      const userId = userIds[index];
      await this.userService.routeDetails({ id: userId });
    }

    const fcmTokens = [...new Set(users.map((a) => a.fcmToken))];

    let title = 'Unable to fetch bank details';
    let subTitle = 'There was issue in fetching bank details. Please try again';
    await this.notificationService.sendPushNotification(
      fcmTokens,
      title,
      subTitle,
    );

    return true;
  }
  //#endregion
}
