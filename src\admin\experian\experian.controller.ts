import { Controller, Get, Query, Res } from '@nestjs/common';
import { kInternalError, kSuccessData } from 'src/constants/responses';
import { ExperianService } from './experian.service';

@Controller('admin/experian')
export class ExperianController {
  constructor(private readonly service: ExperianService) {}

  @Get('getLatestExperianData')
  async funGetExperianData(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.getLatestExperianData(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }
}
