import { Injectable } from '@nestjs/common';
import { k500Error } from 'src/constants/misc';
import { ktrueShieldAuth } from 'src/constants/network';
import { trueShieldVerifyGender } from 'src/constants/network';
import { kInternalError } from 'src/constants/responses';
import { APIService } from 'src/utils/api.service';

@Injectable()
export class truShieldService {
  constructor(private readonly apiService: APIService) {}

  async verifyGender(name) {
    const headers = {
      app_id: ktrueShieldAuth.app_Id,
      secret_key: ktrueShieldAuth.seceret,
    };
    const body = { name, consent: true };
    const response = await this.apiService.requestPost(
      trueShieldVerifyGender,
      body,
      headers,
    );
    if (response == null || !response || response === k500Error)
      return kInternalError;
    return response;
  }
}
