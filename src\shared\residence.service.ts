import { Injectable } from '@nestjs/common';
import { k500Error } from 'src/constants/misc';
import {
  k422ErrorMessage,
  kInternalError,
  kParamMissing,
} from 'src/constants/responses';
import { TypeService } from 'src/utils/type.service';
import { AddressesRepository } from 'src/repositories/addresses.repository';
import {
  ADDRESS_AUTO_VARIFY_PROBABILITY as ADDRESS_AUTO_VERIFY_PROBABILITY,
  HOST_URL,
  Latest_Version,
  SYSTEM_ADMIN_ID,
} from 'src/constants/globals';
import { ResidenceServiceV4 } from 'src/v4/residence/residence.service.v4';
import { KYCRepository } from 'src/repositories/kyc.repository';
import { APIService } from 'src/utils/api.service';

@Injectable()
export class ResidenceSharedService {
  constructor(
    private readonly addressRepo: AddressesRepository,
    private readonly typeService: TypeService,
    private readonly residenceService: ResidenceServiceV4,
    private readonly kycRepo: KYCRepository,
    private readonly api: APIService,
  ) {}

  delay = (ms) => new Promise((res) => setTimeout(res, ms));

  async handleCamsFlow(data: any) {
    try {
      await this.delay(2500);
      const userId = data.userId;

      const callBackData = {
        url: HOST_URL + `${Latest_Version}/banking/checkAAStatus`,
        urlMethod: 'POST',
        waitForCallbackResponse: true,
        directSource: {
          userId,
          type: 'CAMS_CHECK_CONSENT',
          subType: '',
          checkStage: 'First',
        },
        directProcessing: true,
      };
      return { callbackList: [callBackData] };
    } catch (error) {
      console.error('Error in: ', error);
      return kInternalError;
    }
  }

  async getAadhaarNumber(userId) {
    try {
      const attributes = ['aadhaarNumber'];
      const options = { order: [['id', 'DESC']], where: { userId } };

      const kycData = await this.kycRepo.getRowWhereData(attributes, options);
      if (kycData == k500Error) return kInternalError;
      if (!kycData) return k422ErrorMessage('No data found');

      return kycData.aadhaarNumber;
    } catch (error) {
      console.error('Error in: ', error);
      return kInternalError;
    }
  }

  async handleSubscriptionFlow(data: any) {
    try {
      const loanId = data.loanId;
      if (!loanId) return kParamMissing('loanId');
      const response = data.response;
      if (!response) return kParamMissing('response');
      const queryData = decodeURIComponent(response);
      const querySpans = queryData?.split('&');
      const callbackList: any = [
        {
          url: HOST_URL + `${Latest_Version}/mandate/checkStatus`,
          directSource: { loanId },
          urlMethod: 'POST',
          waitForCallbackResponse: true,
        },
      ];
      return { callbackList, directProcessing: true };
    } catch (error) {
      console.error('Error in: ', error);
      return kInternalError;
    }
  }

  async addAddressData(addressList: any[], userId: string, type: string) {
    try {
      const response = JSON.stringify(addressList);
      const creationData: any = {
        adminId: SYSTEM_ADMIN_ID,
        response,
        status: '5',
        type,
        userId,
      };
      const priveusAddresses = await this.addressRepo.getTableWhereData(
        ['address', 'id', 'type', 'status'],
        { where: { userId } },
      );
      let availableAddress: any = {};
      if (priveusAddresses && priveusAddresses !== k500Error) {
        for (let i = 0; i < priveusAddresses.length; i++) {
          let key = '';
          if (typeof priveusAddresses[i]['address'] === 'string')
            key = `${[
              ...priveusAddresses[i]?.address?.split(' '),
            ]}`.toLocaleLowerCase();
          availableAddress[key] = true;
        }
      }
      for (let index = 0; index < 10; index++) {
        try {
          const address = addressList[index].deliveryAddress;
          if (
            !availableAddress[`${[...address.split(' ')]}`.toLocaleLowerCase()]
          ) {
            creationData.address = address;
            await this.addressRepo.createRowData(creationData);
          }
        } catch (error) {}
      }

      return {};
    } catch (error) {
      console.error('Error in: ', error);
      return k500Error;
    }
  }
}
