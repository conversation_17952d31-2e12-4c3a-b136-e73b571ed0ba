import { Injectable } from '@nestjs/common';
import {
  k400ErrorMessage,
  kInternalError,
  kParamMissing,
  kSuccessData,
} from 'src/constants/responses';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { CryptService } from 'src/utils/crypt.service';
import { k500Error } from 'src/constants/misc';
import { AdminEligibilityEntity } from 'src/entities/adminEligibility.entity';
import { PAGE, PAGE_LIMIT } from 'src/constants/globals';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { Op } from 'sequelize';
import { YES_NO_OBJECT } from 'src/constants/objects';
import { TypeService } from 'src/utils/type.service';
import { FileService } from 'src/utils/file.service';
import { v4 as uuidv4 } from 'uuid';
import {
  checkValidString,
  regEmail,
  regPanCard,
} from 'src/constants/validation';
import { DateService } from 'src/utils/date.service';
import { kNoDataFound } from 'src/constants/strings';

@Injectable()
export class EmployeeEligibilityService {
  constructor(
    private readonly cryptService: CryptService,
    private readonly repoManager: RepositoryManager,
    private readonly commonSharedService: CommonSharedService,
    private readonly typeService: TypeService,
    private readonly fileServices: FileService,
    private readonly dateService: DateService,
  ) {}

  //#region Create or update record
  async createOrUpdateRow(body) {
    try {
      /// Validations
      if (!body?.emp_name) return kParamMissing('emp_name');
      if (!body?.emp_code) return kParamMissing('emp_code');
      if (!body?.department) return kParamMissing('department');
      if (!body?.emp_phone) return kParamMissing('emp_phone');
      if (!body?.email) return kParamMissing('email');
      if (!body?.aadhaar_num) return kParamMissing('aadhaar_num');
      if (!body?.pan_num) return kParamMissing('pan_num');
      if (!body?.adminId) return kParamMissing('adminId');

      /// Find employee details
      if (!body?.emp_phone) return kParamMissing('emp_phone');
      const hash_emp_phone = this.cryptService.getMD5Hash(
        String(body?.emp_phone),
      );
      const is_data_exist = await this.checkIfDataExists(hash_emp_phone);
      if (is_data_exist == k500Error) return kInternalError;
      if (is_data_exist && !body?.id) return kParamMissing('id');

      const data = await this.prepareEmployeeData(
        body,
        hash_emp_phone,
        is_data_exist,
      );
      if (!data) return k400ErrorMessage(kNoDataFound);
      /// If no data found then create, else update
      if (!is_data_exist)
        await this.repoManager.createRowData(AdminEligibilityEntity, data);
      else
        await this.repoManager.updateRowDataWithOptions(
          AdminEligibilityEntity,
          data,
          {},
          body?.id,
        );

      /// Update employee eligibility list to redis details on create or update
      await this.commonSharedService.employeeEligibilityList();
      return kSuccessData;
    } catch (error) {
      return kInternalError;
    }
  }
  //#endregion

  //#region Check if data already exists before creating new entry
  private async checkIfDataExists(hash_emp_phone) {
    const options = { where: { hash_emp_phone } };
    return await this.repoManager.getCountsWhere(
      AdminEligibilityEntity,
      options,
    );
  }
  //#endregion

  //#region Prepare data
  private async prepareEmployeeData(body, hash_emp_phone, is_data_exist) {
    let department_data: any;
    if (body?.department) {
      department_data = await this.commonSharedService.getDepartment(
        body?.department,
      );
      if (!department_data) return;
    }

    let admin_data: any;
    if (body?.adminId) {
      admin_data = await this.commonSharedService.getAdminData(body?.adminId);
      if (!admin_data) return;
    }

    const payload = {
      emp_name: body?.emp_name,
      emp_code: body?.emp_code ? body?.emp_code.toUpperCase() : null,
      department_id: department_data?.id,
      enc_emp_phone: this.cryptService.encryptPhone(body?.emp_phone),
      hash_emp_phone,
      email: body?.email,
      enc_aadhaar: await this.cryptService.encryptText(
        String(body?.aadhaar_num),
      ),
      hash_aadhaar: this.cryptService.getMD5Hash(String(body?.aadhaar_num)),
      enc_pan: await this.cryptService.encryptText(body?.pan_num),
      hash_pan: this.cryptService.getMD5Hash(body?.pan_num),
      is_eligible:
        typeof body?.is_eligible == 'boolean' ? body?.is_eligible : false,
      updated_by: admin_data?.id,
    };

    if (is_data_exist) {
      payload['id'] = body?.id;
      payload['updated_by'] = body?.adminId;
      if (typeof body?.is_eligible == 'boolean')
        payload['is_eligible'] = body?.is_eligible;
    }

    return payload;
  }
  //#endregion

  //#region Get and find record(s)
  async getEmployeeDetails(query) {
    try {
      /// Get employee details
      const response = await this.getEmployeeData(query);
      if (response?.message || response == k500Error) return kInternalError;

      /// Prepare employee details
      return await this.prepareResponse(response);
    } catch (error) {
      return kInternalError;
    }
  }
  //#endregion

  //#region Get or find record(s)
  private async getEmployeeData(query) {
    const attributes = [
      'id',
      'emp_name',
      'emp_code',
      'department_id',
      'enc_emp_phone',
      'email',
      'enc_aadhaar',
      'enc_pan',
      'is_eligible',
      'updated_by',
      'updated_at',
    ];

    const options: any = await this.prepareOptions(query);
    if (!options) return options;

    return await this.repoManager.getTableCountWhereData(
      AdminEligibilityEntity,
      attributes,
      options,
    );
  }
  //#endregion

  //#region Prepare options for getting and finding record(s)
  private async prepareOptions(query) {
    const limit = query?.limit ?? PAGE_LIMIT;
    const offset = (Number(query?.page ?? PAGE) - PAGE) * PAGE_LIMIT;

    const options: any = {
      order: [['id', 'DESC']],
      where: {},
      offset,
      limit,
    };

    if (['true', 'false'].includes(query?.is_eligible))
      options.where = {
        is_eligible: query.is_eligible,
      };

    if (query?.department)
      options.where = {
        ...options.where,
        department_id: query.department,
      };

    /// Prepare search if applicable
    if (
      query?.searchText &&
      query?.searchText != '' &&
      query?.searchText?.length > 3
    ) {
      const search_query = this.prepareEmployeeSearch(query?.searchText);
      if (search_query)
        options.where = {
          ...options.where,
          ...search_query,
        };
    }
    return options;
  }
  //#endregion

  //#region Prepare search query
  private prepareEmployeeSearch(searchText) {
    // Search for phone number
    if (/^\d{10}$/.test(searchText.trim())) {
      const hash_phone = this.cryptService.getMD5Hash(String(searchText));
      return { [Op.or]: { hash_emp_phone: hash_phone } };
    }
    /// Check search with aadhar number
    else if (/^\d{12}$/.test(searchText.trim())) {
      const hash_aadhaar = this.cryptService.getMD5Hash(String(searchText));
      return { hash_aadhaar };
    }
    /// Check search with email
    else if (searchText.includes('@'))
      return { email: { [Op.iLike]: `%${searchText}%` } };
    /// check pan card search
    else if (
      searchText.length == 10 &&
      /^[a-zA-Z]{5}[0-9]{4}[a-zA-Z]{1}$/.test(searchText)
    ) {
      let hash_pan = searchText.toUpperCase();
      hash_pan = this.cryptService.getMD5Hash(hash_pan);
      return { hash_pan };
    }
    /// Search by name of employee code
    else if (isNaN(searchText)) {
      return {
        [Op.or]: {
          emp_name: { [Op.iLike]: `%${searchText}%` },
          emp_code: { [Op.iLike]: `%${searchText.toUpperCase()}%` },
        },
      };
    }
  }
  //#endregion

  //#region Prepare readable response
  private async prepareResponse(response) {
    if (!response?.rows?.length) return { count: 0, rows: [] };

    ///get department details
    const department_data = await this.commonSharedService.getDepartment();

    const response_data = await Promise.all(
      response?.rows?.map(async (emp) => {
        const { department } = emp?.department_id
          ? department_data.find((item) => item.id === emp?.department_id)
          : '-';

        const aadhaar_num = emp?.enc_aadhaar
          ? await this.cryptService.decryptText(emp?.enc_aadhaar)
          : '-';

        let updated_by = emp?.updated_by
          ? await this.commonSharedService.getAdminData(emp.updated_by)
          : '-';
        updated_by = updated_by?.fullName;

        return {
          Id: emp?.id ?? '-',
          'Employee name': emp?.emp_name ?? '-',
          'Employee code': emp?.emp_code ?? '-',
          Department: department ?? '-',
          Phone: emp?.enc_emp_phone
            ? this.cryptService.decryptPhone(emp?.enc_emp_phone)
            : '-',
          Email: emp?.email ?? '-',
          Aadhaar: emp?.enc_aadhaar
            ? this.typeService.formatAadhaarNumber(aadhaar_num)
            : '-',
          PAN: emp?.enc_pan
            ? await this.cryptService.decryptText(emp?.enc_pan)
            : '-',
          'Eligible for loan': YES_NO_OBJECT[emp?.is_eligible] ?? '-',
          'Last action by': updated_by ?? '-',
          'Last updated': emp?.updated_at
            ? this.dateService.readableDate(emp.updated_at)
            : '-',
        };
      }),
    );

    return { count: response.count, rows: response_data };
  }
  //#endregion

  async uploadExcel(file, body) {
    if (file) {
      const read_data_from_file: any = await this.fileServices.excelToArray(
        file.filename,
      );

      if (read_data_from_file?.message) return read_data_from_file;

      //Remove empty objects from read file data if any
      const data_from_file = read_data_from_file.filter(
        (data) => Object.keys(data).length !== 0,
      );
      const department_data = await this.commonSharedService.getDepartment();

      const acceptedData = this.validateDataFromFile(
        data_from_file,
        department_data,
      );

      return await this.validatePayloadAndStore(acceptedData, body);
    }
  }

  //#region Validate data extracted from Excel file
  private validateDataFromFile(fileData, department_data) {
    const acceptedData = [];
    const rejectedData = [];

    /// Check if required fields exists or not
    fileData.forEach((data: any) => {
      const tempId = uuidv4();

      //#region Extract work email
      const tempEmail = data['Work email'];
      const match = tempEmail?.hyperlink?.match(/mailto:(.*)/);
      const email = match ? match[1] : null; /// Extract email after 'mailto:'

      if (tempEmail)
        data['Work email'] =
          typeof tempEmail === 'string'
            ? tempEmail
            : (tempEmail && typeof tempEmail === 'object'
                ? email
                : tempEmail.text) ?? '';

      //#endregion
      let isDataRejected = false; /// Flag to manage accepted data array

      //#region Required fields validations
      if (!data['Full name'] || data['Full name'].trim() === '') {
        rejectedData.push({
          ...data,
          tempId,
          reasons: ['Full name does not exist'],
          'Full name': null,
        });

        isDataRejected = true;
      }

      if (!data['Employee Code'] || data['Employee Code'].trim() === '') {
        rejectedData.push({
          ...data,
          tempId,
          reasons: ['Employee Code does not exist'],
          'Employee Code': null,
        });

        isDataRejected = true;
      }

      if (!data['Department'] || data['Department'].trim() === '') {
        rejectedData.push({
          ...data,
          tempId,
          reasons: ['Department does not exist'],
          Department: null,
        });

        isDataRejected = true;
      }

      if (!data['Mobile number'] || data['Mobile number'] === '') {
        const getRowIndex = rejectedData.findIndex(
          (row) => row.tempId === tempId,
        );

        if (getRowIndex !== -1) {
          rejectedData[getRowIndex].reasons.push(
            'Mobile number does not exist',
          );
          rejectedData[getRowIndex]['Mobile number'] = null;
        } else {
          rejectedData.push({
            ...data,
            tempId,
            reasons: ['Mobile number does not exist'],
            Phone: null,
          });
        }

        isDataRejected = true;
      }

      if (!data['Work email'] || data['Work email'] === '') {
        const getRowIndex = rejectedData.findIndex(
          (row) => row.tempId === tempId,
        );

        if (getRowIndex !== -1) {
          rejectedData[getRowIndex].reasons.push('Work email does not exist');
          rejectedData[getRowIndex]['Work email'] = null;
        } else {
          rejectedData.push({
            ...data,
            tempId,
            reasons: ['Work email does not exist'],
            'Work email': null,
          });
        }

        isDataRejected = true;
      }

      if (!data['Aadhar no.'] || data['Aadhar no.'] === '') {
        const getRowIndex = rejectedData.findIndex(
          (row) => row.tempId === tempId,
        );

        if (getRowIndex !== -1) {
          rejectedData[getRowIndex].reasons.push('Aadhar no. does not exist');
          rejectedData[getRowIndex]['Aadhar no.'] = null;
        } else {
          rejectedData.push({
            ...data,
            tempId,
            reasons: ['Aadhar no. does not exist'],
            'Aadhar no.': null,
          });
        }

        isDataRejected = true;
      }

      if (!data['PAN no.'] || data['PAN no.'] === '') {
        const getRowIndex = rejectedData.findIndex(
          (row) => row.tempId === tempId,
        );

        if (getRowIndex !== -1) {
          rejectedData[getRowIndex].reasons.push('PAN no. does not exist');
          rejectedData[getRowIndex]['PAN no.'] = null;
        } else {
          rejectedData.push({
            ...data,
            tempId,
            reasons: ['PAN no. does not exist'],
            'PAN no.': null,
          });
        }

        isDataRejected = true;
      }
      //#endregion

      //#region Invalid data validations
      if (
        data['Full name'] &&
        (typeof data['Full name'] !== 'string' ||
          !checkValidString(data['Full name'].trim()))
      ) {
        const getRowIndex = rejectedData.findIndex(
          (row) => row.tempId === tempId,
        );

        if (getRowIndex !== -1) {
          rejectedData[getRowIndex].reasons.push('Invalid Full name');
          rejectedData[getRowIndex]['Full name'] = null;
        } else {
          rejectedData.push({
            ...data,
            tempId,
            reasons: ['Invalid Full name'],
            'Full name': null,
          });
        }

        isDataRejected = true;
      }

      if (data['Employee Code'] && typeof data['Employee Code'] !== 'string') {
        const getRowIndex = rejectedData.findIndex(
          (row) => row.tempId === tempId,
        );

        if (getRowIndex !== -1) {
          rejectedData[getRowIndex].reasons.push('Invalid Employee Code');
          rejectedData[getRowIndex]['Employee Code'] = null;
        } else {
          rejectedData.push({
            ...data,
            tempId,
            reasons: ['Invalid Employee Code'],
            'Employee Code': null,
          });
        }

        isDataRejected = true;
      }

      if (
        data['Department'] &&
        (typeof data['Department'] !== 'string' ||
          !checkValidString(data['Department'].trim()) ||
          !department_data.some(
            (item) => item.department === data['Department'].toUpperCase(),
          ))
      ) {
        const getRowIndex = rejectedData.findIndex(
          (row) => row.tempId === tempId,
        );

        if (getRowIndex !== -1) {
          rejectedData[getRowIndex].reasons.push('Invalid Department');
          rejectedData[getRowIndex]['Department'] = null;
        } else {
          rejectedData.push({
            ...data,
            tempId,
            reasons: ['Invalid Department'],
            Department: null,
          });
        }

        isDataRejected = true;
      }

      if (
        data['Mobile number'] &&
        (isNaN(data['Mobile number']) ||
          String(data['Mobile number']).length !== 10)
      ) {
        const getRowIndex = rejectedData.findIndex(
          (row) => row.tempId === tempId,
        );

        if (getRowIndex !== -1) {
          rejectedData[getRowIndex].reasons.push('Invalid Mobile number');
          rejectedData[getRowIndex]['Mobile number'] = null;
        } else {
          rejectedData.push({
            ...data,
            tempId,
            reasons: ['Invalid Mobile number'],
            'Mobile number': null,
          });
        }

        isDataRejected = true;
      }

      if (data['Work email'] && !regEmail(data['Work email'])) {
        const getRowIndex = rejectedData.findIndex(
          (row) => row.tempId === tempId,
        );

        if (getRowIndex !== -1) {
          rejectedData[getRowIndex].reasons.push('Invalid Work email');
          rejectedData[getRowIndex]['Work email'] = null;
        } else {
          rejectedData.push({
            ...data,
            tempId,
            reasons: ['Invalid Work email'],
            'Work email': null,
          });
        }

        isDataRejected = true;
      }

      if (
        data['Aadhar no.'] &&
        (isNaN(data['Aadhar no.']) || String(data['Aadhar no.']).length !== 12)
      ) {
        const getRowIndex = rejectedData.findIndex(
          (row) => row.tempId === tempId,
        );

        if (getRowIndex !== -1) {
          rejectedData[getRowIndex].reasons.push('Invalid Aadhar no.');
          rejectedData[getRowIndex]['Aadhar no.'] = null;
        } else {
          rejectedData.push({
            ...data,
            tempId,
            reasons: ['Invalid Aadhar no.'],
            'Aadhar no.': null,
          });
        }

        isDataRejected = true;
      }

      if (data['PAN no.'] && !regPanCard(data['PAN no.'])) {
        const getRowIndex = rejectedData.findIndex(
          (row) => row.tempId === tempId,
        );

        if (getRowIndex !== -1) {
          rejectedData[getRowIndex].reasons.push('Invalid PAN no.');
          rejectedData[getRowIndex]['PAN no.'] = null;
        } else {
          rejectedData.push({
            ...data,
            tempId,
            reasons: ['Invalid PAN no.'],
            'PAN no.': null,
          });
        }

        isDataRejected = true;
      }
      //#endregion

      if (!isDataRejected) {
        const deptResult = department_data.find(
          (item: any) => item.department === data.Department.toUpperCase(),
        );
        /// Check if duplicate entry is there in the acceptedData
        const checkIfDataExists = acceptedData.find((item) => {
          if (item.emp_phone === data['Mobile number']) return true;
          else return false;
        });

        if (!checkIfDataExists) {
          acceptedData.push({
            emp_name: data['Full name'],
            emp_code: data['Employee Code'],
            department: deptResult?.id ?? null,
            emp_phone: data['Mobile number'],
            email: data['Work email'],
            aadhaar_num: data['Aadhar no.'],
            pan_num: data['PAN no.'],
          });
        } else {
          rejectedData.push({
            ...data,
            tempId,
            reasons: ['Duplicate entry in the file.'],
          });
        }
      }
    });

    return acceptedData;
  }
  //#endregion

  //#region Validate payload, if exists in database then skip, else store to database
  private async validatePayloadAndStore(acceptedData: any, body: any) {
    try {
      const list_of_hash_phone = acceptedData.map((item) =>
        this.cryptService.getMD5Hash(String(item.emp_phone)),
      );

      const attributes = ['id', 'hash_emp_phone'];
      const options = {
        where: { hash_emp_phone: { [Op.in]: list_of_hash_phone } },
      };

      const checkForDuplicate = await this.repoManager.getTableWhereData(
        AdminEligibilityEntity,
        attributes,
        options,
      );
      if (!checkForDuplicate || checkForDuplicate === k500Error)
        return kInternalError;

      /// Remove already existing records from the accepted array & add them in rejected array
      if (checkForDuplicate.length) {
        for (let i = 0; i < checkForDuplicate.length; i++) {
          const dbRecord = checkForDuplicate[i];

          const indexToRemove = acceptedData.findIndex((item) => {
            const item_hash_emp_phone = this.cryptService.getMD5Hash(
              String(item.emp_phone),
            );
            if (item_hash_emp_phone === dbRecord.hash_emp_phone) return true;
            else return false;
          });

          if (indexToRemove > -1) acceptedData.splice(indexToRemove, 1);
        }
      }

      const admin_data = await this.commonSharedService.getAdminData(
        body?.adminId,
      );
      /// Save all the to admin eligibility table
      for (let i = 0; i < acceptedData.length; i++) {
        const item = acceptedData[i];
        item.adminId = admin_data?.id ?? null;
        await this.createOrUpdateRow(item);
      }
    } catch (error) {
      console.log('error: ', error);
      return kInternalError;
    }
  }
  //#endregion
}
