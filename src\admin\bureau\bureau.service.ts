import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { BUREAU_REDIS_KEY } from 'src/constants/globals';
import { k500Error } from 'src/constants/misc';
import { NUMBERS } from 'src/constants/numbers';
import { kParamMissing } from 'src/constants/responses';
import { CibilScoreEntity } from 'src/entities/cibil.score.entity';
import { ExperianScoreEntity } from 'src/entities/experianScore.entity';
import { RedisService } from 'src/redis/redis.service';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { CibilService } from 'src/shared/cibil.service';
import { ExperianSharedService } from 'src/shared/experian.service';
import { TypeService } from 'src/utils/type.service';

@Injectable()
export class BureauService {
  constructor(
    // repo
    private readonly repo: RepositoryManager,
    @Inject(forwardRef(() => CibilService))
    private readonly cibilService: CibilService,
    @Inject(forwardRef(() => ExperianSharedService))
    private readonly experianService: ExperianSharedService,
    private readonly typeService: TypeService,
    private readonly redis: RedisService,
  ) {}

  async getAllBureauDataUserWise(reqData) {
    const userId = reqData?.userId;
    if (!userId) return kParamMissing('userId');

    // check if redis has data already
    const dataFromRedis = await this.getRedisDataForMultipleUserId(userId);
    if (dataFromRedis) return dataFromRedis;

    const cibilResult = await this.fetchAndProcessCibilData(userId);
    const experianResult = await this.fetchAndProcessExperianData(userId);

    // concat and sort both's Bureau data
    const finalData = this.mergeAndSortResults(cibilResult, experianResult);

    // set prepared data in redis
    if (finalData.length > 0)
      await this.setRedisDataForMultipleUserId(userId, finalData);
    return finalData;
  }

  //Fetch and Process Cibil Data
  private async fetchAndProcessCibilData(userId: string) {
    // Fetch cibil data
    const cibilAttributes = [
      'id',
      'loanId',
      'status',
      'cibilScore',
      'plScore',
      'totalAccounts',
      'overdueAccounts',
      'zeroBalanceAccounts',
      'highCreditAmount',
      'currentBalance',
      'overdueBalance',
      'totalOverdueDays',
      'PLOutstanding',
      'totalOutstanding',
      'monthlyIncome',
      'totalInquiry',
      'inquiryPast30Days',
      'inquiryPast12Months',
      'inquiryPast24Months',
      'recentDateOpened',
      'oldestDateOpened',
      'recentInquiryDate',
      'oldestInquiryDate',
      'PLAccounts',
      'scores',
      'responsedata',
    ];

    const rawQuery = `SELECT "${cibilAttributes.join('","')}"
        FROM "CibilScoreEntities"
        WHERE "type" = '1' AND "status" = '1' AND "userId" = '${userId}'
        ORDER BY "id" DESC;`;

    const cibilResponse = await this.repo.injectRawQuery(
      CibilScoreEntity,
      rawQuery,
      {
        useMaster: false,
      },
    );

    // prepare cibil response
    const result = [];
    for (let i = 0; i < cibilResponse.length; i++) {
      const currentData = cibilResponse[i];
      if (
        currentData?.responsedata?.internal_source !== 'POSTGRES' &&
        currentData?.scores
      ) {
        const date = currentData?.scores[0]?.scoreDate;
        const cibilData = this.cibilService.prepareCibilData(
          currentData,
          'cibilDetails',
        );
        result.push({
          type: 'CIBIL',
          date: this.formatDate(date),
          id: currentData?.id,
          data: cibilData,
        });
      }
    }

    // calculate diff between previous and current cibil data
    this.calculateDifferences(result);
    return result;
  }

  //Fetch and Process Experian Data
  private async fetchAndProcessExperianData(userId: string) {
    // Fetch experian data
    const experianAttributes = [
      'id',
      'experianScore',
      'overdueAccounts',
      'inquiryPast30Days',
      'PLAccounts',
      'overdueAmount',
      'totalDelayDays',
      'PLOutstanding',
      'totalOutstanding',
      'experianFetchDate',
      'formattedResponse',
    ];

    const experianRawQuery = `SELECT "${experianAttributes.join('","')}"
      FROM "ExperianScoreEntities"
      WHERE ("internal_source" IS NULL OR "internal_source" != 1) AND "userId" = '${userId}'
      ORDER BY "id" DESC;`;

    const experianResponse = await this.repo.injectRawQuery(
      ExperianScoreEntity,
      experianRawQuery,
      { useMaster: false },
    );
    if (experianResponse === k500Error) throw new Error();

    // prepare experian response
    const result = [];
    for (let i = 0; i < experianResponse.length; i++) {
      const currentData = experianResponse[i];
      if (currentData) {
        const date = currentData?.experianFetchDate;
        const experianData = this.experianService.prepareExperianData(
          currentData,
          'experianDetails',
        );
        if (!experianData) continue;
        result.push({
          type: 'Experian',
          date: this.typeService.getDateFormated(date, '/'),
          id: currentData?.id,
          data: experianData,
        });
      }
    }

    // calculate diff between previous and current experian data
    this.calculateDifferences(result);
    return result;
  }

  async getBureauDataIdWise(reqData) {
    const userId = reqData?.userId;
    if (!userId) return kParamMissing('userId');
    const id = reqData?.id;
    if (!id) return kParamMissing('id');
    const type = reqData?.type;
    if (!type) return kParamMissing('type');

    let result;
    const dataFromRedis = await this.getRedisDataForIdWise(userId, id, type);
    if (dataFromRedis) return dataFromRedis;

    if (type == 'CIBIL')
      result = await this.cibilService.getCibilDataIdWise(reqData);
    else result = await this.experianService.getExperianDataIdWise(reqData);

    await this.setRedisDataForIdWise(userId, id, type, result);

    return result;
  }

  // get user bureau score data
  async getBureauScoreData(reqData) {
    const userId = reqData.userId;
    if (!userId) return kParamMissing('userId');

    const dataFromRedis = await this.getRedisDataForFV(userId);
    if (dataFromRedis) return dataFromRedis;

    const cibilData = await this.cibilService.funGetUserCibilScoreData(reqData);
    if (cibilData?.message) return cibilData;
    const experianData: any =
      await this.experianService.funGetUserExperianScoreData(reqData);
    if (experianData?.message) return experianData;

    const data = { cibilData, experianData };
    await this.setRedisDataForFV(userId, data);
    return data;
  }
  //#endregion

  private formatDate(date: string) {
    return `${date.slice(0, 2)}/${date.slice(2, 4)}/${date.slice(4)}`;
  }

  private calculateDifferences(dataArray) {
    for (let i = dataArray.length - 2; i >= 0; i--) {
      let currentEle = dataArray[i].data;
      let previousEle = dataArray[i + 1].data;

      const currentPlScore =
        currentEle?.plScore === '-' ? 0 : currentEle?.plScore ?? 0;
      const previousPlScore =
        previousEle?.plScore === '-' ? 0 : previousEle?.plScore ?? 0;

      // Check if plScore is '-' and treat it as 0
      currentEle.diffInBureau = this.calculatePercentage(
        currentEle?.bureauScore ?? 0,
        previousEle?.bureauScore ?? 0,
      );

      currentEle.diffInPl =
        currentPlScore || previousPlScore
          ? this.calculatePercentage(currentPlScore, previousPlScore)
          : 0;
      currentEle.diffInTotalAccounts =
        (currentEle?.totalAccounts ?? 0) - (previousEle?.totalAccounts ?? 0);
      currentEle.diffinOverdueAccounts =
        (currentEle?.overdueAccounts ?? 0) -
        (previousEle?.overdueAccounts ?? 0);
      currentEle.diffInZeroBalanceAccounts =
        (currentEle?.zeroBalanceAccounts ?? 0) -
        (previousEle?.zeroBalanceAccounts ?? 0);
      currentEle.diffInHighCreditAmount =
        (currentEle?.highCreditAmount ?? 0) -
        (previousEle?.highCreditAmount ?? 0);
      currentEle.diffInOverdueBalance =
        (currentEle?.overdueBalance ?? 0) - (previousEle?.overdueBalance ?? 0);
      currentEle.diffInTotalOutstandingBalance =
        (currentEle?.totalOutstanding ?? 0) -
        (previousEle?.totalOutstanding ?? 0);
      currentEle.diffInPLoutstanding =
        (currentEle?.PLOutstanding ?? 0) - (previousEle?.PLOutstanding ?? 0);
      currentEle.diffInPlAccounts =
        (currentEle?.PLAccounts ?? 0) - (previousEle?.PLAccounts ?? 0);
      currentEle.diffInDelaydays =
        (currentEle?.totalOverdueDays ?? 0) -
        (previousEle?.totalOverdueDays ?? 0);
      currentEle.diffInTotalInquiry =
        (currentEle?.totalInquiry ?? 0) - (previousEle?.totalInquiry ?? 0);
    }
  }

  private mergeAndSortResults(cibilResult, experianResult) {
    return [...cibilResult, ...experianResult].sort((a, b) => {
      return (
        new Date(b.date.split('/').reverse().join('/')).getTime() -
        new Date(a.date.split('/').reverse().join('/')).getTime()
      );
    });
  }

  calculatePercentage(current: number, previous: number) {
    // calculate percentage difference between any two number
    const difference = current - previous;

    const percentageDifference = (difference / previous) * 100;
    return +percentageDifference.toFixed(2);
  }

  async getRedisDataForMultipleUserId(userId) {
    let key = `${userId}_${BUREAU_REDIS_KEY.FOR_MULTIPLE_DATA}`;
    let redisData = await this.redis.get(key);
    redisData = redisData ? JSON.parse(redisData) : null;
    return redisData;
  }

  async setRedisDataForMultipleUserId(userId, data) {
    let key = `${userId}_${BUREAU_REDIS_KEY.FOR_MULTIPLE_DATA}`;
    data = JSON.stringify(data);
    await this.redis.set(key, data, NUMBERS.FIVE_DAYS_IN_SECONDS);
  }

  async deleteRedisDataForMultipleUserId(userId) {
    let key = `${userId}_${BUREAU_REDIS_KEY.FOR_MULTIPLE_DATA}`;
    await this.redis.del(key);
  }

  async getRedisDataForIdWise(userId, id, type) {
    const key = `${userId}_${id}_${BUREAU_REDIS_KEY.FOR_ID_WISE_DATA}_${type}`;
    let redisData = await this.redis.get(key);
    return redisData ? JSON.parse(redisData) : null;
  }

  async setRedisDataForIdWise(userId, id, type, data) {
    const key = `${userId}_${id}_${BUREAU_REDIS_KEY.FOR_ID_WISE_DATA}_${type}`;
    await this.redis.set(
      key,
      JSON.stringify(data),
      NUMBERS.FIVE_DAYS_IN_SECONDS,
    );
  }

  async deleteRedisDataForIdWise(userId, id) {
    const key = `${userId}_${id}_${BUREAU_REDIS_KEY.FOR_ID_WISE_DATA}`;
    await this.redis.del(key);
  }

  async getRedisDataForFV(userId) {
    let key = `${userId}_${BUREAU_REDIS_KEY.FOR_FV_DATA}`;
    let redisData = await this.redis.get(key);
    redisData = redisData ? JSON.parse(redisData) : null;
    return redisData;
  }

  async setRedisDataForFV(userId, data) {
    let key = `${userId}_${BUREAU_REDIS_KEY.FOR_FV_DATA}`;
    data = JSON.stringify(data);
    await this.redis.set(key, data, NUMBERS.FIVE_DAYS_IN_SECONDS);
  }

  async deleteRedisDataForFV(userId) {
    let key = `${userId}_${BUREAU_REDIS_KEY.FOR_FV_DATA}`;
    await this.redis.del(key);
  }
}
