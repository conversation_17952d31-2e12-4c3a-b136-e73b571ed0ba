import { Inject, Injectable } from '@nestjs/common';
import { LEAD_TRACKING_FILE_REPOSITORY } from 'src/constants/entities';
import { RepositoryManager } from './repository.manager';
import { k500Error } from 'src/constants/misc';
import { LeadTrackingFileEntity } from 'src/entities/leadTrackingFile.entity';

@Injectable()
export class LeadTrackingFileRepository {
  constructor(
    @Inject(LEAD_TRACKING_FILE_REPOSITORY)
    private readonly repository: LeadTrackingFileEntity,
    private readonly repoManager: RepositoryManager,
  ) {}

  async createRowData(createData) {
    try {
      return await this.repoManager.createRowData(this.repository, createData);
    } catch (error) {
      return k500Error;
    }
  }

  async getTableWhereData(attributes: string[], options: any) {
    try {
      return await this.repoManager.getTableWhereData(
        this.repository,
        attributes,
        options,
      );
    } catch (error) {
      return k500Error;
    }
  }

  async bulkCreate(data) {
    try {
      return await this.repoManager.bulkCreate(this.repository, data);
    } catch (error) {
      return k500Error;
    }
  }

  async getTableWhereDataWithCounts(attributes: string[], options: any) {
    try {
      return this.repoManager.getTableCountWhereData(
        this.repository,
        attributes,
        options,
      );
    } catch (error) {
      return k500Error;
    }
  }
}
