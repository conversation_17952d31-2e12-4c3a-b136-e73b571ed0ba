// Imports
import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { Op } from 'sequelize';
import {
  CASE_DISPOSAL,
  GLOBAL_FLOW,
  SYSTEM_ADMIN_ID,
  WARRENT,
} from 'src/constants/globals';
import { PAGE_LIMIT, legalString } from 'src/constants/globals';
import { k500Error } from 'src/constants/misc';
import {
  k409ErrorMessage,
  kInvalidParamValue,
  k422ErrorMessage,
  kInternalError,
  kParamMissing,
  kParamsMissing,
  kWrongDetails,
  kSUCCESSMessage,
  k400ErrorMessage,
} from 'src/constants/responses';
import {
  kNoDataFound,
  SelfieverificationSuccess,
  SSelfieNotVerify,
  SSelfieVerification,
  kCoolOffPeriodOverTitle,
  kCoolOffPeriodOverContent,
  kDisbursementInProcess,
  kYouHaveAccess,
} from 'src/constants/strings';
import { KYCEntity } from 'src/entities/kyc.entity';
import { loanTransaction } from 'src/entities/loan.entity';
import { MasterEntity } from 'src/entities/master.entity';
import { registeredUsers } from 'src/entities/user.entity';
import { UserSelfieEntity } from 'src/entities/user.selfie.entity';
import { DeviceRepository } from 'src/repositories/device.repositoy';
import { InstallAppRepository } from 'src/repositories/installApps.repository';
import { ChangeLogsRepository } from 'src/repositories/changeLogs.repository';
import { ContactLogRepository } from 'src/repositories/contact.log.repository';
import { KYCRepository } from 'src/repositories/kyc.repository';
import { LoanRepository } from 'src/repositories/loan.repository';
import { MasterRepository } from 'src/repositories/master.repository';
import { MediaRepository } from 'src/repositories/media.repository';
import { BlockUserHistoryRepository } from 'src/repositories/user.blockHistory.repository';
import { UserRepository } from 'src/repositories/user.repository';
import { UserSelfieRepository } from 'src/repositories/user.selfie.repository';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { EligibilitySharedService } from 'src/shared/eligibility.shared.service';
import { MigrationSharedService } from 'src/shared/migration.service';
import { SharedNotificationService } from 'src/shared/services/notification.service';
import { CryptService } from 'src/utils/crypt.service';
import { FileService } from 'src/utils/file.service';
import { TypeService } from 'src/utils/type.service';
import { AddressesRepository } from 'src/repositories/addresses.repository';
import { UserServiceV4 } from 'src/v4/user/user.service.v4';
import { DeviceSIMRepository } from 'src/repositories/deviceSIM.repository';
import { UserPermissionRepository } from 'src/repositories/userPermission.repository';
import { MailTrackerRepository } from 'src/repositories/mail.tracker.repository';
import { DateService } from 'src/utils/date.service';
import { ReasonsEntity } from 'src/entities/Reasons.entity';
import { PredictionRepository } from 'src/repositories/prediction.repository';
import { UserActivityRepository } from 'src/repositories/user.activity.repository';
import { SequelOptions } from 'src/interfaces/include.options';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { PromoCodeService } from 'src/shared/promo.code.service';
import { EmiEntity } from 'src/entities/emi.entity';
import { TransactionEntity } from 'src/entities/transaction.entity';
import { esignEntity } from 'src/entities/esign.entity';
import { employmentDetails } from 'src/entities/employment.entity';
import { EmiSharedService } from 'src/shared/emi.service';
import { StringService } from 'src/utils/string.service';
import { ESignRepository } from 'src/repositories/esign.repository';
import { RedisService } from 'src/redis/redis.service';
import { NUMBERS } from 'src/constants/numbers';
import { HashPhoneEntity } from 'src/entities/hashPhone.entity';
import { EMIRepository } from 'src/repositories/emi.repository';
import { ErrorContextService } from 'src/utils/error.context.service';
import { MongoQueryService } from 'src/database/mongodb/mongo.query.service';
import {
  kCollectionTabUploadDocList,
  kLegalTabUploadDocList,
  LOAN_STATUS_OBJECT,
  PERIODIC_ENTITY_STATUS,
  USER_LOAN_STATUS,
} from 'src/constants/objects';
import { InsuranceRepository } from 'src/repositories/insurance.repository';
import { PeriodicEntity } from 'src/entities/periodic.entity';
import { WhatsAppService } from 'src/thirdParty/whatsApp/whatsApp.service';
import { APIService } from 'src/utils/api.service';
import { EnvConfig } from 'src/configs/env.config';
import { CAAssignmentService } from 'src/shared/assignment/caAssignCase.service';

@Injectable()
export class UserService {
  constructor(
    private readonly fileService: FileService,
    private readonly userRepo: UserRepository,
    private readonly cryptService: CryptService,
    private readonly typeService: TypeService,
    private readonly promoCodeService: PromoCodeService,
    private readonly userSelfieRepo: UserSelfieRepository,
    private readonly masterRepo: MasterRepository,
    private readonly sharedCommonService: CommonSharedService,
    @Inject(forwardRef(() => MigrationSharedService))
    private readonly sharedMigration: MigrationSharedService,
    @Inject(forwardRef(() => UserServiceV4))
    private readonly userService: UserServiceV4,
    private readonly sharedNotificationService: SharedNotificationService,
    private readonly loanRepo: LoanRepository,
    private readonly userBlockHistoryRepo: BlockUserHistoryRepository,
    @Inject(forwardRef(() => EligibilitySharedService))
    private readonly eligibilitySharedService: EligibilitySharedService,
    private readonly commonSharedService: CommonSharedService,
    private readonly addressRepo: AddressesRepository,
    private readonly mediaRepo: MediaRepository,
    private readonly kycRepo: KYCRepository,
    private readonly installAppRepo: InstallAppRepository,
    private readonly deviceRepo: DeviceRepository,
    private readonly contactLogRepo: ContactLogRepository,
    private readonly changeLogsRepo: ChangeLogsRepository,
    private readonly deviceSIMRepo: DeviceSIMRepository,
    private readonly userPermissionRepo: UserPermissionRepository,
    private readonly mailTrackerRepo: MailTrackerRepository,
    private readonly predictRepo: PredictionRepository,
    private readonly dateService: DateService,
    private readonly userActivityRepo: UserActivityRepository,
    private readonly eSignRepo: ESignRepository,
    private readonly emiRepo: EMIRepository,
    private readonly whatsappService: WhatsAppService,
    // Database
    private readonly repo: RepositoryManager,
    @Inject(forwardRef(() => EmiSharedService))
    private readonly emiSharedService: EmiSharedService,
    private readonly StringService: StringService,
    private readonly redisService: RedisService,
    private readonly errorContextService: ErrorContextService,
    private readonly mongoQueryService: MongoQueryService,
    private readonly insuranceRepo: InsuranceRepository,
    private readonly apiService: APIService,
    private readonly caAssignmentService: CAAssignmentService,
  ) {}

  async getAllSelfieRetakeData(query) {
    try {
      const options = await this.selfieVerificationOptions(query);
      if (options?.message) return options;
      const selfieData = await this.findSelfieData(options);
      if (selfieData?.message) return selfieData;
      const finalData: any = await this.prepareSelfieRowData(selfieData?.rows);
      if (finalData?.message) return finalData;
      return { count: selfieData.count, rows: finalData };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region  pre pare option for selfie verification
  async selfieVerificationOptions(query) {
    try {
      const status = query?.status ?? '0';
      const page = query?.page ?? 1;
      const searchText = query?.searchText;
      const startDate = query?.startDate ?? null;
      const endDate = query?.endDate ?? null;
      const download = query?.download ?? 'false';
      const toDay = this.typeService.getGlobalDate(new Date());
      const where: any = { status: '0' };
      if (status != '0' && startDate && endDate) {
        const range = this.typeService.getUTCDateRange(
          startDate.toString(),
          endDate.toString(),
        );
        where.createdAt = {
          [Op.gte]: range.fromDate,
          [Op.lte]: range.endDate,
        };
      }
      const selfieInclude: any = {
        model: UserSelfieEntity,
        where,
        attributes: [
          'id',
          'image',
          'status',
          'verifiedDate',
          'tempImage',
          'adminId',
          'response',
          'rejectReason',
          'updatedAt',
        ],
      };
      const userOptions: any = {
        where: { isBlacklist: { [Op.ne]: '1' } },
        include: [selfieInclude],
      };

      if (status && status != '4') {
        if (status == '1' || status == '3')
          selfieInclude.where.status = { [Op.or]: ['1', '3'] };
        else selfieInclude.where.status = status;
        if (status == '0') {
          userOptions.where['NextDateForApply'] = {
            [Op.or]: [{ [Op.lte]: toDay.toJSON() }, { [Op.eq]: null }],
          };
        }
      } else selfieInclude.where['status'] = { [Op.or]: ['1', '3', '2', '0'] };

      if (searchText) {
        let encryptedData = '';
        if (!isNaN(searchText)) {
          encryptedData = await this.cryptService.encryptPhone(searchText);
          encryptedData = encryptedData.split('===')[1];
        }
        userOptions.where = {
          ...userOptions.where,
          ...{
            [Op.or]: [
              { fullName: { [Op.iRegexp]: searchText } },
              {
                phone: {
                  [Op.like]: encryptedData ? '%' + encryptedData + '%' : null,
                },
              },
              { email: { [Op.iRegexp]: searchText } },
            ],
          },
        };
      }
      if (status != '0' && download != 'true') {
        userOptions.offset = +(page ?? 1) * PAGE_LIMIT - PAGE_LIMIT;
        userOptions.limit = PAGE_LIMIT;
      }
      return userOptions;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  async findSelfieData(options) {
    try {
      const userAttr = [
        'id',
        'phone',
        'fullName',
        'createdAt',
        'completedLoans',
      ];
      const result = await this.userRepo.getTableWhereDataWithCounts(
        userAttr,
        options,
      );
      if (!result || result === k500Error) return kInternalError;
      result.rows.forEach((element) => {
        element.phone = this.cryptService.decryptPhone(element.phone);
      });
      return result;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async prepareSelfieRowData(list: any[]) {
    try {
      const finalData = [];
      for (let index = 0; index < list.length; index++) {
        const ele = list[index];
        try {
          const tempData: any = {};
          const selfieData = ele?.selfieData;
          const date = selfieData?.verifiedDate ?? selfieData?.updatedAt;
          const lastUpdate = this.typeService.getDateFormatted(date);
          tempData['Mobile number'] = ele?.phone;
          tempData['Name'] = ele?.fullName;
          tempData['Completed loans'] = ele?.completedLoans;
          tempData['Profile image'] = selfieData?.image ?? '-';
          tempData['Profile_tempImg'] = selfieData?.tempImage ?? '-';
          tempData['Last updated'] = lastUpdate;
          tempData['Last action by'] =
            (await this.commonSharedService.getAdminData(selfieData?.adminId))
              ?.fullName ?? 'SYSTEM';
          tempData['Status'] = selfieData?.status ?? '-';
          tempData['Reject reason'] = selfieData?.rejectReason ?? '-';
          tempData['userId'] = ele?.id ?? '-';
          tempData['selfieId'] = selfieData?.id ?? '-';
          finalData.push(tempData);
        } catch (error) {}
      }
      return finalData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async funChanegSelfieStatus(body) {
    try {
      if (!body.status || !body.userId) return kParamsMissing;
      if (body.status == '2' && !body.rejectReason)
        return kParamMissing('rejectReason');
      if (!body.adminId) return kParamMissing('adminId');
      const userId = body.userId;
      const adminId = body.adminId;
      const rejectReason = body.rejectReason;
      const status = body.status;
      const allAdminData = await this.caAssignmentService.getCAData();
      if (allAdminData?.message) return kInternalError;
      const adminData = allAdminData.find((ele) => ele.id == body.adminId);
      if (adminData && !adminData?.verificationAccessStatus?.selfie)
        return k422ErrorMessage(kYouHaveAccess);
      const masterInclude = {
        model: MasterEntity,
        attributes: ['id', 'loanId', 'status', 'dates', 'rejection'],
      };
      const selfieInclude: any = { model: UserSelfieEntity };
      selfieInclude.attributes = [
        'id',
        'image',
        'status',
        'adminId',
        'tempImage',
      ];
      const userAttr = ['id', 'phone', 'fullName', 'fcmToken'];
      const userOptions = {
        where: { id: userId },
        include: [selfieInclude, masterInclude],
      };
      const userData = await this.userRepo.getRowWhereData(
        userAttr,
        userOptions,
      );
      if (userData === k500Error) return kInternalError;
      if (!userData) return k422ErrorMessage('User data not found!');
      const approved = [1, 3, 4];
      const selfieData = userData?.selfieData ?? {};
      const masterData = userData?.masterData;
      const statusData = masterData?.status ?? {};
      const rejection = masterData?.rejection ?? {};
      if ([1, 3].includes(statusData?.eSign))
        return k422ErrorMessage('Selfie status not updated!');
      const aadhaarStatus = statusData?.aadhaar ?? -1;
      const dates = masterData?.dates ?? {};
      const isAadhaarVerified = aadhaarStatus == '1' || aadhaarStatus == '3';
      const isApproved = status == '1' || status == '3';
      const verifiedDate = new Date();
      const rawData: any = {
        status,
        adminId,
        rejectReason: status == '2' ? rejectReason : null,
        verifiedDate: verifiedDate.toJSON(),
      };
      statusData.selfie = +status;
      dates.selfie = verifiedDate.getTime();
      rejection.selfie = rejectReason;
      const id = selfieData?.id;
      const selfieURL = selfieData?.tempImage;
      if (isApproved && selfieURL) {
        rawData.image = selfieURL;
        const updatedData = { selfieId: id, image: selfieURL };
        const userUpdate = await this.userRepo.updateRowData(
          updatedData,
          userId,
        );
        if (userUpdate === k500Error) return kInternalError;
      }

      if (isApproved && !isAadhaarVerified) {
        statusData.selfie = 5;
        rawData.status = '5';
      }

      const updateDataRes = await this.userSelfieRepo.updateRowData(
        rawData,
        id,
      );
      if (updateDataRes === k500Error) return kInternalError;
      await this.redisService.del(`SELFIE_DATA_${userId}`);
      await this.redisService.del(`APP_USER_PROFILE_DATA_${userId}`);

      if (
        approved.includes(statusData?.pan) &&
        approved.includes(statusData?.selfie) &&
        ![1, 3, 2].includes(statusData?.loan) &&
        [1, 3].includes(statusData.bank) &&
        masterData?.loanId
      ) {
        statusData.loan = 0;
        statusData.eligibility = 0;
      }
      await this.masterRepo.updateRowData(
        { status: statusData, dates, rejection },
        masterData?.id,
      );

      // redis delete
      await this.redisService.del(`SELFIE_DATA_${userId}`);
      await this.redisService.del(`${userId}_USER_BASIC_DETAILS`);

      let rejectReasonData: any = {};
      if (rejectReason && status == '2') {
        rejectReasonData =
          await this.commonSharedService.getRejectReasonTemplate(rejectReason);
        if (rejectReasonData?.message) return rejectReasonData;
      }

      /// check final approval when bank is approved
      if (
        (statusData?.bank == 1 || statusData?.bank == 3) &&
        masterData?.loanId &&
        statusData?.selfie == 3 &&
        statusData?.loan != 1 &&
        statusData?.loan != 3
      ) {
        const finalData = { loanId: masterData?.loanId, userId };
        const finalApproval = await this.eligibilitySharedService.finalApproval(
          finalData,
        );
        if (finalApproval.message) return finalApproval;
      }
      await this.userService.routeDetails({ id: userId });
      await this.sendSelfieVerificationNotify(
        userData,
        status,
        rejectReasonData,
        adminId,
      );
      return 'Selfie status updated!';
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async sendSelfieVerificationNotify(
    userData,
    status,
    rejectionReason,
    adminId,
  ) {
    try {
      userData.phone = await this.cryptService.decryptPhone(userData.phone);
      let title;
      let body;
      if (status == 3 || status == 1) {
        title = SSelfieVerification;
        body = SelfieverificationSuccess;
      } else if (status == 2) {
        title = SSelfieNotVerify;
        body = rejectionReason?.content;
      } else return {};

      // Push notification
      await this.sharedNotificationService.sendPushNotification(
        userData.fcmToken,
        title,
        body,
        {},
        true,
        adminId,
      );
      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }

  async funSetNextEligibiltyDate(data) {
    try {
      const userId = data?.userId;
      const adminId = data?.adminId;
      if (!userId || !adminId) return kParamsMissing;
      let nextApplyDate = data?.eligibilityDate;
      nextApplyDate = nextApplyDate
        ? this.typeService.getGlobalDate(nextApplyDate)
        : null;
      const toDay = this.typeService.getGlobalDate(new Date());
      const masterInclude = {
        model: MasterEntity,
        attributes: ['id', 'status', 'loanId', 'coolOffData'],
      };
      const options = { where: { id: userId }, include: [masterInclude] };
      const userData = await this.userRepo.getRowWhereData(
        ['id', 'fullName', 'masterId', 'fcmToken'],
        options,
      );
      if (!userData || userData == k500Error)
        return k422ErrorMessage('User not found!');
      const masterData = userData?.masterData ?? {};
      const statusData = masterData?.status ?? {};
      if (statusData?.disbursement == 0)
        return k422ErrorMessage(kDisbursementInProcess);
      const coolOffData = masterData?.coolOffData ?? {};

      let redisData = await this.redisService.get(
        'CSE_REJECT_LOAN' + masterData?.loanId,
      );
      if (redisData) redisData = JSON.parse(redisData);

      ///need to check user is cool-off or not and cse flow
      if (
        GLOBAL_FLOW.IS_CSE_REJECTION_FLOW &&
        !coolOffData?.coolOffEndsOn &&
        redisData
      ) {
        await this.redisService.redisDeleteByKey(
          'CSE_REJECT_LOAN' + masterData?.loanId,
        );
        return true;
      }

      const masterId = masterData.id;
      const fcmToken = userData?.fcmToken;
      if (nextApplyDate) {
        coolOffData.coolOffStartedOn = toDay.toJSON();
        coolOffData.coolOffEndsOn = nextApplyDate.toJSON();
        coolOffData.count = (coolOffData?.count ?? 0) + 1;
      } else {
        coolOffData.coolOffStartedOn = '';
        coolOffData.coolOffEndsOn = '';
      }
      const update = await this.masterRepo.updateRowData(
        { coolOffData },
        masterId,
      );

      // Entry in history
      await this.userBlockHistoryRepo.createRowData({
        isBlacklist: '0',
        coolOfDate: nextApplyDate?.toJSON(),
        userId,
        blockedBy: adminId,
      });

      await this.userService.routeDetails({ id: userId });

      if (!nextApplyDate && fcmToken) {
        await this.sharedNotificationService.sendPushNotification(
          fcmToken,
          kCoolOffPeriodOverTitle,
          kCoolOffPeriodOverContent,
          {},
          true,
          adminId,
        );
      }

      return update;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async updateBulkEligibility(reqData) {
    const numbers = reqData.numbers ?? [];
    if (numbers.length > 0) {
      for (let index = 0; index < numbers.length; index++) {
        try {
          const number = this.cryptService
            .encryptPhone(numbers[index])
            .split('===')[1];

          const attributes = ['id'];
          const options = { where: { phone: { [Op.like]: '%' + number } } };
          const userData = await this.repo.getRowWhereData(
            registeredUsers,
            attributes,
            options,
          );
          console.log(userData);
          if (!userData || userData === k500Error) continue;

          const body = {
            userId: userData.id,
            eligibilityDate: null,
            adminId: reqData.adminId ?? SYSTEM_ADMIN_ID,
          };
          await this.funSetNextEligibiltyDate(body);
          console.log(userData, index, numbers.length);
        } catch (error) {}
      }
    }

    const userIds = reqData.userIds ?? [];
    if (userIds.length > 0) {
      const splittedSpans = this.typeService.splitToNChunks(userIds, 30);
      for (let index = 0; index < splittedSpans.length; index++) {
        const targetIds = splittedSpans[index];
        const list = [];
        for (let i = 0; i < targetIds.length; i++) {
          const userId = targetIds[i];
          const body = {
            userId,
            eligibilityDate: null,
            adminId: reqData.adminId ?? SYSTEM_ADMIN_ID,
          };
          list.push(this.funSetNextEligibiltyDate(body));
        }
        console.log({ index, total: splittedSpans.length });
        await Promise.all(list);
      }
    }

    return {};
  }

  // Get customer details page
  async getUserProfile(reqData) {
    // Params validation
    const userId = reqData.userId?.trim();
    if (!userId) return kParamMissing('userId');

    const key = `${userId}_USER_PROFILE`;
    const userProfileData = await this.redisService.getKeyDetails(key);
    if (userProfileData) return JSON.parse(userProfileData);

    const masterInclude: any = { model: MasterEntity };
    masterInclude.attributes = ['miscData', 'loanId'];
    const include = [masterInclude];
    const attributes = ['isBlacklist'];
    const options = { useMaster: false, include, where: { id: userId } };
    const userDetails = await this.userRepo.getRowWhereData(
      attributes,
      options,
    );
    if (userDetails == k500Error) return kInternalError;
    if (!userDetails) return k422ErrorMessage(kNoDataFound);
    const loanId = userDetails.masterData?.loanId;

    // get prediction data
    const predictData = await this.predictRepo.getRowWhereData(['reason'], {
      where: { loanId },
      order: [['id', 'DESC']],
    });
    if (predictData == k500Error) return kInternalError;
    const reasonData = predictData
      ? JSON.parse(predictData?.reason) ?? '-'
      : '-';
    const matchLatLongCount = reasonData?.matchLatLongCount ?? '0';
    const exactAadhaarAddressCount = reasonData?.exactMatchAddressCount ?? 0;

    // add and update last location
    const userData = {
      id: userId,
      otherDetails: {},
      docCounts: {},
      matchLatLongCount,
      exactAadhaarAddressCount,
    };
    userData.docCounts = await this.documentSCounts(userId);
    await this.getOtherDetails(userDetails, userData);

    await this.redisService.set(
      key,
      JSON.stringify(userData),
      NUMBERS.SEVEN_DAYS_IN_SECONDS,
    );
    return userData;
  }

  async documentSCounts(userId) {
    const data = {
      otherDoc: 0,
      kycDoc: 0,
      legalDoc: 0,
      collectionDoc: 0,
      totalDoc: 0,
    };
    try {
      const obj = { [Op.or]: [{ [Op.eq]: false }, { [Op.eq]: null }] };
      const mediaOptions = {
        where: {
          userId,
          isDeleted: obj,
        },
      };
      let [otherDoc, homeProof, legalDoc, collectionDoc] = await Promise.all([
        this.mediaRepo.getCountsWhere({
          where: {
            ...mediaOptions.where,
            docType: { [Op.notIn]: kLegalTabUploadDocList },
          },
        }),
        this.mediaRepo.getCountsWhere({
          where: { ...mediaOptions.where, docType: 'addressProof' },
        }),
        this.mediaRepo.getCountsWhere({
          where: {
            ...mediaOptions.where,
            docType: { [Op.in]: kLegalTabUploadDocList },
          },
        }),
        this.mediaRepo.getCountsWhere({
          where: {
            ...mediaOptions.where,
            docType: { [Op.in]: kCollectionTabUploadDocList },
          },
        }),
      ]);
      data.otherDoc = otherDoc === k500Error ? 0 : otherDoc;
      data.legalDoc = legalDoc === k500Error ? 0 : legalDoc;
      data.collectionDoc = collectionDoc === k500Error ? 0 : collectionDoc;
      if (homeProof == 0) {
        let userData = await this.userRepo.getCountsWhere({
          where: { id: userId, homeProofImage: { [Op.ne]: null } },
        });
        if (userData === k500Error) userData = 0;
        otherDoc = +otherDoc + +userData;
      }

      let rawQuery = `SELECT COUNT("id") FROM "loanTransactions"
      WHERE "userId" = '${userId}' AND "nocURL" IS NOT NULL`;

      let [nocDoc, loanDoc, insuranceDoc] = await Promise.all([
        this.repo.injectRawQuery(loanTransaction, rawQuery, {
          source: 'REPLICA',
        }),
        this.eSignRepo.getCountsWhere({
          where: { userId },
        }),
        this.insuranceRepo.getCountsWhere({
          where: { userId },
        }),
      ]);
      if (nocDoc === k500Error) nocDoc = 0;
      else nocDoc = +(nocDoc[0].count ?? '0');

      if (loanDoc === k500Error) loanDoc = 0;
      insuranceDoc = insuranceDoc === k500Error ? 0 : insuranceDoc * 2;
      data.otherDoc = otherDoc + nocDoc + loanDoc + homeProof + insuranceDoc;
      const attributes = ['aadhaarFront', 'aadhaarBack', 'pan'];
      let kycData = await this.kycRepo.getRowWhereData(attributes, {
        where: { userId },
        order: [['id', 'DESC']],
      });
      if (kycData === k500Error) kycData = {};
      let kycDoc = 0;
      if (kycData?.aadhaarFront) kycDoc++;
      if (kycData?.aadhaarBack) kycDoc++;
      if (kycData?.pan) kycDoc++;
      data.kycDoc = kycDoc;
      data.totalDoc =
        data.otherDoc + data.kycDoc + data.legalDoc + data.collectionDoc;
      return data;
    } catch (error) {
      return data;
    }
  }

  // Other details
  async getOtherDetails(userDetails, userData) {
    const masterData = userDetails.masterData ?? {};
    const miscData = masterData.miscData ?? {};
    const legalData = userDetails.legalData;
    let legalStepDate = userDetails?.legalData?.dates?.disposalDate ?? null;
    legalStepDate = this.typeService.getGlobalDate(legalStepDate);
    legalStepDate = this.typeService.dateToJsonStr(legalStepDate, 'DD/MM/YYYY');
    // Last location
    let lastLocation = miscData?.lastLocation ?? '-';
    let lastLocationDateTime = miscData.lastLocationDateTime ?? '-';

    if (lastLocation == '-') {
      const migratedData: any = await this.sharedMigration.migrateLastLocation(
        userData.id,
      );
      if (migratedData.message) return migratedData;
      lastLocation = migratedData.lastLocation;
      lastLocationDateTime = migratedData.lastLocationDateTime;
    }

    const otherDetails: any = {
      lastLocation: '-',
      lastLocationDateTime,
    };
    otherDetails.lastLocation = lastLocation;
    otherDetails.lastLocationDateTime =
      lastLocationDateTime != '-' ? new Date(lastLocationDateTime) : '-';
    // last summons date
    if (legalData?.createdAt) {
      let createdAt = legalData.createdAt;
      let legalCreatedAt: any = this.typeService.getGlobalDate(createdAt);
      let type = legalData.type;
      legalCreatedAt = this.typeService.dateToJsonStr(
        legalCreatedAt,
        'DD/MM/YYYY',
      );
      let legalStep = legalString[type];
      if (legalData.type === 9) {
        legalCreatedAt = legalStepDate;
        otherDetails.legalData = { legalStep, legalCreatedAt };
      } else {
        otherDetails.legalData = { legalStep, legalCreatedAt };
      }
      otherDetails;
    }
    userData.otherDetails = otherDetails;
  }

  // user basic details (OLD version)
  async funGetUserBasicDetails(query) {
    // Validation -> Parameters
    if (!query.userId) return kParamMissing('userId');

    const userId = query.userId;
    const attributes = [
      'id',
      'fullName',
      'phone',
      'email',
      'communicationLanguage',
      'interestRate',
      'isRedProfile',
      'NextDateForApply',
      'createdAt',
      'lastOnlineTime',
      'defaulterCount',
      'isBlacklist',
      'loanStatus',
      'isDeleted',
      'totalContact',
      'phoneStatusVerified',
      'eligibleForPromoCode',
      'otherPhone',
      'otherEmail',
      'addedBy',
      'isCibilConsent',
      'userSelectedGender',
      'lastCrm',
      'recentDeviceId',
      'mostFrequentHour',
      'mostFrequentDay',
      'gmvAmount',
      'uniqueId',
      'pinCrm',
      'allowedPromotionalContent',
    ];
    const selfieInclude: any = { model: UserSelfieEntity };
    selfieInclude.attributes = ['image', 'tempImage', 'status', 'response'];
    const kycInclude: any = { model: KYCEntity };
    kycInclude.attributes = [
      'maskedAadhaar',
      'aadhaarStatus',
      'aadhaarDOB',
      'aadhaarAddress',
      'panCardNumber',
      'panStatus',
      'aadhaarLatLong',
      'profileImage',
      'updatedAt',
    ];

    const emiInclude: any = { model: EmiEntity, required: false };
    emiInclude.attributes = [
      'payment_status',
      'payment_due_status',
      'principalCovered',
      'legalCharge',
      'paid_principal',
    ];
    emiInclude.where = { payment_due_status: '1', payment_status: '0' };
    const include = [kycInclude, selfieInclude, emiInclude];
    let options: any = {
      useMaster: false,
      include,
      where: { id: userId },
    };

    // temporary redis code commented due to PROD issue
    // const key = `${userId}_USER_BASIC_DETAILS`;
    // let userData = await this.redisService.getKeyDetails(key);
    // if (!userData) {
    // Hit -> Query
    const userData = await this.userRepo.getRowWhereData(attributes, options);

    // Validation -> Query data
    if (userData === k500Error) throw new Error();
    if (!userData) return k422ErrorMessage(kNoDataFound);
    // } else userData = JSON.parse(userData);

    //master include for get loanId and inside the include of loantrasaction
    const masterOptions = {
      useMaster: false,
      where: { userId },
      include: {
        model: loanTransaction,
        attributes: ['id', 'followerId', 'loanStatus'],
      },
      order: [['id', 'desc']],
    };

    const masterData = await this.masterRepo.getRowWhereData(
      ['id', 'loanId', 'miscData', 'coolOffData', 'status'],
      masterOptions,
    );
    if (masterData == k500Error) return kInternalError;
    if (!masterData) return k422ErrorMessage(kNoDataFound);

    // Checks -> Email status
    if (masterData) {
      const emailStatus = (masterData.status ?? {}).email;
      if (emailStatus == 1 || emailStatus == 3) userData.isEmailVerified = true;
      else userData.isEmailVerified = false;
    }
    const approvedStatus = ['1', '3'];
    // if (
    //   userData?.isEmailVerified &&
    //   approvedStatus.includes(userData?.kycData?.aadhaarStatus) &&
    //   approvedStatus.includes(userData?.kycData?.panStatus)
    // ) {
    //   await this.redisService.set(
    //     key,
    //     JSON.stringify(userData),
    //     NUMBERS.SEVEN_DAYS_IN_SECONDS,
    //   );
    // }
    //get promo code data (waveofamount,percentage,promocode)
    let reqData: any = { userId };
    let promoCodeData;
    if (
      masterData?.loanData?.loanStatus === 'Active' &&
      userData?.eligibleForPromoCode
    ) {
      reqData = { userId, loanId: masterData?.loanId };
      promoCodeData = await this.promoCodeService.getUserWaiveOffEligibility(
        reqData,
      );
    }

    const discountPercentage = promoCodeData?.emiDetails?.discount ?? '-';

    let waiveOffamount = await this.typeService.amountNumberWithCommas(
      parseInt(promoCodeData?.emiDetails?.discountAmount ?? 0),
    );
    const promoCode = promoCodeData?.emiDetails?.promoCode ?? '-';

    //user's data
    let lastActiveAgoMinutes: any = Infinity;
    if (userData?.lastOnlineTime) {
      const lastOnlineTime = this.typeService.dateTimeToDate(
        userData?.lastOnlineTime,
      );
      lastActiveAgoMinutes = this.typeService.dateDifference(
        lastOnlineTime,
        new Date(),
        'Minutes',
      );
      userData.lastOnlineTime =
        this.typeService.convertMinutesToHours(lastOnlineTime);
      userData.isOnline = lastActiveAgoMinutes < 5;
    }

    userData.phone = this.cryptService.decryptPhone(userData.phone);
    userData.lastCrm = {
      remark: userData?.lastCrm?.remark ?? '-',
      titleName: userData?.lastCrm?.titleName ?? '-',
      statusName: userData?.lastCrm?.statusName ?? '-',
      adminName: userData?.lastCrm?.adminName ?? '-',
      createdAt: userData?.lastCrm?.createdAt ?? '-',
    };

    userData.followerName =
      (
        await this.commonSharedService.getAdminData(
          masterData?.loanData?.followerId,
        )
      ).fullName ?? '-';

    userData.lastLocation = masterData?.miscData?.lastLocation ?? '-';
    delete userData?.masterData;

    userData.promoCodeData = {
      discountPercentage,
      waiveOffamount,
      promoCode,
    };

    // selfie data
    if (userData.selfieData) {
      let similarity = 0;
      let confidence = 0;
      let response = userData?.selfieData?.response;
      if (response) {
        response = JSON.parse(response);
        similarity = response?.FaceMatches[0]?.Similarity ?? 0;
        confidence = response?.SourceImageFace?.Confidence ?? 0;
        if (response?.UnmatchedFaces?.length > 0 && similarity <= 50)
          userData.selfieData.isImageMessage = 'Face not matched';
        else if (similarity <= 50)
          userData.selfieData.isImageMessage = 'Face not matched';
      } else userData.selfieData.isImageMessage = 'Face not Detected';
      userData.selfieData.Similarity = Math.round(similarity);
      userData.selfieData.Confidence = Math.round(confidence);
      userData.profileUpdatedBy =
        (
          await this.commonSharedService.getAdminData(
            userData?.selfieData?.adminId,
          )
        ).fullName ?? '-';
    }

    // User's Communication Language
    const language = { 1: 'ENGLISH', 2: 'HINDI' };
    userData.communicationLanguage =
      language[userData?.communicationLanguage] ?? '-';

    // User cool off data
    let coolOffEndDate = masterData?.coolOffData?.coolOffEndsOn ?? '';
    let coolOffStartDate = masterData?.coolOffData?.coolOffStartedOn ?? '';
    coolOffEndDate = this.typeService.getGlobalDate(coolOffEndDate) ?? '';
    coolOffStartDate = this.typeService.getGlobalDate(coolOffEndDate) ?? '';
    const totalDays =
      this.typeService.dateDifference(coolOffEndDate, coolOffStartDate) ?? '-';
    userData.coolOffDays = totalDays;

    //kyc data
    if (userData.kycData) {
      if (userData?.kycData?.aadhaarAddress) {
        userData.kycData.aadhaarAddress =
          this.typeService.getAadhaarAddress(userData?.kycData)?.address ?? '-';
      }
      userData.kycData.aadhaarImage = userData?.kycData?.profileImage ?? '-';
      delete userData.kycData.profileImage;
      if (userData?.kycData?.aadhaarDOB) {
        userData.kycData.aadhaarDOB =
          await this.typeService.getDateAsPerAadhaarDOB(
            userData?.kycData?.aadhaarDOB,
          );
        const dateFormat = this.typeService.getDateFormated(
          userData?.kycData?.aadhaarDOB,
        );
        const modifiedDateFormat = `${dateFormat.slice(0, 6)}${dateFormat.slice(
          -2,
        )}`;
        const dateOfBirth = this.typeService.getAgeFromAadhar(
          userData?.kycData?.aadhaarDOB ?? '-',
        );

        userData.kycData.dateOfBirth = `${modifiedDateFormat} (${dateOfBirth} Years)`;
        delete userData.kycData.aadhaarDOB;
      } else userData.kycData.dateOfBirth = '-';
    }

    // if old defulter then check red profile tag
    if ((userData.isRedProfile ?? 0) != 0) {
      switch (userData.isRedProfile) {
        case 1:
          userData.redProfileStage = 'Invited';
          break;
        case 2:
          userData.redProfileStage = 'New loan in process';
          break;
        case 3:
          userData.redProfileStage = 'New mandate registered';
          break;
        default:
          break;
      }
      userData.isRedProfile = true;
    } else userData.isRedProfile = false;

    let principalEMI = 0;
    let legalFees = 0;
    let paidPrincipal = 0;
    userData?.emiData?.map((ele) => {
      principalEMI += ele?.principalCovered ?? 0;
      legalFees += ele?.legalCharge ?? 0;
      paidPrincipal += ele?.paid_principal ?? 0;
    });
    const defaultedAmount = principalEMI - paidPrincipal;
    if (defaultedAmount) {
      userData.principalEMI = defaultedAmount;
    }
    if (legalFees) {
      userData.legalFees = legalFees;
    }
    // Pin crm description
    if (userData.pinCrm) {
      userData.pinDescription = userData.pinCrm?.remark;
      userData.pinAdminName = userData.pinCrm?.adminName;
      userData.pinCreatedAt = userData.pinCrm?.createdAt;
    }
    delete userData.pinCrm;

    return userData;
  }

  // user basic details (NEW version) (Query break and improve version)
  async funGetUserBasicDetailsNew(query) {
    // Validation -> Parameters
    if (!query.userId) return kParamMissing('userId');
    const userId = query.userId;

    // Get Registered Users Data
    const registeredUserAttributes = [
      'id',
      'fullName',
      'phone',
      'email',
      'communicationLanguage',
      'interestRate',
      'isRedProfile',
      'NextDateForApply',
      'createdAt',
      'lastOnlineTime',
      'isBlacklist',
      'loanStatus',
      'isDeleted',
      'totalContact',
      'phoneStatusVerified',
      'eligibleForPromoCode',
      'otherPhone',
      'otherEmail',
      'addedBy',
      'isCibilConsent',
      'userSelectedGender',
      'lastCrm',
      'recentDeviceId',
      'mostFrequentHour',
      'mostFrequentDay',
      'gmvAmount',
      'uniqueId',
      'pinCrm',
      'allowedPromotionalContent',
      'kycId',
      'selfieId',
      'totalCredit',
      'remainingCredit',
      'creditData',
    ];
    let registeredUserOptions: any = {
      useMaster: false,
      where: { id: userId },
      order: [['id', 'DESC']],
    };
    const registeredUserData = await this.userRepo.getRowWhereData(
      registeredUserAttributes,
      registeredUserOptions,
    );
    // Validation -> Query data
    if (registeredUserData === k500Error) throw new Error();
    if (!registeredUserData) return k422ErrorMessage(kNoDataFound);
    const { kycId, selfieId, ...restUserData } = registeredUserData;

    // Get KYC Data of Registered User
    const kycAttributes = [
      'maskedAadhaar',
      'aadhaarStatus',
      'aadhaarDOB',
      'aadhaarAddress',
      'panCardNumber',
      'panStatus',
      'aadhaarLatLong',
      'profileImage',
      'updatedAt',
    ];
    let kycOptions: any = { where: { id: kycId } };
    let kycData = await this.kycRepo.getRowWhereData(kycAttributes, kycOptions);
    // Validation -> Query data
    if (kycData === k500Error) throw new Error();
    if (!kycData) kycData = null;

    // Get User Selfie Data
    let selfieData = await this.commonSharedService.getSelfieData({
      userId,
      callFnFrom: '2',
    });
    // Validation -> Query data
    if (selfieData === k500Error) throw new Error();
    if (!selfieData || !selfieData?.id) selfieData = null;

    // Get EMI Data
    const emiAttributes = [
      'payment_status',
      'payment_due_status',
      'principalCovered',
      'legalCharge',
      'paid_principal',
    ];
    let emiOptions = {
      where: { userId, payment_due_status: '1', payment_status: '0' },
    };
    let emiData = await this.emiRepo.getTableWhereData(
      emiAttributes,
      emiOptions,
    );
    // Validation -> Query data
    if (emiData === k500Error) throw new Error();
    if (!emiData) emiData = [];

    let userData = {
      ...restUserData,
      kycData,
      selfieData,
      emiData,
    };

    //master include for get loanId and inside the include of loantrasaction
    const masterOptions = {
      useMaster: false,
      where: { userId },
      include: {
        model: loanTransaction,
        attributes: ['id', 'followerId', 'loanStatus'],
      },
      order: [['id', 'desc']],
    };

    const masterData = await this.masterRepo.getRowWhereData(
      ['id', 'loanId', 'miscData', 'coolOffData', 'status'],
      masterOptions,
    );
    if (masterData == k500Error) return kInternalError;
    if (!masterData) return k422ErrorMessage(kNoDataFound);

    // Checks -> Email status
    if (masterData) {
      const emailStatus = (masterData.status ?? {}).email;
      if (emailStatus == 1 || emailStatus == 3) userData.isEmailVerified = true;
      else userData.isEmailVerified = false;
    }

    // is whatsapp check
    const whatsApp = await this.repo.getRowWhereData(
      HashPhoneEntity,
      ['isWhatsApp'],
      { where: { userId } },
    );

    if (whatsApp == k500Error) return kInternalError;
    userData.isWhatsApp = whatsApp?.isWhatsApp;
    //get promo code data (waveofamount,percentage,promocode)
    let reqData: any = { userId };
    let promoCodeData;
    if (
      masterData?.loanData?.loanStatus === 'Active' &&
      userData?.eligibleForPromoCode
    ) {
      reqData = { userId, loanId: masterData?.loanId };
      promoCodeData = await this.promoCodeService.getUserWaiveOffEligibility(
        reqData,
      );
      if (promoCodeData == k500Error) return kInternalError;
      if (!promoCodeData) return k422ErrorMessage(kNoDataFound);
    }
    const discountPercentage = promoCodeData?.emiDetails?.discount ?? '-';
    let waiveOffamount = await this.typeService.amountNumberWithCommas(
      parseInt(promoCodeData?.emiDetails?.discountAmount ?? 0),
    );
    const promoCode = promoCodeData?.emiDetails?.promoCode ?? '-';

    //user's data
    let lastActiveAgoMinutes: any = Infinity;
    if (userData?.lastOnlineTime) {
      const lastOnlineTime = this.typeService.dateTimeToDate(
        userData?.lastOnlineTime,
      );
      lastActiveAgoMinutes = this.typeService.dateDifference(
        lastOnlineTime,
        new Date(),
        'Minutes',
      );
      userData.lastOnlineTime =
        this.typeService.convertMinutesToHours(lastOnlineTime);
      userData.isOnline = lastActiveAgoMinutes < 5;
    }

    userData.phone = this.cryptService.decryptPhone(userData.phone);
    userData.lastCrm = {
      remark: userData?.lastCrm?.remark ?? '-',
      titleName: userData?.lastCrm?.titleName ?? '-',
      statusName: userData?.lastCrm?.statusName ?? '-',
      adminName: userData?.lastCrm?.adminName ?? '-',
      createdAt: userData?.lastCrm?.createdAt ?? '-',
    };
    userData.followerName =
      (
        await this.commonSharedService.getAdminData(
          masterData?.loanData?.followerId,
        )
      ).fullName ?? '-';

    userData.lastLocation = masterData?.miscData?.lastLocation ?? '-';
    delete userData?.masterData;

    userData.promoCodeData = {
      discountPercentage,
      waiveOffamount,
      promoCode,
    };
    // selfie data
    if (userData.selfieData) {
      let similarity = 0;
      let confidence = 0;
      let response = userData?.selfieData?.response;
      if (response) {
        response = JSON.parse(response);
        similarity = response?.FaceMatches[0]?.Similarity ?? 0;
        confidence = response?.SourceImageFace?.Confidence ?? 0;
        if (response?.UnmatchedFaces?.length > 0 && similarity <= 50)
          userData.selfieData.isImageMessage = 'Face not matched';
        else if (similarity <= 50)
          userData.selfieData.isImageMessage = 'Face not matched';
      } else userData.selfieData.isImageMessage = 'Face not Detected';
      userData.selfieData.Similarity = Math.round(similarity);
      userData.selfieData.Confidence = Math.round(confidence);
      userData.profileUpdatedBy =
        (
          await this.commonSharedService.getAdminData(
            userData?.selfieData?.adminId,
          )
        ).fullName ?? '-';
    }
    // User's Communication Language
    const language = { 1: 'ENGLISH', 2: 'HINDI' };
    userData.communicationLanguage =
      language[userData?.communicationLanguage] ?? '-';

    // User cool off data
    let coolOffEndDate = masterData?.coolOffData?.coolOffEndsOn ?? '';
    let coolOffStartDate = masterData?.coolOffData?.coolOffStartedOn ?? '';
    coolOffEndDate = this.typeService.getGlobalDate(coolOffEndDate) ?? '';
    coolOffStartDate = this.typeService.getGlobalDate(coolOffEndDate) ?? '';
    const totalDays =
      this.typeService.dateDifference(coolOffEndDate, coolOffStartDate) ?? '-';
    userData.coolOffDays = totalDays;

    ///check need to collect legal details or case is filled or not
    const legalData = await this.funGetMediaDetails(userId);
    if (legalData?.id && legalData?.docType != 'Disposal Copy')
      userData['Cases Against'] = true;

    //kyc data
    if (userData.kycData) {
      if (userData?.kycData?.aadhaarAddress) {
        userData.kycData.aadhaarAddress =
          this.typeService.getAadhaarAddress(userData?.kycData)?.address ?? '-';
      }
      userData.kycData.aadhaarImage = userData?.kycData?.profileImage ?? '-';
      delete userData.kycData.profileImage;
      if (userData?.kycData?.aadhaarDOB) {
        userData.kycData.aadhaarDOB =
          await this.typeService.getDateAsPerAadhaarDOB(
            userData?.kycData?.aadhaarDOB,
          );
        const dateFormat = this.typeService.getDateFormated(
          userData?.kycData?.aadhaarDOB,
        );
        const modifiedDateFormat = `${dateFormat.slice(0, 6)}${dateFormat.slice(
          -2,
        )}`;
        const dateOfBirth = this.typeService.getAgeFromAadhar(
          userData?.kycData?.aadhaarDOB ?? '-',
        );

        userData.kycData.dateOfBirth = `${modifiedDateFormat} (${dateOfBirth} Years)`;
        delete userData.kycData.aadhaarDOB;
      } else userData.kycData.dateOfBirth = '-';
    }

    // if old defulter then check red profile tag
    if ((userData.isRedProfile ?? 0) != 0) {
      switch (userData.isRedProfile) {
        case 1:
          userData.redProfileStage = 'Invited';
          break;
        case 2:
          userData.redProfileStage = 'New loan in process';
          break;
        case 3:
          userData.redProfileStage = 'New mandate registered';
          break;
        default:
          break;
      }
      userData.isRedProfile = true;
    } else userData.isRedProfile = false;

    let principalEMI = 0;
    let legalFees = 0;
    let paidPrincipal = 0;

    userData?.emiData?.map((ele) => {
      principalEMI += ele?.principalCovered ?? 0;
      legalFees += ele?.legalCharge ?? 0;
      paidPrincipal += ele?.paid_principal ?? 0;
    });

    const defaultedAmount = principalEMI - paidPrincipal;
    if (defaultedAmount) {
      userData.principalEMI = defaultedAmount;
    }
    if (legalFees) {
      userData.legalFees = legalFees;
    }
    // Pin crm description
    if (userData.pinCrm) {
      userData.pinDescription = userData.pinCrm?.remark;
      userData.pinAdminName = userData.pinCrm?.adminName;
      userData.pinCreatedAt = userData.pinCrm?.createdAt;
      userData.pinAdminRole = userData.pinCrm?.adminRole;
    }
    delete userData.pinCrm;
    userData.otherPhone = userData?.otherPhone?.map((el) =>
      this.cryptService.decryptPhone(el),
    );

    // Credit Info
    userData.creditInfo = {
      totalCredits: userData?.totalCredit ?? 0,
      usedCredits:
        userData?.totalCredit ?? 0 > 0
          ? (userData?.totalCredit ?? 0) -
            ((userData?.remainingCredit ?? 0) +
              (userData?.creditData?.amount ?? 0))
          : 0,
      bankTransferCredits: userData?.creditData?.amount ?? 0,
      remainingCredits: userData?.remainingCredit ?? 0,
    };
    delete userData.totalCredit;
    delete userData.remainingCredit;
    return userData;
  }

  //#region
  private async funGetMediaDetails(userId) {
    const attributes = ['id', 'docType'];
    const options = {
      where: {
        userId,
        docType: [
          'Petition',
          'Complaint Copy',
          'Cyber Complaints',
          'Summons',
          'Warrants',
          'Disposal Copy',
        ],
      },
      order: [['id', 'DESC']],
    };
    return await this.mediaRepo.getRowWhereData(attributes, options);
  }
  //#endregion

  async getAllAddressData(query) {
    try {
      const userId = query?.userId;
      if (!userId) return kParamsMissing;
      const attributes = [
        'address',
        'lat',
        'long',
        'type',
        'probability',
        'subType',
        'status',
        'addressFlag',
        'updatedAt',
      ];
      const data = await this.addressRepo.getTableWhereData(attributes, {
        where: { userId },
      });
      if (data === k500Error) return kInternalError;
      // sorting and arrange by type
      const userAdd = [];
      const bankAdd = [];
      const typeAdd = [];
      const otherAdd = [];
      const cibilAdd = [];
      const addressFlag = [1, 2, 3];
      for (let index = 0; index < data.length; index++) {
        try {
          const ele = data[index];
          const type = ele?.type;
          const flag = ele?.addressFlag;
          ele.updatedAt = this.typeService.getDateFormatted(ele.updatedAt);
          if (type == '11') bankAdd.push(ele);
          else if (type == '12') typeAdd.push(ele);
          else if (type == '13') {
            if (addressFlag.includes(flag)) userAdd.push(ele);
            else cibilAdd.push(ele);
          } else otherAdd.push(ele);
        } catch (error) {}
      }
      const finalAdd = [
        ...userAdd,
        ...bankAdd,
        ...typeAdd,
        ...otherAdd,
        ...cibilAdd,
      ];
      return finalAdd;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // # start region get user block and cool off details
  async getUserBlockDetails(query) {
    try {
      // Params validation
      const userId = query?.userId;
      if (!userId) return kParamMissing('userId');

      const todayDate = this.typeService.getGlobalDate(new Date());
      let responseObject: any = {
        isBlacklist: '0',
        isCoolOff: '0',
      };
      const masterInclude = {
        model: MasterEntity,
        attributes: ['coolOffData', 'status', 'loanId'],
      };

      const userAtr = ['isBlacklist'];
      const userOptions = {
        useMaster: false,
        where: { id: userId },
        include: [masterInclude],
      };
      const userData = await this.userRepo.getRowWhereData(
        userAtr,
        userOptions,
      );
      if (userData === k500Error) return kInternalError;

      //get block details
      const reasonInclude = {
        model: ReasonsEntity,
        attributes: ['reason'],
      };
      const blockAtr = ['reason', 'isBlacklist', 'blockedBy', 'reasonId'];
      const blockOptions = {
        useMaster: false,
        where: { userId },
        order: [['id', 'desc']],
        include: [reasonInclude],
      };
      const blockData = await this.userBlockHistoryRepo.getRowWhereData(
        blockAtr,
        blockOptions,
      );
      if (blockData === k500Error) return kInternalError;

      // // Get admin details
      const adminData = await this.commonSharedService.getAdminData(
        blockData?.blockedBy,
      );

      const loanStatus = userData?.masterData?.status?.loan;
      const disbursedStatus = userData?.masterData?.status?.disbursement;

      let redisData = await this.redisService.get(
        'CSE_REJECT_LOAN' + userData?.masterData?.loanId,
      );
      if (redisData) redisData = JSON.parse(redisData);
      const options: any = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      };

      if (
        GLOBAL_FLOW.IS_CSE_REJECTION_FLOW &&
        [1, 3].includes(+loanStatus) &&
        disbursedStatus < 0 &&
        redisData
      ) {
        const adminName =
          (await this.commonSharedService.getAdminData(redisData?.adminId))
            ?.fullName ?? null;

        if (redisData?.type == 1) {
          return {
            isBlacklist: '1',
            isCoolOff: '0',
            adminName,
            reason: redisData?.reason ?? redisData?.remark,
            blockedBy: blockData?.blockedBy,
            userId,
            reasonId: null,
            title:
              'User is blocked | Blocked by: ' +
                adminName +
                '| Reason:' +
                redisData?.reason || redisData?.remark,
          };
        }
        return {
          isBlacklist: '0',
          isCoolOff: '1',
          adminName,
          coolOffEndsOn: redisData?.nextApplyDate
            ? this.typeService
                .getGlobalDate(new Date(redisData?.nextApplyDate))
                .toLocaleDateString('en-IN', options)
            : redisData?.nextApplyDate,
          coolOffStartedOn: null,
          coolOffCount: userData?.masterData?.coolOffData?.count,
          reason: redisData?.reason ?? redisData?.remark,
          nextEligibleDate: redisData?.nextApplyDate
            ? this.typeService
                .getGlobalDate(new Date(redisData?.nextApplyDate))
                .toLocaleDateString('en-IN', options)
            : redisData?.nextApplyDate,
        };
      }

      if (adminData) responseObject.adminName = adminData?.fullName ?? null;
      let coolOffEndsOn = userData?.masterData?.coolOffData?.coolOffEndsOn;
      coolOffEndsOn = coolOffEndsOn
        ? this.typeService.getGlobalDate(coolOffEndsOn)
        : coolOffEndsOn;

      // if user is blackList
      if (userData?.isBlacklist === '1') {
        responseObject.isBlacklist = userData?.isBlacklist;
        responseObject.reason = blockData?.reasonData?.reason;
        responseObject.blockedBy = blockData?.blockedBy;
        responseObject.userId = userId;
        responseObject.reasonId = blockData?.reasonId;
        let title = `User is blocked | Blocked by: ${responseObject?.adminName}`;
        if (responseObject?.reason) {
          title += `| Reason: ${responseObject.reason}`;
        }
        responseObject.title = title;
      } else if (coolOffEndsOn && coolOffEndsOn > todayDate) {
        //if user is not blocked but cooloff
        responseObject.isBlacklist = userData?.isBlacklist;
        responseObject.coolOffEndsOn = coolOffEndsOn;
        responseObject.coolOffStartedOn =
          userData?.masterData?.coolOffData?.coolOffStartedOn;
        responseObject.coolOffCount = userData?.masterData?.coolOffData?.count;
        responseObject.isCoolOff = '1';
        responseObject.reason = blockData?.reasonData?.reason
          ? blockData.reasonData.reason
          : blockData?.reason ?? userData?.masterData?.coolOffData?.reason;
        const date = new Date(coolOffEndsOn);
        let nextEligibleDate = date.toLocaleDateString('en-IN', options);
        nextEligibleDate = nextEligibleDate.replace(',', '');
        responseObject.nextEligibleDate = nextEligibleDate;
      } else if (
        responseObject.isBlacklist === '0' &&
        responseObject.isCoolOff === '0' &&
        responseObject.adminName === 'System'
      ) {
        delete responseObject.adminName;
      }
      return responseObject;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  async addMissingAppsFlyerIds() {
    try {
      const loanIds = [];
      const userInclude: any = { model: registeredUsers };
      userInclude.attributes = ['appsFlyerId', 'recentDeviceId'];
      const include = [userInclude];
      const targetList = await this.loanRepo.getTableWhereData(
        ['id', 'userId'],
        {
          include,
          where: { id: loanIds },
        },
      );
      if (targetList == k500Error) return kInternalError;

      const finalizedList = [];
      for (let index = 0; index < targetList.length; index++) {
        try {
          const loanData = targetList[index];
          const userId = loanData.userId;
          const userData = loanData.registeredUsers ?? {};
          // if (!userData.appsFlyerId) {
          //   const appsFlyerId = await this.googleService.appsFlyerDetails({
          //     deviceId: userData.recentDeviceId,
          //   });
          //   if (appsFlyerId.message) continue;
          //   userData.appsFlyerId = appsFlyerId;
          //   await this.userRepo.updateRowData({ appsFlyerId }, userId);
          // }
          finalizedList.push({
            loanId: loanData.id,
            appsFlyerId: userData.appsFlyerId,
          });
        } catch (error) {}
      }
      const rawExcelData = {
        sheets: ['Ap'],
        data: [finalizedList],
        sheetName: 'data.xlsx',
        needFindTuneKey: false,
      };
      const excelResponse: any = await this.fileService.objectToExcel(
        rawExcelData,
      );
      if (excelResponse?.message) return excelResponse;
    } catch (error) {}
  }

  async getUserInstalledApps(reqData) {
    try {
      let { userId } = reqData;
      if (!userId) return kParamMissing('userId');

      // retriving the PK of recent deviceId
      const registerUserAttributes = ['recentDeviceId'];
      const registerUserOptions = {
        where: {
          id: userId,
        },
      };

      const registerUserResult = await this.userRepo.getRowWhereData(
        registerUserAttributes,
        registerUserOptions,
      );

      if (!registerUserResult) return k422ErrorMessage(kNoDataFound);

      if (registerUserResult === k500Error) return registerUserResult;

      const { recentDeviceId } = registerUserResult;

      if (!recentDeviceId) return k422ErrorMessage('recentDeviceId not found');

      const attributes = ['id'];
      const options = {
        where: {
          deviceId: recentDeviceId,
          userId,
        },
      };

      const deviceResult = await this.deviceRepo.getRowWhereData(
        attributes,
        options,
      );

      if (!deviceResult) return k422ErrorMessage(kNoDataFound);

      if (deviceResult === k500Error) return deviceResult;

      const { id: deviceIdPK } = deviceResult;

      const installAppAttributes = [
        'appName',
        'packageName',
        'category',
        'status',
      ];
      const installAppOptions = {
        where: {
          deviceId: deviceIdPK,
        },
      };

      const installAppResult = await this.installAppRepo.getTableWhereData(
        installAppAttributes,
        installAppOptions,
      );

      if (installAppResult === k500Error) return installAppResult;

      // preparing data

      return {
        length: installAppResult.length,
        apps: installAppResult,
      };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region get call log
  async getCallLog(query) {
    try {
      const userId = query?.userId;
      if (!userId) return kParamMissing('userId');
      const options = { where: { userId } };
      const att = ['phone', 'name', 'timeStamps', 'duration'];
      const result = await this.contactLogRepo.getTableWhereData(att, options);
      if (result === k500Error) return kInternalError;
      let finalList = [];
      result.forEach((ele) => {
        try {
          ele.phone = this.cryptService.decryptPhone(ele.phone);
          const timeStamps = ele?.timeStamps ?? [];
          const durationData = ele?.duration ?? {};
          const temp = {
            phone: ele.phone,
            name: ele.name == 'null' ? 'Unknown' : ele.name,
          };
          timeStamps.forEach((time) => {
            try {
              const date = new Date(time).toJSON();
              const duration = durationData[time] ?? 0;
              finalList.push({ ...temp, date, duration });
            } catch (er) {}
          });
        } catch (error) {}
      });
      finalList = finalList.sort(
        (b, a) => new Date(a?.date).getTime() - new Date(b?.date).getTime(),
      );
      return finalList;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  // ADD and DELETE other phone and email
  async updateOtherPhoneEmail(body) {
    try {
      const userId = body?.userId;
      const adminId = body?.adminId;
      const request = body?.request;
      const phone = body?.phone?.toString();
      const email = body?.email;
      if (!userId) return kParamMissing('userId');
      if (!adminId) return kParamMissing('adminId');
      if (!request) return kParamMissing('request');
      if (!phone && !email) return kParamMissing('phone or email');
      const requestType = ['ADD', 'DELETE'];
      if (!requestType.includes(request)) return kInvalidParamValue('request');
      const attributes = [
        'id',
        'otherPhone',
        'otherEmail',
        'allPhone',
        'allEmail',
        'addedBy',
      ];
      const options = { where: { id: userId } };
      const userData = await this.userRepo.getRowWhereData(attributes, options);
      if (userData === k500Error) return kInternalError;
      let otherPhone = userData?.otherPhone ?? [];
      const otherEmail = userData?.otherEmail ?? [];
      let allPhone = userData?.allPhone ?? [];
      const allEmail = userData?.allEmail ?? [];
      const addedBy = userData?.addedBy ?? {};
      const adminName =
        (await this.commonSharedService.getAdminData(adminId)).fullName ?? '-';
      allPhone = allPhone.map((el) => this.cryptService.decryptPhone(el));
      otherPhone = otherPhone.map((el) => this.cryptService.decryptPhone(el));
      if (phone) {
        if (request == 'ADD') {
          if (!allPhone.includes(phone)) {
            allPhone.push(phone);
            otherPhone.push(phone);
            addedBy[phone] = adminName;
            userData.allPhone = allPhone;
            userData.otherPhone = otherPhone;
          } else return k409ErrorMessage();
        } else if (request == 'DELETE' && allPhone.includes(phone)) {
          const index = allPhone.indexOf(phone);
          if (index > -1) allPhone.splice(index, 1);
          const ind = otherPhone.indexOf(phone);
          if (ind > -1) otherPhone.splice(ind, 1);
          userData.allPhone = allPhone;
          userData.otherPhone = otherPhone;
        } else return kWrongDetails;
      } else if (email) {
        if (request == 'ADD') {
          if (!allEmail.includes(email)) {
            allEmail.push(email);
            otherEmail.push(email);
            addedBy[email] = adminName;
          } else return k409ErrorMessage();
        } else if (request == 'DELETE' && allEmail.includes(email)) {
          const index = allEmail.indexOf(email);
          if (index > -1) allEmail.splice(index, 1);
          const ind = otherEmail.indexOf(email);
          if (ind > -1) otherEmail.splice(ind, 1);
        } else return kWrongDetails;
      } else return kWrongDetails;
      allPhone = allPhone.map((el) => this.cryptService.encryptPhone(el));
      otherPhone = otherPhone.map((el) => this.cryptService.encryptPhone(el));
      const updateUser = {
        otherPhone,
        otherEmail,
        allPhone,
        allEmail,
        addedBy,
      };
      await this.userRepo.updateRowData(updateUser, userId);
      const data = {
        userId,
        type: 'User',
        subType: `${request} ${phone ?? email} user other ${
          phone ? 'PHONE' : 'EMAIL'
        }`,
        oldData: request == 'DELETE' ? phone ?? email : '',
        newData: request == 'ADD' ? phone ?? email : '',
        adminId,
        ip: body.ip,
      };
      await this.changeLogsRepo.create(data);
      return userData;
    } catch (error) {}
  }

  // remove coollOff and blackList
  async bulkUpdateCoolOffBlock(body) {
    try {
      const userId = body?.userId;
      const phone = body?.phone;
      const adminId = body?.adminId;
      const reason = body?.reason;
      const coolOffDays = +(body?.coolOffDays ?? 30);
      const addCoolOff = body?.addCoolOff;
      const blockUser = body?.blockUser;
      if (!userId && !phone) return kParamMissing('userId or phone');
      if (!adminId) return kParamMissing('adminId');
      if (!reason) return kParamMissing('reason');
      let userWhere: any = { id: userId };
      if (phone) {
        if (Array.isArray(phone)) {
          const encPhone: any = [];
          for (let i = 0; i < phone.length; i++) {
            try {
              const ph = phone[i];
              if (!isNaN(ph)) {
                let searchText = this.cryptService.encryptPhone(ph);
                searchText = searchText.split('===')[1];
                encPhone.push({ [Op.iRegexp]: searchText });
              }
            } catch (error) {}
          }
          userWhere = { phone: { [Op.or]: encPhone } };
        } else {
          let searchText = this.cryptService.encryptPhone(phone);
          searchText = searchText.split('===')[1];
          userWhere = { phone: { [Op.iRegexp]: searchText } };
        }
      }
      const masterInclude = {
        model: MasterEntity,
        attributes: ['id', 'coolOffData', 'loanId'],
      };
      const checkUser = await this.userRepo.getTableWhereData(
        [
          'id',
          'isBlacklist',
          'fullName',
          'phone',
          'email',
          'fcmToken',
          'appType',
          'hashPhone',
        ],
        { where: userWhere, include: [masterInclude] },
      );
      if (checkUser === k500Error) return kInternalError;
      const length = checkUser.length;
      if (length == 0) return {};

      const hashPhones = checkUser.map((item) => item?.hashPhone);
      const nonWhatsAppHashPhone =
        await this.whatsappService.getNonWhatsAppUsers(hashPhones);

      const cDate = new Date();
      if (addCoolOff && coolOffDays)
        cDate.setDate(cDate.getDate() + coolOffDays);
      const nextApplyDate = this.typeService.getGlobalDate(cDate);
      const today = this.typeService.getGlobalDate(new Date());
      const finalData = [];
      for (let index = 0; index < length; index++) {
        try {
          const ele = checkUser[index];
          const id = ele.id;
          const name = ele?.fullName;
          const email = ele?.email;
          const fcmToken = ele?.fcmToken;
          const appType = ele?.appType;
          const phone = this.cryptService.decryptPhone(ele?.phone);
          const masterData = ele?.masterData;
          const loanId = ele?.loanId;
          const coolOffData = masterData?.coolOffData ?? {};
          coolOffData.coolOffStartedOn = '';
          coolOffData.coolOffEndsOn = '';
          let updatedData: any = { isBlacklist: '0', NextDateForApply: null };
          if (blockUser) updatedData = { isBlacklist: '1' };
          else if (addCoolOff && coolOffDays) {
            updatedData = { NextDateForApply: nextApplyDate };
            coolOffData.coolOffStartedOn = today.toJSON();
            coolOffData.coolOffEndsOn = nextApplyDate.toJSON();
            coolOffData.count = (coolOffData?.count ?? 0) + 1;
            coolOffData.reason = reason;
          }
          const updateUser = await this.userRepo.updateRowData(updatedData, id);
          if (updateUser === k500Error) continue;

          if (masterData?.id) {
            const updateMaster = await this.masterRepo.updateRowData(
              { coolOffData },
              masterData?.id,
            );
            if (updateMaster === k500Error) continue;
          }
          await this.userService.routeDetails({ id });
          const obj = { userId: id, name, phone };
          finalData.push(obj);
          const historyData: any = {
            userId: id,
            reason,
            isBlacklist: blockUser ? '1' : '0',
            blockedBy: adminId,
          };
          if (addCoolOff && coolOffDays)
            historyData.coolOfDate = nextApplyDate?.toJSON();

          await this.userBlockHistoryRepo.createRowData(historyData);
          if (blockUser || addCoolOff) continue;
          // Send WhatsApp
          if (!nonWhatsAppHashPhone?.includes(ele?.hashPhone)) {
            const preparedData = {
              loanId: loanId,
              userId: id,
              customerName: name ?? 'Dear User',
              email: email ?? '-',
              number: phone,
              title: kCoolOffPeriodOverTitle,
              requestData: kCoolOffPeriodOverTitle,
              appType,
            };
            this.whatsappService.sendWhatsAppMessageMicroService(preparedData);
          }

          // Send Notification
          if (fcmToken) {
            this.sharedNotificationService.sendPushNotification(
              fcmToken,
              kCoolOffPeriodOverTitle,
              kCoolOffPeriodOverContent,
              {},
              true,
              adminId,
            );
          }
        } catch (error) {}
      }
      // const fileName = `removeCoollOffAndBlackList_${new Date().getTime()}`;
      // const excelData: any = {
      //   sheets: [fileName],
      //   data: [finalData],
      //   sheetName: `${fileName}.xlsx`,
      // };
      // await this.typeService._objectToExcel(excelData);
      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async getUserByDeviceId(query) {
    try {
      const deviceId = query?.deviceId;
      if (!deviceId) return kParamMissing('deviceId');
      if (deviceId.includes('-')) return deviceId;
      const deviceData = await this.deviceRepo.getRowWhereData(
        ['id', 'userId', 'deviceId'],
        { where: { deviceId: deviceId }, order: [['id', 'DESC']] },
      );
      if (deviceData === k500Error) return kInternalError;
      return deviceData?.userId;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //Get Device SIM Info Data
  async getDeviceSIMInfo(query) {
    try {
      const userId = query?.userId;
      if (!userId) return kParamMissing('userId');
      const att = [
        'id',
        'simNumber',
        'operatorName',
        'isActive',
        'simNumber2',
        'operatorName2',
      ];
      const option = { where: { userId }, order: [['isActive', 'desc']] };
      const result = await this.deviceSIMRepo.getTableWhereData(att, option);
      if (result === k500Error) return kInternalError;
      return result;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // #start region get user permission
  async getUserPermission(query) {
    const searchText: string = query?.searchText;
    const page: number = query?.page;
    const attributes = [
      'id',
      'title',
      'img',
      'asset',
      'android',
      'IOS',
      'description',
      'updatedBy',
      'updatedAt',
    ];
    const options: any = {};
    if (page) {
      const offset = page * PAGE_LIMIT - PAGE_LIMIT;
      options.offset = offset;
      options.limit = PAGE_LIMIT;
    }
    if (searchText) {
      options.where = { title: { [Op.iRegexp]: searchText } };
    }
    const result = await this.userPermissionRepo.getTableWhereDataWithCounts(
      attributes,
      options,
    );

    if (result === k500Error) return kInternalError;
    const preparedList = [];
    const data = result?.rows;
    for (let i = 0; i < data.length; i++) {
      const ele = data[i];
      preparedList.push({
        id: ele.id,
        Title: ele.title,
        img: ele.img,
        asset: ele.asset,
        Android: ele.android,
        IOS: ele.IOS,
        Description: ele.description,
        'Updated By': ele?.updatedBy
          ? (await this.sharedCommonService.getAdminData(ele?.updatedBy))
              ?.fullName ?? '-'
          : '-',
        'Updated At': this.dateService.readableDate(ele?.updatedAt, true),
      });
    }

    return { count: result.count, rows: preparedList };
  }

  //#region
  async getMailTrackerLogsByUser(reqData) {
    try {
      const userId = reqData?.userId;
      if (!userId) return kParamMissing('userId');
      const trackAttr = [
        'id',
        'status',
        'type',
        'title',
        'subStatus',
        'createdAt',
        'refrenceId',
        'requestData',
        'sentBy',
        'statusDate',
        'loanId',
        'content',
        'source',
      ];
      const trackOptions = await this.prepareOptionsForMailTracker(reqData);
      if (trackOptions?.message) return kInternalError;
      // finding the data
      let trackData: any = await this.mailTrackerRepo.getTableWhereData(
        null,
        trackAttr,
        trackOptions,
        true,
      );
      if (trackData === k500Error) return kInternalError;
      // preparing the data
      const filteredData: any = await this.prepareDataMailTrakerUser(trackData);
      trackData.forEach((row) => {
        let formattedDate: any = new Date(row?.createdAt);
        formattedDate = this.dateService.readableDate(formattedDate);
        row.createdAt = formattedDate;
      });
      if (filteredData?.message) return kInternalError;
      trackData = filteredData;
      return trackData;
    } catch (error) {
      console.log(error);
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  async prepareOptionsForMailTracker(reqData) {
    try {
      const userId = reqData.userId;
      const loanId = reqData.loanId;
      const searchText = reqData?.searchText;
      const download = reqData?.download ?? false;
      const page = reqData?.page ?? 1;
      const dropdownStatus = reqData?.dropdownStatus;

      const options: any = {
        where: { userId },
        // order: [['id', 'DESC']],
      };
      if (loanId) options.where.loanId = loanId;
      if (dropdownStatus) options.where.status = dropdownStatus;
      //For search Text
      if (searchText) {
        if (searchText?.toLowerCase().startsWith('l-')) {
          options.where.loanId = searchText.replace('l-', '');
        } else options.where.title = { [Op.iRegexp]: searchText };
      }
      //For pagination
      if (download != 'true') {
        // options.offset = +(page ?? 1) * PAGE_LIMIT - PAGE_LIMIT;
        // options.limit = PAGE_LIMIT;
      }
      return options;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#endregion
  async prepareDataMailTrakerUser(trackData) {
    const filteredData = [];
    try {
      const length = trackData.length;
      if (length.length == 0) return k422ErrorMessage(kNoDataFound);
      for (let index = 0; index < length; index++) {
        try {
          const ele = trackData[index];
          const refrenceId = ele.refrenceId != '' ? ele.refrenceId : '-';
          const sentBy = ele?.sentBy ?? 'system';
          const loanId = ele?.loanId ?? '-';
          let title = ele?.title ?? '-';
          let description = ele?.content ?? '-';
          const requestData = ele?.requestData;
          let source = ele.source;
          if ((ele.type == 'TEXT' || ele.type == 'WHATSAPP') && source)
            source = this.cryptService.decryptPhone(source);

          if (requestData) {
            // get data from redis
            let key = `TEMPLATE_DATA_BY_TEMPID`;
            let template = await this.redisService.get(key);
            template = template ?? null;
            template = JSON.parse(template) ?? [];

            //filter data from template id
            if (template.length) {
              template = template.filter(
                (el) =>
                  el.templateId == requestData ||
                  el.lspTemplateId == requestData,
              );
              if (template.length > 0) {
                title = template[0]?.title ?? '-';
                description = template[0]?.content ?? '-';
              }
            }
          }
          if (ele.type == 'EMAIL') description = '-';

          // let date: any = new Date(ele?.statusDate ?? ele?.createdAt);
          // date.setHours(date.getHours() + 5);
          // date.setMinutes(date.getMinutes() + 30);
          // date = this.dateService.dateToReadableFormat(date);
          // date = `${date.readableStr} at ${date.hours}:${date.minutes} ${date.meridiem}`;

          filteredData.push({
            type: ele.type,
            refrenceId: refrenceId ?? '-',
            title: title.includes('Verification code')
              ? 'Verification code'
              : title,
            description,
            source,
            sentBy,
            loanId,
            id: ele.id,
            status: ele.status,
            sub: ele?.subStatus ?? '-',
            date: ele?.statusDate ?? ele?.createdAt,
          });
        } catch (error) {}
      }
      return filteredData;
    } catch (error) {
      kInternalError;
    }
  }

  async trackAppInstallForRegUser(body) {
    try {
      return {};
      const days = body?.days ?? 7;
      const today = new Date();
      const date = today;
      date.setDate(date.getDate() - days);

      const date1 = this.typeService.getUTCDate(date.toString());

      const attributes = ['id', 'fcmToken'];
      const options: any = {
        where: {
          createdAt: { [Op.gte]: date1 },
          completedLoans: 0,
          fcmToken: {
            [Op.and]: [{ [Op.ne]: null }, { [Op.ne]: '' }],
          },
        },
      };
      const userData = await this.userRepo.getTableWhereData(
        attributes,
        options,
      );
      if (userData == k500Error) return kInternalError;

      const length = userData.length;
      const spans = await this.typeService.splitToNChunks(userData, 50);

      for (let index = 0; index < spans.length; index++) {
        const list = [];
        const targetList = spans[index];
        for (let i = 0; i < targetList.length; i++) {
          const item = targetList[i];
          list.push(this.updateCheckAppInstalled(item));
        }
        await Promise.all(list);
      }
      return length;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region update install data
  async updateCheckAppInstalled(data: any) {
    try {
      const id = data?.id;
      const fcm = data?.fcmToken;
      const loanData = data?.loanData;

      if (id) {
        const result = await this.sharedNotificationService.checkAppIsInstall(
          fcm,
        );

        const isUnInstallApp = this.typeService
          .getGlobalDate(new Date())
          .toJSON();
        let loanId;
        if (loanData)
          try {
            loanId = loanData[0]?.id;
          } catch (error) {}

        const updateData: any = {
          userId: id,
          type: result ? 'INSTALL_APP_CHECK' : 'UNINSTALL_APP_CHECK',
          date: isUnInstallApp,
        };
        if (loanId) data.loanId = loanId;
        await this.userActivityRepo.findOrCreate(updateData, updateData);

        if (!result) {
          if (!data?.isUnInstallApp)
            await this.userRepo.updateRowDataWithOptions(
              { isUnInstallApp },
              { isUnInstallApp: { [Op.eq]: null } },
              id,
            );
        } else await this.userRepo.updateRowData({ isUnInstallApp: null }, id);
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  async removeFromCoolOff(reqData) {
    // Preparation -> Parameters
    const userIds = reqData.userIds;
    if (!userIds) return kParamMissing('userIds');

    // Preparation -> Query
    const userAttr = ['id'];
    const masterInclude: SequelOptions = { model: MasterEntity };
    masterInclude.attributes = ['userId'];
    masterInclude.where = { status: { loan: 2 } };
    const include = [masterInclude];
    const userOptions = { include, where: { id: { [Op.in]: userIds } } };
    // Hit -> Query
    const userList = await this.repo.getTableWhereData(
      registeredUsers,
      userAttr,
      userOptions,
    );
    // Validation -> Query
    if (userList === k500Error) throw new Error();

    for (let index = 0; index < userList.length; index++) {
      try {
        const userData = userList[index];
        await this.funSetNextEligibiltyDate({
          userId: userData.id,
          eligibilityDate: null,
          adminId: SYSTEM_ADMIN_ID,
        });
      } catch (error) {}
    }

    return {};
  }

  async getFramesSessionList(reqData) {
    const userId = reqData.userId;

    // Comment Postgres frames
    // const options = {
    //   where: { userId, sessionId: { [Op.ne]: null } },
    //   group: ['sessionId', 'createdAt'],
    //   order: [['createdAt']],
    // };
    // let sessionId = await this.repo.getTableWhereData(
    //   FramesEntity,
    //   ['sessionId'],
    //   options,
    // );
    // if (sessionId == k500Error) throw new Error();
    let sessionId = [];

    // FETCH MONGO RECORDS
    const mongoOps = [
      { $match: { userId, sessionId: { $ne: null } } },
      {
        $group: {
          _id: { sessionId: '$sessionId' },
          sessionId: { $first: '$sessionId' },
          createdAt: { $min: '$createdAt' },
        },
      },
      { $sort: { createdAt: 1 } },
      { $project: { sessionId: 1, _id: 0 } },
    ];
    const mongoFrames = await this.mongoQueryService.getAggData(
      userId,
      mongoOps,
    );
    if (mongoFrames === k500Error) throw new Error();
    if (mongoFrames?.length > 0) sessionId = [...sessionId, ...mongoFrames];

    const sessionList = [...new Set(sessionId.map((ele) => ele?.sessionId))];
    return sessionList;
  }

  async getFrames(reqData) {
    const userId = reqData?.userId;
    if (!userId) return kParamMissing('userId');
    const sessionId = reqData?.id;
    if (!sessionId) return kParamMissing('sessionId');

    // Comment Postgres frames
    // const options: any = {
    //   where: { userId, sessionId },
    //   order: [['createdAt']],
    // };
    // let userData = await this.repo.getTableCountWhereData(
    //   FramesEntity,
    //   ['url', 'screenName', 'createdAt'],
    //   options,
    // );
    // if (userData == k500Error) throw new Error();

    // FETCH MONGO RECORDS
    const mongoOps = [
      { $match: { userId, sessionId } },
      { $sort: { createdAt: 1 } },
      { $project: { url: 1, createdAt: 1, screenName: 1, _id: 0 } },
    ];
    const mongoFrames = await this.mongoQueryService.getAggData(
      userId,
      mongoOps,
    );
    if (mongoFrames === k500Error) throw new Error();
    const mapData = { count: mongoFrames?.length, rows: mongoFrames };
    // replace because same sessionId cannot appear in both db
    // if (mongoFrames?.length > 0) userData = mapData;

    mapData?.rows?.forEach((ele) => {
      ele.createdAt = this.dateService.readableDate(ele?.createdAt, true);
    });
    return mapData;
  }

  //#region start get user repayment details
  async userRepaymentDetails(query) {
    try {
      const userId = query?.userId;
      if (!userId) return kParamMissing('userId');

      // Get loan data
      const loanData = await this.funCollectData(query);
      if (loanData?.message) return loanData;

      // calculate repaidAmount , dueAmount, remaining Amount
      let amountData: any = {};
      if (['Active', 'Complete'].includes(loanData?.loanStatus)) {
        amountData = await this.emiSharedService.prepareEMIDetails(loanData);
        if ((amountData?.totalCreditUsed ?? 0) > 0)
          amountData.totalReceived -= amountData?.totalCreditUsed;
      }
      // seperate transaction from loanData
      const emiData = loanData?.emiData ?? [];
      const penalty_days = this.typeService.getOverDueDay(emiData);
      const transData = loanData?.transactionData ?? [];
      transData.sort((a, b) => b?.id - a?.id);
      let obj = { loanId: loanData?.id ?? '-' };
      const autodebit = transData?.find(
        (f) => f?.subSource == 'AUTODEBIT' && f?.type != 'REFUND',
      );
      if (autodebit) {
        let subscriptionDate = autodebit?.subscriptionDate;
        let autoDebitDate = subscriptionDate
          ? subscriptionDate
          : autodebit.createdAt;
        autoDebitDate = this.typeService.dateToJsonStr(
          autoDebitDate,
          'DD/MM/YYYY',
        );
        const response = autodebit?.response
          ? JSON.parse(autodebit?.response)
          : {};
        const failedMessage =
          response?.payment?.failureReason ||
          response?.response?.data?.message ||
          response?.error_description ||
          response?.error_message ||
          response?.reason ||
          '-';
        obj['autoDebitDate'] = autoDebitDate;
        obj['last_auto_debit_status'] = autodebit.status;
        obj['last_auto_debit_message'] =
          autodebit.status === 'COMPLETED' ? 'SUCCESS' : failedMessage;
        obj['last_auto_debit_amount'] =
          autodebit.status === 'INITIALIZED'
            ? this.StringService.readableAmount(autodebit.paidAmount)
            : '-';
      }
      // get legalStep and create legal date
      let legalStepAndDate = !loanData?.id
        ? {}
        : await this.commonSharedService.getLegalDataByLoanId(loanData?.id);
      if (legalStepAndDate) {
        // get next and first hearing date
        let caseDetails = legalStepAndDate?.caseDetails;
        let dates = legalStepAndDate?.dates;
        let type = legalStepAndDate.type;
        let createdAt = legalStepAndDate.createdAt;
        if (type == CASE_DISPOSAL && dates?.disposalDate)
          createdAt = dates?.disposalDate;
        let legalCreatedAt: any = this.typeService.getGlobalDate(createdAt);
        legalCreatedAt = this.typeService.dateToJsonStr(
          legalCreatedAt,
          'DD/MM/YYYY',
        );
        let legalStep = legalString[type];
        obj['legalStep'] =
          type == WARRENT
            ? legalStepAndDate.subType == 2
              ? 'Non-Bailable Warrant'
              : 'Bailable Warrant'
            : legalStep ?? '-';
        obj['legalDate'] = legalCreatedAt ?? '-';

        //extract latest hearing date
        const hearingDate =
          dates?.nextHearingDate ?? caseDetails?.firstHearingDate;
        obj['hearingDate'] =
          hearingDate && hearingDate != -1
            ? this.typeService.dateToJsonStr(hearingDate, 'DD/MM/YYYY')
            : '-';
      }
      let nexSalaryDate = loanData?.employmentData?.otherInfo?.nextPayDate;
      if (nexSalaryDate)
        nexSalaryDate = this.typeService.dateToJsonStr(
          nexSalaryDate,
          'DD/MM/YYYY',
        );

      obj['nextSalaryDate'] = nexSalaryDate ?? '-';
      obj['interestRatePerDay'] = loanData?.interestRate
        ? `@${loanData.interestRate}%`
        : '-';
      obj['loanAmount'] = this.StringService.readableAmount(
        loanData?.netApprovedAmount,
      );
      if (loanData?.emiSelection?.selectedEmiDate)
        obj['userSelectEMIsDate'] = loanData?.emiSelection?.selectedEmiDate;
      obj['overdueDays'] = penalty_days;
      obj['loanAgreement'] = loanData?.eSignData?.signed_document_upload ?? '-';
      if (amountData?.totalReceivable)
        obj['totalReceivable'] = this.StringService.readableAmount(
          amountData?.totalReceivable,
        );
      if (amountData?.totalReceived)
        obj['totalReceived'] = this.StringService.readableAmount(
          amountData?.totalReceived,
        );
      if (amountData?.totalWaiver)
        obj['totalWaiver'] = this.StringService.readableAmount(
          amountData?.totalWaiver,
        );
      if (amountData?.totalCreditUsed)
        obj['totalCreditUsed'] = this.StringService.readableAmount(
          amountData?.totalCreditUsed,
        );
      const totalRemaining = amountData?.totalRemaining;
      if (totalRemaining == 0 || totalRemaining > 0)
        obj['totalRemaining'] = this.StringService.readableAmount(
          amountData?.totalRemaining,
        );
      if (loanData?.charges?.collateral_charge_amt)
        obj['collateralAmount'] = this.StringService.readableAmount(
          loanData?.charges?.collateral_charge_amt,
        );
      //prepare emi Data
      let dueDate = '-';
      let paidEmi = 0;
      emiData.sort((a, b) => b?.id - a?.id);
      for (let i = 0; i < emiData?.length; i++) {
        try {
          const el = emiData[i];
          if (el.payment_status == '1') paidEmi++;
          else if (el.payment_status == '0')
            dueDate = this.typeService.dateToJsonStr(
              el?.emi_date,
              'DD/MM/YYYY',
            );
        } catch (error) {}
      }
      obj['dueDate'] = dueDate;
      obj['emiCount'] = `${paidEmi}/${emiData?.length ?? 0}`;
      return obj;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region Get User Repayment Details
  private async funCollectData(query) {
    const userId = query?.userId;
    let loanId = query?.loanId;
    const loanData = await this.funGetLoanData(userId, loanId);
    if (!loanData) return k400ErrorMessage(kNoDataFound);
    if (!loanId) loanId = loanData?.id;
    loanData.registeredUsers = await this.funGetUserData(userId);
    if (!loanData?.registeredUsers) return k400ErrorMessage(kNoDataFound);

    loanData.employmentData = await this.funGetEmploymentData(userId);
    const show = !['InProcess', 'Rejected'].includes(loanData?.loanStatus);
    if (show) {
      loanData.emiData = await this.funGetEMIData(loanId);
      loanData.transactionData = await this.funGetTransactionData(loanId);
      loanData.eSignData = loanData?.esign_id
        ? await this.funGetESignData(loanData?.esign_id)
        : {};
    }
    delete loanData?.esign_id;
    return loanData;
  }
  //#endregion

  //#region Get User Repayment Details
  private async funGetLoanData(userId, loanId?) {
    const attributes = [
      'id',
      'userId',
      'netApprovedAmount',
      'interestRate',
      'loanStatus',
      'emiSelection',
      'loan_disbursement_date',
      'penaltyCharges',
      'charges',
      'esign_id',
    ];
    const options: any = {
      where: { userId },
      order: [['id', 'DESC']],
      useMaster: false,
    };
    if (loanId) options.where.id = loanId;
    const data = await this.repo.getRowWhereData(
      loanTransaction,
      attributes,
      options,
    );
    if (data === k500Error) throw new Error();
    return data;
  }
  //#endregion

  //#region Get User Repayment Details
  private async funGetUserData(userId) {
    const attributes = ['id', 'completedLoans'];
    const options = { where: { id: userId }, useMaster: false };
    const data = await this.repo.getRowWhereData(
      registeredUsers,
      attributes,
      options,
    );
    if (data === k500Error) throw new Error();
    return data;
  }
  //#endregion

  //#region Get User Repayment Details
  private async funGetEmploymentData(userId) {
    const attributes = ['id', 'otherInfo'];
    const options = {
      where: { userId },
      order: [['id', 'DESC']],
      useMaster: false,
    };
    const data = await this.repo.getRowWhereData(
      employmentDetails,
      attributes,
      options,
    );
    if (data === k500Error) throw new Error();
    return data;
  }
  //#endregion

  //#region Get User Repayment Details
  private async funGetEMIData(loanId) {
    const emiAttr = [
      'id',
      'userId',
      'bounceCharge',
      'gstOnBounceCharge',
      'emi_amount',
      'emi_date',
      'payment_status',
      'payment_due_status',
      'principalCovered',
      'interestCalculate',
      'penalty',
      'totalPenalty',
      'penalty_days',
      'partPaymentPenaltyAmount',
      'waiver',
      'paid_waiver',
      'unpaid_waiver',
      'fullPayPrincipal',
      'fullPayPenalty',
      'fullPayInterest',
      'penalty_update_date',
      'pay_type',
      'legalCharge',
      'legalChargeGST',
      'fullPayLegalCharge',
      'regInterestAmount',
      'dpdAmount',
      'penaltyChargesGST',
      'fullPayPenal',
      'fullPayRegInterest',
      'fullPayBounce',
      'waived_regInterest',
      'waived_bounce',
      'waived_penal',
      'waived_legal',
      'paid_principal',
      'paid_interest',
      'paidBounceCharge',
      'paidPenalCharge',
      'paidLegalCharge',
      'adScreenshotUrl',
      'paidRegInterestAmount',
      'forClosureAmount',
      'sgstForClosureCharge',
      'cgstForClosureCharge',
      'igstForClosureCharge',
    ];
    const options = {
      where: { loanId },
      order: [['id', 'DESC']],
      useMaster: false,
    };
    const data = await this.repo.getTableWhereData(EmiEntity, emiAttr, options);
    if (data === k500Error) throw new Error();
    return data;
  }
  //#endregion

  //#region Get User Repayment Details
  private async funGetTransactionData(loanId) {
    const attributes = [
      'id',
      'emiId',
      'userId',
      'paidAmount',
      'status',
      'completionDate',
      'principalAmount',
      'interestAmount',
      'penaltyAmount',
      'subscriptionDate',
      'transactionId',
      'type',
      'response',
      'subSource',
      'createdAt',
      'cgstOnLegalCharge',
      'sgstOnLegalCharge',
      'igstOnLegalCharge',
      'legalCharge',
      'cgstOnPenalCharge',
      'sgstOnPenalCharge',
      'igstOnPenalCharge',
      'penalCharge',
      'regInterestAmount',
      'bounceCharge',
      'cgstOnBounceCharge',
      'sgstOnBounceCharge',
      'igstOnBounceCharge',
      'source',
    ];
    const options = { where: { loanId }, useMaster: false };
    const data = await this.repo.getTableWhereData(
      TransactionEntity,
      attributes,
      options,
    );
    if (data === k500Error) throw new Error();
    return data;
  }
  //#endregion

  //#region Get User Repayment Details
  private async funGetESignData(id) {
    if (!id) return {};
    const attributes = ['id', 'signed_document_upload'];
    const options = { where: { id }, useMaster: false };
    const data = await this.repo.getRowWhereData(
      esignEntity,
      attributes,
      options,
    );
    if (data === k500Error) throw new Error();
    return data;
  }
  //#endregion

  async decryptedPhoneNumbers(reqData): Promise<any> {
    const userIds = reqData.userIds;
    if (!userIds) return kParamMissing('userIds');
    const needExcel = reqData.needExcel == true;

    // Preparation -> Query
    const userAttr = ['id', 'phone'];
    const userOptions = { where: { id: userIds } };
    // Hit -> Query
    const userList = await this.userRepo.getTableWhereData(
      userAttr,
      userOptions,
    );
    if (userList == k500Error) throw new Error();

    userList.forEach((el) => {
      el.phone = this.cryptService.decryptPhone(el.phone);
    });
    if (!needExcel) return userList;

    // Generation -> Excel
    const rawExcelData = {
      sheets: ['Users'],
      data: [userList],
      sheetName: 'Users.xlsx',
    };
    const url: any = await this.fileService.objectToExcelURL(rawExcelData);
    if (!url || url.message) throw new Error();
    return { url };
  }

  async numberToUserIds(reqData): Promise<any> {
    let numbers = reqData.numbers;
    if (!numbers) return kParamMissing('numbers');
    numbers = numbers.map((el) => this.cryptService.getMD5Hash(el));

    const hashPhoneData = await this.repo.getTableWhereData(
      HashPhoneEntity,
      ['userId'],
      { where: { hashPhone: numbers } },
    );
    if (hashPhoneData === k500Error) throw new Error();

    const userIds = [...new Set(hashPhoneData.map((el) => el.userId))];

    const userAttr = ['id', 'phone'];
    const userOptions = { where: { id: userIds } };
    const userList = await this.repo.getTableWhereData(
      registeredUsers,
      userAttr,
      userOptions,
    );
    if (userList == k500Error) throw new Error();

    userList.forEach((el) => {
      el.phone = this.cryptService.decryptPhone(el.phone);
    });

    // Generation -> Excel
    const rawExcelData = {
      sheets: ['Users'],
      data: [userList],
      sheetName: 'Users.xlsx',
    };
    const url: any = await this.fileService.objectToExcelURL(rawExcelData);
    if (!url || url.message) throw new Error();

    return { url };
  }

  // user other details
  async funGetUserOtherDetails(query) {
    // Validation -> Parameters
    if (!query.userId) return kParamMissing('userId');

    const userId = query.userId;
    const attributes = [
      'id',
      'fullName',
      'gender',
      'typeOfDevice',
      'politicallyExposed',
      'categoryScore',
      'allowedPromotionalContent',
    ];

    const kycInclude: any = { model: KYCEntity };
    kycInclude.attributes = ['kyc_mode'];

    const include = [kycInclude];
    let options: any = { useMaster: false, include, where: { id: userId } };

    const userData = await this.userRepo.getRowWhereData(attributes, options);
    // Validation -> Query data
    if (userData === k500Error) throw new Error();
    if (!userData) return k422ErrorMessage(kNoDataFound);

    const politicallyStatus = {
      1: 'Yes',
      0: 'No',
    };
    userData.politicallyExposed =
      politicallyStatus[userData?.politicallyExposed] ?? 'No';

    //kyc data
    if (userData.kycData) {
      const kycMode = userData?.kycData?.kyc_mode
        ? userData.kycData.kyc_mode == 'DIGILOCKER_IN_HOUSE'
          ? 'DIGILOCKER'
          : userData.kycData.kyc_mode
        : '-';
      userData.kyc_mode = kycMode;
      delete userData.kycData;
    }

    // Comment postgres Frames
    // // Frames Session count
    // const ops = {
    //   useMaster: false,
    //   where: { userId, sessionId: { [Op.ne]: null } },
    //   group: 'sessionId',
    // };
    // let framesSession = await this.repo.getCountsWhere(FramesEntity, ops);
    // if (framesSession === k500Error) throw new Error();
    // userData.framesSessionCount = framesSession.length;
    userData.framesSessionCount = 0;
    // Fetch Mongo Records and combine both
    const mongoOps = [
      {
        $match: {
          userId,
          sessionId: { $ne: null },
        },
      },
      {
        $group: {
          _id: '$sessionId',
          count: { $sum: 1 },
        },
      },
      {
        $project: {
          _id: 0,
          count: 1,
        },
      },
    ];
    let mongoFrames: any = await this.mongoQueryService.getAggData(
      userId,
      mongoOps,
    );
    if (mongoFrames === k500Error) throw new Error();
    if (mongoFrames?.length > 0)
      userData.framesSessionCount += mongoFrames.length;

    // Type of device
    if (userData.typeOfDevice == '0') userData.typeOfDevice = 'ANDROID';
    else if (userData.typeOfDevice == '1') userData.typeOfDevice = 'IOS';
    else if (userData.typeOfDevice == '2') userData.typeOfDevice = 'WEB';

    // Risk Category
    if (userData.categoryScore !== null) {
      if (userData.categoryScore < 0) userData.categoryScore = 'High Risk';
      else if (userData.categoryScore >= 0 && userData.categoryScore <= 25)
        userData.categoryScore = 'Moderate Risk';
      else if (userData.categoryScore > 25) userData.categoryScore = 'Low Risk';
    }

    return userData;
  }

  //#region
  async getUserSpendAnalysis(query) {
    const data = await this.getSpendAnalysisData(query);
    if (data == k500Error) return kInternalError;
    return await this.prepareData(data);
  }
  //#endregion

  //#region
  private async getSpendAnalysisData(query) {
    const users = await this.getPeriodicData(query);
    if (!users?.length) return k422ErrorMessage(kNoDataFound);
    return await this.getUserData(users);
  }
  //#endregion

  //region
  private async getPeriodicData(query) {
    const status = query?.status || PERIODIC_ENTITY_STATUS.RESPONSE_RECEIVED;
    const periodic_status = Object.values(PERIODIC_ENTITY_STATUS);

    if (periodic_status?.length && !periodic_status.includes(+status)) return;

    const options: any = {
      where: { status, type: 6 },
    };

    // If dateFilter applied....
    if (query?.startDate && query?.endDate) {
      let dateRange = this.typeService.getUTCDateRange(
        query.startDate.toString(),
        query.endDate.toString(),
      );
      options.where.createdAt = {
        [Op.gte]: dateRange.fromDate,
        [Op.lte]: dateRange.endDate,
      };
    }

    const users = await this.repo.getTableWhereData(
      PeriodicEntity,
      ['userId'],
      options,
    );
    if (users === k500Error) return;
    return users;
  }
  //#endregion

  //#region
  private async getUserData(users) {
    const userIds = [...new Set(users?.map((user) => user?.userId))];
    if (!userIds.length) return;

    const attributes = [
      'id',
      'fullName',
      'phone',
      'completedLoans',
      'financialSummaryData',
      'loanStatus',
      'isBlacklist',
      'stage',
      'masterId',
    ];
    const options = { where: { id: userIds } };
    const userList = await this.userRepo.getTableWhereData(attributes, options);
    if (userList == k500Error) return k500Error;

    // Preparing masterIds and fetching master data....
    const masterIds = userList.map((user) => user?.masterId);
    const masterList = await this.masterRepo.getTableWhereData(
      ['otherInfo', 'status', 'coolOffData', 'id'],
      {
        where: { id: masterIds },
        order: [['id', 'DESC']],
      },
    );
    if (masterList == k500Error) return k500Error;
    return { userList, masterList };
  }
  //#endregion

  //#region
  private async prepareData(data) {
    if (!data?.userList?.length) return kSUCCESSMessage(kNoDataFound, []);
    const userList = data?.userList;
    const masterList = data?.masterList;
    const response = [];
    for (let index = 0; index < userList.length; index++) {
      try {
        const element = userList[index];
        const masterData = masterList.find((el) => el?.id == element?.masterId);
        const summary = element?.financialSummaryData ?? {};

        let activeConsent;
        let totalBanks = 0;
        let totalBalance = 0;

        Object.entries(summary).forEach(([key, values]) => {
          if (!Array.isArray(values)) return;
          values.forEach((entry) => {
            if (entry.consentStatus === 'ACTIVE') {
              activeConsent = {
                [key]: entry,
              };
              totalBanks += 1;
              if (entry.accountBalance)
                totalBalance += parseFloat(entry?.accountBalance);
            }
          });
        });

        let bank;
        if (activeConsent) bank = Object.keys(activeConsent)[0];
        const concentDate = activeConsent?.[bank]?.createdAt || null;
        let consentEndDate;
        if (concentDate) {
          consentEndDate = new Date(concentDate).setMonth(
            new Date(concentDate).getMonth() + 12,
          );
        }

        ///manage user status
        let userStatus = 'Active';
        if (element?.isBlackList == '1') userStatus = 'Blocked';
        else if (masterData?.coolOffData?.coolOffEndsOn) {
          const coolOffEndDate = this.typeService.getGlobalDate(
            masterData?.coolOffData?.coolOffEndsOn,
          );
          const today = this.typeService.getGlobalDate(new Date());
          if (coolOffEndDate >= today) userStatus = 'Cool-Off';
        }

        const object = {
          userId: element?.id,
          Name: element?.fullName || '-',
          'Mobile Number': element?.phone
            ? this.cryptService.decryptPhone(element?.phone)
            : '-',
          'Completed Loans': element?.completedLoans || '0',
          'Entered Salary': masterData?.otherInfo?.salaryInfo
            ? '₹' + masterData?.otherInfo?.salaryInfo
            : '-',
          'Total Balance': '₹' + totalBalance,
          'Total Bank Accounts': totalBanks,
          Bank: bank,
          'Last Fetched Date': activeConsent?.[bank]?.updatedAt
            ? this.dateService.readableDate(activeConsent?.[bank]?.updatedAt)
            : '-',
          'Last Balance': activeConsent?.[bank]?.lastBalance
            ? '₹' + activeConsent?.[bank]?.lastBalance
            : '-',
          'Salary Amount Fetched': activeConsent?.[bank]?.salaryAmount
            ? '₹' + activeConsent?.[bank]?.salaryAmount
            : '-',
          'Salary Count': activeConsent?.[bank]?.salaryCount || '-',
          'ECS Bounce': activeConsent?.[bank]?.ecsCharge || '-',
          'Consent Start Date': concentDate
            ? this.dateService.readableDate(concentDate, true)
            : '-',
          'Consent End Date': concentDate
            ? this.dateService.readableDate(consentEndDate, true)
            : '-',
          'Loan Status':
            masterData?.status?.loan &&
            ['-2', '-1', 1, 0, 3].includes(masterData?.status?.loan)
              ? LOAN_STATUS_OBJECT['-1']
              : LOAN_STATUS_OBJECT[masterData?.status?.loan] ||
                LOAN_STATUS_OBJECT['-1'],
          'Due Status': element?.loanStatus
            ? USER_LOAN_STATUS[+element?.loanStatus]
            : '-',
          'User Status': userStatus,
          'Applied Loan Status': element?.stage
            ? this.commonSharedService.stageNumberToStr(element?.stage)
            : '-',
        };
        response.push(object);
      } catch (error) {
        console.log(error);
      }
    }
    return response;
  }
  //#endregion

  async processNumbersUpdate(reqData: any) {
    const oldNumber = reqData?.oldNumber;
    if (!oldNumber) return kParamMissing('oldNumber');
    const newNumber = reqData?.newNumber;
    if (!newNumber) return kParamMissing('newNumber');
    const isReqFromLsp = reqData?.isReqFromLsp;

    // Hashed Phones
    const oldHashNumber = this.cryptService.getMD5Hash(oldNumber);
    const newHashNumber = this.cryptService.getMD5Hash(newNumber);

    // Encr Phones
    const oldEncNumber = this.cryptService.encryptPhone(oldNumber);
    const newEncNumber = this.cryptService.encryptPhone(newNumber);

    const attr = ['id', 'phone', 'hashPhone'];
    const ops = {
      where: { hashPhone: [oldHashNumber, newHashNumber] },
    };
    const userData = await this.userRepo.getTableWhereData(attr, ops);
    if (!userData || userData == k500Error)
      return k422ErrorMessage(kNoDataFound);

    // Same logic in LSP
    // Same logic in Other NBFC (API call from LSP)
    const oldUser = userData.find((item) => item.hashPhone === oldHashNumber);
    const newUser = userData.find((item) => item.hashPhone === newHashNumber);
    // Should not call API if both users dont exist
    if (!oldUser && !newUser) return k422ErrorMessage(kNoDataFound);

    if (oldUser && newUser) {
      // If both profile exists then swap
      const bothSwapRes: any = await this.swapProfileNumbers(oldUser, newUser);
      if (bothSwapRes?.message) return bothSwapRes;
    } else {
      // If only one profile exists then only replace
      const replacePayload = {
        updateUserId: oldUser ? oldUser.id : newUser.id,
        updateHashNum: oldUser ? newHashNumber : oldHashNumber,
        updateEncNum: oldUser ? newEncNumber : oldEncNumber,
      };
      const replaceNumberRes: any = await this.replaceProfileNumber(
        replacePayload,
      );
      if (replaceNumberRes?.message) return replaceNumberRes;
    }

    // swap numbers in LSP (Ignore if request come from LSP)
    if (!isReqFromLsp) {
      const lspUrl =
        EnvConfig.url.lspBaseLink + '/v4/user/swapLspProfileNumbers';
      const lspSwapPayload = {
        oldNumber: oldNumber,
        newNumber: newNumber,
        nbfcType: EnvConfig.nbfcType,
      };
      const lspRes = await this.apiService.post(lspUrl, lspSwapPayload);
      if (!lspRes || !lspRes?.valid)
        return k422ErrorMessage('Error updating data from LSP!');
    }
    return true;
  }

  private async swapProfileNumbers(oldUser, newUser) {
    const oldProfileId = oldUser?.id;
    const newProfileId = newUser?.id;

    // Update New profile with old profile number
    const newUpdateData = {
      phone: newUser.phone.slice(0, -1) + '1', // handle unique constraint
      hashPhone: newUser.hashPhone.slice(0, -1) + '1', // handle unique constraint
    };
    const newUpdate = await this.userRepo.updateRowData(
      newUpdateData,
      newProfileId,
    );
    if (newUpdate === k500Error) return kInternalError;

    // Update Old profile with new profile number
    const oldUpdateData = {
      phone: newUser.phone,
      hashPhone: newUser.hashPhone,
    };
    const oldUpdate = await this.userRepo.updateRowData(
      oldUpdateData,
      oldProfileId,
    );
    if (oldUpdate === k500Error) return kInternalError;

    // Update remaining new Profile hashPhone
    const newHashUpdateData = {
      phone: oldUser.phone,
      hashPhone: oldUser.hashPhone, // handle unique constraint
    };
    const newHashUpdate = await this.userRepo.updateRowData(
      newHashUpdateData,
      newProfileId,
    );
    if (newHashUpdate === k500Error) return kInternalError;

    const refreshPayload = {
      userIds: [oldProfileId, newProfileId],
    };
    await this.sharedMigration.refreshUserStage(refreshPayload);
    return true;
  }

  private async replaceProfileNumber(updateData) {
    const { updateUserId, updateHashNum, updateEncNum } = updateData;
    // Update remaining new Profile hashPhone
    const userUpdateData = {
      phone: updateEncNum,
      hashPhone: updateHashNum,
    };
    const updatedUser = await this.userRepo.updateRowData(
      userUpdateData,
      updateUserId,
    );
    if (updatedUser === k500Error) return kInternalError;

    const refreshPayload = {
      userIds: [updateUserId],
    };
    await this.sharedMigration.refreshUserStage(refreshPayload);
    return true;
  }

  async deleteSelfieData(body) {
    const userId = body?.userId;
    if (!userId) return kParamMissing('userId');
    const type = body?.type ?? 'REDIS';
    if (type == 'ALL') {
      await this.repo.deleteWhereData(
        UserSelfieEntity,
        { where: { userId } },
        false,
      );
    }
    if (Array.isArray(userId)) {
      for (let i = 0; i < userId.length; i++) {
        const id = userId[i];
        await this.redisService.del(`SELFIE_DATA_${id}`);
        await this.redisService.del(`${id}ROUTE_DETAILS`);
        await this.userService.routeDetails({ id });
      }
    } else {
      await this.redisService.del(`SELFIE_DATA_${userId}`);
      await this.redisService.del(`${userId}ROUTE_DETAILS`);
      return await this.userService.routeDetails({ id: userId });
    }
    return {};
  }
}
