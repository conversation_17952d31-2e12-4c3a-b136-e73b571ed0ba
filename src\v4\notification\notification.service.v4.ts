import { Injectable } from '@nestjs/common';
import { k500Error } from 'src/constants/misc';
import { kInternalError, kParamMissing } from 'src/constants/responses';
import { MailTrackerRepository } from 'src/repositories/mail.tracker.repository';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { UserRepository } from 'src/repositories/user.repository';
import { Op } from 'sequelize';
@Injectable()
export class NotificationServiceV4 {
  constructor(
    private readonly mailTrackerRepo: MailTrackerRepository,
    private readonly userRepo: UserRepository,
    private readonly commonService: CommonSharedService,
  ) {}

  async countList(reqData, headers) {
    // Params validation
    const userId = reqData.userId;
    if (!userId) return kParamMissing('userId');
    const isCount = reqData.isCount;
    if (!isCount) return kParamMissing('isCount');
    let appType = headers?.apptype ?? reqData?.appType;

    const userData = await this.userRepo.getRowWhereData(
      ['id', 'appType', 'typeOfDevice'],
      { where: { id: userId } },
    );
    if (userData == k500Error) return kInternalError;
    appType = !appType ? userData?.appType : appType;

    const limit = 50;
    const attributes = [
      'id',
      'title',
      'content',
      'createdAt',
      'notificationFlag',
    ];
    const options: any = {
      useMaster: false,
      where: {
        userId,
        type: 'NOTIFICATION',
        title: { [Op.not]: 'Chat support' },
      },
      order: [['id', 'DESC']],
      limit,
    };
    if (isCount == 'true') {
      options.where.notificationFlag = '0';
      const counts = await this.mailTrackerRepo.getCountsWhere(
        options.where.type,
        options,
        true,
      );
      if (counts === k500Error) return kInternalError;
      return { counts };
    }
    let notifications = await this.mailTrackerRepo.getTableWhereData(
      options.where.type,
      attributes,
      options,
      true,
    );
    if (notifications === k500Error) return kInternalError;
    const filteredNotifications = [];
    const idsToUpdate = [];
    for (let i = 0; i < notifications.length; i++) {
      const el: any = notifications[i];
      try {
        const key = el?.title;

        // Skip this notification completely
        if (
          key === 'Where Did Your Money Go? Find Out Now! 💸' &&
          appType === '1'
        ) {
          continue;
        } else if (
          key === 'Where Did Your Money Go? Find Out Now! 💸' &&
          appType === '0'
        ) {
          if (userData?.typeOfDevice != '2') el.onTapRoute = 'selectBank';
          el.icon = `https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Mar-2025/*************.svg`;
        }

        el.body = el?.content;
        delete el?.content;
        el.icon = this.commonService.getAppNotificationIcons(appType, key);
        el.notificationFlag = el.notificationFlag?.toString();
        if (el.notificationFlag === '0') idsToUpdate.push(el.id);

        // Keep the element in final array
        filteredNotifications.push(el);
      } catch (error) {}
    }

    // Now replace notifications with the filtered list
    notifications = filteredNotifications;

    // if (notifications.length == idsToUpdate.length) {
    if (!options?.where?.type) options.where.type = 'NOTIFICATION';
    await this.mailTrackerRepo.updateRowWhereData(
      options.where.type,
      { notificationFlag: '1' },
      { where: { userId, notificationFlag: '0' } },
      true,
    );
    return { notifications };
  }
}
