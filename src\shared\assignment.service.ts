import { Injectable } from '@nestjs/common';
import { k500Error } from 'src/constants/misc';
import { kInternalError } from 'src/constants/responses';
import { BANKINGADMINS } from 'src/constants/strings';
import { AdminRepository } from 'src/repositories/admin.repository';
import { AdminRoleRepository } from 'src/repositories/admin_role.repository';
import { COLLECTION } from 'src/utils/type.service';
import { RedisService } from 'src/redis/redis.service';
import { ErrorContextService } from 'src/utils/error.context.service';
import { CSE_ROLE_ID, SYSTEM_ADMIN_ID } from 'src/constants/globals';
import { REDIS_KEY } from 'src/constants/objects';

@Injectable()
export class AssignmentSharedService {
  constructor(
    private readonly adminRepo: AdminRepository,
    private readonly adminRoles: AdminRoleRepository,
    private readonly redisService: RedisService,
    private readonly errorContextService: ErrorContextService,
  ) {}

  async fetchadmins(ids, type, redisKey) {
    try {
      const shiftDetails = JSON.parse(
        await this.redisService.get(`SHIFTPLAN_${CSE_ROLE_ID}`),
      );
      let assingData = await this.redisService.get(redisKey);
      const object = {
        id: 1,
        type,
        lastUpdateIndex: 0,
      };
      if (!assingData) {
        let setassingData = await this.redisService.set(
          redisKey,
          JSON.stringify(object),
        );
        if (!setassingData) return kInternalError;
        assingData = await this.redisService.get(redisKey);
      }
      let adminData: any;
      try {
        adminData = await this.fetchEmployeesInShift(CSE_ROLE_ID);
      } catch (error) {}
      if (!adminData) return kInternalError;
      let lastUpdateIndex = JSON.parse(assingData).lastUpdateIndex;
      let result: any = [];
      for (const item of ids) {
        const data = await this.assignMasterIds(
          item,
          lastUpdateIndex,
          adminData,
          true,
        );
        if (!data) return kInternalError;
        result.push(data.result);
        lastUpdateIndex = data.lastUpdateIndex;
        shiftDetails.find((shift) => {
          shift.employees.find((cse) => {
            if (data.result.assignTo == cse.id) {
              cse.caseCount += 1;
            }
          });
        });
      }
      object.lastUpdateIndex = lastUpdateIndex;
      await this.redisService.set(redisKey, JSON.stringify(object));
      await this.redisService.set(
        `SHIFTPLAN_${CSE_ROLE_ID}`,
        JSON.stringify(shiftDetails),
      );
      return result;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async assignMasterIds(masterId, lastUpdateIndex, adminData, isUpdated) {
    let index = isUpdated
      ? lastUpdateIndex + 1 >= adminData.employeesData.length
        ? 0
        : lastUpdateIndex + 1
      : lastUpdateIndex;
    const finalData = {
      result: {
        id: masterId,
        assignTo: adminData.employeesData[index].id,
      },
      lastUpdateIndex: index,
    };
    return finalData;
  }

  async fetchAdminAccordingRole(type, needData = false) {
    try {
      if (!type) return false;
      const now = new Date();
      const endOfDay = new Date();
      endOfDay.setHours(23, 59, 59, 999);

      const ttlInSeconds = Math.floor(
        (endOfDay.getTime() - now.getTime()) / 1000,
      );
      // get data from redis admin role
      const key = REDIS_KEY.ADMIN_ROLES;
      const redisAdminRole = await this.redisService.get(key);

      let adminRoleData = null;
      if (redisAdminRole) adminRoleData = JSON.parse(redisAdminRole);
      else {
        let where: any = {};
        adminRoleData = await this.adminRoles.getTableWhereData(
          ['id', 'title', 'isActive', 'adminId'],
          where,
        );
        if (adminRoleData == k500Error) return false;
        await this.redisService.set(
          key,
          JSON.stringify(adminRoleData),
          ttlInSeconds,
        );
      }

      let title;
      if (type == BANKINGADMINS) title = 'cse';
      else if (type == COLLECTION) title = 'collection';
      else if (type) title = type;

      const adminRole = adminRoleData.find(
        (item) => item?.isActive == '1' && title == item?.title,
      );

      // find admin
      let adminData: any = [];
      const adminKey = REDIS_KEY.ADMINS_DATA;
      const redisAdmins = await this.redisService.get(adminKey);
      if (redisAdmins) adminData = JSON.parse(redisAdmins);
      else {
        let options = {
          useMaster: false,
          order: [['id', 'DESC']],
        };
        let attr = [
          'id',
          'otherData',
          'email',
          'phone',
          'companyPhone',
          'fullName',
          'roleId',
          'isActive',
          'isLogin',
        ];
        adminData = await this.adminRepo.getTableWhereData(attr, options);
        if (!adminData || adminData == k500Error) return false;
        await this.redisService.set(
          adminKey,
          JSON.stringify(adminData),
          ttlInSeconds,
        );
      }
      const findLoginAdmin = adminData.filter(
        (item) =>
          item.roleId === adminRole.id &&
          item.isActive == '1' &&
          item.isLogin == '1',
      );

      if (findLoginAdmin?.length == 0) {
        const findActiveAdmin = adminData.filter(
          (item) =>
            item.roleId === adminRole.id &&
            item.isActive == '1' &&
            item.isLogin == '0',
        );
        adminData = findActiveAdmin;
      } else adminData = findLoginAdmin;
      if (!adminData) return false;
      return needData ? adminData : adminData.map((ele) => ele.id);
    } catch (error) {
      return false;
    }
  }

  async fetchEmployeesInShift(roleId) {
    try {
      const key = `SHIFTPLAN_${roleId}`;
      const shiftData = JSON.parse(await this.redisService.get(key));

      const currentTime = new Date().toTimeString().split(' ')[0];

      const activeShift = shiftData.find((shift) => {
        const { startTime, endTime } = shift;

        if (startTime < endTime) {
          return currentTime >= startTime && currentTime < endTime;
        } else {
          return currentTime >= startTime || currentTime < endTime;
        }
      });

      if (!activeShift) {
        return { employeesData: [] };
      }

      const activeShiftId = activeShift.id;

      const overlappingShift = shiftData.find((shift) => {
        const { startTime, endTime } = shift;

        if (activeShiftId === shift.id) return false;

        if (currentTime >= startTime && currentTime < endTime) {
          return true;
        }

        return false;
      });

      if (overlappingShift) {
        let result;
        try {
          // Reallocate cases between active and overlapping shift
          result = await this.reallocateCasesBetweenShifts(
            activeShift,
            overlappingShift,
          );
        } catch (error) {
          console.log('error', error);
        }

        return { employeesData: result, shiftData };
      }

      const employeesData = activeShift.employees.sort(
        (a, b) => (a.caseCount || 0) - (b.caseCount || 0),
      );
      return { employeesData, shiftData };
    } catch (error) {}
  }

  // for each new cases that will come during the current active shift
  async allocateNewCase(roleId) {
    try {
      const employees: any = await this.fetchEmployeesInShift(roleId);
      if (employees == k500Error || employees?.employeesData?.length === 0)
        return { id: SYSTEM_ADMIN_ID };
      return employees?.employeesData[0];
    } catch (error) {
      return { id: SYSTEM_ADMIN_ID };
    }
  }

  async reallocateCasesBetweenShifts(oldShift: any, newShift: any) {
    try {
      const currentTime = new Date().toTimeString().split(' ')[0];

      const oldShiftEndTime = oldShift.endTime;
      const newShiftStartTime = newShift.startTime;

      // Only redistribute if a transition has occurred
      if (currentTime >= newShiftStartTime && currentTime <= oldShiftEndTime) {
        // Fetch employees and cases
        const oldShiftEmployees: any = await this.fetchEmployeesShiftWise(
          CSE_ROLE_ID,
          oldShift.id,
        );
        const newShiftEmployees: any = await this.fetchEmployeesShiftWise(
          CSE_ROLE_ID,
          newShift.id,
        );

        const oldShiftCaseCounts = [];
        if (oldShiftEmployees?.length)
          oldShiftEmployees.forEach((employee) => {
            oldShiftCaseCounts.push(employee.caseCount);
          });

        const newShiftCaseCounts = [];
        if (newShiftEmployees?.length)
          newShiftEmployees.forEach((employee) => {
            newShiftCaseCounts.push(employee.caseCount);
          });

        // Check case distribution between shifts
        const maxOldShiftCases = Math.max(...oldShiftCaseCounts);
        const minNewShiftCases = Math.min(...newShiftCaseCounts);

        const prioritizeNewShift = minNewShiftCases < maxOldShiftCases;

        let assignedEmployee;

        if (prioritizeNewShift) {
          // Assign exclusively to new shift employees
          const newShiftEmployee = newShiftEmployees.sort(
            (a, b) => (a.caseCount || 0) - (b.caseCount || 0),
          );
          assignedEmployee = newShiftEmployee;
        } else {
          // Assign to employees in both shifts once balanced
          const allEmployees = [
            ...oldShiftEmployees,
            ...newShiftEmployees,
          ].sort((a, b) => (a.caseCount || 0) - (b.caseCount || 0));
          assignedEmployee = allEmployees;
        }
        return assignedEmployee;
      }
    } catch (error) {
      console.log('error', error);
    }
  }

  async fetchEmployeesShiftWise(roleId, shiftId) {
    const employeesDetails = JSON.parse(
      await this.redisService.get(`SHIFTPLAN_${roleId}`),
    );
    const shiftDetails = employeesDetails.find(
      (shiftData) => shiftId == shiftData.id,
    );
    return shiftDetails?.employees;
  }
}
