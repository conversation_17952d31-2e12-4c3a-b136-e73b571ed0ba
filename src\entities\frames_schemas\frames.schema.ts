// Imports
import { Document } from 'mongoose';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';

export type FramesDocument = Frames & Document;

@Schema({ timestamps: true, strict: false })
export class Frames {
  @Prop({ required: true })
  deviceId: string;

  @Prop({ required: false })
  userId: string;

  @Prop({ required: false })
  url: string;

  @Prop({ required: false })
  sessionId: string;

  @Prop({ required: false })
  screenName: string;

  @Prop({ type: Date, required: false })
  createdAt: Date;

  @Prop({ type: Date, required: false })
  expireAt: Date;
}

export const FramesSchema = SchemaFactory.createForClass(Frames);

// Add TTL index
FramesSchema.index({ expireAt: 1 }, { expireAfterSeconds: 0 });
