// Imports
import { Model, Table } from 'sequelize-typescript';
import { Column, DataType } from 'sequelize-typescript';

@Table({})
export class SMS_0 extends Model<SMS_0> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
  })
  source: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  requestData: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  service: string;
}

@Table({})
export class SMS_1 extends Model<SMS_1> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
  })
  source: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  requestData: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  service: string;
}

@Table({})
export class SMS_2 extends Model<SMS_2> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
  })
  source: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  requestData: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  service: string;
}

@Table({})
export class SMS_3 extends Model<SMS_3> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
  })
  source: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  requestData: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  service: string;
}

@Table({})
export class SMS_4 extends Model<SMS_4> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
  })
  source: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  requestData: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  service: string;
}

@Table({})
export class SMS_5 extends Model<SMS_5> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
  })
  source: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  requestData: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  service: string;
}

@Table({})
export class SMS_6 extends Model<SMS_6> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
  })
  source: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  requestData: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  service: string;
}

@Table({})
export class SMS_7 extends Model<SMS_7> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
  })
  source: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  requestData: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  service: string;
}

@Table({})
export class SMS_8 extends Model<SMS_8> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
  })
  source: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  requestData: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  service: string;
}

@Table({})
export class SMS_9 extends Model<SMS_9> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
  })
  source: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  requestData: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  service: string;
}
