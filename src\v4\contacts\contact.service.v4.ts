// Imports
import { Injectable } from '@nestjs/common';
import { kInternalError, kParamMissing } from 'src/constants/responses';
import { ContactSharedService } from 'src/shared/contact.service';

@Injectable()
export class ContactServiceV4 {
  constructor(private readonly sharedContacts: ContactSharedService) {}

  async syncCallLogs(reqData) {
    try {
      // Params validation
      const userId = reqData.userId;
      if (!userId) return kParamMissing('userId');
      const callLogs = reqData.callLogs;
      if (!callLogs) return kParamMissing('callLogs');
      const result: any = await this.sharedContacts.syncCallLogsData(
        callLogs,
        userId,
      );
      if (result?.message) return result;
      return result;
    } catch (error) {
      console.error('Error in: ', error);
      return kInternalError;
    }
  }
}
