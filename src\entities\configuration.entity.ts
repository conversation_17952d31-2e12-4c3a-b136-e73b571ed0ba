// Imports
import { Table, Column, Model, DataType } from 'sequelize-typescript';
import { EnvConfig } from 'src/configs/env.config';
@Table({})
export class Configuration extends Model<Configuration> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.ARRAY(DataType.TEXT),
    allowNull: true,
  })
  emailDomain: string[];

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  chatbotData: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  androidVersion: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  iosVersion: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  netBankingUrl: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  emandateKey: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  getEmandateStatusUrl: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  esignKey: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  getEsignStatusUrl: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  esignReturnUrl: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  emandateReturnUrl: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  bankingProAppID: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  bankingProKeySecret: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  isRazorpay: boolean;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  cashFreeService: boolean;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: true,
  })
  stuckContactUsFlow: boolean;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  nbfcAndroidVersion: string;
  
  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  androidIosVersion: string;
}
