import { Injectable } from '@nestjs/common';
import { kInternalError } from 'src/constants/responses';

@Injectable()
export class ErrorContextService {
  private error: Error | null = null;

  throwAndSetCtxErr(err?: any) {
    try {
      throw err ?? new Error();
    } catch (error) {
      this.setCtxError(error);
    }
  }

  setCtxError(error: Error) {
    this.error = error;
  }

  getCtxError(): Error | null {
    return this.error;
  }

  clearCtxError() {
    this.error = null;
  }
}
