import { Body, Controller, Get, Post, Query, Res } from '@nestjs/common';
import { shiftPlanService } from './shiftPlan.service';
import { kInternalError, kSuccessData } from 'src/constants/responses';

@Controller('admin/shiftPlan')
export class shiftPlanController {
  constructor(private readonly shiftService: shiftPlanService) {}

  @Get('getShiftPlan')
  async getShiftPlan(@Query() query, @Res() res) {
    try {
      const data: any = await this.shiftService.getShiftList(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
        console.error("Error in: ", error);
        return res.send(kInternalError);
      }
  }

  @Post('createShiftPlan')
  async funCreateShiftPlan(@Body() body, @Res() res) {
    try {
      const data: any = await this.shiftService.createShift(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      console.error("Error in: ", error);
      return res.send(kInternalError);
    }
  }

  @Post('updateShiftPlan')
  async funUpdateShiftPlan(@Body() body, @Res() res){
    try {
      const data: any = await this.shiftService.updateShift(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      console.error("Error in: ", error);
      return res.send(kInternalError);
    }
  }

  // @Post('deleteShiftPlan')
  // async funDeleteShiftPlan(@Body() body, @Res() res){
  //   try {
  //     const data: any = await this.shiftService.deleteShift(body);
  //     if (data?.message) return res.send(data);
  //     return res.send({ ...kSuccessData, data });
  //   } catch (error) {
  //     console.error("Error in: ", error);
  //     return res.send(kInternalError);
  //   }
  // }

  @Get('employeesByRoleId')
  async funEmployeesByRoleId(@Query() query, @Res() res){
    try {
      const data: any = await this.shiftService.employeesByRoleId(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      console.error("Error in: ", error);
      return res.send(kInternalError);
    }
  }
}