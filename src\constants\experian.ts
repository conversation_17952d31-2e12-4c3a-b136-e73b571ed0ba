export const experianAccountHoldertypeCode = {
  '1': 'Individual',
  '2': 'Joint',
  '3': 'Authorised User',
  '7': 'Guarantor',
  Z: 'Deceased',
};

export const experianAccountType = {
  '01': 'AUTO LOAN',
  '02': 'HOUSING LOAN',
  '03': 'PROPERTY LOAN',
  '04': 'LOAN AGAINST SHARES/SECURITIES',
  '05': 'PERSONAL LOAN',
  '06': 'CONSUMER LOAN',
  '07': 'GOLD LOAN',
  '08': 'EDUCATIONAL LOAN',
  '09': 'LOAN TO PROFESSIONAL',
  '10': 'CREDIT CARD',
  '11': 'LEASING',
  '12': 'OVERDRAFT',
  '13': 'TWO-WHEELER LOAN',
  '14': 'NON-FUNDED CREDIT FACILITY',
  '15': 'LOAN AGAINST BANK DEPOSITS',
  '16': 'FLEET CARD',
  '17': 'Commercial Vehicle Loan',
  '18': 'Telco - Wireless',
  '19': 'Telco - Broadband',
  '20': 'Telco - Landline',
  '23': 'GECL Secured',
  '24': 'GECL Unsecured',
  '31': 'Secured Credit Card',
  '32': 'Used Car Loan',
  '33': 'Construction Equipment Loan',
  '34': 'Tractor Loan',
  '35': 'Corporate Credit Card',
  '36': 'Kisan Credit Card',
  '37': 'Loan on Credit Card',
  '38': 'Prime Minister Jaan Dhan Yojana - Overdraft',
  '39': 'Mudra Loans - Shishu / Kishor / Tarun',
  '40': 'Microfinance - Business Loan',
  '41': 'Microfinance - Personal Loan',
  '42': 'Microfinance - Housing Loan',
  '43': 'Microfinance - Others',
  '44': 'Pradhan Mantri Awas Yojana - Credit Link Subsidy Scheme MAY CLSS',
  '45': 'P2P Personal Loan',
  '46': 'P2P Auto Loan',
  '47': 'P2P Education Loan',
  '51': 'BUSINESS LOAN – GENERAL',
  '52': 'BUSINESS LOAN –PRIORITY SECTOR – SMALL BUSINESS',
  '53': 'BUSINESS LOAN –PRIORITY SECTOR – AGRICULTURE',
  '54': 'BUSINESS LOAN –PRIORITY SECTOR – OTHERS',
  '55': 'BUSINESS NON-FUNDED CREDIT FACILITY – GENERAL',
  '56': 'BUSINESS NON-FUNDED CREDIT FACILITY – PRIORITY SECTOR – SMALL BUSINESS',
  '57': 'BUSINESS NON-FUNDED CREDIT FACILITY – PRIORITY SECTOR – AGRICULTURE',
  '58': 'BUSINESS NON-FUNDED CREDIT FACILITY – PRIORITY SECTOR – OTHERS',
  '59': 'BUSINESS LOANS AGAINST BANK DEPOSITS',
  '60': 'Staff Loan',
  '61': 'Business Loan - Unsecured',
  '00': 'Other',
  '50': 'Business Loan - Secured',
  '69': 'Short Term Personal Loan [Unsecured]',
  '70': 'Priority Sector Gold Loan [Secured]',
  '71': 'Temporary Overdraft [Unsecured]',
};

export const experianEmploymentStatus = {
  S: 'Salaried',
  N: 'Non-Salaried',
  E: 'Self-employed',
  P: 'Self-employed Professional',
  U: 'Unemployed',
};

export const experianFrequencyOfPayment = {
  D: 'Daily',
  W: 'Weekly',
  F: 'Bi-Weekly',
  M: 'Monthly',
  Q: '3 Monthly (qaurterly)',
  BP: 'Bullet Payment',
  HY: 'Half-Yearly',
  Y: 'Yearly',
  OD: 'On-demand',
};

export const experianGenderCode = {
  '1': 'Male',
  '2': 'Female',
  '3': 'Transgender',
  '4': 'Unknown',
};

export const experianStateCode = {
  '01': 'JAMMU and KASHMIR',
  '02': 'HIMACHAL PRADESH',
  '03': 'PUNJAB',
  '04': 'CHANDIGARH',
  '05': 'UTTARANCHAL',
  '06': 'HARYANA',
  '07': 'DELHI',
  '08': 'RAJASTHAN',
  '09': 'UTTAR PRADESH',
  '10': 'BIHAR',
  '11': 'SIKKIM',
  '12': 'ARUNACHAL PRADESH',
  '13': 'NAGALAND',
  '14': 'MANIPUR',
  '15': 'MIZORAM',
  '16': 'TRIPURA',
  '17': 'MEGHALAYA',
  '18': 'ASSAM',
  '19': 'WEST BENGAL',
  '20': 'JHARKHAND',
  '21': 'ORISSA',
  '22': 'CHHATTISGARH',
  '23': 'MADHYA PRADESH',
  '24': 'GUJARAT',
  '25': 'DAMAN and DIU',
  '26': 'DADRA and NAGAR HAVELI',
  '27': 'MAHARASHTRA',
  '28': 'ANDHRA PRADESH',
  '29': 'KARNATAKA',
  '30': 'GOA',
  '31': 'LAKSHADWEEP',
  '32': 'KERALA',
  '33': 'TAMIL NADU',
  '34': 'PONDICHERRY',
  '35': 'ANDAMAN and NICOBAR ISLANDS',
  '36': 'TELANGANA',
  '99': 'APO ADDRESS',
};

export const experianMaritalStatus = {
  '1': 'Single',
  '2': 'Married',
  '3': 'Widow/Widower',
  '4': 'Divorced',
};

export const experianAccountStatusCode = {
  '00': 'No Suit Filed',
  '89': 'Wilful Default',
  '93': 'Suit Filed (Wilful Default)',
  '97': 'Suit Filed (Wilful Default) and Written-off',
  '30': 'Restructured',
  '31': 'Restructured Loan (Govt. Mandated)',
  '32': 'Settled',
  '33': 'Post (WO) Settled',
  '34': 'Account Sold',
  '35': 'Written Off and Account Sold',
  '36': 'Account Purchased',
  '37': 'Account Purchased and Written Off',
  '38': 'Account Purchased and Settled',
  '39': 'Account Purchased and Restructured',
  '40': 'Status Cleared',
  '41': 'Restructured Loan',
  '42': 'Restructured Loan (Govt. Mandated)',
  '43': 'Written-off',
  '44': 'Settled',
  '45': 'Post (WO) Settled',
  '46': 'Account Sold',
  '47': 'Written Off and Account Sold',
  '48': 'Account Purchased',
  '49': 'Account Purchased and Written Off',
  '50': 'Account Purchased and Settled',
  '51': 'Account Purchased and Restructured',
  '52': 'Status Cleared',
  '53': 'Suit Filed',
  '54': 'Suit Filed and Written-off',
  '55': 'Suit Filed and Settled',
  '56': 'Suit Filed and Post (WO) Settled',
  '57': 'Suit Filed and Account Sold',
  '58': 'Suit Filed and Written Off and Account Sold',
  '59': 'Suit Filed and Account Purchased',
  '60': 'Suit Filed and Account Purchased and Written Off',
  '61': 'Suit Filed and Account Purchased and Settled',
  '62': 'Suit Filed and Account Purchased and Restructured',
  '63': 'Suit Filed and Status Cleared',
  '64': 'Wilful Default and Restructured Loan',
  '65': 'Wilful Default and Restructured Loan (Govt. Mandated)',
  '66': 'Wilful Default and Settled',
  '67': 'Wilful Default and Post (WO) Settled',
  '68': 'Wilful Default and Account Sold',
  '69': 'Wilful Default and Written Off and Account Sold',
  '70': 'Wilful Default and Account Purchased',
  '72': 'Wilful Default and Account Purchased and Written Off',
  '73': 'Wilful Default and Account Purchased and Settled',
  '74': 'Wilful Default and Account Purchased and Restructured',
  '75': 'Wilful Default and Status Cleared',
  '76': 'Suit Filed (Wilful Default) and Restructured',
  '77': 'Suit Filed (Wilful Default) and Restructured Loan (Govt. Mandated)',
  '79': 'Suit Filed (Wilful Default) and Settled',
  '81': 'Suit Filed (Wilful Default) and Post (WO) Settled',
  '85': 'Suit Filed (Wilful Default) and Account Sold',
  '86': 'Suit Filed (Wilful Default) and Written Off and Account Sold',
  '87': 'Suit Filed (Wilful Default) and Account Purchased',
  '88': 'Suit Filed (Wilful Default) and Account Purchased and Written Off',
  '90': 'Suit Filed (Wilful Default) and Account Purchased and Restructured',
  '91': 'Suit Filed (Wilful Default) and Status Cleared',
  '94': 'Suit Filed (Wilful Default) and Account Purchased and Settled',
  '11': 'ACTIVE',
  '21': 'ACTIVE',
  '22': 'ACTIVE',
  '23': 'ACTIVE',
  '24': 'ACTIVE',
  '25': 'ACTIVE',
  '71': 'ACTIVE',
  '78': 'ACTIVE',
  '80': 'ACTIVE',
  '82': 'ACTIVE',
  '83': 'ACTIVE',
  '84': 'ACTIVE',
  '12': 'CLOSED',
  '13': 'CLOSED',
  '14': 'CLOSED',
  '15': 'CLOSED',
  '16': 'CLOSED',
  '17': 'CLOSED',
  '130': 'Restructured due to COVID-19',
  '131': 'Restructured due to Natural Calamity',
  '132': 'Post Write Off Closed',
  '133': 'Restructured & Closed',
  '134': 'Auctioned & Settled',
  '135': 'Repossessed & Settled',
  '136': 'Guarantee Invoked',
  '137': 'Entity Ceased while Account was Open',
  '138': 'Entity Ceased while Account was Closed',
};

export const experianInstitutionCode = {
  COB: 'Co-operative Bank',
  FOR: 'Foreign Bank',
  HFC: 'Housing Finance Company',
  NBF: 'Non-Banking Financial Institution',
  PUB: 'Public Sector Bank',
  PVT: 'Private Sector Bank',
  RRB: 'Regional Rural Bank',
  TEL: 'Telecom',
  SRC: 'Securities Firm',
  MFI: 'Microfinance Institutions',
  INS: 'Insurance Sector',
  CCS: 'Cooperative Credit Society',
  BRO: 'Brokerage Firm',
  CRA: 'Credit Rating Agency',
};

export const experianTelephoneType = {
  '00': 'Not Classified',
  '01': 'Mobile Phone',
  '02': 'Home Phone',
  '03': 'Office Phone',
};

export const experianAddressType = {
  '01': 'Permanent address',
  '02': 'Residence address',
  '03': 'Office address',
  '04': 'Not Categorised',
};

export const experianCollateralType = {
  '99': 'No Collateral',
  '11': 'Property',
  '12': 'Gold',
  '13': 'Shares',
  '14': 'Saving Account and Fixed Deposit',
  '15': 'Multiple Securities',
  '16': 'Others',
};

export const experianSuitFiled = {
  '00': 'No Suit Filed',
  '01': 'Suit Filed',
  '02': 'Wilful Default',
  '03': 'Suit Filed (Wilful Default)',
};

export const experianCreditFacilityStatus = {
  '00': 'Restructured Loan',
  '01': 'Restructured Loan (Govt. Mandated)',
  '02': 'Written-off',
  '03': 'Settled',
  '04': 'Post (WO) Settled',
  '05': 'Account Sold',
  '06': 'Written Off and Account Sold',
  '07': 'Account Purchased',
  '08': 'Account Purchased and Written Off',
  '09': 'Account Purchased and Settled',
  '10': 'Account Purchased and Restructured',
  '11': 'Restructured due to Natural Calamity',
  '12': 'Restructured due to COVID-19',
  '13': 'Post rite Off Closed',
  '14': 'Restructured & Closed',
  '15': 'Auctioned & Settled',
  '16': 'Repossessed & Settled',
  '17': 'Guarantee Invoked',
  '99': 'Clear Existing Status',
};

export const experianEnquiryReason = {
  '1': 'Agricultural Machinery',
  '2': 'Animal Husbandry',
  '3': 'Aquaculture',
  '4': 'Biogas Plant',
  '5': 'Crop Loan',
  '6': 'Horticulture',
  '7': 'Irrigation System',
  '8': 'New Car',
  '9': 'Overdraft against Car',
  '10': 'Used Car',
  '11': 'General',
  '12': 'Small & Medium Business',
  '13': 'Professionals',
  '14': 'Trade',
  '15': 'Bus',
  '16': 'Tempo',
  '17': 'Tipper',
  '18': 'Truck',
  '20': 'Forklift',
  '21': 'Wheel Loaders',
  '22': 'Consumer Search',
  '23': 'Credit Card',
  '24': 'Fleet Card',
  '25': 'For Working Executives',
  '26': 'Study Abroad',
  '27': 'Study in India',
  '28': 'Leasing',
  '29': 'Bank Deposits',
  '30': 'Gold',
  '31': 'Govt. Bonds / PPF / NSC / KVP / FD',
  '32': 'Shares and Mutual Funds',
  '33': 'Business Loan',
  '34': 'Housing Loan',
  '35': 'Personal Loan',
  '36': 'Agriculture',
  '37': 'General',
  '38': 'Small Business',
  '39': 'Computers / Laptops',
  '40': 'Consumer Durables',
  '41': 'Marriage / Religious Ceremonies',
  '42': 'Travel',
  '43': 'Balance Transfer',
  '44': 'Home Improvement / Extension',
  '45': 'Land',
  '46': 'Lease Rental Discounting',
  '47': 'Loan against Property',
  '48': 'New Home',
  '49': 'Office Premises',
  '50': 'Under construction',
  '51': 'Broadband',
  '52': 'Landline',
  '53': 'Mobile',
  '54': 'Three Wheeler',
  '55': 'Two Wheeler',
  '56': 'Cash credit facility',
  '57': 'Overdraft',
  '58': 'Term Loan',
  '60': 'Microfinance Detailed Report',
  '61': 'Summary Report',
  '62': 'VB OLM Retrieval Service',
  '63': 'Account Review',
  '64': 'Retro Enquiry',
  '65': 'Locate Plus',
  '66': 'Consumer Search Loan',
  '67': 'Indicative Report',
  '68': 'Consumer Search Loan',
  '69': 'Bank OLM Retrieval Service',
  '70': 'Adviser Liability',
  '71': 'Secured (Account Group for Portfolio Review response)',
  '72': 'Unsecured (Account Group for Portfolio Review response)',
  '99': 'Others',
};

export const experianPaymentHistory = {
  '0': '0',
  '1': '30',
  '2': '60',
  '3': '90',
  '4': '120',
  '5': '150',
  '6': '180',
};

const generateRandomScore = (Math.floor(Math.random() * 51) + 750).toString();

export const kExperianMockResponse = {
  INProfileResponse: {
    CAPS: {
      CAPS_Summary: {
        CAPSLast7Days: '0',
        CAPSLast30Days: '0',
        CAPSLast90Days: '0',
        CAPSLast180Days: '0',
      },
    },
    SCORE: {
      BureauScore: generateRandomScore,
      BureauScoreConfidLevel: '',
    },
    Header: {
      ReportDate: '********',
      ReportTime: '124754',
      SystemCode: '0',
      MessageText: '',
    },
    UserMessage: {
      UserMessageText: 'Normal Response',
    },
    CAIS_Account: {
      CAIS_Summary: {
        Credit_Account: {
          CreditAccountTotal: '4',
          CreditAccountActive: '3',
          CreditAccountClosed: '1',
          CreditAccountDefault: '0',
          CADSuitFiledCurrentBalance: '0',
        },
        Total_Outstanding_Balance: {
          Outstanding_Balance_All: '388100',
          Outstanding_Balance_Secured: '204100',
          Outstanding_Balance_UnSecured: '184000',
          Outstanding_Balance_Secured_Percentage: '53',
          Outstanding_Balance_UnSecured_Percentage: '47',
        },
      },
      CAIS_Account_DETAILS: [
        {
          Income: '',
          Open_Date: '********',
          Date_Closed: '',
          Account_Type: '02',
          CurrencyCode: 'INR',
          Date_Reported: '********',
          Account_Number: 'XXXXXXXXX5122',
          Account_Status: '11',
          DateOfAddition: '********',
          Payment_Rating: '?',
          Portfolio_Type: 'M',
          Terms_Duration: '',
          Amount_Past_Due: '',
          Current_Balance: '107000',
          Occupation_Code: '',
          Special_Comment: '',
          Subscriber_Name: 'icicibank',
          Terms_Frequency: '',
          Income_Indicator: '',
          Rate_of_Interest: '',
          Repayment_Tenure: '0',
          Consumer_comments: '',
          DefaultStatusDate: '',
          Settlement_Amount: '',
          Type_of_Collateral: '',
          WriteOffStatusDate: '',
          CAIS_Holder_Details: {
            Alias: '',
            Gender_Code: '1',
            Date_of_birth: '********',
            Income_TAX_PAN: 'XXXXXX350Y',
            Passport_Number: '',
            Voter_ID_Number: '',
            Surname_Non_Normalized: 'NENSI',
            First_Name_Non_Normalized: 'JOGANI',
            Middle_Name_1_Non_Normalized: '',
            Middle_Name_2_Non_Normalized: '',
            Middle_Name_3_Non_Normalized: '',
          },
          Credit_Limit_Amount: '',
          Subscriber_comments: '',
          Value_of_Collateral: '',
          CAIS_Account_History: [
            {
              Year: '2024',
              Month: '03',
              Days_Past_Due: '',
              Asset_Classification: '?',
            },
            {
              Year: '2024',
              Month: '02',
              Days_Past_Due: '',
              Asset_Classification: '?',
            },
            {
              Year: '2024',
              Month: '01',
              Days_Past_Due: '',
              Asset_Classification: '?',
            },
          ],
          Date_of_Last_Payment: '',
          LitigationStatusDate: '',
          AccountHoldertypeCode: '1',
          Identification_Number: '**********',
          Promotional_Rate_Flag: '',
          Written_Off_Amt_Total: '',
          CAIS_Holder_ID_Details: {
            EMailId: '',
            Income_TAX_PAN: 'XXXXXX350Y',
            PAN_Issue_Date: '',
            Passport_Number: '',
            Voter_ID_Number: '',
            Ration_Card_Number: '',
            PAN_Expiration_Date: '',
            Passport_Issue_Date: '',
            Universal_ID_Number: '',
            Voter_ID_Issue_Date: '',
            Driver_License_Number: '',
            Ration_Card_Issue_Date: '',
            Universal_ID_Issue_Date: '',
            Passport_Expiration_Date: '',
            Voter_ID_Expiration_Date: '',
            Driver_License_Issue_Date: '',
            Ration_Card_Expiration_Date: '',
            Universal_ID_Expiration_Date: '',
            Driver_License_Expiration_Date: '',
          },
          Payment_History_Profile: '????????????????????????????????????',
          SuitFiled_WilfulDefault: '01',
          CAIS_Holder_Phone_Details: {
            EMailId: '<EMAIL>',
            FaxNumber: '',
            Telephone_Type: '',
            Telephone_Number: 'XXXXXX0022',
            Telephone_Extension: '',
            Mobile_Telephone_Number: '',
          },
          Date_of_First_Delinquency: '',
          Written_Off_Amt_Principal: '',
          Income_Frequency_Indicator: '',
          Original_Charge_Off_Amount: '',
          Written_off_Settled_Status: '',
          CAIS_Holder_Address_Details: {
            City_non_normalized: 'MUMBAI',
            State_non_normalized: '27',
            CountryCode_non_normalized: 'IB',
            Residence_code_non_normalized: '',
            ZIP_Postal_Code_non_normalized: '400064',
            Address_indicator_non_normalized: '',
            Fifth_Line_Of_Address_non_normalized: '',
            First_Line_Of_Address_non_normalized: 'KIRAN KUNJ SOC',
            Third_Line_Of_Address_non_normalized: 'MUMBAI',
            Second_Line_Of_Address_non_normalized: 'BHADRAM NAGAR',
          },
          Value_of_Credits_Last_Month: '',
          Scheduled_Monthly_Payment_Amount: '',
          Highest_Credit_or_Original_Loan_Amount: '407000',
          SuitFiledWillfulDefaultWrittenOffStatus: '',
        },
        {
          Income: '',
          Open_Date: '********',
          Date_Closed: '********',
          Account_Type: '05',
          CurrencyCode: 'INR',
          Date_Reported: '********',
          Account_Number: 'XXXXXXXXX5121',
          Account_Status: '12',
          DateOfAddition: '********',
          Payment_Rating: '?',
          Portfolio_Type: 'I',
          Terms_Duration: '',
          Amount_Past_Due: '',
          Current_Balance: '97000',
          Occupation_Code: '',
          Special_Comment: '',
          Subscriber_Name: 'icicibank',
          Terms_Frequency: '',
          Income_Indicator: '',
          Rate_of_Interest: '',
          Repayment_Tenure: '0',
          Consumer_comments: '',
          DefaultStatusDate: '',
          Settlement_Amount: '',
          Type_of_Collateral: '',
          WriteOffStatusDate: '',
          CAIS_Holder_Details: {
            Alias: '',
            Gender_Code: '1',
            Date_of_birth: '********',
            Income_TAX_PAN: 'XXXXXX350Y',
            Passport_Number: '',
            Voter_ID_Number: '',
            Surname_Non_Normalized: 'NENSI',
            First_Name_Non_Normalized: 'JOGANI',
            Middle_Name_1_Non_Normalized: '',
            Middle_Name_2_Non_Normalized: '',
            Middle_Name_3_Non_Normalized: '',
          },
          Credit_Limit_Amount: '',
          Subscriber_comments: '',
          Value_of_Collateral: '',
          CAIS_Account_History: [
            {
              Year: '2024',
              Month: '03',
              Days_Past_Due: '',
              Asset_Classification: '?',
            },
            {
              Year: '2024',
              Month: '02',
              Days_Past_Due: '',
              Asset_Classification: '?',
            },
            {
              Year: '2024',
              Month: '01',
              Days_Past_Due: '',
              Asset_Classification: '?',
            },
          ],
          Date_of_Last_Payment: '',
          LitigationStatusDate: '',
          AccountHoldertypeCode: '1',
          Identification_Number: '**********',
          Promotional_Rate_Flag: '',
          Written_Off_Amt_Total: '',
          CAIS_Holder_ID_Details: {
            EMailId: '',
            Income_TAX_PAN: 'XXXXXX350Y',
            PAN_Issue_Date: '',
            Passport_Number: '',
            Voter_ID_Number: '',
            Ration_Card_Number: '',
            PAN_Expiration_Date: '',
            Passport_Issue_Date: '',
            Universal_ID_Number: '',
            Voter_ID_Issue_Date: '',
            Driver_License_Number: '',
            Ration_Card_Issue_Date: '',
            Universal_ID_Issue_Date: '',
            Passport_Expiration_Date: '',
            Voter_ID_Expiration_Date: '',
            Driver_License_Issue_Date: '',
            Ration_Card_Expiration_Date: '',
            Universal_ID_Expiration_Date: '',
            Driver_License_Expiration_Date: '',
          },
          Payment_History_Profile: '????????????????????????????????????',
          SuitFiled_WilfulDefault: '',
          CAIS_Holder_Phone_Details: {
            EMailId: '<EMAIL>',
            FaxNumber: '',
            Telephone_Type: '',
            Telephone_Number: 'XXXXXX0022',
            Telephone_Extension: '',
            Mobile_Telephone_Number: '',
          },
          Date_of_First_Delinquency: '',
          Written_Off_Amt_Principal: '',
          Income_Frequency_Indicator: '',
          Original_Charge_Off_Amount: '',
          Written_off_Settled_Status: '',
          CAIS_Holder_Address_Details: {
            City_non_normalized: 'MUMBAI',
            State_non_normalized: '27',
            CountryCode_non_normalized: 'IB',
            Residence_code_non_normalized: '',
            ZIP_Postal_Code_non_normalized: '400064',
            Address_indicator_non_normalized: '',
            Fifth_Line_Of_Address_non_normalized: '',
            First_Line_Of_Address_non_normalized: 'KIRAN KUNJ SOC',
            Third_Line_Of_Address_non_normalized: 'MUMBAI',
            Second_Line_Of_Address_non_normalized: 'BHADRAM NAGAR',
          },
          Value_of_Credits_Last_Month: '',
          Scheduled_Monthly_Payment_Amount: '',
          Highest_Credit_or_Original_Loan_Amount: '307000',
          SuitFiledWillfulDefaultWrittenOffStatus: '',
        },
        {
          Income: '',
          Open_Date: '********',
          Date_Closed: '',
          Account_Type: '03',
          CurrencyCode: 'INR',
          Date_Reported: '********',
          Account_Number: 'XXXXXXXXX5124',
          Account_Status: '11',
          DateOfAddition: '********',
          Payment_Rating: '?',
          Portfolio_Type: 'I',
          Terms_Duration: '',
          Amount_Past_Due: '',
          Current_Balance: '97100',
          Occupation_Code: '',
          Special_Comment: '',
          Subscriber_Name: 'icicibank',
          Terms_Frequency: '',
          Income_Indicator: '',
          Rate_of_Interest: '',
          Repayment_Tenure: '0',
          Consumer_comments: '',
          DefaultStatusDate: '',
          Settlement_Amount: '',
          Type_of_Collateral: '',
          WriteOffStatusDate: '',
          CAIS_Holder_Details: {
            Alias: '',
            Gender_Code: '1',
            Date_of_birth: '********',
            Income_TAX_PAN: 'XXXXXX350Y',
            Passport_Number: '',
            Voter_ID_Number: '',
            Surname_Non_Normalized: 'NENSI',
            First_Name_Non_Normalized: 'JOGANI',
            Middle_Name_1_Non_Normalized: '',
            Middle_Name_2_Non_Normalized: '',
            Middle_Name_3_Non_Normalized: '',
          },
          Credit_Limit_Amount: '',
          Subscriber_comments: '',
          Value_of_Collateral: '',
          CAIS_Account_History: [
            {
              Year: '2024',
              Month: '03',
              Days_Past_Due: '',
              Asset_Classification: '?',
            },
            {
              Year: '2024',
              Month: '02',
              Days_Past_Due: '',
              Asset_Classification: '?',
            },
            {
              Year: '2024',
              Month: '01',
              Days_Past_Due: '',
              Asset_Classification: '?',
            },
          ],
          Date_of_Last_Payment: '',
          LitigationStatusDate: '',
          AccountHoldertypeCode: '1',
          Identification_Number: '**********',
          Promotional_Rate_Flag: '',
          Written_Off_Amt_Total: '',
          CAIS_Holder_ID_Details: {
            EMailId: '',
            Income_TAX_PAN: 'XXXXXX350Y',
            PAN_Issue_Date: '',
            Passport_Number: '',
            Voter_ID_Number: '',
            Ration_Card_Number: '',
            PAN_Expiration_Date: '',
            Passport_Issue_Date: '',
            Universal_ID_Number: '',
            Voter_ID_Issue_Date: '',
            Driver_License_Number: '',
            Ration_Card_Issue_Date: '',
            Universal_ID_Issue_Date: '',
            Passport_Expiration_Date: '',
            Voter_ID_Expiration_Date: '',
            Driver_License_Issue_Date: '',
            Ration_Card_Expiration_Date: '',
            Universal_ID_Expiration_Date: '',
            Driver_License_Expiration_Date: '',
          },
          Payment_History_Profile: '????????????????????????????????????',
          SuitFiled_WilfulDefault: '',
          CAIS_Holder_Phone_Details: {
            EMailId: '<EMAIL>',
            FaxNumber: '',
            Telephone_Type: '',
            Telephone_Number: 'XXXXXX0022',
            Telephone_Extension: '',
            Mobile_Telephone_Number: '',
          },
          Date_of_First_Delinquency: '',
          Written_Off_Amt_Principal: '',
          Income_Frequency_Indicator: '',
          Original_Charge_Off_Amount: '',
          Written_off_Settled_Status: '02',
          CAIS_Holder_Address_Details: {
            City_non_normalized: 'MUMBAI',
            State_non_normalized: '27',
            CountryCode_non_normalized: 'IB',
            Residence_code_non_normalized: '',
            ZIP_Postal_Code_non_normalized: '400064',
            Address_indicator_non_normalized: '',
            Fifth_Line_Of_Address_non_normalized: '',
            First_Line_Of_Address_non_normalized: 'KIRAN KUNJ SOC',
            Third_Line_Of_Address_non_normalized: 'MUMBAI',
            Second_Line_Of_Address_non_normalized: 'BHADRAM NAGAR',
          },
          Value_of_Credits_Last_Month: '',
          Scheduled_Monthly_Payment_Amount: '',
          Highest_Credit_or_Original_Loan_Amount: '317000',
          SuitFiledWillfulDefaultWrittenOffStatus: '',
        },
        {
          Income: '',
          Open_Date: '********',
          Date_Closed: '',
          Account_Type: '10',
          CurrencyCode: 'INR',
          Date_Reported: '********',
          Account_Number: 'XXXXXXXXX5123',
          Account_Status: '11',
          DateOfAddition: '********',
          Payment_Rating: '?',
          Portfolio_Type: 'R',
          Terms_Duration: '',
          Amount_Past_Due: '',
          Current_Balance: '87000',
          Occupation_Code: '',
          Special_Comment: '',
          Subscriber_Name: 'icicibank',
          Terms_Frequency: '',
          Income_Indicator: '',
          Rate_of_Interest: '',
          Repayment_Tenure: '0',
          Consumer_comments: '',
          DefaultStatusDate: '',
          Settlement_Amount: '',
          Type_of_Collateral: '',
          WriteOffStatusDate: '',
          CAIS_Holder_Details: {
            Alias: '',
            Gender_Code: '1',
            Date_of_birth: '********',
            Income_TAX_PAN: 'XXXXXX350Y',
            Passport_Number: '',
            Voter_ID_Number: '',
            Surname_Non_Normalized: 'NENSI',
            First_Name_Non_Normalized: 'JOGANI',
            Middle_Name_1_Non_Normalized: '',
            Middle_Name_2_Non_Normalized: '',
            Middle_Name_3_Non_Normalized: '',
          },
          Credit_Limit_Amount: '150000',
          Subscriber_comments: '',
          Value_of_Collateral: '',
          CAIS_Account_History: [
            {
              Year: '2024',
              Month: '03',
              Days_Past_Due: '',
              Asset_Classification: '?',
            },
            {
              Year: '2024',
              Month: '02',
              Days_Past_Due: '',
              Asset_Classification: '?',
            },
            {
              Year: '2024',
              Month: '01',
              Days_Past_Due: '',
              Asset_Classification: '?',
            },
          ],
          Date_of_Last_Payment: '',
          LitigationStatusDate: '',
          AccountHoldertypeCode: '1',
          Identification_Number: '**********',
          Promotional_Rate_Flag: '',
          Written_Off_Amt_Total: '',
          CAIS_Holder_ID_Details: {
            EMailId: '',
            Income_TAX_PAN: 'XXXXXX350Y',
            PAN_Issue_Date: '',
            Passport_Number: '',
            Voter_ID_Number: '',
            Ration_Card_Number: '',
            PAN_Expiration_Date: '',
            Passport_Issue_Date: '',
            Universal_ID_Number: '',
            Voter_ID_Issue_Date: '',
            Driver_License_Number: '',
            Ration_Card_Issue_Date: '',
            Universal_ID_Issue_Date: '',
            Passport_Expiration_Date: '',
            Voter_ID_Expiration_Date: '',
            Driver_License_Issue_Date: '',
            Ration_Card_Expiration_Date: '',
            Universal_ID_Expiration_Date: '',
            Driver_License_Expiration_Date: '',
          },
          Payment_History_Profile: '????????????????????????????????????',
          SuitFiled_WilfulDefault: '',
          CAIS_Holder_Phone_Details: {
            EMailId: '<EMAIL>',
            FaxNumber: '',
            Telephone_Type: '',
            Telephone_Number: 'XXXXXX0022',
            Telephone_Extension: '',
            Mobile_Telephone_Number: '',
          },
          Date_of_First_Delinquency: '',
          Written_Off_Amt_Principal: '',
          Income_Frequency_Indicator: '',
          Original_Charge_Off_Amount: '',
          Written_off_Settled_Status: '',
          CAIS_Holder_Address_Details: {
            City_non_normalized: 'MUMBAI',
            State_non_normalized: '27',
            CountryCode_non_normalized: 'IB',
            Residence_code_non_normalized: '',
            ZIP_Postal_Code_non_normalized: '400064',
            Address_indicator_non_normalized: '',
            Fifth_Line_Of_Address_non_normalized: '',
            First_Line_Of_Address_non_normalized: 'KIRAN KUNJ SOC',
            Third_Line_Of_Address_non_normalized: 'MUMBAI',
            Second_Line_Of_Address_non_normalized: 'BHADRAM NAGAR',
          },
          Value_of_Credits_Last_Month: '',
          Scheduled_Monthly_Payment_Amount: '',
          Highest_Credit_or_Original_Loan_Amount: '207000',
          SuitFiledWillfulDefaultWrittenOffStatus: '',
        },
      ],
    },
    Match_result: {
      Exact_match: 'Y',
    },
    NonCreditCAPS: {
      NonCreditCAPS_Summary: {
        NonCreditCAPSLast7Days: '0',
        NonCreditCAPSLast30Days: '0',
        NonCreditCAPSLast90Days: '0',
        NonCreditCAPSLast180Days: '0',
      },
    },
    TotalCAPS_Summary: {
      TotalCAPSLast7Days: '0',
      TotalCAPSLast30Days: '0',
      TotalCAPSLast90Days: '0',
      TotalCAPSLast180Days: '0',
    },
    CreditProfileHeader: {
      Version: 'V2.4',
      ReportDate: '********',
      ReportTime: '124754',
      Subscriber: '',
      ReportNumber: '1740035874441',
      Subscriber_Name: 'Bureau Disclosure Report with Customized Match V3',
      Enquiry_Username: 'customized_match_v3__chinmayfinl~DS',
    },
    Current_Application: {
      Current_Application_Details: {
        Enquiry_Reason: '6',
        Amount_Financed: '0',
        Finance_Purpose: '',
        Current_Other_Details: {
          Income: '0',
          Marital_Status: '',
          Employment_Status: '',
          Time_with_Employer: '',
          Number_of_Major_Credit_Card_Held: '',
        },
        Duration_Of_Agreement: '0',
        Current_Applicant_Details: {
          EMailId: '',
          Last_Name: '',
          First_Name: 'Nensi Jogani',
          Gender_Code: '',
          IncomeTaxPan: '',
          Middle_Name1: '',
          Middle_Name2: '',
          Middle_Name3: '',
          PAN_Issue_Date: '',
          Telephone_Type: '',
          Passport_number: '',
          MobilePhoneNumber: '**********',
          Ration_Card_Number: '',
          PAN_Expiration_Date: '',
          Passport_Issue_Date: '',
          Telephone_Extension: '',
          Universal_ID_Number: '',
          Voter_ID_Issue_Date: '',
          Driver_License_Number: '',
          Voter_s_Identity_Card: '',
          Ration_Card_Issue_Date: '',
          Date_Of_Birth_Applicant: '',
          Universal_ID_Issue_Date: '',
          Passport_Expiration_Date: '',
          Voter_ID_Expiration_Date: '',
          Driver_License_Issue_Date: '',
          Ration_Card_Expiration_Date: '',
          Universal_ID_Expiration_Date: '',
          Driver_License_Expiration_Date: '',
          Telephone_Number_Applicant_1st: '',
        },
        Current_Applicant_Address_Details: {
          City: '',
          State: '',
          PINCode: '',
          Landmark: '',
          Country_Code: 'IB',
          BldgNoSocietyName: '',
          FlatNoPlotNoHouseNo: '',
          RoadNoNameAreaLocality: '',
        },
        Current_Applicant_Additional_AddressDetails: '',
      },
    },
  },
};
