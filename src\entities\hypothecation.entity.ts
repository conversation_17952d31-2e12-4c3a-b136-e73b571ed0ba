import { Column, DataType, Model, Table } from 'sequelize-typescript';
@Table({})
export class HypothecationEntity extends Model<HypothecationEntity> {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    allowNull: false,
  })
  id: number;

  @Column({
    type: DataType.STRING(128),
    allowNull: false,
  })
  lenderName: string;

  @Column({
    type: DataType.DOUBLE,
    allowNull: false,
  })
  loanAmount: number;

  @Column({
    type: DataType.DOUBLE,
    allowNull: false,
  })
  hypothecatedAmount: number;

  @Column({
    type: DataType.DOUBLE,
    allowNull: false,
  })
  minLoanAmount: number;

  @Column({
    type: DataType.DOUBLE,
    allowNull: false,
  })
  outStandingAmount: number;

  @Column({
    type: DataType.DOUBLE,
    allowNull: false,
  })
  maxLoanAmount: number;

  @Column({
    type: DataType.DOUBLE,
    allowNull: false,
  })
  hypothecatedpercentage: number;

  @Column({
    type: DataType.SMALLINT,
    allowNull: false,
  })
  dayOfEMI: number; //emi day on every month

  @Column({
    type: DataType.SMALLINT,
    allowNull: false,
  })
  loanTenure: number; //loan tenure in months

  @Column({
    type: DataType.DOUBLE,
    allowNull: false,
  })
  approvedPrincipalAmount: number;

  @Column({
    type: DataType.DOUBLE,
    allowNull: false,
  })
  emiAmount: number;

  @Column({
    type: DataType.DOUBLE,
    allowNull: false,
  })
  repaymentPercentage: number;

  @Column({
    type: DataType.SMALLINT,
    allowNull: false,
  })
  max_dpd: number;

  @Column({
    type: DataType.ARRAY(DataType.STRING),
    allowNull: true,
  })
  exception_state: number;

  @Column({
    type: DataType.DOUBLE,
    allowNull: false,
  })
  interestAmount: number; //as per repayment

  @Column({
    type: DataType.SMALLINT,
    allowNull: true,
  })
  adminId: number;

  @Column({ type: DataType.TEXT, allowNull: false })
  repaymentSchedule: string;

  @Column({ type: DataType.TEXT, allowNull: true })
  lenderDoc: string;
}
