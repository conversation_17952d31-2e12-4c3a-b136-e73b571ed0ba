// Imports
import { Controller, Get, Query, Redirect, Res } from '@nestjs/common';
import { Response } from 'express';
import { ZohoService } from './zoho.service';
import { kInternalError, kSuccessData } from 'src/constants/responses';

@Controller('thirdParty/zoho')
export class ZohoController {
  constructor(private readonly service: ZohoService) {}

  @Get('callback')
  async handleZohoCallback(@Query('code') code: string, @Res() res: Response) {
    try {
      const data = await this.service.exchangeCodeForToken(code);
      if (data?.message) return res.send(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }
}
