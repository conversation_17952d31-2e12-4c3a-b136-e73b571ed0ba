import { Injectable } from '@nestjs/common';
import {
  nConvertBase64ToPdf,
  nInsertLog,
  nPrepareWebViewData,
} from 'src/constants/network';
import { kParamMissing } from 'src/constants/responses';
import { DateService } from 'src/utils/date.service';

@Injectable()
export class AXISService {
  constructor(private readonly dateService: DateService) { }

  getAXISNetbanking(reqData) {
    let currentDate = new Date();
    currentDate.setMonth(currentDate.getMonth() + 1);
    const todayDateInfo = this.dateService.formateMonthDate(currentDate);
    currentDate.setDate(currentDate.getDate() - 120);
    currentDate.setMonth(currentDate.getMonth() - 1);
    // Set fromDate to 6 months ago
    const fromDateInfo = this.dateService.formateMonthDate(currentDate);
    return {
      title: 'Verify your bank',
      initialLoader: true,
      initialURL: 'https://omni.axisbank.co.in/axisretailbanking/',
      type: 'BANK',
      jsTriggers: {
        'https://omni.axisbank.co.in/axisretailbanking/': {
          onLoadStart: {
            state: { isLoader: true, isProcessing: true },
            triggers: [
              `var getLoginInterval= setInterval(() => {
              const gotToLogin = document.getElementById("loginAgainGoTo");
              console.log("Go LOGIN AGAIN",gotToLogin)
                            if(gotToLogin){
                              gotToLogin.click()
                              console.log("startProcessing")
                              clearInterval(getLoginInterval);
                            }
                },1000);                        
           `,
              `var checkError= setInterval(() => {
                  const popupContainer=document.querySelector(".cdk-overlay-container")
                  if(popupContainer){
                  const popup=document.getElementsByTagName("mat-dialog-container")
                          if(popup.length>0){    
                            if(popup[0]?.innerText.includes('Error Message')){
                            console.log("stopProcessing")
                            clearInterval(checkError);
                            
                           }
                          if(popup[0]?.innerText.includes('Change Password')) {
                          clearInterval(checkError)
                          console.log("stopProcessing")
                          }   

                                    
                          createLink=document.createElement("a")
                          createLink.textContent = "Close"; // Set the link text
                          createLink.href = "#"; 
                          createLink.onclick = function(event) {
                                event.preventDefault(); 
                                window.location.reload()
   
                            };
                            popup[0].appendChild(createLink)}
                                       
                          }
              },1000);`,
              `
              var checkLogoutInterval= setInterval(() => {
                try{
                     const buttonREgister=document.querySelectorAll("button")
                           if(buttonREgister){
                           for(let index=0;index<buttonREgister.length;index++){
                           let each=buttonREgister[index]                               
                            if(each.innerText=='Register') {
                            each.style.display='none'
                            cleartInterval(checkLogoutInterval)
                            }

                          }
                           }
                     }catch(error){}     
             },1000)`,
            ],
          },
          count: 1,
          onLoadStop: {
            state: { isLoader: false, isProcessing: false },
            triggers: [
              `var evaluateStep = {}`,
              `function waitForCondition(conditionFn, intervalTime = 2000) {
           return new Promise((resolve) => {
        const interval = setInterval(() => {
          if (conditionFn()) {
            clearInterval(interval);
            resolve();
          }
        }, intervalTime);
      });
    };`,
              `function delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
    } `,
              `function hideElement() {
      console.log("LOADER-> #01");
      const loginCard = document.querySelector(".login-card");
      if (loginCard) {
        try {
          const otherFeilds = document.getElementsByTagName("app-staticfields")
          otherFeilds[0].style.display = 'none'
          const offerFeilds = document.getElementsByTagName("app-prelogin-offers")
          offerFeilds[0].style.display = 'none'
          const quetionFeilds = document.getElementsByTagName("app-qr-section")
          quetionFeilds[0].style.display = 'none'
          const footerFeilds = document.getElementsByTagName("app-new-footer")
          footerFeilds[0].style.display = 'none'

          document.querySelector("#hamburger").style.display = 'none';
          document.querySelectorAll(".mod-btn").forEach(each => {
            each.style.display = 'none';
          });
          const forCud = document.querySelector("#GCIDINP");
          forCud.style.display = 'none';
          const forgotPassBTN = document.querySelectorAll("#FGTPASS");
          for (let index = 0; index < forgotPassBTN.length; index++) {
            forgotPassBTN[index].style.display = 'none';
          }

          function findElement(tagName, text) {
            try {
              const elements = document.getElementsByTagName(tagName);
              for (let element of elements) {
                if (element.innerText.trim() === text) {
                  return element; // Return the first matching element
                }
              }
              return null; // Return null if no match is found
            } catch (error) {
              return null;
            }
          }

          const loginText = findElement("div", "Login directly to");
          loginText.style.display = 'none';
          const loginFooter = document.querySelector(".loginFooter");
          loginFooter.style.display = "none";
          document.querySelectorAll(".mat-select-trigger").forEach(each => each.style.display = 'none');
          document.querySelector(".keybox").style.display = 'none';
        } catch (error) { }
        console.log("stopProcessing")
        const loginButton = document.getElementById("APLOGIN");
        if (loginButton) {
          loginButton.addEventListener("click", () => {
              console.log("startProcessing")
              
          });
        }
        loginCard.querySelector(".mat-form-field-type-mat-select").style.display = 'none'
        evaluateStep['0'] = true;

      }
    } `,
              `var checkLogoutLoadedElement= setInterval(() => {
               let is_login = sessionStorage.getItem('loggedIn');
               if(is_login){
               console.log("startProcessing");
               clearInterval(checkLogoutLoadedElement);
              }
               },500);`,
              ,
              `function checkLoginLoad() {
      return waitForCondition(() => {
          const loginCard = document.querySelector(".login-card");
        if (loginCard) {
          console.log("stopProcessing")
          hideElement()
          return true
        }
        return false
      });
    }; `,

              `
      var checkChangePassword= setInterval(() => {
                 const button=document.getElementsByClassName("mat-raised-button")
                 for(let i=0;i<button.length;i++){
                  const btn=button[0]
                  const innerText=(btn.innerText).toLowerCase()
                  if(innerText=='change password' || innerText=='logout'){
                    console.log("stopProcessing")
                    btn.remove()
                    clearInterval(checkChangePassword);
                }
              }
          },500);

              (async function () {
        function clickOnAccounts() {
          return waitForCondition(() => {
           if (evaluateStep['1']) return true;
           const relationSection = document.querySelector(".relation-sec")
           if (!relationSection) return false

           const accountText = relationSection.querySelector(".text-16")
          if (!accountText || accountText.innerText != 'ACCOUNTS') return false
          evaluateStep['1'] = true;
          console.log("LOADER-> #02");
          clearInterval(checkChangePassword);
          accountText.click();
          return true; // Once clicked, condition met
          return false; // Continue waiting if condition not met
        });
      };

      function clickOnStatementsTab() {

        return waitForCondition(() => {
          if (evaluateStep['2']) return true;
          const accountTabs = document.querySelector(".accounttabs")
          if (!accountTabs) return false

          const statmentTabList = accountTabs.querySelectorAll('.mat-tab-label');
          if (!statmentTabList || (statmentTabList ?? []).length == 0) return false
          let clicked = false;

          (statmentTabList ?? []).forEach((each) => {
            const innerTextTab = (each?.innerText ?? '').toLowerCase();
            if (innerTextTab.includes('statements')) {
              evaluateStep['2'] = true;
              each.click();
              console.log("LOADER-> #03");
              clicked = true; // Mark as clicked
            }
          });
          return clicked; // Stop interval if condition met
        });
      };

      function clickOnSelectForm() {
        return waitForCondition(() => {
          const selectForm = document.getElementsByClassName('mat-form-field-infix');
          if (selectForm[1]) {

            evaluateStep['3'] = true;
            selectForm[1].click();
            return true; // Form clicked
          }
          return false; // Continue waiting if form not found
        });
      };

      function clickOnDetailedStatements() {
        if (evaluateStep['4']) return true;
        return waitForCondition(() => {
          const matOptions = document.querySelectorAll('mat-option');
          let clicked = false;
          matOptions.forEach((each) => {
            const innerText = (each?.innerText ?? '').toLowerCase();
            if (innerText.includes('detailed statements')) {

              evaluateStep['4'] = true;
              console.log("LOADER-> #04");
              each.click();
              clicked = true;
            }
          });
          return clicked; // Stop interval if condition met
        });
      };

      function setDateFields() {
        return waitForCondition(() => {
          const fromDate = document.querySelector('#state_fromdate');
          const toDate = document.querySelector('#state_todate');
          if (fromDate && toDate) {

            toDate.value = "${todayDateInfo}"
            fromDate.value = "${fromDateInfo}"
            const event = new Event('input', { bubbles: true });
            fromDate.dispatchEvent(event);
            toDate.dispatchEvent(event);
            evaluateStep['5'] = true;
            return true; // Date fields set successfully
            
          }
          return false; // Continue waiting if the fields are not found
        });
      };

      function clickOnGoButton() {
        return waitForCondition(() => {
          const goButton = document.getElementById('go');

          if (goButton) {
            goButton.click(); // Click the button
            evaluateStep['6'] = true;
            return true; // Condition met
          }
          return false; // Continue waiting if button not found
        });
      };

      function clickOnSelectDownloadForm() {
        return waitForCondition(() => {
          const downloadSelect = document.querySelector('#btmDownload');

          if (downloadSelect) {
            downloadSelect.click(); // Click the button
            evaluateStep['7'] = true;
            console.log("LOADER-> #05");
            return true; // Condition met
          }
          return false;
        });
      };

      function selectPDF() {
        return waitForCondition(() => {
          const selectPDFOptions = document.getElementsByTagName('mat-option');
          for (let index = 0; index < selectPDFOptions.length; index++) {
            const eachSelect = selectPDFOptions[index];
            const innerText = (eachSelect.innerText ?? '').toLowerCase().trim();
            if (innerText == "pdf") {             
              eachSelect.click(); 
              evaluateStep['8'] = true;
              return true; // Condition met, stop waiting
            }
          }
          return false; // Continue waiting if "PDF" not found
        });
      };

      function downLoadStatement() {
        return waitForCondition(() => {
          const downloadPDFSelector = document.getElementsByClassName('download')[1];
          if (downloadPDFSelector) {
            downloadPDFSelector.querySelector('a').click();
            evaluateStep['9'] = true;
            console.log("LOADER-> #6");
            return true; // Condition met
          }
          return false; // Continue waiting if button not found
        }, 1000);
      };

      const executeAllSteps = async () => {
        if (!evaluateStep['1']) await clickOnAccounts()
        if (!evaluateStep['2']) await clickOnStatementsTab()
        if (!evaluateStep['3']) await clickOnSelectForm()
        if (!evaluateStep['4']) await clickOnDetailedStatements()
        if (!evaluateStep['5']) await setDateFields()
        if (!evaluateStep['6']) await clickOnGoButton()
        if (!evaluateStep['7']) await clickOnSelectDownloadForm()
        if (!evaluateStep['8']) await selectPDF()
        if (!evaluateStep['9']) await downLoadStatement()

      };
      try {
        await checkLoginLoad()
      } catch (error) { }
      await executeAllSteps();
    })(); `,
            ],
          },

          allowAnyConsole: true,
          consoles: [
            {
              combinations: ['startProcessing'],
              state: { isProcessing: true, isLoader: true },
            },
            {
              combinations: ['stopProcessing'],
              state: {
                isProcessing: false,
                isLoader: false,
                isNetbankingLoader: false,
              },
            },
            {
              // Metrics #01
              apiTriggers: [
                {
                  url: nInsertLog,
                  method: 'POST',
                  needWebResponse: false,
                  body: {
                    loanId: reqData.loanId,
                    userId: reqData.userId,
                    type: 1,
                    subType: 1,
                    status: 2,
                    values: { bankCode: 'AXIS', step: 1 },
                  },
                },
              ],
              // Loader #2
              combinations: ['LOADER-> #01'],
            },
            {
              apiTriggers: [
                {
                  url: nInsertLog,
                  method: 'POST',
                  needWebResponse: false,
                  body: {
                    loanId: reqData.loanId,
                    userId: reqData.userId,
                    type: 1,
                    subType: 1,
                    status: 2,
                    values: { bankCode: 'AXIS', step: 2 },
                  },
                },
              ],
              // Loader #2
              jsTriggers: [`LITT_SKIP_JS_LOADER_PERC_20`],
              combinations: ['LOADER-> #02'],
            },
            {
              apiTriggers: [
                {
                  url: nInsertLog,
                  method: 'POST',
                  needWebResponse: false,
                  body: {
                    loanId: reqData.loanId,
                    userId: reqData.userId,
                    type: 1,
                    subType: 1,
                    status: 2,
                    values: { bankCode: 'AXIS', step: 3 },
                  },
                },
              ],
              // Loader #2
              jsTriggers: [`LITT_SKIP_JS_LOADER_PERC_30`],
              combinations: ['LOADER-> #03'],
            },
            {
              apiTriggers: [
                {
                  url: nInsertLog,
                  method: 'POST',
                  needWebResponse: false,
                  body: {
                    loanId: reqData.loanId,
                    userId: reqData.userId,
                    type: 1,
                    subType: 1,
                    status: 2,
                    values: { bankCode: 'AXIS', step: 4 },
                  },
                },
              ],
              // Loader #4
              jsTriggers: [`LITT_SKIP_JS_LOADER_PERC_40`],
              combinations: ['LOADER-> #04'],
            },
            {
              apiTriggers: [
                {
                  url: nInsertLog,
                  method: 'POST',
                  needWebResponse: false,
                  body: {
                    loanId: reqData.loanId,
                    userId: reqData.userId,
                    type: 1,
                    subType: 1,
                    status: 2,
                    values: { bankCode: 'AXIS', step: 5 },
                  },
                },
              ],
              jsTriggers: [`LITT_SKIP_JS_LOADER_PERC_60`],
              // Loader #4
              combinations: ['LOADER-> #05'],
            },
            {
              apiTriggers: [
                {
                  url: nInsertLog,
                  method: 'POST',
                  needWebResponse: false,
                  body: {
                    loanId: reqData.loanId,
                    userId: reqData.userId,
                    type: 1,
                    subType: 1,
                    status: 2,
                    values: { bankCode: 'AXIS', step: 6 },
                  },
                },
              ],
              // Loader #4
              jsTriggers: [`LITT_SKIP_JS_LOADER_PERC_80`],
              combinations: ['LOADER-> #06'],
            },
            {
              apiTriggers: [
                {
                  url: nInsertLog,
                  method: 'POST',
                  needWebResponse: false,
                  body: {
                    loanId: reqData.loanId,
                    userId: reqData.userId,
                    type: 1,
                    subType: 1,
                    status: 2,
                    values: { bankCode: 'AXIS', step: 7 },
                  },
                },
              ],
              // Loader #4
              combinations: ['LOADER-> #07'],
            },
            {
              apiTriggers: [
                {
                  url: nConvertBase64ToPdf,
                  method: 'POST',
                  body: {
                    bankCode: 'AXIS',
                    loanId: reqData.loanId,
                    userId: reqData.userId,
                    type: 'REQUEST__BASE64__',
                  },
                  needWebResponse: true,
                },
              ],
              combinations: ['__ON_DOWNLOAD_COMPLETE__'],
            },
          ],
        },
      },
    };
  }

  private getRelevantLoaderSteps(totalSteps: number) {
    try {
      totalSteps = +totalSteps;
      const loaderSteps = [];
      for (let i = 0; i < totalSteps; i++) {
        try {
          if (i == totalSteps - 1) {
            loaderSteps.push(100);
          } else {
            loaderSteps.push(Math.floor(100 / totalSteps) * (i + 1));
          }
        } catch (error) { }
      }
      return loaderSteps;
    } catch (error) { }
  }
}
