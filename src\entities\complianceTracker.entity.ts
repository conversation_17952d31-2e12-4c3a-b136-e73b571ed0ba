import { values } from 'pdf-lib';
import { ForeignKey, Model, Table } from 'sequelize-typescript';
import { Column, DataType } from 'sequelize-typescript';
import { admin } from './admin.entity';

@Table({})
export class complianceTracker extends Model<complianceTracker> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ForeignKey(() => admin)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  preparer: number;

  @ForeignKey(() => admin)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  reviewer: number;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  fileUrl: any;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  preparerRemark: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  reviewerRemark: string;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  reviewDate: Date;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  category: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    values: ['0', '1', '2'],
    comment:'0-Pending  1-Accepted 2-Rejected',

  })
  status: string   

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  uploadedMonth: string
}