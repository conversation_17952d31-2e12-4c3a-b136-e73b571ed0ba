// Imports
import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Res,
  UseGuards,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import {
  kBadRequest,
  kInternalError,
  kParamsMissing,
  kSuccessData,
} from 'src/constants/responses';
import { EligibilitySharedService } from './eligibility.shared.service';
import { MigrationSharedService } from './migration.service';
import { UserServiceV4 } from 'src/v4/user/user.service.v4';
import { DevOpsGuard, SensitiveGuard } from 'src/authentication/auth.guard';
import { CommonSharedService } from './common.shared.service';
import { EmiSharedService } from './emi.service';
import { ContactSharedService } from './contact.service';
import { k500Error } from 'src/constants/misc';
import { SharedTransactionService } from './transaction.service';
import { UserSharedService } from './user.share.service';
import { UserService } from 'src/admin/user/user.service';
import { CalculationSharedService } from './calculation.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { kUploadFileObj } from 'src/constants/objects';
import { CibilService } from './cibil.service';
import { FileService } from 'src/utils/file.service';
import { allTriggers, TriggerService } from './trigger.service';
import { ErrorContextService } from 'src/utils/error.context.service';
import { AlertSharedService } from './alert.service';

@Controller('shared')
export class SharedController {
  constructor(
    private readonly eligibility: EligibilitySharedService,
    private readonly migration: MigrationSharedService,
    private readonly sharedEMI: EmiSharedService,
    private readonly userService: UserServiceV4,
    private readonly commonService: CommonSharedService,
    private readonly sharedContacts: ContactSharedService,
    private readonly sharedTransaction: SharedTransactionService,
    private readonly sharedUser: UserSharedService,
    private readonly adminUserService: UserService,
    private readonly calculation: CalculationSharedService,
    private readonly cibilService: CibilService,
    private readonly fileService: FileService,
    private readonly triggerService: TriggerService,
    private readonly errorContextService: ErrorContextService,
    private readonly alertSharedService: AlertSharedService,
  ) {}

  @Get('getUserIdSeries')
  async funGetUserIdSeries(@Query() query, @Res() res) {
    try {
      //Params validation
      const userId: string = query?.userId;
      if (!userId) return res.json(kParamsMissing);
      const data = await this.alertSharedService.getPgSeriesFromUUID(userId);
      if (data == null) return res.json(kInternalError);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Post('removeFromCoolOff')
  async funRemoveFromCoolOff(@Body() body, @Res() res) {
    try {
      const data: any = await this.adminUserService.removeFromCoolOff(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Post('validateStateWiseEligibility')
  async funValidateStateWiseEligibility(@Body() body, @Res() res) {
    try {
      const data: any = await this.eligibility.validateStateWiseEligibility(
        body,
      );
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Post('checkInterestRate')
  async funCheckInterestRate(@Body() body, @Res() res) {
    try {
      const data: any = await this.commonService.getEligibleInterestRate(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  //#region addOrUpdateStaticConfig
  @Post('addOrUpdateStaticConfig')
  async addOrUpdateStaticConfig(@Body() body, @Res() res) {
    try {
      const data: any = await this.commonService.addOrUpdateStaticConfig(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  //#endregion
  @Get('migrateMasterLoanRejactByBank')
  async migrateMasterLoanRejactByBank(@Query() query, @Res() res) {
    try {
      const data: any = await this.migration.migrateMasterLoanRejactByBank(
        query,
      );
      if (data?.message) return res.send(data);

      if (data?.userIds) {
        for (let index = 0; index < data?.userIds?.length; index++) {
          if (index % 100 == 0) console.log(index);
          await this.userService.routeDetails({
            id: data?.userIds[index],
          });
        }
      }
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  //#region refresh insurance array value
  @Get('refreshInsuranceArray')
  async refreshInsuranceArray(@Query() query, @Res() res) {
    try {
      const planId = query?.planId;
      const data: any = await this.commonService.refreshInsuranceArray(planId);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  //#region Scoring

  //#region Scoring
  @Get('calculateScore')
  async funCalculateScore(@Query() query, @Res() res) {
    try {
      const data: any = await this.eligibility.calculateScore(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }
  //#endregion

  @UseGuards(SensitiveGuard)
  @Post('changeScoreJson')
  async funChangeScoreJson(@Body() body, @Res() res) {
    try {
      const data: any = await this.eligibility.changeScoreJson(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }
  //#endregion

  @Post('refreshUserStage')
  async funRefreshUserStage(@Body() body, @Res() res) {
    try {
      const data: any = await this.migration.refreshUserStage(body);
      if (data?.message) return res.send(data);

      if (data?.userIds) {
        for (let index = 0; index < data?.userIds?.length; index++) {
          if (index % 100 == 0) console.log(index);
          const response = await this.userService.routeDetails({
            id: data?.userIds[index],
            // isAdminReq: true
          });
          if (response.message) console.log({ id: data?.userIds[index] });
          console.log(response?.continueRoute);
        }
      }

      return res.send(kSuccessData);
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Get('getTansactionQueryParams')
  async getTansactionQueryParams(@Query() query, @Res() res) {
    try {
      const data: any = await this.commonService.getTansactionQueryParams(
        query?.loanId,
        query?.isCheckAddional ?? false,
      );
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Post('checkNReVerifyBank')
  async funCheckNReVerifyBank(@Body() body, @Res() res) {
    try {
      const data: any = await this.migration.checkNReVerifyBank(body);
      if (data?.userIds) {
        for (let index = 0; index < data?.userIds?.length; index++) {
          if (index % 100 == 0) console.log(index);
          const response = await this.userService.routeDetails({
            id: data?.userIds[index],
            // isAdminReq: true
          });
          if (response.message) console.log({ id: data?.userIds[index] });
          console.log(response?.continueRoute);
        }
      }
    } catch (error) {}
  }

  @Post('refreshCalculation')
  async funRefreshCalculation(@Body() body, @Res() res) {
    try {
      const data: any = await this.sharedEMI.refreshCalculation(body.loanId);
      if (data.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Post('migrateVerifiedSalaryDate')
  async funMigrateVerifiedSalaryDate(@Res() res) {
    try {
      await this.migration.migrateVerifiedSalaryDate();
      return res.send(kSuccessData);
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  // migration api for adminId to FollowerId in Transaction
  @Post('oldCrmToLastCrmInUser')
  async funOldCrmToLastCrmInUser(@Res() res) {
    try {
      const data: any = await this.migration.oldCrmToLastCrmInUser();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Get('migrateFollowerIdInTransaction')
  async migrateFollowerIdInTransaction(@Query() query, @Res() res) {
    try {
      const data = await this.migration.migrateFollowerIdInTransaction(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Get('migrateTansactionCompletionDate')
  async migrateTansactionCompletionDate(@Body() body, @Res() res) {
    try {
      const data = await this.migration.migrateTansactionCompletionDate();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  // migrate not updated WorkMail Status
  @Get('migrateWorkMailStatus')
  async migrateWorkMailStatus(@Res() res) {
    try {
      const data: any = await this.migration.migrateWorkMailStatus();
      if (data?.message) return res.send(data);
      return res.send(kSuccessData);
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Post('syncRemainingContacts')
  async funSyncRemainingContacts(@Res() res) {
    try {
      await this.sharedContacts.syncRemainingContacts();
      return res.send(kSuccessData);
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  // SENSITIVE TEST API
  @UseGuards(DevOpsGuard)
  @Post('checkLoanEligibility')
  async funCheckLoanEligibility(@Body() body, @Res() res) {
    try {
      const data: any = await this.eligibility.checkLoanEligibility(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Post('resetAffectedUsers')
  async funResetAffectedUsers(@Res() res) {
    try {
      const data: any = await this.migration.resetAffectedUsers();
      if (data?.message) return res.send(data);
      if (data?.userIds) {
        for (let index = 0; index < data?.userIds?.length; index++) {
          if (index % 100 == 0) console.log(index);
          const response = await this.userService.routeDetails({
            id: data?.userIds[index],
          });
          if (response.message) console.log({ id: data?.userIds[index] });
        }
      }
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Get('checkCFOrder')
  async funCheckCFOrder(@Query() query, @Res() res) {
    try {
      //Params validation
      const loanId: number = query?.loanId;
      if (!loanId) return res.json(kParamsMissing);
      const checkAllPending = query?.checkAllPending ?? false;
      const data = await this.sharedTransaction.checkCFOrder(
        loanId,
        checkAllPending,
      );
      if (data == k500Error) return res.json(kInternalError);
      else if (data == false) return res.json(kBadRequest);
      return res.send({ ...kSuccessData, data });
    } catch (error) {}
  }

  @Post('migrateRejectReason')
  async funMigrateRejectReason(@Body() body, @Res() res) {
    try {
      const data: any = await this.migration.migrateRejectReason();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  // migrate old user's categorization due to employement startdate missing
  @Post('migrateUserCategorization')
  async migrateUserCategorization(@Res() res) {
    try {
      const data: any = await this.migration.migrateUserCategorization();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  //sgst and cgst migration
  @Post('migrateCgstAndSgstAmount')
  async funMigrateCgstAndSgstAmount(@Res() res) {
    try {
      const data: any = await this.migration.migrateCgstAndSgstAmount();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  //migration kyc lat log
  @Post('migrateKYCLatLongToPoint')
  async migrateKYCLatLongToPoint(@Res() res) {
    try {
      const data: any = await this.migration.migrateKYCLatLongToPoint();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  //migrate delete error files
  @Post('migrateDeleteErrorFiles')
  async funMigrateDeleteErrorFiles(@Res() res) {
    try {
      const data: any = await this.migration.migrateDeleteErrorFiles();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }
  //migrate loanCompletionDate
  @Post('migrateLoanCompletionDate')
  async funMigrateLoanCompletionDate(@Res() res) {
    try {
      const data: any = await this.migration.migrateLoanCompletionDate();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  // check stuck user's RouteDetails (ex.after disbursement)
  @Get('checkStuckRouteDetails')
  async funCheckStuckRouteDetails(@Query() query, @Res() res) {
    try {
      const data: any = await this.sharedUser.checkStuckRouteDetails(query);
      if (data?.message) return res.json(data);
      if (data.length > 0) {
        for (let index = 0; index < data.length; index++) {
          try {
            const id = data[index];
            if (index % 100 == 0) console.log(index);
            const response: any = await this.userService.routeDetails({ id });
            if (response?.message) console.log('Route Error', id);
            console.log(response?.continueRoute);
          } catch (error) {}
        }
      }
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  //migrate loan Unbalanced status
  @Post('migrateLoanBalancedStatus')
  async funMigrateLoanBalancedStatus(@Body() body, @Res() res) {
    try {
      const data: any = await this.migration.migrateLoanBalancedStatus(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  //migrate  aadhaarResponse column address related keys to aadhaarAddress column in KYCentities
  @Get('migrateAadhaarResponseToAadhaarAddress')
  async funMigrateAadhaarResponseToAadhaarAddress(@Res() res) {
    try {
      const data: any =
        await this.migration.migrateAaddharResponseToAadhaarAddress();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  //migrate user from blacklist and cooloff to active for company blaclist
  @Get('migrateCoolOffUSer')
  async migrateCoolOffUSer(@Res() res) {
    try {
      const data: any = await this.migration.migrateCoolOffUSer();
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }
  //#endregion

  @Post('sendDisbursementLimitRaisedWhatsAppMsg')
  async funSendDisbursementLimitRaisedWhatsAppMsg(@Res() res) {
    try {
      const data: any =
        await this.commonService.sendDisbursementLimitRaisedWhatsAppMsg();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  //Send notification and whatsapp message when CoolOff is over
  @Get('completionOfCoolOffUSer')
  async completionOfCoolOffUSer(@Res() res) {
    try {
      const data: any = await this.sharedUser.completionOfCoolOffUSer();
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }
  //#endregion

  //migrate loan feesIncome
  @Get('migrateLoanfeesIncome')
  async migrateLoanfeesIncome(@Res() res) {
    try {
      const data: any = await this.migration.migrateLoanfeesIncome();
      if (data?.message) return res.send(data);
      return res.json(kSuccessData);
    } catch (error) {
      return res.json(kInternalError);
    }
  }
  //#endregion

  @Post('calculateCLTV')
  async funCalculateCLTV(@Body() body, @Res() res) {
    try {
      const data = await this.calculation.calculateCLTV(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Post('migrateNewUserData')
  async funMigrateNewUserData(@Body() Body, @Res() res) {
    try {
      const data: any = await this.migration.migrateNewUserData(Body);
      if (data?.message) return res.send(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Post('migrateExistingUser')
  async funMigrateExistingUser(@Body() body, @Res() res) {
    try {
      const data: any = await this.migration.migrateExistingUser(body);
      if (data?.message) return res.send(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      console.log({ error });
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Post('migrateExistingUserv2')
  async funMigrateExistingUserV2(@Body() body, @Res() res) {
    try {
      const data: any = await this.migration.migrateExistingUserV2(body);
      if (data?.message) return res.send(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      console.log({ error });
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  // migrate loan reject reason
  @Post('migrateLoanRejectRemark')
  async funMigrateLoanRejectRemark(@Body() body, @Res() res) {
    try {
      const data: any = await this.migration.migrateLoanRejectRemark(body);
      if (data?.message) return res.send(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Get('migrateKycData')
  async migrateKycData(@Res() res) {
    try {
      const data: any = await this.migration.migrateKycData();
      if (data?.message) return res.send(data);
      return res.json(kSuccessData);
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Get('migratePredictionData')
  async funMigratePredictionData(@Body() body, @Res() res) {
    try {
      const data: any = await this.migration.migratePredictionData();
      if (data?.message) return res.json(kInternalError);
      return res.json({ ...kSuccessData });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Get('migrateIntrestRate')
  async funMigrateIntrestRate(@Query() query, @Res() res) {
    try {
      const data: any = await this.migration.migrateIntrestRate(query);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  //start region migrate upi payments mismatched date data
  @Post('migrateUpiMismatchedDateData')
  async funMigrateUpiMismatchedDateData(@Body() body, @Res() res) {
    try {
      const data: any = await this.migration.migrateUpiMismatchedDateData(body);
      if (data?.message) return res.json(kInternalError);
      return res.json({ ...kSuccessData });
    } catch (error) {
      return res.json(kInternalError);
    }
  }
  //#endregion

  @Post('adminAutoLogout')
  async adminAutoLogout(@Res() res) {
    try {
      const data: any = await this.commonService.adminAutoLogout();
      if (data.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Post('splitPaymentsforPI')
  async funSplitPaymentsforPI(@Body() body) {
    return await this.calculation.splitPaymentsforPI(body);
  }

  // migrate user to bank under verification for loan offer screen
  @Post('migrateUserFromFVtoBank')
  async funMigrateUserFromFVtoBank(@Res() res) {
    try {
      const data: any = await this.migration.migrateUserFromFVtoBank();
      if (data?.message) return res.json(kInternalError);
      return res.json({ ...kSuccessData });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Post('reCalculatePaidAmounts')
  async funReCalculatePaidAmounts(@Body() body, @Res() res) {
    try {
      const data: any = await this.sharedTransaction.reCalculatePaidAmounts(
        body,
      );
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Post('migrateUserPhone')
  async migrateUserPhone(@Res() res) {
    try {
      const data: any = await this.migration.migrateUserPhone();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Post('migrationCltvData')
  async funmigrationCltvData(@Body() body, @Res() res) {
    const data: any = await this.migration.migrationCltvData(body);
    if (data?.message) return res.send(data);
    return res.send({ ...kSuccessData, data });
  }

  @Post('addMissingCompany')
  async funaddMissingCompany(@Body() body, @Res() res) {
    const data: any = await this.migration.addMissingCompany(body);
    if (data?.message) return res.send(data);
    return res.send({ ...kSuccessData, data });
  }

  @Post('checkUserEligiblity')
  async funCheckUserEligiblity(@Body() body, @Res() res) {
    try {
      const data: any = await this.eligibility.checkUserEligiblity(
        body?.userId,
      );
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Post('getUserIdFromPhone')
  async funGetUserIdFromPhone(@Body() body, @Res() res) {
    try {
      const data: any = await this.commonService.funGetUserIdFromPhone(body);
      if (data.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Post('migrateAddressData')
  async funMigrateActiveLoanAddresses(@Body() body, @Res() res) {
    try {
      const limit = body?.limit ?? 100;
      const offset = body?.offset ?? 0;
      const data: any = await this.migration.migrateActiveLoanAddress(
        limit,
        offset,
      );
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Post('addCompanyDataInCompanyRepo')
  async finAddCompanyDataInCompanyRepo(@Body() body, @Res() res) {
    const data: any = await this.migration.addCompanyDataInCompanyRepo();
    if (data?.message) return res.send(data);
    return res.send({ ...kSuccessData, data });
  }

  @Post('migrateAadhaarAndRegDates')
  async migrateAadhaarAndRegDates(@Body() body, @Res() res) {
    try {
      const data: any = await this.migration.migrateAadhaarAndRegDates();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Post('migratePurposeId')
  async finMigratePurposeId(@Body() body, @Res() res) {
    const data: any = await this.migration.funMigratePurposeId(body);
    if (data?.message) return res.send(data);
    return res.send({ ...kSuccessData, data });
  }

  @Post('migrateFirstInquiryDate')
  async funMigrateFirstInquiryDate(@Body() body, @Res() res) {
    try {
      const limit = body?.limit ?? 100;
      const offset = body?.offset ?? 0;
      const data: any = await this.migration.migrateFirstInquiryDate(
        limit,
        offset,
      );
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Post('handleIncompleteKYCDetails')
  async handleIncompleteKYCDetails(@Res() res) {
    try {
      const data: any = await this.migration.handleIncompleteKYCDetails();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Post('migrateCoolOffUsers')
  async migrateCoolOffUsers(@Res() res) {
    try {
      const data: any = await this.migration.migrateCoolOffUsers();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Post('insertBatchCibilDataFile')
  @UseInterceptors(FileInterceptor('file', kUploadFileObj()))
  async funInsertBatchCibilDataFile(
    @UploadedFile() file,
    @Body() body,
    @Res() res,
  ) {
    try {
      body.file = file;
      const data: any = await this.cibilService.insertBatchCibilDataFile({
        body,
        file,
      });
      await this.fileService.removeFile(file?.filename);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Get('getBatchFileCibilData')
  async funGetBatchFileCibilData(@Query() query, @Res() res) {
    try {
      const data: any = await this.cibilService.getBatchFileCibilData(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Get('getBatchCibilData')
  async funGetBatchCibilData(@Query() query, @Res() res) {
    try {
      const data: any = await this.cibilService.getBatchCibilData(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }
  @Post('migratePhoneWithHashPhoneInProtean')
  async migratePhoneWithHashPhoneInProtean(@Body() body, @Res() res) {
    try {
      const limit = body?.limit ?? 100;
      const offset = body?.offset ?? 0;
      const data: any = await this.migration.migratePhoneWithHashPhoneInProtean(
        limit,
        offset,
      );
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Post('migrateAddPhoneForHashPhoneInProtean')
  async migratePhoneInProtean(@Body() body, @Res() res) {
    try {
      const limit = body?.limit ?? 100;
      const offset = body?.offset ?? 0;
      const data: any =
        await this.migration.migrateAddPhoneForHashPhoneInProtean(
          limit,
          offset,
        );
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Get('/getRowQuery')
  async getRowQuery() {
    const trigger_row = [];
    allTriggers.forEach((element) => {
      const trigger = this.triggerService.createTrigger(element);
      trigger_row.push(trigger);
      console.log(trigger);
      console.log('');
      console.log('');
    });

    return { data: trigger_row };
  }

  // migrate hashPhone to hashPhoneEntity
  @Post('migrateHashPhone')
  async funMigrateHashPhone(@Res() res) {
    try {
      const data: any = await this.migration.migrateHashPhone();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  //#region Migration for FinanceBudhhaLeadsEntities
  @Get('migratePANEncryption')
  async funMigratePANEncryption(@Res() res) {
    const data: any = await this.migration.migratePANEncryption();
    if (data?.message) return res.send(data);
    return res.send({ ...kSuccessData, data });
  }
  //#endregion

  @Post('migratePanDates')
  async funMigratePanDates(@Body() body, @Res() res) {
    try {
      const data: any = await this.migration.migratePanDates(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }
  @Post('migrateFcmToken')
  async funMigrateFcmToken(@Res() res) {
    try {
      const data: any = await this.migration.migrateFcmToken();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Get('selfieImageMigration')
  async funSelfieImageMigration(@Query() query, @Res() res) {
    try {
      const batchSize = query?.batchSize ?? 100;
      const startDate = query?.startDate;
      const endDate = query?.endDate;
      if (!startDate || !endDate) return res.json(kParamsMissing);
      const data: any = await this.migration.funSelfieImageMigration(
        parseInt(batchSize),
        startDate,
        endDate,
      );
      if (data?.message) return res.json(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      console.error('Error in: ', error);
      return res.send(kInternalError);
    }
  }

  @Post('rejectNotEligibleUsers')
  async funRejectNotEligibleUsers(@Body() body, @Res() res) {
    try {
      const data = await this.migration.rejectNotEligibleUsers(body);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Post('migratecompanyName')
  async funMigratecompanyName(@Query() query, @Res() res) {
    try {
      let batchSize = query?.batchSize ?? 100;
      batchSize = parseInt(batchSize);
      if (batchSize % 2 == 1) batchSize = batchSize * 2;
      const data: any = await this.migration.migratecompanyName(batchSize);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Post('migrateDeleteFileOfGoogleCloud')
  async funMigrateDeleteFileOfGoogleCloud(@Query() query, @Res() res) {
    try {
      let batchSize = query?.batchSize ?? 100;
      batchSize = parseInt(batchSize);
      const data: any = await this.migration.migrateDeleteFileOfGoogleCloud(
        batchSize,
      );
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  //#region Migration of user data from registeredUsers entity to loanTransactions entity.
  @Get('migrateUserDetails')
  async funMigrateUserDetails(@Query() query, @Res() res) {
    try {
      let batchSize = query?.batchSize ?? 100;
      batchSize = parseInt(batchSize);
      const data: any = await this.migration.migrateUserDetails(batchSize);
      if (data?.message) return res.json(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }
  //#endregion Migration of user data from registeredUsers entity to loanTransactions entity.

  @Post('migrateOldGSTData')
  async funMigrateOldGSTData(@Res() res) {
    try {
      const data: any = await this.migration.migrateOldGSTData();
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Post('migratePenalAndBounceGstOfWaver')
  async funMigratePenalAndBounceGstOfWaver(@Body() body, @Res() res) {
    try {
      const data: any = await this.migration.migratePenalAndBounceGstOfWaver(
        body,
      );
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }
  @Post('migrateCrmDispositionAndTitle')
  async funMigrateCrmDispositionAndTitle(@Res() res) {
    try {
      const data: any = await this.migration.migrateCrmDispositionAndTitle();
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Post('migrateProteanEntityData')
  async funMigrateProteanEntityData(@Query() query, @Res() res) {
    try {
      let batchSize = query?.batchSize ?? 100;
      batchSize = parseInt(batchSize);
      const data: any = await this.migration.migrateProteanEntityData(
        batchSize,
      );
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  //#region Migrate lead's disbursement amount to Lead Tracking Table
  @Get('migrateDisbursementAmount')
  async funMigrateDisbursmentAmount(@Query() query, @Res() res) {
    try {
      let batchSize = query?.batchSize ?? 100;
      batchSize = parseInt(batchSize);
      const data: any = await this.migration.funMigrateDisbursmentAmount(
        batchSize,
      );
      if (data?.message) return res.json(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }
  //#endregion

  //#region Migrate State data on base of pincode for Lead Tracking Table.
  @Get('migrateStatePincode')
  async funMigrateStatePincode(@Query() query, @Res() res) {
    try {
      let batchSize = query?.batchSize ?? 100;
      batchSize = parseInt(batchSize);
      const data: any = await this.migration.funMigrateStatePincode(batchSize);
      if (data?.message) return res.json(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }
  //#endregion

  // Check ML CFL Score
  @Get('CFLScoreForApproval')
  async funCFLScoreForApproval(@Query() query, @Res() res) {
    try {
      const data: any = await this.eligibility.CFLScoreForApproval(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  // calculate LeadScore
  @Get('calculateLeadScore')
  async funCalculateLeadScore(@Query() query, @Res() res) {
    try {
      const data: any = await this.eligibility.calculateLeadScore(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  //#region Migrate all lead's data to AcceptedLead and RejectedLead (Partition Tables)
  @Get('migrateAllLeadsToPartition')
  async funMigrateAllLeadsToPartition(@Query() query, @Res() res) {
    try {
      let batchSize = query?.batchSize ?? 100;
      batchSize = parseInt(batchSize);
      const data: any = await this.migration.funMigrateAllLeadsToPartition(
        batchSize,
      );
      if (data?.message) return res.json(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }
  //#endregion

  @Post('migrateCompanyIdInLoan')
  async funMigrateCompanyIdInLoan(@Res() res) {
    try {
      const data: any = await this.migration.migrateCompanyIdInLoan();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  // migrate cibil request data to archive
  @Post('migrateCibilReqData')
  async funMigrateCibilReqData(@Res() res) {
    try {
      const data: any = await this.migration.migrateCibilReqData();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Post('getUsedCredit')
  async getUsedCredit(@Body() body, @Res() res) {
    try {
      const data: any = await this.sharedTransaction.getUsedCredit(
        body.loanId,
        body.userId,
      );
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Post('migrateFilingDates')
  async funMigrateFilingDates(@Query() query, @Res() res) {
    try {
      const data: any = await this.migration.migrateFilingDates(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Get('migrateZoopKycData')
  async migrateZoopKycData(@Query() query, @Res() res) {
    try {
      const data: any = await this.migration.migrateZoopKycData(query);
      return res.send({ data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send({ data: false });
    }
  }

  @Post('addBankTransferCreditData')
  async addBankTransferCreditData(@Res() res) {
    try {
      const data: any = await this.migration.addBankTransferCreditData();
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  //#region Run a CRON job to expire 'Accepted' leads as 'Expired', for those leads whose lead status is still 'Accepted', even after 30 days of lead's creation date.
  @Post('expireOnlyAcceptedLeads')
  async funExpireOnlyAcceptedLeads(@Query() query, @Res() res) {
    try {
      let batchSize = query?.batchSize ?? 100;
      batchSize = parseInt(batchSize);
      const data: any = await this.migration.funExpireOnlyAcceptedLeads(
        batchSize,
      );
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      console.log(error);
      return res.send(kInternalError);
    }
  }
  //#endregion

  /// DO NOT REMOVE THIS MIGRATION CODE ==================================================================
  //#region Might need migration for expiring leads in case of expiring in-process(1) or disbursed(3) leads
  // Migration code to update lead status as 'Expired', for those leads whose lead status is not 'Disbursed', even after 30 days of lead's creation date.
  // @Post('expireAllLeadsInProgress')
  // async funExpireAllLeadsInProgress(@Query() query, @Res() res) {
  //   try {
  //     let batchSize = query?.batchSize ?? 100;
  //     batchSize = parseInt(batchSize);

  //     const data: any = await this.migration.funExpireAllLeadsInProgress(
  //       batchSize,
  //     );
  //     if (data?.message) return res.send(data);
  //     return res.send({ ...kSuccessData, data });
  //   } catch (error) {
  //     console.log(error);
  //     return res.send(kInternalError);
  //   }
  // }
  //#endregion
  /// DO NOT REMOVE THIS MIGRATION CODE ==================================================================

  @Post('migrateLspId')
  async funMigrateLspId(@Res() res) {
    try {
      const data: any = await this.migration.migrateLspId();
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Post('qaTestingOfEligibilty')
  async funQaTestingOfEligibilty(@Body() body, @Res() res) {
    try {
      const baseEligibility: any =
        await this.eligibility.nbfcFirstvalidateEligiblityForLoan(body);
      if (baseEligibility?.message) return res.json(baseEligibility);

      const data: any = await this.eligibility.markEligibilityAsComplete(
        baseEligibility,
        true,
      );
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, baseEligibility, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }
}
