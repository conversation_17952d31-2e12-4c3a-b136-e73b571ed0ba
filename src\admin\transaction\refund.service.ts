// Imports
import { v4 as uuidv4 } from 'uuid';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { Op, Sequelize } from 'sequelize';
import { PAGE_LIMIT, SYSTEM_ADMIN_ID } from 'src/constants/globals';
import { k500Error } from 'src/constants/misc';
import {
  k422ErrorMessage,
  kInternalError,
  kParamMissing,
} from 'src/constants/responses';
import {
  kAutoDebit,
  kCashfree,
  kCompleted,
  kDirectBankPay,
  kFailed,
  KICICIUPI,
  KICICIUPI2,
  kInitiated,
  kManual,
  kNoTransactionForRefund,
  kRazorpay,
  kRefund,
  kSomthinfWentWrong,
  KYESUPI,
  kYouHaveAccess,
} from 'src/constants/strings';
import { BankingEntity } from 'src/entities/banking.entity';
import { KYCEntity } from 'src/entities/kyc.entity';
import { TransactionEntity } from 'src/entities/transaction.entity';
import { registeredUsers } from 'src/entities/user.entity';
import { AdminRepository } from 'src/repositories/admin.repository';
import { EMIRepository } from 'src/repositories/emi.repository';
import { LoanRepository } from 'src/repositories/loan.repository';
import { TransactionRepository } from 'src/repositories/transaction.repository';
import { SharedNotificationService } from 'src/shared/services/notification.service';
import { CashFreeService } from 'src/thirdParty/cashfree/cashfree.service';
import { NotificationService } from 'src/thirdParty/notificationService/notification.service';
import { RazorpoayService } from 'src/thirdParty/razorpay/razorpay.service';
import { CryptService } from 'src/utils/crypt.service';
import { FileService } from 'src/utils/file.service';
import { TypeService } from 'src/utils/type.service';
import { ICICIThirdParty } from 'src/thirdParty/icici/icici.service';
import { SystemTraceEntity } from 'src/entities/system_trace.entity';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { DateService } from 'src/utils/date.service';
import {
  CLOUD_FOLDER_PATH,
  kLspMsg91Templates,
  kMsg91Templates,
} from 'src/constants/objects';
import { SlackService } from 'src/thirdParty/slack/slack.service';
import { ErrorContextService } from 'src/utils/error.context.service';
import { YESService } from 'src/thirdParty/yes/yes.service';
import { SharedTransactionService } from 'src/shared/transaction.service';
import { EnvConfig } from 'src/configs/env.config';

@Injectable()
export class RefundService {
  constructor(
    private readonly repository: TransactionRepository,
    private readonly typeService: TypeService,
    private readonly adminRepo: AdminRepository,
    private readonly emiRepo: EMIRepository,
    private readonly loanRepo: LoanRepository,
    private readonly cryptService: CryptService,
    private readonly cashFreeService: CashFreeService,
    private readonly razorpoayService: RazorpoayService,
    private readonly notificationService: NotificationService,
    private readonly sharedNotification: SharedNotificationService,
    private readonly iciciService: ICICIThirdParty,
    private readonly fileService: FileService,
    private readonly dateService: DateService,
    // Database
    private readonly repoManager: RepositoryManager,
    private readonly slackService: SlackService,
    private readonly errorContextService: ErrorContextService,
    private readonly yesUpiService: YESService,
    @Inject(forwardRef(() => SharedTransactionService))
    private readonly sharedTransactionService: SharedTransactionService,
  ) {}

  //#region pendding
  async getRefundablesData(
    startDate,
    endDate,
    adminId,
    loanId = -1,
    download = 'false',
  ) {
    try {
      // Validation -> Authorization
      const access = await this.adminRepo.checkHasAccess(adminId, 'refund');
      if (access !== true) return access;

      // Gather data -> Transactions
      const transData = await this.getDateTransaction(
        startDate,
        endDate,
        loanId,
      );
      if (transData?.message) return transData;

      // Gather data -> Loan and Emi
      const emiData: any = await this.getLoanEMIData(transData?.loanIdList);
      if (emiData?.message) return emiData;

      // Calculation -> Excessive amount
      const prePareData = this.prePareAmountOfRefund(transData, emiData);
      if (!prePareData || prePareData?.message) return prePareData;

      const filteredData: any = await this.findUserNameForRefund(prePareData);
      if (filteredData?.message) return filteredData;

      // Validation -> Authorization
      const edit = await this.adminRepo.checkHasAccess(adminId, 'refund', 2);

      let isRefund = false;
      if (edit === true) isRefund = true;

      // Excel download
      if (download == 'true') {
        const preparedData = filteredData.filteredData.map((item) => ({
          'loan ID': item?.loanId,
          Name: item?.name,
          Email: item?.email,
          'Paid Amount': item?.paidAmount,
          'Refund Amount': item?.amount,
          'Total emi amount': item?.emiAmount,
          'Loan Status': item?.userType,
        }));

        const path = 'Refund.xlsx';
        const rawExcelData = {
          sheets: ['Refund'],
          data: [preparedData],
          sheetName: path,
          needFindTuneKey: false,
        };
        const excelResponse: any = await this.fileService.objectToExcel(
          rawExcelData,
        );
        if (excelResponse?.message) return excelResponse;
        const fileURL = await this.fileService.uploadFile(
          excelResponse?.filePath,
          CLOUD_FOLDER_PATH.reports,
          'xlsx',
        );
        if (fileURL.message) return fileURL;
        return { fileURL };
      }

      return {
        isRefund,
        filteredData: filteredData.filteredData,
        lastADTrans: filteredData.lastADTrans,
      };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region get date rage paid transaction and loanIdList
  private async getDateTransaction(
    startDate,
    endDate,
    loanId = -1,
  ): Promise<any> {
    try {
      // Start Date and End Date
      startDate = this.typeService.getGlobalDate(startDate).toJSON();
      endDate = this.typeService.getGlobalDate(endDate).toJSON();

      // Fetching Loan Ids Which Made Payment Range of startDate-endDate
      const refundStatus = ['INITIALIZED'];
      const options: any = {
        where: {
          [Op.or]: [
            { status: 'COMPLETED' },
            { status: refundStatus, type: 'REFUND' },
          ],
          completionDate: { [Op.gte]: startDate, [Op.lte]: endDate },
        },
        group: ['loanId'],
      };
      const attributes = ['loanId'];
      const transactions =
        loanId === -1
          ? await this.repository.getTableWhereData(attributes, options)
          : [{ loanId }];
      if (!transactions || transactions === k500Error)
        throw new Error('Error in Fetching Transactions');

      // Prepared Unique Loan Ids
      const loanIdList = [...new Set(transactions.map((tran) => tran.loanId))];

      // Total Paid Amount of Loans
      const transAttributes: any = [
        'loanId',
        [Sequelize.fn('SUM', Sequelize.col('paidAmount')), 'amount'],
      ];
      const transOptions: any = {
        where: {
          [Op.or]: [
            { status: 'COMPLETED' },
            { status: refundStatus, type: 'REFUND' },
          ],
          loanId: loanIdList,
        },
        group: ['loanId'],
      };
      const paidAmountList = await this.repository.getTableWhereData(
        transAttributes,
        transOptions,
      );
      if (!paidAmountList || paidAmountList === k500Error)
        throw new Error('Error in Fetching Paid Amount for Loans');

      return { loanIdList, paidAmountList };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region get base loanId get EMI Paid or not data
  private async getLoanEMIData(loanIdList) {
    try {
      // Get EMI Ids from Transactions
      const options = { where: { loanId: { [Op.in]: loanIdList } } };
      const attributes = ['emiId', 'status', 'type'];
      const emiDataFromTrans = await this.repository.getTableWhereData(
        attributes,
        options,
      );
      if (!emiDataFromTrans || emiDataFromTrans === k500Error)
        throw new Error('Error in Fetching EMI Ids from Transactions');

      // EMI Ids Filteration
      let emiIds = [];
      emiDataFromTrans.forEach((trans) => {
        if (
          trans?.status === 'COMPLETED' ||
          (['INITIALIZED', 'FAILED'].includes(trans?.status) &&
            trans?.type === 'REFUND')
        )
          emiIds.push(trans.emiId);
      });

      // Prepared Unique EMI Ids
      const emiIdList = [...new Set(emiIds)];
      const date = this.typeService.getGlobalDate(new Date());

      const option = {
        where: {
          loanId: loanIdList,
          [Op.or]: [
            { id: emiIdList },
            { payment_status: '1' },
            { payment_status: '0', payment_due_status: '1' },
            {
              [Op.and]: [
                { payment_status: '0' },
                { emi_date: { [Op.lte]: date.toJSON() } },
              ],
            },
          ],
        },
        order: [['id', 'DESC']],
      };
      const emiAttributes: any = [
        'id',
        'loanId',
        'emi_date',
        'payment_done_date',
        'principalCovered',
        'payment_due_status',
        'interestCalculate',
        'penalty',
        'regInterestAmount',
        'bounceCharge',
        'dpdAmount',
        'legalCharge',
        'gstOnBounceCharge',
        'penaltyChargesGST',
        'legalChargeGST',
        'partPaymentPenaltyAmount',
        'fullPayInterest',
        'pay_type',
        'waiver',
        'paid_waiver',
        'unpaid_waiver',
        'sgstForClosureCharge',
        'cgstForClosureCharge',
        'igstForClosureCharge',
        'forClosureAmount',
      ];
      const result = await this.emiRepo.getTableWhereData(
        emiAttributes,
        option,
      );
      if (!result || result == k500Error) return kInternalError;

      const loanData = await this.loanRepo.getTableWhereData(
        ['id', 'penaltyCharges'],
        { where: { id: { [Op.in]: loanIdList } } },
      );
      if (loanData == k500Error) throw new Error('Error in Fetching Loan Data');

      for (let index = 0; index < result.length; index++) {
        const ele = result[index];
        // Managing Amts Because We Took Charges By Rounding off Earlier Payments
        ele.regInterestAmount = this.typeService.manageAmount(
          ele?.regInterestAmount ?? 0,
        );
        ele.dpdAmount = this.typeService.manageAmount(ele?.dpdAmount ?? 0);
        if (ele?.penaltyChargesGST) {
          let cGstOnPenal = this.typeService.manageAmount(
            (ele.penaltyChargesGST ?? 0) / 2,
          );
          let sGstOnPenal = this.typeService.manageAmount(
            (ele.penaltyChargesGST ?? 0) / 2,
          );
          ele.penaltyChargesGST = cGstOnPenal + sGstOnPenal;
        }
      }
      return { result, loanData };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region prePare amount
  private prePareAmountOfRefund(transactionData, emiData): any {
    try {
      const list = [];
      const filteredData = [];
      const paidAmountList = transactionData?.paidAmountList;
      paidAmountList.forEach((ele) => {
        try {
          const loanId = ele?.loanId;
          const filter = emiData?.result?.filter((f) => f.loanId === loanId);
          let emiAmount = 0;
          let waiver = 0;
          const emiList = [];
          filter.forEach((emi) => {
            try {
              const tempAmount: any = this.caclitionOfEMIamount(
                emi,
                emiData?.loanData,
                loanId,
              );
              if (tempAmount?.message) return tempAmount;
              emiList.push({
                emiId: emi.id,
                amount: tempAmount.amount,
                pay_type: emi.pay_type,
              });
              emiAmount += tempAmount.amount;
              waiver += tempAmount.waiver;
            } catch (error) {}
          });
          const amount = ele.amount - emiAmount;
          // Make sure total emi amount will be there just to be safe
          if (amount > 10 && emiAmount) {
            const find = filter.find((f) => f.payment_due_status == '1');
            list.push(loanId);
            filteredData.push({
              emiList,
              loanId,
              emiAmount,
              paidAmount: ele.amount,
              amount,
              waiver,
              userType: find ? 'Delay' : 'On-Time',
            });
          }
        } catch (error) {}
      });
      return { list, filteredData };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region caclution of emi amount
  private caclitionOfEMIamount(emi, loanData, loanId) {
    let amount = 0;
    let waiver = 0;
    try {
      const loan = loanData?.find((l) => l.id === loanId);
      // Flag to Check That User is Old or New
      const isNewUser = loan?.penaltyCharges?.MODIFICATION_CALCULATION ?? false;
      const emiDate = new Date(emi.emi_date).getTime();
      let doneDate;

      // Refund Amt & Waiver Amt Preparation
      if (emi.payment_done_date && emi.pay_type === 'FULLPAY')
        doneDate = new Date(emi.payment_done_date).getTime();
      if (doneDate && doneDate < emiDate) amount += emi.fullPayInterest;
      else amount += emi.interestCalculate;
      amount += emi.principalCovered;
      amount += emi.regInterestAmount ?? 0;
      amount += emi.dpdAmount ?? 0;
      amount += emi.penaltyChargesGST ?? 0;
      amount += emi.legalCharge ?? 0;
      amount += emi.legalChargeGST ?? 0;
      if (!isNewUser) {
        amount += emi.penalty ?? 0;
        amount += emi.partPaymentPenaltyAmount ?? 0;
      } else {
        amount += emi.bounceCharge ?? 0;
        amount += emi.gstOnBounceCharge ?? 0;
      }
      // Fullpay
      amount += emi.forClosureAmount ?? 0;
      amount += emi.sgstForClosureCharge ?? 0;
      amount += emi.cgstForClosureCharge ?? 0;
      amount += emi.igstForClosureCharge ?? 0;

      waiver += emi.waiver ?? 0;
      waiver += emi.paid_waiver ?? 0;
      waiver += emi.unpaid_waiver ?? 0;
      // amount += waiver;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
    return { amount, waiver };
  }
  //#endregion

  //#region find user name for refund
  private async findUserNameForRefund(prePareData) {
    try {
      const tranModel: any = {
        model: TransactionEntity,
        attributes: [
          'id',
          'paidAmount',
          'emiId',
          'status',
          'type',
          'subSource',
          'principalAmount',
          'interestAmount',
          'regInterestAmount',
          'legalCharge',
          'bounceCharge',
          'penaltyAmount',
          'penalCharge',
          'sgstOnBounceCharge',
          'cgstOnBounceCharge',
          'igstOnBounceCharge',
          'sgstOnPenalCharge',
          'cgstOnPenalCharge',
          'igstOnPenalCharge',
          'sgstOnLegalCharge',
          'cgstOnLegalCharge',
          'igstOnLegalCharge',
        ],
        where: {
          [Op.or]: [
            { status: 'COMPLETED' },
            { status: ['INITIALIZED', 'FAILED'], type: 'REFUND' },
          ],
        },
        required: false,
      };
      const options = {
        where: { id: prePareData.list },
        include: [tranModel],
      };
      const att = ['id', 'appType', 'userId', 'fullName', 'email'];
      const result = await this.loanRepo.getTableWhereData(att, options);

      if (!result || result === k500Error) return kInternalError;
      let lastADTrans = {};
      prePareData.filteredData.forEach((ele) => {
        try {
          const find = result.find((f) => f.id === ele.loanId);
          ele.name = find?.fullName ?? '-';
          ele.email = find?.email ?? '-';
          ele.userId = find?.userId ?? '-';
          ele.appType = find?.appType;
          const tranData = find?.transactionData ?? [];
          // Filtering Data of AUTO-DEBIT
          const filteredData = tranData.filter(
            (item) => item.type === 'EMIPAY' && item.subSource === 'AUTODEBIT',
          );

          // Selecting Latest AD Trans.
          lastADTrans =
            filteredData.reduce(
              (maxItem, currentItem) =>
                currentItem.id > maxItem.id ? currentItem : maxItem,
              filteredData[0],
            ) ?? {};

          ele.tranData = tranData;
          const checkRefund = tranData.find(
            (t) => t?.type == 'REFUND' && t?.status == 'FAILED',
          );
          ele.failedRefund = !checkRefund ? false : true;

          for (let index = 0; index < ele.emiList.length; index++) {
            try {
              const emi = ele.emiList[index];
              let sumEMI = tranData
                .filter((f) => f.emiId === emi.emiId && f.status !== 'FAILED')
                .reduce((sum, a) => sum + a.paidAmount, 0);
              const find = tranData.find((f) => f.type === 'FULLPAY');
              if (find && emi?.pay_type == 'FULLPAY') sumEMI += find.paidAmount;
              const diff = sumEMI - emi.amount;
              if (diff > 10) {
                ele.emiId = emi.emiId;
                break;
              }
            } catch (error) {}
          }
          if (!ele.emiId) {
            const amountArray: any = [
              ...new Set(ele.emiList.map((f) => f.amount)),
            ];
            const closest = amountArray.reduce(function (prev, curr) {
              return Math.abs(curr - ele.amount) < Math.abs(prev - ele.amount)
                ? curr
                : prev;
            });
            const find = ele.emiList.find((f) => f.amount === closest);
            if (find) ele.emiId = find.emiId;
          }
          if (!ele.emiId) ele.emiId = ele.emiList[0].emiId;

          ele.amount = Math.floor(ele.amount);
          ele.paidAmount = Math.floor(ele.paidAmount);
          ele.emiAmount = Math.floor(ele.emiAmount);
          delete ele.emiList;
        } catch (error) {}
      });
      return { filteredData: prePareData.filteredData, lastADTrans };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region get all refunded data
  async getAllRefundedData(
    startDate,
    endDate,
    page,
    status,
    download = 'false',
    skipPageLimit = 'false',
    adminId,
  ) {
    try {
      startDate = this.typeService.getGlobalDate(startDate).toJSON();
      endDate = await this.typeService.getGlobalDate(endDate).toJSON();
      const att = [
        'id',
        'type',
        'paidAmount',
        'loanId',
        'utr',
        'source',
        'status',
        'completionDate',
      ];
      if (status == '2') att.push('response');
      const userInclude = {
        model: registeredUsers,
        attributes: ['id', 'fullName', 'phone'],
      };
      const options: any = {
        where: {
          type: 'REFUND',
          completionDate: { [Op.gte]: startDate, [Op.lte]: endDate },
        },
        include: [userInclude],
        order: [['completionDate', 'DESC']],
      };

      if (status == '0') options.where.status = 'INITIALIZED';
      else if (status == '1') options.where.status = 'COMPLETED';
      else if (status == '2') options.where.status = 'FAILED';

      if (download != 'true' && skipPageLimit != 'true') {
        const skip1 = (page ?? 1) * PAGE_LIMIT - PAGE_LIMIT;
        options.offset = skip1;
        options.limit = 1 * PAGE_LIMIT;
      }

      const result = await this.repository.getTableWhereDataWithCounts(
        att,
        options,
      );
      if (!result || result == k500Error) return kInternalError;
      let prepareData: any[] = [];

      for (let index = 0; index < result.rows.length; index++) {
        const element = result.rows[index];

        // Get Refund Data for Failed Refund
        let refundData: any = {};
        if (status == '2')
          refundData = await this.getRefundablesData(
            null,
            null,
            adminId,
            element?.loanId,
          );

        // Preparing Data for Refund Only If Refund is Possible
        let conditionForFailedRefund =
          refundData?.filteredData?.length > 0 &&
          (refundData?.filteredData[0]?.amount ?? 0) > 0;
        if (status == '0' || status == '1') conditionForFailedRefund = true;
        if (conditionForFailedRefund) {
          try {
            if (status == '2') {
              const res = JSON.parse(element?.response);
              element.description =
                res?.data?.error?.description ?? res?.data?.message ?? '-';
              delete element?.response;
            }
          } catch (error) {}

          if (element.userData && element.userData.phone) {
            element.userData.phone = this.cryptService.decryptPhone(
              element.userData?.phone,
            );
          }
          let readableDate: any;
          if (element?.completionDate) {
            readableDate = this.dateService.dateToReadableFormat(
              element.completionDate,
            );
          }
          if (download == 'true') {
            const newData = {
              'Loan ID': element?.loanId,
              Name: element?.userData?.fullName ?? '-',
              Phone: element?.userData?.phone ?? '-',
              'Paid Amount': element?.paidAmount,
              Source: element?.source,
              UTR: element?.utr ?? '-',
              Status: element?.status,
              Description: element?.description ?? '-',
              'Refund Date': readableDate.readableStr ?? '-',
            };
            prepareData.push(newData);
          } else prepareData.push(element);
        } else continue;
      }

      if (download == 'true') {
        const path = 'Refund.xlsx';
        const rawExcelData = {
          sheets: ['Refund'],
          data: [prepareData],
          sheetName: path,
          needFindTuneKey: false,
        };
        const excelResponse: any = await this.fileService.objectToExcel(
          rawExcelData,
        );
        if (excelResponse?.message) return excelResponse;

        const fileURL = await this.fileService.uploadFile(
          excelResponse?.filePath,
          CLOUD_FOLDER_PATH.reports,
          'xlsx',
        );
        if (fileURL.message) return fileURL;
        return { fileURL };
      }
      if (status == '0' || status == '1') return result;
      return {
        count: prepareData.length,
        rows: prepareData,
      };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region check And Place Refund
  async checkAndPlaceRefund(body) {
    try {
      // Validation -> Parameters
      const adminId = body?.adminId;
      if (!adminId) return kParamMissing('adminId');
      const loanId = body?.loanId;
      if (!loanId) return kParamMissing('loanId');

      const result = await this.getRefundablesData(null, null, adminId, loanId);
      // Validation -> Gathered data
      if (result?.message) return result;
      if (!result?.isRefund) return k422ErrorMessage(kYouHaveAccess);

      // Initiate -> Refund process
      const refund = result?.filteredData[0];
      const lastADTrans = result?.lastADTrans;
      if (refund?.loanId) {
        // Validation -> Applicable refund amount and transaction
        const tranData = await this.getTransactionDataFroRfund(refund.loanId);
        if (!tranData || tranData?.message) return tranData;

        // Calculation -> Refund amount
        const prePareData = this.prePareRefundAmount(
          refund,
          tranData,
          adminId,
          lastADTrans,
        );
        if (prePareData?.message) return prePareData;
        // Iteration -> Refund initiate
        const sessionId: string = uuidv4();
        for (let index = 0; index < prePareData.length; index++) {
          const ele = prePareData[index];
          ele.loanId = loanId;

          // Create -> System trace row data
          const systemCreationData = {
            sessionId,
            type: 7,
            loanId,
            userId: ele.rawData?.userId,
            uniqueId: `T${7}=L${loanId}=E${ele.rawData.emiId ?? -1}`,
          };
          // Hit -> Query
          const createdData = await this.repoManager.createRowData(
            SystemTraceEntity,
            systemCreationData,
          );
          if (createdData === k500Error) continue;

          await this.refundInitialized(ele);
        }
        return prePareData;
      } else return kInternalError;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region get transaction data
  private async getTransactionDataFroRfund(
    loanId,
    directEntry = false,
  ): Promise<any> {
    try {
      // User's Latest Transaction Must Be Last 150 Days.
      const validationForDays = 150;
      const date = new Date();
      date.setDate(date.getDate() - validationForDays);
      const att = [
        'id',
        'paidAmount',
        'transactionId',
        'utr',
        'type',
        'status',
        'source',
        'subSource',
        'emiId',
        'response',
        'principalAmount',
        'interestAmount',
        'penaltyAmount',
        'regInterestAmount',
        'legalCharge',
        'bounceCharge',
        'penalCharge',
        'sgstOnBounceCharge',
        'cgstOnBounceCharge',
        'igstOnBounceCharge',
        'sgstOnPenalCharge',
        'cgstOnPenalCharge',
        'igstOnPenalCharge',
        'sgstOnLegalCharge',
        'cgstOnLegalCharge',
        'igstOnLegalCharge',
      ];
      let options = {
        where: {
          completionDate: { [Op.gte]: date },
          [Op.or]: [
            { status: 'COMPLETED', type: { [Op.ne]: 'REFUND' } },
            { status: 'INITIALIZED', type: 'REFUND' },
          ],
          loanId,
        },
      };
      if (directEntry) delete options?.where?.completionDate;
      const tranData = await this.repository.getTableWhereData(att, options);
      if (!tranData || tranData == k500Error) return kInternalError;
      if (tranData.length === 0)
        return k422ErrorMessage(
          `No Latest Transaction in last ${validationForDays} Days! Try Manual Refund`,
        );
      const tran = [];
      tranData.forEach((ele) => {
        try {
          const source = ele.source;
          const subSource = ele.subSource;
          if (
            source === kRazorpay ||
            (source === kCashfree && subSource != kAutoDebit) ||
            source === KICICIUPI ||
            source === KICICIUPI2 ||
            source === KYESUPI
          ) {
            tran.push(ele);
          }
        } catch (error) {}
      });
      if (tran.length === 0) return k422ErrorMessage(kNoTransactionForRefund);

      return tran;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region prePare refund amount for place
  private prePareRefundAmount(refund, tranData, adminId, lastADTrans): any {
    try {
      let amount = refund.amount;
      const emiId = refund.emiId;
      if (amount > 0) {
        // Prevention -> Duplication
        const find = tranData.find((f) => f.status === kInitiated);
        if (find) return k422ErrorMessage(kSomthinfWentWrong);

        let amountSameAsEMITran;
        let refundTOSameEMITran;
        let refundTOTakeAnyTran;
        let nonICICIUPITrans;

        // RPC 1.1.0 -> ICICI UPI refund should be last priority for refund for settlement profit
        tranData = tranData.sort((a, b) => {
          if (
            a.source === KICICIUPI ||
            a.source === KICICIUPI2 ||
            a.source === KYESUPI
          )
            return 1;
          if (
            b.source === KICICIUPI ||
            b.source === KICICIUPI2 ||
            b.source === KYESUPI
          )
            return -1;
          return 0;
        });
        tranData.forEach((ele) => {
          try {
            if (
              ele.paidAmount < amount + 1 &&
              ele.paidAmount > amount - 1 &&
              ele.emiId == emiId
            ) {
              amountSameAsEMITran = ele;
            }
            if (ele.emiId == emiId && ele.paidAmount > amount - 1)
              refundTOSameEMITran = ele;
            if (ele.paidAmount > amount - 1) refundTOTakeAnyTran = ele;
            // Non auto debit transaction with least priority of ICICI UPI
            if (
              ele.paidAmount > amount - 1 &&
              ele.source != KICICIUPI &&
              ele.source != KICICIUPI2 &&
              ele.source != KYESUPI &&
              !nonICICIUPITrans
            ) {
              nonICICIUPITrans = ele;
            }
          } catch (error) {}
        });
        const refundTran = amountSameAsEMITran ?? refundTOSameEMITran;
        const refundArray = [];
        const useTran = [];

        // Priority -> #01 Non ICICI UPI
        if (nonICICIUPITrans && amount > 0) {
          const data: any = this.prePareRefundObject(
            refund,
            nonICICIUPITrans,
            amount,
            lastADTrans,
          );
          if (data?.message) return data;
          data.rawData.adminId = adminId;
          refundArray.push(data);
          amount += data.rawData.paidAmount;
          useTran.push(nonICICIUPITrans.id);
        }

        /// this for emi amount to amount or geter than paid amount of same emi
        if (refundTran && amount > 0) {
          const data: any = this.prePareRefundObject(
            refund,
            refundTran,
            amount,
            lastADTrans,
          );
          if (data?.message) return data;
          data.rawData.adminId = adminId;
          refundArray.push(data);
          amount += data.rawData.paidAmount;
          useTran.push(refundTran.id);
        }
        /// check same emi
        if (amount > 0) {
          const filter = tranData.filter((f) => f.emiId === emiId);
          filter.forEach((tran) => {
            try {
              if (!useTran.includes(tran.id) && amount > 0) {
                let tempAmount = tran.paidAmount;
                if (tempAmount > amount) tempAmount = amount;
                const data: any = this.prePareRefundObject(
                  refund,
                  tran,
                  tempAmount,
                  lastADTrans,
                );
                if (!data?.message) {
                  data.rawData.adminId = adminId;
                  refundArray.push(data);
                  amount += data.rawData.paidAmount;
                  useTran.push(tran.id);
                }
              }
            } catch (error) {}
          });
        }

        /// check is refund any other transaction
        if (amount > 0 && refundTOTakeAnyTran) {
          if (!useTran.includes(refundTOTakeAnyTran.id)) {
            const data: any = this.prePareRefundObject(
              refund,
              refundTOTakeAnyTran,
              amount,
              lastADTrans,
            );
            if (data?.message) return data;
            data.rawData.adminId = adminId;
            refundArray.push(data);
            amount += data.rawData.paidAmount;
            useTran.push(refundTOTakeAnyTran.id);
          }
        }

        /// now check other emis
        if (amount > 0) {
          const filter = tranData.filter((f) => f.emiId != emiId);
          filter.forEach((tran) => {
            try {
              if (!useTran.includes(tran.id) && amount > 0) {
                let tempAmount = tran.paidAmount;
                if (tempAmount > amount) tempAmount = amount;
                const data: any = this.prePareRefundObject(
                  refund,
                  tran,
                  tempAmount,
                  lastADTrans,
                );
                if (!data?.message) {
                  data.rawData.adminId = adminId;
                  refundArray.push(data);
                  amount += data.rawData.paidAmount;
                  useTran.push(tran.id);
                }
              }
            } catch (error) {}
          });
        }

        if (amount > 0) {
          const data: any = this.prePareRefundObject(
            refund,
            null,
            amount,
            lastADTrans,
          );
          if (!data?.message) {
            data.rawData.adminId = adminId;
            refundArray.push(data);
            amount += data.rawData.paidAmount;
          }
        }
        return refundArray;
      } else return k422ErrorMessage(kSomthinfWentWrong);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  private prePareRefundObject(refund, refundTran, amount, lastADTrans) {
    try {
      const emiId = refund.emiId;
      let transactionId = 'RE-' + emiId + '-' + refund.loanId + '-A-' + amount;
      const source = refundTran?.source ?? 'RAZORPAY';
      const subSource = refundTran?.subSource ?? 'WEB';
      if (refundTran?.id) transactionId += '-' + refundTran?.id;
      if (refund?.failedRefund == true && refund?.tranData) {
        try {
          let failedRefund = refund?.tranData.filter(
            (f) => f?.status == kFailed && f?.type == 'REFUND',
          );
          failedRefund = failedRefund.sort((a, b) => b?.id - a?.id)[0];
          const lastFailId = failedRefund?.id;
          if (lastFailId) transactionId += '-' + lastFailId;
        } catch (error) {}
      }
      if (source === KYESUPI)
        transactionId = transactionId.replace(/[^a-zA-Z0-9]/g, '');
      let bifurcation = this.prepareBifurcationForRefund(
        refundTran,
        amount,
        refund,
        lastADTrans,
      );
      const rawData = {
        transactionId,
        paidAmount: -amount,
        status: 'INITIALIZED',
        type: 'REFUND',
        userId: refund.userId,
        name: refund.name,
        email: refund.email,
        loanId: refund.loanId,
        appType: refund?.appType,
        emiId,
        source,
        subSource,
        completionDate: this.typeService.getGlobalDate(new Date()).toJSON(),
        response: refundTran?.response,
        ...bifurcation,
      };

      return { refundTran, rawData };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  private prepareBifurcationForRefund(refundTran, amount, refund, lastADTrans) {
    let tempPaidAmt = amount;
    let bifurcationObj = {
      principalAmount: 0,
      interestAmount: 0,
      regInterestAmount: 0,
      legalCharge: 0,
      bounceCharge: 0,
      penaltyAmount: 0,
      penalCharge: 0,
      sgstOnBounceCharge: 0,
      cgstOnBounceCharge: 0,
      igstOnBounceCharge: 0,
      sgstOnPenalCharge: 0,
      cgstOnPenalCharge: 0,
      igstOnPenalCharge: 0,
      sgstOnLegalCharge: 0,
      cgstOnLegalCharge: 0,
      igstOnLegalCharge: 0,
    };
    if (refundTran?.paidAmount > amount && refundTran?.type == 'EMIPAY')
      return bifurcationObj;

    // When Transaction is EMIPAY
    if (refundTran?.type == 'EMIPAY') {
      bifurcationObj.principalAmount = refundTran?.principalAmount ?? 0;
      bifurcationObj.interestAmount = refundTran?.interestAmount ?? 0;
      bifurcationObj.regInterestAmount = refundTran?.regInterestAmount ?? 0;
      bifurcationObj.penaltyAmount = refundTran?.penaltyAmount ?? 0;
      bifurcationObj.legalCharge = refundTran?.legalCharge ?? 0;
      bifurcationObj.bounceCharge = refundTran?.bounceCharge ?? 0;
      bifurcationObj.penalCharge = refundTran?.penalCharge ?? 0;
      bifurcationObj.sgstOnBounceCharge = refundTran?.sgstOnBounceCharge ?? 0;
      bifurcationObj.cgstOnBounceCharge = refundTran?.cgstOnBounceCharge ?? 0;
      bifurcationObj.igstOnBounceCharge = refundTran?.igstOnBounceCharge ?? 0;
      bifurcationObj.sgstOnPenalCharge = refundTran?.sgstOnPenalCharge ?? 0;
      bifurcationObj.cgstOnPenalCharge = refundTran?.cgstOnPenalCharge ?? 0;
      bifurcationObj.igstOnPenalCharge = refundTran?.igstOnPenalCharge ?? 0;
      bifurcationObj.sgstOnLegalCharge = refundTran?.sgstOnLegalCharge ?? 0;
      bifurcationObj.cgstOnLegalCharge = refundTran?.cgstOnLegalCharge ?? 0;
      bifurcationObj.igstOnLegalCharge = refundTran?.igstOnLegalCharge ?? 0;
    }
    // When Transaction is PARTPAY
    if (refundTran?.type == 'PARTPAY') {
      if (amount > 0) {
        if (refundTran.principalAmount >= amount) {
          bifurcationObj.principalAmount = amount;
          amount = 0;
        } else {
          bifurcationObj.principalAmount = refundTran?.principalAmount;
          amount -= refundTran.principalAmount;
        }
      }
      if (amount > 0) {
        if (refundTran.interestAmount >= amount) {
          bifurcationObj.interestAmount = amount;
          amount = 0;
        } else {
          bifurcationObj.interestAmount = refundTran.interestAmount;
          amount -= refundTran.interestAmount;
        }
      }
      // Deferred Interest
      if (amount > 0) {
        if (refundTran.regInterestAmount >= amount) {
          bifurcationObj.regInterestAmount = amount;
          amount = 0;
        } else {
          bifurcationObj.regInterestAmount = refundTran?.regInterestAmount;
          amount -= refundTran.regInterestAmount;
        }
      }
      // BOUNCE CHARGES
      // Bounce Charge Calculation
      let bounceCharge =
        (refundTran?.bounceCharge ?? 0) +
        (refundTran?.sgstOnBounceCharge ?? 0) +
        (refundTran?.cgstOnBounceCharge ?? 0) +
        (refundTran?.igstOnBounceCharge ?? 0);
      // Preparing Igst Option from GSTs in Transactions
      let iGst = (refundTran?.igstOnBounceCharge ?? 0) > 0;
      if (!iGst && (refundTran?.cgstOnBounceCharge ?? 0) > 0) iGst = false;

      if (amount > 0) {
        if (bounceCharge >= amount) {
          bifurcationObj.bounceCharge = amount;
          amount = 0;
        } else {
          bifurcationObj.bounceCharge = bounceCharge;
          amount -= bounceCharge;
        }
      }
      // Bounce GST
      let tempBounce = bifurcationObj.bounceCharge;
      let gstBounceChargeAmount = 0;
      gstBounceChargeAmount = this.typeService.manageAmount(
        gstBounceChargeAmount,
      );
      if (gstBounceChargeAmount % 2 == 1) {
        gstBounceChargeAmount = gstBounceChargeAmount - 1;
      }
      bifurcationObj.bounceCharge =
        bifurcationObj.bounceCharge - gstBounceChargeAmount;
      if (tempBounce > bifurcationObj.bounceCharge + gstBounceChargeAmount)
        bifurcationObj.bounceCharge +=
          tempBounce - bifurcationObj.bounceCharge + gstBounceChargeAmount;
      bifurcationObj.sgstOnBounceCharge = iGst
        ? 0
        : +(gstBounceChargeAmount / 2).toFixed(2);
      bifurcationObj.cgstOnBounceCharge = iGst
        ? 0
        : +(gstBounceChargeAmount / 2).toFixed(2);
      bifurcationObj.igstOnBounceCharge = !iGst
        ? 0
        : +gstBounceChargeAmount.toFixed(2);
      // Penalty (Old User's Penal)
      if (amount > 0) {
        if (refundTran.penaltyAmount >= amount) {
          bifurcationObj.penaltyAmount = amount;
          amount = 0;
        } else {
          bifurcationObj.penaltyAmount = refundTran?.penaltyAmount;
          amount -= refundTran.penaltyAmount;
        }
      }
      // PENAL CHARGES (New Penalty)
      // Penal Charge Calculation
      let penalCharge =
        (refundTran?.penalCharge ?? 0) +
        (refundTran?.sgstOnPenalCharge ?? 0) +
        (refundTran?.cgstOnPenalCharge ?? 0) +
        (refundTran?.igstOnPenalCharge ?? 0);
      if (amount > 0) {
        if (penalCharge >= amount) {
          bifurcationObj.penalCharge = amount;
          amount = 0;
        } else {
          bifurcationObj.penalCharge = penalCharge;
          amount -= penalCharge;
        }
      }
      // Penal GST
      let tempPenal = bifurcationObj.penalCharge;
      let gstPenalChargeAmount = 0;
      gstPenalChargeAmount =
        this.typeService.manageAmount(gstPenalChargeAmount);
      if (gstPenalChargeAmount % 2 == 1) {
        gstPenalChargeAmount = gstPenalChargeAmount - 1;
      }
      bifurcationObj.penalCharge =
        bifurcationObj.penalCharge - gstPenalChargeAmount;
      if (tempPenal > bifurcationObj.penalCharge + gstPenalChargeAmount)
        bifurcationObj.penalCharge +=
          tempPenal - bifurcationObj.penalCharge + gstPenalChargeAmount;
      bifurcationObj.sgstOnPenalCharge = iGst
        ? 0
        : +(gstPenalChargeAmount / 2).toFixed(2);
      bifurcationObj.cgstOnPenalCharge = iGst
        ? 0
        : +(gstPenalChargeAmount / 2).toFixed(2);
      bifurcationObj.igstOnPenalCharge = !iGst
        ? 0
        : +gstPenalChargeAmount.toFixed(2);

      // LEGAL CHARGES
      // Legal Charge Calculation
      let legalCharge =
        (refundTran?.legalCharge ?? 0) +
        (refundTran?.sgstOnLegalCharge ?? 0) +
        (refundTran?.cgstOnLegalCharge ?? 0) +
        (refundTran?.igstOnLegalCharge ?? 0);
      if (amount > 0) {
        if (legalCharge >= amount) {
          bifurcationObj.legalCharge = amount;
          amount = 0;
        } else {
          bifurcationObj.legalCharge = legalCharge;
          amount -= legalCharge;
        }
      }
      // Legal GST
      let tempLegal = bifurcationObj.legalCharge;
      let gstLegalChargeAmount =
        bifurcationObj.legalCharge - bifurcationObj.legalCharge / 1.18;
      gstLegalChargeAmount =
        this.typeService.manageAmount(gstLegalChargeAmount);
      if (gstLegalChargeAmount % 2 == 1) {
        gstLegalChargeAmount = gstLegalChargeAmount - 1;
      }
      bifurcationObj.legalCharge =
        bifurcationObj.legalCharge - gstLegalChargeAmount;
      if (tempLegal > bifurcationObj.legalCharge + gstLegalChargeAmount)
        bifurcationObj.legalCharge +=
          tempLegal - bifurcationObj.legalCharge + gstLegalChargeAmount;
      bifurcationObj.sgstOnLegalCharge = iGst
        ? 0
        : +(gstLegalChargeAmount / 2).toFixed(2);
      bifurcationObj.cgstOnLegalCharge = iGst
        ? 0
        : +(gstLegalChargeAmount / 2).toFixed(2);
      bifurcationObj.igstOnLegalCharge = !iGst
        ? 0
        : +gstLegalChargeAmount.toFixed(2);
    }
    // When Transaction is FULLPAY
    if (refundTran?.type == 'FULLPAY') {
      if (amount > 0) {
        if (lastADTrans.principalAmount >= amount) {
          bifurcationObj.principalAmount = amount;
          amount = 0;
        } else {
          bifurcationObj.principalAmount = lastADTrans?.principalAmount;
          amount -= lastADTrans.principalAmount;
        }
      }
      if (amount > 0) {
        if (lastADTrans.interestAmount >= amount) {
          bifurcationObj.interestAmount = amount;
          amount = 0;
        } else {
          bifurcationObj.interestAmount = lastADTrans.interestAmount;
          amount -= lastADTrans.interestAmount;
        }
      }
      if (amount > 0) {
        bifurcationObj.interestAmount += amount;
      }
    }
    let newPaidAmount = 0;
    for (let key in bifurcationObj) {
      if (typeof bifurcationObj[key] === 'number' && bifurcationObj[key] > 0) {
        newPaidAmount += bifurcationObj[key];
        bifurcationObj[key] *= -1;
      }
    }
    if (newPaidAmount > tempPaidAmt) {
      for (let key in bifurcationObj) {
        if (typeof bifurcationObj[key] === 'number') {
          bifurcationObj[key] *= 0;
        }
      }
    }
    return bifurcationObj;
  }

  //#region refund  INITIALIZED
  private async refundInitialized(prePareData) {
    try {
      const rawData = prePareData?.rawData;
      const loanId = prePareData?.loanId;
      const ICICIPayRes = rawData?.response;
      if (
        rawData?.source === KICICIUPI ||
        rawData?.source === KICICIUPI2 ||
        rawData?.source === KYESUPI
      )
        delete rawData?.response;
      const create = await this.repository.createRowData(rawData);
      if (!create || create === k500Error) return kInternalError;
      // this is for tesing
      // const autoDebit = {
      //   transactionId: 'order_KxcfUAP0FfbQNm',
      //   utr: 'pay_KxcfUGX1tL0uk3',
      //   source: 'RAZORPAY',
      //   subSource: 'AUTODEBIT',
      // };
      // const cashFreeApp = {
      //   transactionId: 'CFORDER1676958720487',
      //   utr: '305281235541',
      //   source: 'CASHFREE',
      //   subSource: 'APP',
      // };
      // const razorpayAPP = {
      //   transactionId: 'order_IvrIJXDEqyJqxo',
      //   utr: 'pay_KuVAT7LIm7Tp3e',
      //   source: 'RAZORPAY',
      //   subSource: 'APP',
      // };
      // const cashFreeAutoDebit = {
      //   transactionId: 'CFORDER1676958720487',
      //   utr: '305281235541',
      //   source: 'CASHFREE',
      //   subSource: 'AUTODEBIT',
      // };
      // const refundTran = cashFreeAutoDebit;
      // const amount = 1;

      const refundTran = prePareData?.refundTran;
      const amount = -1 * rawData.paidAmount;
      const transactionId = rawData?.transactionId; // This is Id of Current Refund Transaction
      const orderId = refundTran?.transactionId; // Id of Which We Are refund to User
      let utr = refundTran?.utr;
      if ((utr ?? '').includes('DMY')) utr = utr.split('DMY')[0];
      let source = refundTran?.source;
      let subSource = refundTran?.subSource ?? '';

      let response;
      if (source === 'CASHFREE' && subSource != 'AUTODEBIT') {
        response = await this.cashFreeService.refundAmount(
          orderId,
          amount,
          transactionId,
        );
      } else if (source === 'RAZORPAY') {
        response = await this.razorpoayService.refundAmount(
          utr,
          amount,
          subSource == 'AUTODEBIT',
        );
        // Send Slack Message for Refund
        let text = `* REFUND INITIALIZED:Razorpay - \nUTR: ${utr} LoanId: ${loanId} \n
      RESPONSE: ${JSON.stringify(response)}} *`;
        await this.slackService.sendMsg({
          channel: EnvConfig.slack.repaymentMoniter,
          text,
          sourceStr: false,
        });
      } else if (source === KICICIUPI || source === KICICIUPI2) {
        response = await this.iciciService.Refund({
          ICICIPayRes,
          transactionId,
          orderId,
          amount,
          loanId,
          source,
        });

        // Send Slack Message for Refund
        let text = `* REFUND INITIALIZED:ICICIUPI - \nUTR: ${utr} LoanId: ${loanId} \n
      RESPONSE: ${JSON.stringify(response)}} *`;
        await this.slackService.sendMsg({
          channel: EnvConfig.slack.repaymentMoniter,
          text,
          sourceStr: false,
        });
      } else if (source === KYESUPI) {
        response = await this.yesUpiService.refund({
          originalNpciTxnId: utr,
          transactionId,
          amount,
          remark: `Refund for ${loanId}`,
        });
      } else {
        const loanId = rawData.loanId;
        const customer = await this.getUserDataForCashfree(loanId, amount);
        if (!customer || customer?.message) return customer;
        response = await this.cashFreeService.payOut(customer);
        subSource = 'CASHFREE';
        source = 'CASHFREE';
        // const customer = await this.getUserDataForRazorpayX(loanId, amount);
        // if (!customer || customer?.message) return customer;
        // response = await this.razorpoayService.payOut(customer, 'refund');
        // subSource = 'RAZORPAY_X';
      }

      if (!response || response?.message) response = { status: 'FAILED' };
      if (source === 'CASHFREE') response.source = source;
      response.subSource = subSource;
      response.completionDate = this.typeService
        .getGlobalDate(new Date())
        .toJSON();
      try {
        const status = response?.status;
        const userId = rawData.userId;
        const appType = rawData?.appType;
        const emailId = rawData.email;
        const title = 'Refund Amount';
        const refundAmount = Math.abs(Math.floor(amount));
        const content = `Dear Customer, Your refund of Rs.${refundAmount} has been processed and will be reflected in your bank account in the next 1 to 7 Days.`;
        if (status === 'COMPLETED' || status === 'INITIALIZED') {
          const userData = [];
          userData.push({ userId, appType });
          const body = {
            userData,
            content,
            title,
            isMsgSent: true,
            smsId:
              appType == 1
                ? kMsg91Templates.RefundSMSId
                : kLspMsg91Templates.RefundSMSId,
            userId,
            message: content,
            smsOptions: { REFUNDVALUE: refundAmount },
          };
          try {
            await this.notificationService.sendChatMsgToUser(body);
          } catch (error) {}
          await this.sharedNotification.sendRefundMail(emailId, rawData);
        }
      } catch (error) {}
      await this.repository.updateRowData(response, create.id);
      return response;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region get Data for reazore pay user
  private async getUserDataForCashfree(id, amount): Promise<any> {
    try {
      const bankingModel: any = { model: BankingEntity };
      bankingModel.attributes = [
        'id',
        'disbursementAccount',
        'disbursementIFSC',
      ];
      const userModel: any = { model: registeredUsers };
      userModel.attributes = ['fullName', 'phone', 'email'];
      const kycModel: any = { model: KYCEntity };
      kycModel.attributes = ['aadhaarAddress'];
      userModel.include = [kycModel];
      const options = {
        where: { id },
        include: [userModel, bankingModel],
      };
      const att = ['id'];
      const loanData = await this.loanRepo.getRowWhereData(att, options);
      if (!loanData || loanData === k500Error) return kInternalError;
      let address = '';
      try {
        address = this.typeService.getUserAadharAddress(
          loanData?.registeredUsers?.kycData?.aadhaarAddress,
        );
      } catch (error) {}

      const phone = this.cryptService.decryptPhone(
        loanData?.registeredUsers?.phone,
      );
      const customerData = {
        name: loanData?.registeredUsers?.fullName,
        email: loanData?.registeredUsers?.email,
        contact: phone,
        loanId: loanData?.id,
        amount: amount * 100,
        ifsc: loanData?.bankingData?.disbursementIFSC,
        account_number: loanData?.bankingData?.disbursementAccount,
        address,
        narration: 'REFUND OF ' + loanData?.id,
      };
      return customerData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region get Data for reazore pay user
  private async getUserDataForRazorpayX(id, amount): Promise<any> {
    try {
      const bankingModel: any = { model: BankingEntity };
      bankingModel.attributes = [
        'id',
        'disbursementAccount',
        'disbursementIFSC',
      ];
      const userModel: any = { model: registeredUsers };
      userModel.attributes = ['fullName', 'phone', 'email'];
      const options = { where: { id }, include: [userModel, bankingModel] };
      const att = ['id'];
      const loanData = await this.loanRepo.getRowWhereData(att, options);
      if (!loanData || loanData === k500Error) return kInternalError;
      const customerData = {
        name: loanData?.registeredUsers?.fullName,
        email: loanData?.registeredUsers?.email,
        contact: loanData?.registeredUsers?.phone,
        loanId: loanData?.id,
        amount,
        ifsc: loanData?.bankingData?.disbursementIFSC,
        accountNumber: loanData?.bankingData?.disbursementAccount,
      };
      return customerData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region  check refund update or not
  async checkPendingRefund() {
    try {
      const options = { where: { type: 'REFUND', status: 'INITIALIZED' } };
      const att = [
        'id',
        'transactionId',
        'utr',
        'source',
        'subSource',
        'response',
      ];
      const result = await this.repository.getTableWhereData(att, options);
      if (!result || result === k500Error) return kInternalError;
      if (!result?.length) return;
      for (let index = 0; index < result.length; index++) {
        const tran = result[index];
        try {
          const response = await this.checkStatusOfRefund(tran);
          if (response?.status) {
            let res = {};
            try {
              if (response?.response) {
                if (tran?.response) {
                  try {
                    res = JSON.parse(tran.response);
                    if (Object.keys(res).length == 0) res = {};
                  } catch (error) {}
                }
                if (
                  response?.status == kFailed ||
                  response?.status == 'FAILURE'
                ) {
                  const failedResponse = JSON.parse(response?.response);
                  if (response?.status == 'FAILURE') response.status = kFailed;
                  if (failedResponse?.status == 'FAILURE')
                    failedResponse.status = kFailed;
                  res = {
                    ...res,
                    status: failedResponse?.status ?? 'FAILED',
                    failedResponse,
                  };
                } else res = { ...res, ...JSON.parse(response?.response) };
                if (Object.keys(res).length > 0)
                  response.response = JSON.stringify(res);
              }
            } catch (error) {}
            response.completionDate = this.typeService
              .getGlobalDate(new Date())
              .toJSON();
            await this.repository.updateRowData(response, tran.id);
          }
        } catch (error) {}
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region check status
  private async checkStatusOfRefund(tran) {
    try {
      const source = tran?.source;
      const subSource = tran?.subSource ?? '';
      const res = tran?.response ? JSON.parse(tran.response) : {};
      let response;
      if (source === 'CASHFREE' && subSource == 'CASHFREE') {
        const utr = tran.utr;
        if (utr) response = await this.cashFreeService.findPayoutData(utr);
      } else if (source === 'CASHFREE' && subSource != 'AUTODEBIT') {
        const orderId = res?.order_id;
        const transactionId = res?.refund_id;
        response = await this.cashFreeService.getRefundStatus(
          orderId,
          transactionId,
        );
      } else if (source === 'RAZORPAY' && subSource != 'RAZORPAY_X') {
        const pay_id = res?.payment_id;
        const refundId = res?.id;

        response = await this.razorpoayService.getRefundStatus(
          pay_id,
          refundId,
          subSource === 'AUTODEBIT',
        );

        // Send Slack Message for Refund Issue
        let text = `* REFUND MONITORING:Razorpay - \nPay_id: ${pay_id} \n RefundId: ${refundId} \n Transaction:${JSON.stringify(
          tran,
        )}
        RESPONSE: ${JSON.stringify(response)} \n BODY: ${JSON.stringify(res)}
        *`;
        await this.slackService.sendMsg({
          channel: EnvConfig.slack.repaymentMoniter,
          text,
          sourceStr: false,
        });
      } else if (source === KICICIUPI || source === KICICIUPI2) {
        const transactionType = 'R';
        const transactionId = tran?.transactionId;
        response = await this.iciciService.CallbackStatus({
          transactionType,
          transactionId,
        });
      } else if (source === KYESUPI) {
        response = await this.yesUpiService.refundStatus({
          transactionId: tran?.transactionId,
        });
      } else {
        const utr = tran.utr;
        if (utr) response = await this.razorpoayService.findPayoutData(utr);
      }
      return response;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region re Initialize Refund
  async reInitializeRefund(id, adminId) {
    try {
      /// check view access
      const access = await this.adminRepo.checkHasAccess(adminId, 'refund');
      if (access !== true) return access;
      const checkIsFiled: any = await this.checkRefundIsFiled(id);
      if (checkIsFiled?.message) return checkIsFiled;
      if (checkIsFiled?.isFailed === true) {
        const result = await this.repository.deleteSingleData(id, false);
        if (result === k500Error) return kInternalError;
        const loanId = checkIsFiled?.loanId;
        if (loanId) return await this.checkAndPlaceRefund({ adminId, loanId });
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region check initi
  private async checkRefundIsFiled(id) {
    try {
      const options = { where: { type: 'REFUND', status: 'FAILED', id } };
      const att = [
        'id',
        'transactionId',
        'utr',
        'source',
        'subSource',
        'response',
        'loanId',
      ];
      const result = await this.repository.getRowWhereData(att, options);
      if (!result || result === k500Error) return kInternalError;
      const response = result?.response;
      const data = await this.checkStatusOfRefund(result);
      if (data?.status == 'FAILED' || !response)
        return { isFailed: true, loanId: result.loanId };
      else return k422ErrorMessage(kSomthinfWentWrong);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  async refundAutomation(query) {
    try {
      return {};
      let totalCount = 0;
      const stDate = new Date();
      stDate.setHours(stDate.getHours() - 48);
      const startDate = query?.startDate ?? stDate;
      const endDate = query?.endDate ?? new Date();
      const adminId = SYSTEM_ADMIN_ID;
      const refundData = await this.getRefundablesData(
        startDate,
        endDate,
        adminId,
      );
      if (refundData === k500Error) return kInternalError;
      if (refundData?.message) return refundData;
      const filteredData = refundData?.filteredData ?? [];
      const refundList = filteredData.filter(
        (f) =>
          f.userType == 'On-Time' && f.waiver == 0 && f.failedRefund == false,
      );

      for (let index = 0; index < refundList.length; index++) {
        try {
          const ele = refundList[index];
          const loanId = ele?.loanId;
          const userId = ele?.userId;

          // Turning Off Refund Automation for Users Who Used Refund Credit
          let isUserFromActiveOntimeBucket =
            await this.sharedTransactionService.isUserFromActiveOntimeBucket(
              userId,
            );
          if (isUserFromActiveOntimeBucket) {
            const text = `*CREDIT USER - REFUND GENERATED*`;
            const body = {
              isUserFromActiveOntimeBucket,
              ele,
            };
            const threads = [`Body details -> ${JSON.stringify(body)}`];
            this.slackService.sendMsg({ text, threads });
            continue;
          }
          const body = { loanId, adminId };
          const refundData = await this.checkAndPlaceRefund(body);
          if (Array.isArray(refundData) && refundData?.length) totalCount++;
        } catch (error) {}
      }

      // slack message in cron-alert
      const slackPayload = {
        url: 'admin/transaction/refundAutomation',
        fieldObj: {
          refundList,
          totalCount,
        },
      };
      this.slackService.sendSlackCronAlert(slackPayload);

      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // Add Refund Transaction
  //#region addRefundTransaction
  async addRefundTransaction(body) {
    const loanId = body?.loanId;
    if (!loanId) return kParamMissing('loanId');
    const adminId = body?.adminId;
    if (!adminId) return kParamMissing('adminId');
    const amount = body?.amount;
    if (!amount) return kParamMissing('amount');
    const refundDate = body?.refundDate;
    if (!refundDate) return kParamMissing('refundDate');
    const utr = body?.utr;
    if (!utr) return kParamMissing('utr');
    const source = body?.source;
    if (!source) return kParamMissing('source');
    if (source === kRazorpay || source === kCashfree) body.subSource = kManual;

    // Check Admin Has Access to Add Refund Entry
    const access = await this.adminRepo.checkHasAccess(adminId, 'refund');
    if (access !== true) return access;

    // Fetch Refundable Data
    const result = await this.getRefundablesData(null, null, adminId, loanId);
    if (result?.message) return result;
    if (!result?.isRefund) return k422ErrorMessage(kYouHaveAccess);

    // Initiate -> Refund process
    const refund = result?.filteredData[0];
    const refundAmount = refund?.amount;

    // Sending Message in Slack - Just for Validation
    if (amount >= refundAmount + 10 || amount <= refundAmount - 10) {
      let text = `*MANUAL REFUND - \nTransaction Initiated With Invalid Amount: L-${loanId} \n Amount:${amount}  Refund Amount:${refundAmount}*`;
      await this.slackService.sendMsg({
        channel: EnvConfig.slack.repaymentMoniter,
        text,
        sourceStr: false,
      });
    }

    if (refund?.loanId) {
      // Validation -> Applicable refund amount and transaction
      const tranData = await this.getTransactionDataFroRfund(
        refund.loanId,
        true,
      );
      if (
        !tranData ||
        (tranData?.message && tranData?.message != kNoTransactionForRefund)
      )
        return tranData;

      // If No Transaction Found for Refund // Adding Dummy Refund Transaction
      if (tranData?.message == kNoTransactionForRefund) {
        const rawData: any = {
          response: null,
          principalAmount: 0,
          interestAmount: 0,
          regInterestAmount: 0,
          legalCharge: 0,
          bounceCharge: 0,
          penaltyAmount: 0,
          penalCharge: 0,
          sgstOnBounceCharge: 0,
          cgstOnBounceCharge: 0,
          igstOnBounceCharge: 0,
          sgstOnPenalCharge: 0,
          cgstOnPenalCharge: 0,
          igstOnPenalCharge: 0,
          sgstOnLegalCharge: 0,
          cgstOnLegalCharge: 0,
          igstOnLegalCharge: 0,
        };
        let completionDate: any = refundDate
          ? new Date(refundDate)
          : new Date();
        const year = completionDate.getFullYear();
        if (isNaN(year) || year == 1970) completionDate = new Date();
        completionDate = this.typeService.getGlobalDate(completionDate);
        completionDate = completionDate.toJSON();
        rawData.paidAmount = -1 * Math.abs(amount);
        rawData.status = kCompleted;
        rawData.source = source ?? KICICIUPI;
        rawData.subSource = body?.subSource ?? kDirectBankPay;
        rawData.utr = body?.utr;
        rawData.loanId = loanId;
        rawData.userId = refund?.userId;
        rawData.type = kRefund;
        rawData.adminId = adminId;
        rawData.completionDate = completionDate;
        rawData.transactionId = 'RE-' + utr;
        const create = await this.repository.createRowData(rawData);
        if (!create || create === k500Error)
          throw new Error('Error in creating refund transaction');
        return true;
      }

      // Calculation -> Refund amount
      const prePareData = this.prePareRefundAmount(
        refund,
        tranData,
        adminId,
        result?.lastADTrans,
      );
      if (prePareData?.message) return prePareData;

      // Iteration -> Refund initiate
      const sessionId: string = uuidv4();
      for (let index = 0; index < prePareData.length; index++) {
        const ele = prePareData[index];
        ele.loanId = loanId;

        // Create -> System trace row data
        const systemCreationData = {
          sessionId,
          type: 7,
          loanId,
          userId: ele.rawData?.userId,
          uniqueId: `T${7}=L${loanId}=E${ele.rawData.emiId ?? -1}`,
        };

        // Adding System Trace Entry to Prevent Duplicate Refunds
        const createdData = await this.repoManager.createRowData(
          SystemTraceEntity,
          systemCreationData,
        );
        if (createdData === k500Error) {
          // Sending Message in Slack - Due to Multiple Refunds for Same Loan and EMI
          let text = `*MANUAL REFUND - \nTransaction Initiated for Same Loan and EMI : L-${loanId}*`;
          await this.slackService.sendMsg({
            channel: EnvConfig.slack.repaymentMoniter,
            text,
            sourceStr: false,
          });
          continue;
        }

        // Updating Direct Success Refund Transaction
        const rawData = ele?.rawData;
        rawData.status = kCompleted;
        rawData.source = source ?? KICICIUPI;
        rawData.subSource = body?.subSource ?? kDirectBankPay;
        rawData.utr = body?.utr;

        // Validation -> Entered Amount Different from Calculated Amount
        if (Math.abs(refundAmount) !== Math.abs(amount)) {
          if (
            Math.abs(amount) > Math.abs(refundAmount) ||
            Math.abs(amount) < Math.abs(refundAmount)
          )
            rawData.paidAmount = -1 * Math.abs(amount);
        }
        const create = await this.repository.createRowData(rawData);
        if (!create || create === k500Error)
          throw new Error('Error in creating refund transaction');
      }
      return prePareData;
    } else
      return k422ErrorMessage('System Refund Not Supported! Contact IT Team`,');
  }
}
