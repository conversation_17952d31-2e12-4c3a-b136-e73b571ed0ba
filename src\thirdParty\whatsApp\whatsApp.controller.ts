import { Response } from 'express';
import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Res,
  Param,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import {
  kInternalError,
  kSuccessData,
  k422ErrorMessage,
} from 'src/constants/responses';
import { WhatsAppService } from './whatsApp.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { kUploadFileObj } from 'src/constants/objects';
import { ErrorContextService } from 'src/utils/error.context.service';

@Controller('whatsApp/')
export class WhatsAppController {
  constructor(
    private readonly service: WhatsAppService,
    private readonly errorContextService: ErrorContextService,
  ) {}

  @Post('sendMessage')
  async sendWhatsAppMessage(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.sendWhatsAppMessageMicroService(
        body,
      );
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData });
    } catch (error) {
      return kInternalError;
    }
  }

  @Post('webhook')
  async funStoreWhatsAppWebhook(
    @Query() query,
    @Body() body,
    @Res() res: Response,
  ) {
    try {
      this.service.storeWhatsAppWebhookResponse(body).catch((err) => {});
      res
        .status(200)
        .setHeader('Content-Type', 'text/html')
        .send(query?.challange ?? body?.challange);
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Get('webhook')
  async funStoreWhatsAppWebhookTest(
    @Query() query,
    @Body() body,
    @Res() res: Response,
  ) {
    try {
      this.service.storeWhatsAppWebhookResponse(query).catch((err) => {});
      res
        .status(200)
        .setHeader('Content-Type', 'text/html')
        .send(query?.challange ?? body?.challange);
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  // start region send whatsapp campaign message
  @Post('sendCampaignMessage')
  async funSendCampaignMessage(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.sendCampaignMessage(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData });
    } catch (error) {
      return kInternalError;
    }
  }

  @Get('sendKYCStageMessage')
  async sendKYCStageMessage(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.sendKYCStageMessage(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return kInternalError;
    }
  }

  @Post('updateTargetNumber')
  async updateTargetNumber(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.updateTargetNumber(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return kInternalError;
    }
  }

  @Get('sendUserStuckWhatsApp')
  async funSendUserStuckWhatsApp(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.sendUserStuckWhatsApp(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return kInternalError;
    }
  }

  @Get('cronJobCheckWhatsAppNumber')
  async cronJobCheckWhatsAppNumber(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.cronJobCheckWhatsAppNumber(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return kInternalError;
    }
  }

  @Post('getNonWhatsAppUsers')
  async getNonWhatsAppUsers(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.getNonWhatsAppUsers(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return kInternalError;
    }
  }
  // end region

  @Post('whatsappChecker')
  @UseInterceptors(FileInterceptor('file', kUploadFileObj()))
  async checkWhatsAppNumber(
    @Body() body,
    @UploadedFile() file: any,
    @Res() res,
  ) {
    try {
      if (file && file?.path) body.file = file;
      const data = await this.service.whatsAppChecker(body);
      if (data?.message) return res.send(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Get('getWhatsappCheckList')
  async funGetWhatsappCheckList(@Res() res) {
    try {
      const data: any = await this.service.getWhatsappCheckList();
      if (data?.message) return res.send(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }
}
