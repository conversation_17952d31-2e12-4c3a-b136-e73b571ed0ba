// Imports
import { LeadServiceV4 } from './lead.service';
import { Body, Controller, Post, Res } from '@nestjs/common';
import { kInternalError, kSuccessData } from 'src/constants/responses';

@Controller('lead')
export class LeadControllerV4 {
    constructor(private readonly service: LeadServiceV4) { }

    @Post('expressEligibility')
    async funExpressEligiblity(@Body() body, @Res() res) {
        try {
            await this.service.expressEligibility(body);
            return res.send(kSuccessData)
        } catch (error) {
            return res.send(kInternalError)
        }
    }
}
