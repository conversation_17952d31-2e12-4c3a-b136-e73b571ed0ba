// Imports
import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { EnvConfig } from 'src/configs/env.config';

@Injectable()
export class LeadOpsGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const value = getKeyValue(context, 'fin-key');
    if (!value) return false;
    if (!EnvConfig.secrets.finKey) return false;
    if (value != EnvConfig.secrets.finKey) return false;
    return true;
  }
}

function getKeyValue(context: ExecutionContext, keyName: string) {
  try {
    const key = keyName?.toLowerCase();
    const request = context.switchToHttp().getRequest();
    const headers = request.headers;
    const encryptedKey = headers[key];
    return encryptedKey;
  } catch (error) {}
}
