import { Body, Controller, Get, Post, Query, Res } from '@nestjs/common';
import { WhatsappLoanFlowService } from './whatsapp.loan.flow.service';
import { kInternalError, kSuccessData } from 'src/constants/responses';
import { Response } from 'express';
@Controller('shared/whatsapp/loan')
export class WhatsappLoanController {
  constructor(private readonly whatsappLoanService: WhatsappLoanFlowService) {}
  //#region  call back whatsapp data for KYC
  @Get('callback')
  async funStoreWhatsAppWebhookTest(
    @Query() query,
    @Body() body,
    @Res() res: Response,
  ) {
    try {
      this.whatsappLoanService
        .callBackDataForWhatsapp(query)
        .catch((err) => {});
      res
        .status(200)
        .status(200)
        .setHeader('Content-Type', 'text/html')
        .send(query?.challange ?? body?.challange);
    } catch (error) {
      return res.send(kInternalError);
    }
  }
}
