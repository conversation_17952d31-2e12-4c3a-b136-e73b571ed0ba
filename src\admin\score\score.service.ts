import { Injectable } from '@nestjs/common';
import { k500Error } from 'src/constants/misc';
import { kInternalError } from 'src/constants/responses';
import { EmployementSectoreRepository } from 'src/repositories/sector.repository';

@Injectable()
export class ScoreService {
  constructor(
    private readonly employmentSectorRepo: EmployementSectoreRepository,
  ) {}

  async getAllSectorData() {
    try {
      const attributes = ['id', 'sectorName', 'sectorStatusVerified'];
      const options = {};
      const sectorData: any = await this.employmentSectorRepo.getTableWhereData(
        attributes,
        options,
      );
      if (sectorData === k500Error) return kInternalError;
      const finalData = this.prepareSectorFinalData(sectorData);
      return finalData;
    } catch (error) {
      console.error('Error in: ', error);
      return kInternalError;
    }
  }

  private prepareSectorFinalData(list) {
    try {
      const finalData = [];
      list.forEach((ele) => {
        try {
          if (ele?.sectorStatusVerified) {
            ele.sectorStatusVerified =
              ele.sectorStatusVerified == '0' ? 'Yes' : 'No';
          }
          const tempData = {};
          tempData['No'] = ele?.id ?? '-';
          tempData['Name'] = ele?.sectorName ?? '-';
          tempData['Is blackList?'] = ele?.sectorStatusVerified ?? '-';
          finalData.push(tempData);
        } catch (error) {}
      });
      return finalData;
    } catch (error) {
      console.error('Error in: ', error);
      return kInternalError;
    }
  }
}
