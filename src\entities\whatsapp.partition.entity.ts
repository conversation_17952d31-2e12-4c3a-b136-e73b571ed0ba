// Imports
import { Model, Table } from 'sequelize-typescript';
import { Column, DataType } from 'sequelize-typescript';

@Table({})
export class WhatsApp_0 extends Model<WhatsApp_0> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
  })
  source: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  requestData: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  service: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  whatsappMsgId: string;
}

@Table({})
export class WhatsApp_1 extends Model<WhatsApp_1> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
  })
  source: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  requestData: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  service: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  whatsappMsgId: string;
}

@Table({})
export class WhatsApp_2 extends Model<WhatsApp_2> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
  })
  source: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  requestData: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  service: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  whatsappMsgId: string;
}

@Table({})
export class WhatsApp_3 extends Model<WhatsApp_3> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
  })
  source: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  requestData: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  service: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  whatsappMsgId: string;
}

@Table({})
export class WhatsApp_4 extends Model<WhatsApp_4> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
  })
  source: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  requestData: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  service: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  whatsappMsgId: string;
}

@Table({})
export class WhatsApp_5 extends Model<WhatsApp_5> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
  })
  source: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  requestData: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  service: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  whatsappMsgId: string;
}

@Table({})
export class WhatsApp_6 extends Model<WhatsApp_6> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
  })
  source: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  requestData: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  service: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  whatsappMsgId: string;
}

@Table({})
export class WhatsApp_7 extends Model<WhatsApp_7> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
  })
  source: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  requestData: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  service: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  whatsappMsgId: string;
}

@Table({})
export class WhatsApp_8 extends Model<WhatsApp_8> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
  })
  source: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  requestData: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  service: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  whatsappMsgId: string;
}

@Table({})
export class WhatsApp_9 extends Model<WhatsApp_9> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
  })
  source: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  requestData: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  service: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  whatsappMsgId: string;
}
