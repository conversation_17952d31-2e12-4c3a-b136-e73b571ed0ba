// Imports
import {
  k422ErrorMessage,
  kInternalError,
  kParamsMissing,
  kSuccessData,
} from 'src/constants/responses';
import { k500Error } from 'src/constants/misc';
import { Body, Controller, Post, Res } from '@nestjs/common';
import { MediaSharedService } from 'src/shared/media.service';
import { CryptService } from 'src/utils/crypt.service';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { registeredUsers } from 'src/entities/user.entity';
import { HashPhoneEntity } from 'src/entities/hashPhone.entity';
import { kNoDataFound } from 'src/constants/strings';
import { ErrorContextService } from 'src/utils/error.context.service';

@Controller('media')
export class MediaControllerV4 {
  constructor(
    private readonly crypt: CryptService,
    private readonly repo: RepositoryManager,
    private readonly mediaSharedService: MediaSharedService,
    private readonly errorContextService: ErrorContextService,
  ) {}

  @Post('uploadMedia')
  async funUploadMedia(@Body() body, @Res() res) {
    try {
      const docUrl: string = body.url;
      const type: string = body.type;
      let userId: string = body.userId;
      const docType: string = body.docType ?? 'Other';
      const password: string = body.password ?? '';
      const adminId = body?.adminId;
      const fromMobile = body?.fromMobile ?? false;

      if (!userId) {
        let phone = (body?.phone ?? '').replace(/ /g, '').replace(/\+/, '');
        phone = this.crypt.getMD5Hash(phone.slice(-10));
        const hashPhoneData = await this.repo.getRowWhereData(
          HashPhoneEntity,
          ['userId'],
          { where: { hashPhone: phone } },
        );
        if (hashPhoneData === k500Error) throw new Error();
        if (!hashPhoneData) return k422ErrorMessage(kNoDataFound);
        userId = hashPhoneData.userId;
      }

      if (!docUrl && !type && !adminId) return res.json(kParamsMissing);
      const mediaData: any = {
        docType,
        docUrl,
        password,
        type,
        userId,
        adminId,
        fromMobile,
      };
      const result = await this.mediaSharedService.uploadMediaToCloud(
        mediaData,
      );
      if (result == k500Error) throw new Error();
      return res.json({ valid: true, data: { mediaUrl: result.docUrl } });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Post('deleteMedia')
  async funDeleteMedia(@Body() body, @Res() res) {
    try {
      const docId: number = +body?.docId;
      if (!docId) return res.json(kParamsMissing);
      const result: any = await this.mediaSharedService.deleteRowData(docId);
      if (result == k500Error) return res.json(kInternalError);
      if (result.message) return res.json(result);
      return res.json(kSuccessData);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }
}
