// Imports
import { Table, Column, Model, DataType,ForeignKey } from 'sequelize-typescript';
import { Department } from './department.entity';

@Table({})
export class RBIGuidelineEntity extends Model<RBIGuidelineEntity> {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  keyPoints: string;
  
  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  docUrl: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  adminId: number;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  rbiCircularNo: string;

  @ForeignKey(() => Department)
  @Column({
    type: DataType.ARRAY(DataType.INTEGER),
    allowNull: true,
    defaultValue: [],
  })
  departmentIds: number;
}
