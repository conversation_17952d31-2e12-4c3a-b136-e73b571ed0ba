// Imports
import { Injectable } from '@nestjs/common';
import { EnvConfig } from 'src/configs/env.config';
import { k500Error } from 'src/constants/misc';
import {
  kZohoDetails,
  ZOHO_REDIRECT_URL,
  ZOHO_TOKEN_URL,
  ZOHO_USER_INFO_URL,
} from 'src/constants/network';
import { kInternalError, kParamMissing } from 'src/constants/responses';
import { APIService } from 'src/utils/api.service';

@Injectable()
export class ZohoService {
  constructor(private readonly api: APIService) {}

  async exchangeCodeForToken(code: string): Promise<any> {
    try {
      if (!code) return kParamMissing('code');
      const reqBody = new URLSearchParams({
        grant_type: kZohoDetails.grant_type,
        code,
        client_id: EnvConfig.zoho.oauth_client_id,
        client_secret: EnvConfig.zoho.oauth_client_secret,
        redirect_uri: ZOHO_REDIRECT_URL,
        scope: kZohoDetails.scope,
      });

      const response = await this.api.post(
        ZOHO_TOKEN_URL,
        reqBody.toString(),
        null,
        null,
        { headers: kZohoDetails.headers },
      );
      if (!response || response === k500Error) return kInternalError;
      if (!response?.access_token) return kInternalError;

      const userData = await this.getUserInfo(response.access_token);
      if (!userData || userData === k500Error) return kInternalError;
      return userData;
    } catch (error) {
      return kInternalError;
    }
  }

  async getUserInfo(accessToken: string): Promise<any> {
    const response = await this.api.get(ZOHO_USER_INFO_URL, null, {
      Authorization: `Bearer ${accessToken}`,
    });
    return response;
  }
}
