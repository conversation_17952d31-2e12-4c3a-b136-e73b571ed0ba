import { Controller, Get, Query, Res } from '@nestjs/common';
import { kInternalError, kSuccessData } from 'src/constants/responses';
import { ScoreService } from './score.service';

@Controller('admin/score/')
export class ScoreController {
  constructor(private readonly service: ScoreService) {}

  //#region getting all employment sectors
  @Get('/getAllEmploymentSectors')
  async findAllSector(@Res() res) {
    try {
      const sectorData: any = await this.service.getAllSectorData();
      if (sectorData?.message) return res.json(sectorData);
      return res.json({ ...kSuccessData, data: sectorData });
    } catch (error) {
      console.error('Error in: ', error);
      return res.json(kInternalError);
    }
  }
}
