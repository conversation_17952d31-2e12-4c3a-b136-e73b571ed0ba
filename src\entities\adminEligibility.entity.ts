import { Column, DataType, Model, Table } from 'sequelize-typescript';

@Table({
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
})
export class AdminEligibilityEntity extends Model<AdminEligibilityEntity> {
  @Column({
    type: DataType.BIGINT,
    primaryKey: true,
    autoIncrement: true,
    allowNull: false,
  })
  id: number;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    comment: '[true: Yes, false: No (default)]',
  })
  is_eligible: boolean;

  @Column({
    type: DataType.SMALLINT,
  })
  department_id: number;

  @Column({
    type: DataType.SMALLINT,
  })
  updated_by: number;

  @Column({
    type: DataType.TEXT,
  })
  emp_code: string;

  @Column({
    type: DataType.TEXT,
  })
  hash_emp_phone: string;

  @Column({
    type: DataType.TEXT,
  })
  hash_aadhaar: string;

  @Column({
    type: DataType.TEXT,
  })
  hash_pan: string;

  @Column({
    type: DataType.TEXT,
  })
  enc_pan: string;

  @Column({
    type: DataType.TEXT,
  })
  email: string;

  @Column({
    type: DataType.TEXT,
  })
  emp_name: string;

  @Column({
    type: DataType.TEXT,
  })
  enc_emp_phone: string;

  @Column({
    type: DataType.TEXT,
  })
  enc_aadhaar: string;
}
