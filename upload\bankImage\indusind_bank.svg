<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 23.0.5, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 50 50" style="enable-background:new 0 0 50 50;" xml:space="preserve">
<style type="text/css">
	.st0{fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
	.st1{fill-rule:evenodd;clip-rule:evenodd;fill:#1C9ECD;}
	.st2{fill:none;stroke:#FFFFFF;stroke-width:0.418;stroke-miterlimit:2.414;}
	.st3{fill-rule:evenodd;clip-rule:evenodd;fill:#98272B;}
	.st4{fill:none;stroke:#9A292D;stroke-width:0.21;stroke-miterlimit:2.414;}
	.st5{fill:none;stroke:#9A292D;stroke-width:0.63;stroke-miterlimit:2.414;}
	.st6{fill:none;stroke:#FFFFFF;stroke-width:0.42;stroke-miterlimit:2.414;}
</style>
<g>
	<path class="st0" d="M25.3,6.5c12.7,0,23,8.3,23,18.5c0,10.2-10.3,18.5-23,18.5c-12.7,0-23-8.3-23-18.5C2.3,14.8,12.6,6.5,25.3,6.5
		"/>
	<path class="st1" d="M23.5,43.5c-6.8-0.4-12.8-3.3-16.7-7.4h7.5C16.6,38.5,19.7,41,23.5,43.5"/>
	<path class="st1" d="M25.8,36h9.3c-2.4,2.5-5.5,4.9-9.3,7.4V36z"/>
	<path class="st1" d="M24.8,43.4c-3.8-2.5-6.9-4.9-9.3-7.4h9.3V43.4z"/>
	<path class="st1" d="M43.8,36c-3.8,4.2-9.8,7-16.6,7.5c3.8-2.5,6.9-5,9.2-7.5H43.8z"/>
	<path class="st1" d="M2.3,24.5C2.4,21,3.8,17.7,6,14.9h7.1c-2.8,3.2-4.1,6.4-4.2,9.6H2.3z"/>
	<path class="st1" d="M13.5,35.1H6c-2.2-2.8-3.6-6.1-3.7-9.6H9C9.2,28.7,10.7,31.9,13.5,35.1"/>
	<path class="st1" d="M48.3,25.5c-0.1,3.5-1.5,6.8-3.7,9.6h-7.4c2.8-3.2,4.3-6.4,4.5-9.6H48.3z"/>
	<path class="st1" d="M44.6,14.9c2.2,2.8,3.6,6.1,3.7,9.6h-6.5c-0.1-3.2-1.4-6.4-4.2-9.6H44.6z"/>
	<path class="st1" d="M27.2,6.5c6.8,0.4,12.7,3.3,16.6,7.5h-7C34.4,11.5,31.2,9,27.2,6.5"/>
	<path class="st1" d="M14,13.9H6.8c3.9-4.2,9.9-7,16.7-7.5C19.5,9,16.3,11.5,14,13.9"/>
	<path class="st1" d="M24.8,13.9h-9.6c2.4-2.4,5.5-4.9,9.6-7.4V13.9z"/>
	<path class="st1" d="M25.8,6.6c4,2.5,7.2,4.9,9.6,7.4h-9.6V6.6z"/>
	<path class="st0" d="M32.3,31.4l0.1,0.1l0,0.3v0.1v0.2c0,0.7-0.4,1-1,1.2h-0.1h-0.1h-0.1l0,0l-0.1,0L31,33.2l0-0.1V33l0-0.1v-0.1
		l0-0.1v-0.1l0.1-0.1l0-0.1l0.1-0.2l0.1,0l0.1,0l0.1-0.1l0.2-0.1l0,0l0-0.1l0-0.1l0-0.1l0-0.1l0-0.2l-0.1-0.1l-0.1-0.2l-0.3-0.4
		c-0.4-0.9,0.2-0.9-0.1-1.7l-0.1-0.2l-0.2-0.3l-0.3-0.5l-0.3-0.4l-0.1-0.2l-0.1-0.2c-0.3-0.7,0-1.5,0.2-2.3v-0.3v-0.3l0-0.3v-0.1
		l0-0.1l0,0l0,0h0l0,0l0,0l0,0l0.1,0.2v0.2l0.1,0.2v0.2V25l0,0.4c-0.2,1.1-0.2,1.8,0.6,2.6l0.2,0.1l0.1,0.1c0.7,0.7,0.4,1.1,0.4,2
		v0.2l0.1,0.3l0.1,0.2l0.1,0.3L32.3,31.4z"/>
	<path class="st0" d="M31.5,26.2v0.2l0,0.2c0.1,1.1,1.2,1.5,1.9,2.2l0.1,0.2l0.1,0.2c0.2,0.6,0,1.9-0.8,1.8l-0.1,0l0,0l0,0l0,0
		c0.6-0.6,0.4-1.6-0.1-2.2l-0.2-0.2l-0.2-0.2l-0.3-0.2c-0.3-0.4-0.3-0.3-0.5-0.8l-0.1-0.2l-0.1-0.2c-0.1-0.4-0.1-0.7-0.1-1.1l0-0.1
		l0,0h0l0,0.1v0.1l0,0.1L31.5,26.2z"/>
	<path class="st0" d="M29.2,28.2l-0.1,0.1v0.2c-0.3,1,0.6,1.6,1,2.5l0,0.2v0.2c0,0.5-0.5,1.6-1.2,1.3l0,0l0,0l0,0v0H29l0.1-0.1
		c0.7-0.4,0.7-1.4,0.4-2.1l-0.1-0.2l-0.1-0.3l-0.1-0.1L29,29.6l0-0.2l0-0.2v-0.2V29v-0.3l0.1-0.3l0.1-0.2l0.1-0.2l0-0.1l0.1,0h0h0v0
		v0.1l0,0.1L29.2,28.2z"/>
	<path class="st2" d="M29.2,28.2l-0.1,0.1v0.2c-0.3,1,0.6,1.6,1,2.5l0,0.2v0.2c0,0.5-0.5,1.6-1.2,1.3l0,0l0,0l0,0v0H29l0.1-0.1
		c0.7-0.4,0.7-1.4,0.4-2.1l-0.1-0.2l-0.1-0.3l-0.1-0.1L29,29.6l0-0.2l0-0.2v-0.2V29v-0.3l0.1-0.3l0.1-0.2l0.1-0.2l0-0.1l0.1,0h0h0v0
		v0.1l0,0.1L29.2,28.2z"/>
	<path class="st3" d="M33.2,27.4l-0.4-0.6l-0.2-0.2l-0.1-0.2c0-0.6,0.1-0.4,0.1-0.7v-0.1l-0.1-0.1c-0.2-0.4-0.9-0.8-1.1-1.2
		l-0.1-0.3l-0.1-0.3l-0.1-0.2v-0.2l0-0.2v-0.2l0-0.2l0-0.1l0.1-0.2l0.1-0.1l0.1-0.1l0.1-0.1l0.1-0.1h0.1l0.2-0.1l0.1,0h0.1l0.1-0.1
		l0.1-0.1l0.1-0.1l0.1-0.2l0-0.1l0-0.1h0.1l0.1,0l0.3,0v0v0l0-0.1c-0.6-1-1-2.2-0.9-3.3v-0.2l0.1-0.3l0.1-0.3l0.1-0.2l0.1-0.3
		l0.2-0.2l0.2-0.2l0.2-0.3l0.2-0.2l0.1-0.2l0-0.1l0-0.1v-0.1v0l0-0.1l0,0l-0.1,0h0l-0.1,0h-0.1l-0.2,0.1l-0.2,0.1l-0.2,0.1l-0.2,0.2
		l-0.2,0.1L31.8,16l-0.1,0.2l-0.1,0.2l-0.1,0.2l-0.2,0.3c-0.4,0.9-0.6,2-0.4,3v0.1v0v0h0l0,0l-0.2-0.2l-0.3-0.2
		c-1.8-1-4.2-0.2-3.8,2.1l0,0.1l0.1,0.2l0.1,0.2l0.1,0.1v0.1l0,0.1l-0.1,0.1l-0.1,0.1l-0.1,0.1l-0.1,0.1l-0.1,0.1l-0.2,0.1l-0.1,0h0
		l0,0h0L25.5,23l-0.3,0.1l-0.2,0h-0.3l-0.3,0l-0.3,0l-0.3,0l-0.3,0l-0.7-0.1l-0.7-0.1l-1.4-0.3l-0.7-0.1l-0.7-0.2l-0.7-0.1l-0.3,0
		c-3-0.5-4.5,1.5-4.2,4.3l0.2,1.4l0.2,1.2l0.1,0.5c0.2,1.2-0.2,2.3-0.3,3.5v0.5l0,0.4c0,0.4,0.2,1,0.6,0.5l0.1-0.1l0-0.2l0.1-0.2
		l0.1-0.4l0-0.4v-0.2l0.1-0.7c0.4-2.7-2.1-7.2,0.5-8.8l0.2-0.1l0.3-0.1l0.4-0.1c0.8-0.2,1.7,0.1,2.4,0.5l0.3,0.2l0.3,0.2
		c1.2,0.8,2.8,1,4.2,1h0.5l0.5,0l0.4,0l0.4,0l0.3-0.1l0.3-0.1c0.4-0.2,1-0.4,1.4-0.2l0.2,0.1l0.2,0.1l0.2,0.2l0.1,0.2l0.1,0.2
		l0.1,0.2l0.1,0.2l0.1,0.2l0.1,0.2l0,0.2v0.1v0.1l0,0.2l0,0.2l-0.1,0.3l-0.1,0l-0.1,0.1c-0.4,0.3-0.7,1.2-0.7,1.7l-0.1,0.4v0.2
		l-0.1,0.1l0,0.1l-0.1,0.1L27.6,30l-0.1,0l-0.1,0.1l-0.1,0.1l-0.2,0.1H27l-0.2,0h-0.2l-0.2,0h-0.2L26,30.1L25.8,30l-0.1,0l0,0.4
		l0.1,0.3l0.1,0.2l0.1,0.1l0.1,0.1l0.1,0.1l0,0.1l0,0.1l0,0.2l0.1,0.3c0.1,0.6,0,1.4,0.1,2l0,0.3l0,0.2l0.1,0l0.2,0.1l0.1,0.1
		l0.1,0.1l0,0.1l0,0.1v0l0.1,0.1h0h0h0.8h0.5h0.2v-0.1l0-0.1l0-0.1l-0.1-0.1l-0.1-0.2L28,34.3l-0.2-0.2l-0.1-0.1l0-0.1l0,0l0-0.1
		l0-0.1v-0.1l0.1-0.2l0.2,0H28l0.3,0h0.3h0.2l0,0.4l0,0.3v0.1v0v0l0.1,0c0.1,0.1,0.3,0.2,0.3,0.4l0,0.1h0.1h0h0.7h0.5h0.2v-0.1
		l0-0.1l-0.1-0.1l-0.1-0.1l-0.2-0.2l-0.2-0.1l-0.1-0.1l-0.1-0.1L30.1,34l0-0.1v-0.1v-0.1v-0.1l0.1-0.2l0.3,0.2l0.2,0.1l0.1,0
		c0.6,0.1,0.8,0.2,1.3,0l0.2-0.1l0.3-0.2c0.5-0.3,0.5-0.8,0.7-1.4l0.1-0.1l0-0.1l0.1-0.1c1.4-0.6,1.7-2.2,0.8-3.4L34,28.4L33.8,28
		L33.2,27.4z"/>
	<path class="st3" d="M32.9,23.9L32.9,23.9l0.2,0.2l0,0.1v0.1v0.2v0.1v0.1l0.1-0.1l0.1-0.1l0.1-0.1l0.1-0.2l0-0.2V24l0-0.1v-0.1
		l0-0.1l-0.1-0.1c-0.1,0-0.6-0.4-0.7-0.2v0.1l0,0.1L32.9,23.9z"/>
	<path class="st4" d="M32.9,23.9L32.9,23.9l0.2,0.2l0,0.1v0.1v0.2v0.1v0.1l0.1-0.1l0.1-0.1l0.1-0.1l0.1-0.2l0-0.2V24l0-0.1v-0.1
		l0-0.1l-0.1-0.1c-0.1,0-0.6-0.4-0.7-0.2v0.1l0,0.1L32.9,23.9z"/>
	<path class="st0" d="M32.3,31.5L32.3,31.5l0.1,0.2l0,0.1l0,0.1v0.2l0,0.1l0,0.1l0,0.2l-0.1,0.2l-0.1,0.2l-0.1,0.2L32,33.2
		c-0.3,0.1-0.2,0.2-0.7,0.2l-0.1,0l-0.1-0.1L31,33.3L31,33.2l0-0.1l0-0.1l0-0.1l0-0.1l0.1-0.1l0.1-0.1l0.1-0.1l0.2-0.1l0.1,0
		l0.1-0.1l0.1-0.1l0-0.1l0-0.1V32v-0.1l0-0.1l-0.1-0.2l-0.1-0.2l-0.2-0.3L31.3,31c-0.2-0.4-0.2-0.5-0.1-0.9l0.1-0.2l0-0.1
		c0-0.3-0.1-0.7-0.3-1l-0.2-0.2l-0.2-0.3l-0.3-0.4c-0.9-1.1,0.1-2.4-0.1-3.8l0,0l0,0l0,0l0.1,0.1l0,0.1c0.5,1.2-0.6,2.4,0.4,3.5
		l0.1,0.1l0.2,0.2c1.4,1.1,0.5,1.7,0.8,2.8l0.1,0.3l0.1,0.3L32.3,31.5z"/>
	<path class="st0" d="M31.7,26.2v0.2v0.2l0,0.2c0.4,1.4,1.4,1.3,1.8,2.1l0.1,0.2l0.1,0.2c0.2,0.6,0.1,1.7-0.7,1.8H33L33,31l0,0v0
		c0.6-0.4,0.5-1.2,0.3-1.8l-0.1-0.2l-0.2-0.2c-0.9-1.1-1.3-0.9-1.3-2.5v-0.2v-0.1l0-0.1h0h0l0,0l0,0.1V26V26.2z"/>
	<path class="st3" d="M36.8,16.4l-0.1-0.1l-0.1-0.2l-0.1,0l0-0.1h-0.1h0l0,0v0l0,0.1l0,0.2v0.2l0.1,0.4l0.1,0.4l0,0.3V18v0.3l0,0.3
		l-0.1,0.2l0,0.2l0,0.2c-0.3,0.8-0.9,1.6-1.7,2l-0.4,0.2l-0.4,0.1l0.4,0.3l0.3,0.3c0.6,0.6,1.2,1.6,0.5,2.4l-0.2,0.3l-0.1,0.2
		l-0.1,0.2l-0.1,0.2l-0.1,0.2L34.5,26l-0.1,0.4l-0.1,0.2l0,0.1l0,0.1l0,0l0,0l-0.1,0h-0.1l-0.1,0h0l-0.1,0v0c0,0.2,0.1,0.6,0.3,0.6
		h0v0h0l0.1-0.1l0-0.2l0.1-0.3l0.1-0.3l0.1-0.6l0.1-0.5v-0.1l0.1-0.2l0.1-0.2l0.1-0.2l0.1-0.1l0.3-0.3l0.2-0.2l0.2-0.2l0.2-0.2
		L36,24l0.1-0.1l0-0.1v-0.1l0-0.1l0-0.2L36,22.9l-0.2-0.7L36,22l0-0.1c0.5-0.6,0.9-1.2,1.1-1.9l0.1-0.2l0.1-0.3
		c0.1-0.8,0.1-1.1-0.1-1.9l-0.1-0.2L37,16.9L36.8,16.4z"/>
	<path class="st5" d="M36.8,16.4l-0.1-0.1l-0.1-0.2l-0.1,0l0-0.1h-0.1h0l0,0v0l0,0.1l0,0.2v0.2l0.1,0.4l0.1,0.4l0,0.3V18v0.3l0,0.3
		l-0.1,0.2l0,0.2l0,0.2c-0.3,0.8-0.9,1.6-1.7,2l-0.4,0.2l-0.4,0.1l0.4,0.3l0.3,0.3c0.6,0.6,1.2,1.6,0.5,2.4l-0.2,0.3l-0.1,0.2
		l-0.1,0.2l-0.1,0.2l-0.1,0.2L34.5,26l-0.1,0.4l-0.1,0.2l0,0.1l0,0.1l0,0l0,0l-0.1,0h-0.1l-0.1,0h0l-0.1,0v0c0,0.2,0.1,0.6,0.3,0.6
		h0v0h0l0.1-0.1l0-0.2l0.1-0.3l0.1-0.3l0.1-0.6l0.1-0.5v-0.1l0.1-0.2l0.1-0.2l0.1-0.2l0.1-0.1l0.3-0.3l0.2-0.2l0.2-0.2l0.2-0.2
		L36,24l0.1-0.1l0-0.1v-0.1l0-0.1l0-0.2L36,22.9l-0.2-0.7L36,22l0-0.1c0.6-0.8,0.9-1.2,1.2-2.2l0.1-0.3l0-0.2c0.1-0.5,0-1.4-0.2-1.9
		L37,16.9L36.8,16.4z"/>
	<path class="st0" d="M29.4,28.2l-0.1,0.2l0,0.2v0.2c0,1,0.8,1.4,1,2.3v0.1v0.1c0,0.5-0.3,1.3-0.9,1.5h-0.1h-0.1l-0.1,0l-0.1-0.1v0
		l0,0l0,0l0.1-0.1l0.1-0.1l0.1-0.1l0.1-0.1l0.1-0.1l0.1-0.1l0.1-0.2l0.1-0.2l0-0.2v-0.2v-0.2c-0.3-0.9-1-1.8-0.6-2.9l0.1-0.3
		l0.1-0.1v0l0.1,0h0l0,0.1l0,0V28L29.4,28.2L29.4,28.2z"/>
	<path class="st6" d="M29.4,28.2l-0.1,0.2l0,0.2v0.2c0,1,0.8,1.4,1,2.3v0.1v0.1c0,0.5-0.3,1.3-0.9,1.5h-0.1h-0.1l-0.1,0l-0.1-0.1v0
		l0,0l0,0l0.1-0.1l0.1-0.1l0.1-0.1l0.1-0.1l0.1-0.1l0.1-0.1l0.1-0.2l0.1-0.2l0-0.2v-0.2v-0.2c-0.3-0.9-1-1.8-0.6-2.9l0.1-0.3
		l0.1-0.1v0l0.1,0h0l0,0.1l0,0V28L29.4,28.2L29.4,28.2z"/>
	<path class="st3" d="M20.5,33.9l-0.1-0.2l-0.1-0.1l-0.1-0.2c-0.2-0.3-0.1-0.7,0-1l0.1-0.2l0.1-0.1l0.1-0.1l0.1-0.1l0.1-0.1l0.1-0.1
		l0.2-0.1l0,0l0.1,0l0-0.1l0-0.1v-0.1L21,31.4l-0.1-0.1c-0.3-0.2-0.2-0.2-0.7,0L20,31.4l-0.2,0.2c-1.2,1.5-0.7,0.9-0.7,2.5v0.2
		l0,0.2v0l0.1,0l0.2,0.1l0.1,0.1l0.1,0.1l0.1,0.1v0.1l0,0.1l0,0c0.4,0,0.8,0,1.2,0h0.1h0.3l0-0.1c0-0.3-0.2-0.5-0.4-0.7l-0.1-0.1
		L20.5,33.9z"/>
	<path class="st3" d="M23.3,30.3L23.3,30.3l-0.2,0.2c-0.2,0.1-0.3,0.3-0.5,0.2l-0.2,0l-0.2-0.1c-0.3-0.1-0.7-0.2-1.1-0.3l-0.3-0.2
		l-0.5-0.2l0.1-0.4l0.1-0.3l0.1-0.3c0.3-1.1-0.9-2-1.7-2.5l-0.1-0.1l-0.1,0c-0.9-0.3-0.9,0.9-0.7,1.4l0.1,0.4l0.1,0.3l0.1,0.3l0,0.3
		c0,0.8-0.4,1.8-1.1,2.3l-0.1,0l-0.2,0l-0.2,0c-0.3,0.1-0.2-0.1-0.5,0.2l-0.1,0.1l0,0.1c-0.1,0.5,0,1.2,0,1.6l0,0.6v0.2v0.2l0,0.2
		l0.1,0c0.7,0.4,0,0.5,0.9,0.5l1.1,0l0-0.1l0-0.1l-0.1-0.1l-0.1-0.1l-0.2-0.2l-0.2-0.2L17.5,34l-0.1-0.1l0-0.1l-0.1-0.1l0-0.1v-0.2
		v-0.1l0.1-0.1l0-0.1l0-0.1l0.2-0.3l0.1-0.1l0.1-0.1l0.1-0.1l0.1-0.1l0.4-0.1l0.3-0.2l0.3-0.2l0.2-0.1l0.2-0.2l0.2-0.2l0.3-0.3
		l0.1-0.2l0.1-0.2l0.1-0.1l0.1-0.1l0.1-0.1l0.1,0h0.1h0l0.4,0.2l0.6,0.2l0.3,0.1L22,31l0.1,0.1l0.1,0.1l0.1,0.1h0.1l0.1,0h0.1l0.1,0
		l0.1,0l0.1-0.1l0.1-0.1l0.2-0.2l0.2-0.2l0.2-0.3H23.3z"/>
</g>
</svg>
