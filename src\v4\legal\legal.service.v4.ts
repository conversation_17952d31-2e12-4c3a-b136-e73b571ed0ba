// Imports
import { Injectable } from '@nestjs/common';
import {
  AUTODEBIT_FAIL,
  CASE_FILLED,
  DEMAND_STATUS,
  LEGAL_STATUS,
  SUMMONS,
  WARRENT,
} from 'src/constants/globals';
import { k500Error } from 'src/constants/misc';
import { kInternalError, kParamMissing } from 'src/constants/responses';
import {
  kLegalDemandLetterIcon,
  kLegalNoticeIcon,
  kLegalSummonsIcon,
  kLegalWarrantIcon,
} from 'src/constants/directories';
import { LegalCollectionRepository } from 'src/repositories/legal.collection.repository';
import { EMIRepository } from 'src/repositories/emi.repository';
import { TypeService } from 'src/utils/type.service';
import { LoanRepository } from 'src/repositories/loan.repository';
import { TransactionEntity } from 'src/entities/transaction.entity';
import { EmiEntity } from 'src/entities/emi.entity';
import { LegalCollectionEntity } from 'src/entities/legal.collection.entity';
import { kPopUpAlertImage } from 'src/constants/objects';
import { registeredUsers } from 'src/entities/user.entity';

@Injectable()
export class LegalServiceV4 {
  constructor(
    private readonly legalCollectionRepo: LegalCollectionRepository,
    private readonly loanRepo: LoanRepository,
    private readonly emiRepo: EMIRepository,
    private readonly typeService: TypeService,
  ) {}

  async addLegalDocs(query) {
    // Validation -> Parameters
    const loanId = query?.loanId;
    if (!loanId) return kParamMissing('loanId');

    const isDelayed = await this.emiRepo.getTableWhereData(
      ['id', 'partOfemi'],
      { where: { loanId, payment_due_status: '1' } },
    );
    if (!isDelayed || isDelayed.length == 0 || isDelayed == k500Error) {
      if (isDelayed == k500Error) {
        throw new Error();
      }
      return {};
    }
    const data: any = {};
    data.popUpAlertData = await this.getPopUpAlertBoxData(query);
    const attributes = ['caseDetails', 'id', 'type', 'url', 'createdAt'];
    const options = { where: { loanId }, order: [['id', 'DESC']] };
    const legalData = await this.legalCollectionRepo.getTableWhereData(
      attributes,
      options,
    );
    if (legalData === k500Error) throw new Error();

    const documents = [];
    const allType = [];
    const docType = [DEMAND_STATUS, SUMMONS, LEGAL_STATUS];

    // Preparation -> Legal data
    for (let index = 0; index < legalData.length; index++) {
      try {
        const noticeData = legalData[index];
        const type = noticeData?.type;
        if (type && !allType.includes(type)) allType.push(type);
        if (docType.includes(type)) {
          const formattedDate = this.typeService.dateToJsonStr(
            noticeData.createdAt,
            'DD/MM/YYYY',
          );
          const preparedObj = this.prepareLegalData(noticeData.type);
          preparedObj.url = noticeData.url;
          preparedObj.previewTitle = 'Created date: ' + formattedDate;
          documents.push(preparedObj);
        }
      } catch (error) {}
    }

    documents.sort((a, b) => a.id - b.id);
    data.documents = documents;
    // It will be return if the last EMI is not in default
    const partOfemi = isDelayed.find((el) => el.partOfemi === 'LAST');
    if (!partOfemi) return data;

    //legal steeper Data
    const legalSteeperInfo: any = [
      { name: 'Demand letter' },
      { name: 'Legal notice' },
      { name: 'Case filed' },
      { name: 'Summons' },
      { name: 'Warrant' },
    ];
    if (!allType.includes(1)) {
      legalSteeperInfo.splice(
        legalSteeperInfo.findIndex((item) => item?.name === 'Demand letter'),
        1,
      );
    }

    let stepperData = { isPendingAssigned: false };
    for (let index = 0; index < legalSteeperInfo.length; index++) {
      try {
        const ele = legalSteeperInfo[index];
        const key = ele?.name ?? '';
        if (key == 'Demand letter')
          this.latestLegalAction(legalData, allType, ele, 1, stepperData);
        else if (key == 'Legal notice')
          this.latestLegalAction(legalData, allType, ele, 2, stepperData);
        else if (key == 'Case filed')
          this.latestLegalAction(legalData, allType, ele, 5, stepperData);
        else if (key == 'Summons')
          this.latestLegalAction(legalData, allType, ele, 6, stepperData);
        else if (key == 'Warrant')
          this.latestLegalAction(legalData, allType, ele, 7, stepperData);
        else if (key == 'Case withdrawal')
          this.latestLegalAction(legalData, allType, ele, 8, stepperData);
        else if (key == 'Case disposal')
          this.latestLegalAction(legalData, allType, ele, 9, stepperData);
      } catch (error) {}
    }
    data.steeperInfo = legalSteeperInfo;
    return data;
  }

  private prepareLegalData(type) {
    const obj: any = {
      [DEMAND_STATUS]: {
        title: 'Demand letter',
        url: '',
        id: DEMAND_STATUS,
        icon: kLegalDemandLetterIcon,
      },
      [LEGAL_STATUS]: {
        title: 'Legal notice',
        url: '',
        id: LEGAL_STATUS,
        icon: kLegalNoticeIcon,
      },
      [SUMMONS]: {
        title: 'Summons',
        id: SUMMONS,
        url: '',
        icon: kLegalSummonsIcon,
      },
    };
    return obj[type];
  }

  latestLegalAction(legalData, allType, ele, type, stepperData) {
    try {
      if (allType.includes(type)) {
        const dL = legalData
          .filter((item) => item?.type === type)
          .sort((a, b) => b?.createdAt - a?.createdAt)[0];

        if (dL.type === CASE_FILLED)
          dL.createdAt = new Date(
            dL.caseDetails?.caseFiledDate ?? dL.createdAt,
          );

        ele.date = this.typeService.getGlobalDate(dL.createdAt);
        ele.isDone = true;
      } else if (!stepperData.isPendingAssigned) {
        stepperData.isPendingAssigned = true;
        ele.isPending = true;
      } else ele.isAwaited = true;
    } catch (error) {
      return ele;
    }
  }

  async getPopUpAlertBoxData(data) {
    const loanId = data?.loanId;
    const attributes = ['id'];
    const options: any = { where: { id: loanId, loanStatus: 'Active' } };
    const transactionInclude = {
      model: TransactionEntity,
      attributes: ['id', 'response', 'emiId'],
      where: { subSource: 'AUTODEBIT', status: 'FAILED', type: 'EMIPAY' },
      required: false,
    };
    const emiInclude = {
      model: EmiEntity,
      attributes: ['id', 'emi_date', 'emiNumber', 'emi_amount', 'penalty_days'],
      where: { payment_status: '0', payment_due_status: '1' },
      include: [transactionInclude],
    };
    const legalInclude = {
      model: LegalCollectionEntity,
      attributes: ['id', 'type', 'subType', 'emiId', 'url'],
      required: false,
    };
    const userInclude = {
      model: registeredUsers,
      attributes: ['id', 'appType'],
    };
    options.include = [userInclude, legalInclude, emiInclude];
    const loanData = await this.loanRepo.getRowWhereData(attributes, options);
    if (loanData === k500Error || !loanData) return {};
    const appType = loanData?.registeredUsers?.appType;
    const emiData = loanData?.emiData;
    const legal = loanData?.legalData;
    const alertDataObj: any = {};

    if (
      legal &&
      [DEMAND_STATUS, LEGAL_STATUS, SUMMONS, WARRENT].includes(legal?.type)
    ) {
      alertDataObj.isLoanBaseAlert = true;
      //for warrent/notice/summons
      if (legal.type != DEMAND_STATUS) {
        let title, description, subTitle, subText;
        if (legal.type == WARRENT) {
          title =
            legal.subType == 2 ? 'Non Bailable Warrant' : 'Bailable Warrant';
          description = `A ${title} has been issued due to the failure to repay your loan within the scheduled timeframe. To prevent further legal action, please settle the outstanding loan amount immediately or reach out to us for further assistance.`;
          subTitle =
            'Due to continued non-repayment and failure to respond to previous legal notices';
          subText = 'Take Immediate Action';
        } else if (legal.type == SUMMONS) {
          title = 'Summons';
          description =
            'Non-repayment of loan within the scheduled timeframe has led to issue of summons. Pay the outstanding amount immediately or contact us to settle the loan and avoid further legal action.';
          subTitle =
            'Legal proceedings has been initiated, for non-repayment of outstanding loan';
          subText = 'Settle Dues Immediately';
        } else if (legal.type == LEGAL_STATUS) {
          title = 'Legal Notice';
          description =
            'Despite previous reminders, no payment has been received. Therefore, a legal notice has been issued.';
          subTitle = 'For continued non-repayment of your outstanding loan';
          subText = 'Immediate action required!';
        }
        alertDataObj.title = title + ' Issued';
        alertDataObj.subTitle = subTitle;
        alertDataObj.subText = subText;
        alertDataObj.description = description;
        if (legal.type != WARRENT) alertDataObj.view = legal.url;
        const loanOverDue = loanData?.emiData[0]?.penalty_days;
        alertDataObj.loanOverDue =
          loanOverDue == 1 ? `${loanOverDue} Day` : `${loanOverDue} Days`;
        alertDataObj.image = kPopUpAlertImage[appType][legal.type];
      }
      //for demand
      else {
        //latest is demand
        const emi = emiData[emiData.length - 1];
        alertDataObj.isLoanBaseAlert = false;
        if (legal.emiId == emi.id) {
          alertDataObj.title = 'Demand Letter Issued';
          alertDataObj.subTitle = `For Your EMI-${emi.emiNumber} non-repayment`;
          alertDataObj.subText = 'Make the Payment Immediately';
          alertDataObj.description = `Kindly clear your overdue EMI to avoid additional charges or legal action.`;
          alertDataObj.view = legal.url;
          alertDataObj.image = kPopUpAlertImage[appType][legal.type];
        }
        // for auto-debit fail and demand not created
        else {
          alertDataObj.title = 'Oops! Auto-Debit Unsuccessful';
          if (emi?.transactionData.length == 0) return {};

          const error = emi?.transactionData[0]?.response
            ? JSON.parse(emi?.transactionData[0]?.response)
            : {};
          const reason =
            error?.payment?.failureReason ??
            error.error_description ??
            error?.error_reason ??
            error?.error_message ??
            '';
          alertDataObj.subTitle = `${
            reason ||
            `Your linked account doesn't have enough funds to process the payment`
          }`;
          alertDataObj.subText = 'Your prompt action required!';
          alertDataObj.description = `Add funds to your account and immediately make the payment to avoid potential legal action`;
          alertDataObj.image = kPopUpAlertImage[appType][AUTODEBIT_FAIL];
        }
        alertDataObj.emiDetails = {
          emiDate: this.typeService.getDateFormated(emi.emi_date),
          emiAmount: emi.emi_amount,
          emiOverDue: emi.penalty_days
            ? emi.penalty_days == 1
              ? `1 Day`
              : `${emi.penalty_days} Days`
            : null,
          emiNumber: emi.emiNumber,
        };
      }
    }
    //for auto-debit fail and not start legal process
    else if (!legal && emiData.length > 0) {
      alertDataObj.isLoanBaseAlert = false;
      const emi = emiData[emiData.length - 1];
      const transaction = emi?.transactionData ?? [];
      if (transaction.length == 0) return {};
      alertDataObj.title = 'Oops! Auto-Debit Unsuccessful';
      const error = transaction[0]?.response
        ? JSON.parse(transaction[0]?.response)
        : {};
      const reason =
        error?.payment?.failureReason ??
        error.error_description ??
        error?.error_reason ??
        error?.error_message ??
        '';
      alertDataObj.subTitle = `${
        reason ||
        `Your linked account doesn't have enough funds to process the payment`
      }`;
      alertDataObj.subText = 'Your prompt action required!';
      alertDataObj.description = `Add funds to your account and immediately make the payment to avoid potential legal action`;
      alertDataObj.emiDetails = {
        emiDate: this.typeService.getDateFormated(emi.emi_date),
        emiAmount: emi.emi_amount,
        emiOverDue: emi.penalty_days
          ? emi.penalty_days == 1
            ? `1 Day`
            : `${emi.penalty_days} Days`
          : null,
        emiNumber: emi.emiNumber,
      };
      alertDataObj.image = kPopUpAlertImage[appType][AUTODEBIT_FAIL];
    }

    return alertDataObj;
  }
}
