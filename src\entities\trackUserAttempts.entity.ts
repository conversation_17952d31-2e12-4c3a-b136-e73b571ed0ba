import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { TrackStepCategory } from './trackStepCategory.entity';
@Table({})
export class TrackUserAttempts extends Model<TrackUserAttempts> {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    allowNull: false,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  apiEndPoint: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  reqBody: string;

  @ForeignKey(() => TrackStepCategory)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  stepId: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  attemptCount: number;

  @Column({
    type: DataType.UUID,
    allowNull: true,
  })
  userId: string;

  @BelongsTo(() => TrackStepCategory)
  trackStepCategory: TrackStepCategory;
}
