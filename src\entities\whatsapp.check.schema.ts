import { Document } from 'mongoose';
import { <PERSON>p, <PERSON><PERSON><PERSON>, SchemaFactory } from '@nestjs/mongoose';

export type WhatsAppCheckDocument = WhatsAppCheck & Document;

@Schema({ timestamps: true })
export class WhatsAppCheck {
  @Prop({ required: true })
  number: string;

  @Prop({ required: true })
  isOnWhatsApp: boolean;

  @Prop({ required: false })
  batchId: string;

  @Prop({ type: Date, required: false })
  createdAt: Date;
}

export const WhatsAppCheckSchema = SchemaFactory.createForClass(WhatsAppCheck);
