// Imports
import { Body, Controller, Get, Query, Res } from '@nestjs/common';
import { kInternalError, kSuccessData } from 'src/constants/responses';
import { ResidenceService } from './residence.service';

@Controller('admin/residence')
export class ResidenceController {
  constructor(private readonly service: ResidenceService) {}

  @Get('addressList')
  async funAddressList(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.addressList(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      console.error('Error in: ', error);
      return res.send(kInternalError);
    }
  }
}
