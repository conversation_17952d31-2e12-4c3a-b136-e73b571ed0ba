import { Column, DataType, Model, Table } from 'sequelize-typescript';
@Table({})
export class accountSectionSummary extends Model<accountSectionSummary> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  reportDate: string;

  @Column({
    type: DataType.DOUBLE,
    allowNull: true,
    defaultValue: 0,
  })
  expense: number;

  @Column({ type: DataType.JSON, allowNull: true })
  summaryData: any;

  @Column({ type: DataType.JSON, allowNull: true })
  disbursementData: any;

  @Column({ type: DataType.TEXT, allowNull: true })
  disbursementListURL: any;

  @Column({ type: DataType.JSON, allowNull: true })
  repaymentData: any;

  @Column({ type: DataType.TEXT, allowNull: true })
  repaymentListURL: any;

  @Column({ type: DataType.JSON, allowNull: true })
  interestIncomeData: any;

  @Column({ type: DataType.TEXT, allowNull: true })
  interestIncomeListURL: any;

}
