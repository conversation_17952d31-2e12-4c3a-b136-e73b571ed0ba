import * as crypto from 'crypto';

// Imports
import { Injectable } from '@nestjs/common';
import { ErrorContextService } from 'src/utils/error.context.service';
import { isLOG } from 'src/constants/globals';
import { k500Error } from 'src/constants/misc';
import { MailTrackerEntity } from 'src/entities/mail.tracker.entity';
import { RepositoryManager } from 'src/repositories/repository.manager';
import {
  alertCategory,
  getCategoryFromClassName,
  getSafeTime,
  kMailTrackerStatus,
  kMailTrackerSubStatus,
  kMailTrackerType,
  mapAllKeys,
  mapObj,
  notificationModels,
  smsModels,
  whatsappModels,
} from 'src/constants/mailTracker';

@Injectable()
export class AlertSharedService {
  constructor(
    private readonly errorContextService: ErrorContextService,
    private readonly repoManager: RepositoryManager,
  ) {}

  getPgSeriesFromUUID(uuid: string) {
    try {
      // Get series number based on UUID
      if (!uuid) return null;
      const hash = crypto.createHash('md5').update(uuid).digest('hex'); // Get a hash of UUID
      const num = parseInt(hash.substring(0, 8), 16); // Convert first 8 hex chars to int
      const series = num % 10;
      return series;
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  private getPgRepo(userId, category, isForce: boolean) {
    const series = this.getPgSeriesFromUUID(userId);
    // Map Models based on categories
    const categoryModel: any = mapObj[category];
    if (!categoryModel) {
      return series == null
        ? [
            MailTrackerEntity,
            ...smsModels,
            ...notificationModels,
            ...whatsappModels,
          ]
        : [
            MailTrackerEntity,
            smsModels[series],
            notificationModels[series],
            whatsappModels[series],
          ];
    }

    let finalModel = categoryModel;
    if (Array.isArray(categoryModel)) {
      if (isForce)
        finalModel =
          series == null
            ? [MailTrackerEntity, ...categoryModel]
            : [MailTrackerEntity, categoryModel[series]];
      else finalModel = series == null ? categoryModel : categoryModel[series];
    }
    return finalModel;
  }

  async createRowData(category: string, data: any) {
    try {
      const userId = data?.userId;
      if (!userId || !category) return k500Error;
      const categoryModel = this.getPgRepo(userId, category, false);
      const { passData, passType } = this.convertDbTypes(category, data);
      const createdData = await categoryModel.create(passData);
      if (!createdData) return k500Error;
      let createData = createdData['dataValues'];
      const mappedkey = mapAllKeys(createData, passType);
      return mappedkey;
    } catch (error) {
      console.log(error);
      this.errorContextService.throwAndSetCtxErr(error);
      if (isLOG) console.log(error);
      return k500Error;
    }
  }

  async getTableWhereData(
    category: string,
    attributes: string[],
    options: any,
    isForce: boolean,
  ) {
    try {
      const originalWhere = { ...options?.where };
      const originalAttr = [...attributes];
      const userId = options?.where?.userId;
      const categoryModel = this.getPgRepo(userId, category, isForce);
      let listData;
      let typeArr = [];
      if (Array.isArray(categoryModel)) {
        typeArr = categoryModel.map((item) => getCategoryFromClassName(item));
        listData = await Promise.all(
          categoryModel.map((model) => {
            let convObj;
            if (options?.where && model !== MailTrackerEntity)
              convObj = this.convertDbTypes(
                category ?? getCategoryFromClassName(model, true),
                options.where,
                attributes,
                originalWhere,
                originalAttr,
              );
            if (convObj) options.where = convObj?.passData;
            if (convObj?.passAttr) attributes = convObj.passAttr;
            return model.findAll({
              attributes,
              ...options,
              distinct: true,
            });
          }),
        );
      } else {
        let convObj;
        if (options?.where && categoryModel !== MailTrackerEntity)
          convObj = this.convertDbTypes(
            category ?? getCategoryFromClassName(categoryModel, true),
            options.where,
            attributes,
          );
        if (convObj) options.where = convObj?.passData;
        typeArr.push(getCategoryFromClassName(categoryModel));
        if (convObj?.passAttr) attributes = convObj.passAttr;
        listData = [
          await categoryModel.findAll({
            attributes,
            ...options,
            distinct: true,
          }),
        ];
      }
      if (listData.length !== typeArr.length) return k500Error;
      listData = this.mapListType(listData, typeArr);
      listData = listData.flat();
      listData = listData.map((element) => element.get({ plain: true }));
      listData = listData.map((item) => mapAllKeys(item, item?.type));
      listData = listData.sort(
        (a, b) => getSafeTime(b?.createdAt) - getSafeTime(a?.createdAt),
      );

      return listData;
    } catch (error) {
      console.log(error);
      this.errorContextService.throwAndSetCtxErr(error);
      if (isLOG) console.log(error);
      return k500Error;
    }
  }

  async getRowWhereData(
    category: string,
    attributes: string[],
    options: any,
    isForce: boolean,
  ) {
    try {
      const originalWhere = { ...options?.where };
      const originalAttr = [...attributes];
      const userId = options?.where?.userId;
      const categoryModel = this.getPgRepo(userId, category, isForce);
      let results: any;
      let typeArr = [];
      if (Array.isArray(categoryModel)) {
        typeArr = categoryModel.map((item) => getCategoryFromClassName(item));
        results = await Promise.all(
          categoryModel.map((model) => {
            let convObj;
            if (options?.where && model !== MailTrackerEntity)
              convObj = this.convertDbTypes(
                category ?? getCategoryFromClassName(model, true),
                options.where,
                attributes,
                originalWhere,
                originalAttr,
              );
            if (convObj) options.where = convObj?.passData;
            if (convObj?.passAttr) attributes = convObj.passAttr;
            return model.findOne({ attributes, ...options });
          }),
        );
      } else {
        let convObj;
        if (options?.where && categoryModel !== MailTrackerEntity)
          convObj = this.convertDbTypes(
            category ?? getCategoryFromClassName(categoryModel, true),
            options.where,
            attributes,
          );
        if (convObj) options.where = convObj?.passData;
        typeArr.push(getCategoryFromClassName(categoryModel));
        if (convObj?.passAttr) attributes = convObj.passAttr;
        results = [await categoryModel.findOne({ attributes, ...options })];
      }
      if (results.length !== typeArr.length) return k500Error;
      results = this.mapListType(results, typeArr);
      let data = results.find((item) => item !== null) || null;
      if (!data) return;
      data = data.get({ plain: true });
      data = mapAllKeys(data);
      return data;
    } catch (error) {
      console.log(error);
      this.errorContextService.throwAndSetCtxErr(error);
      if (isLOG) console.log(error);
      return k500Error;
    }
  }

  async updateRowWhereData(
    category: string,
    updatedData,
    options,
    isForce: boolean,
  ) {
    try {
      if (!category) return k500Error;
      const originalWhere = { ...options?.where };
      const originalUpdate = { ...updatedData };
      const userId = options?.where?.userId;
      const categoryModel = this.getPgRepo(userId, category, isForce);
      let results;
      if (Array.isArray(categoryModel)) {
        results = await Promise.all(
          categoryModel.map((model) => {
            let convObj;
            let convObjUpdate;
            if (options?.where && model !== MailTrackerEntity) {
              convObj = this.convertDbTypes(
                category,
                options.where,
                null,
                originalWhere,
              );
              convObjUpdate = this.convertDbTypes(
                category,
                updatedData,
                null,
                originalUpdate,
              );
            }
            if (convObj) options.where = convObj?.passData;
            if (convObjUpdate) updatedData = convObjUpdate?.passData;
            return model.update(updatedData, options);
          }),
        );
      } else {
        let convObj;
        let convObjUpdate;
        if (options?.where && categoryModel !== MailTrackerEntity) {
          convObj = this.convertDbTypes(category, options.where);
          convObjUpdate = this.convertDbTypes(category, updatedData);
        }
        if (convObj) options.where = convObj?.passData;
        if (convObjUpdate) updatedData = convObjUpdate?.passData;
        results = [await categoryModel.update(updatedData, options)];
      }
      const data = results.filter(([count]) => count > 0);
      return data;
    } catch (error) {
      console.log(error);
      this.errorContextService.throwAndSetCtxErr(error);
      if (isLOG) console.log(error);
      return k500Error;
    }
  }

  async updateRowData(
    category: string,
    updatedData: any,
    id,
    silent = false,
    userId: string = null,
  ) {
    try {
      if (!userId || !category) return k500Error;
      if (updatedData) {
        const { passData } = this.convertDbTypes(category, updatedData);
        updatedData = passData;
      }
      const categoryModel = this.getPgRepo(userId, category, false);
      return await categoryModel.update(updatedData, { where: { id }, silent });
    } catch (error) {
      console.log(error);
      this.errorContextService.throwAndSetCtxErr(error);
      if (isLOG) console.log(error);
      return k500Error;
    }
  }

  async getCountsWhere(category: string, options: any, isForce: boolean) {
    try {
      if (!category) return k500Error;
      const originalWhere = { ...options?.where };
      const userId = options?.where?.userId;
      const categoryModel = this.getPgRepo(userId, category, isForce);
      let counts;
      if (Array.isArray(categoryModel)) {
        counts = await Promise.all(
          categoryModel.map((model) => {
            let convObj;
            if (options?.where && model !== MailTrackerEntity)
              convObj = this.convertDbTypes(
                category,
                options.where,
                null,
                originalWhere,
              );
            if (convObj) options.where = convObj?.passData;
            return model.count(options);
          }),
        );
      } else {
        let convObj;
        if (options?.where && categoryModel !== MailTrackerEntity)
          convObj = this.convertDbTypes(category, options.where);
        if (convObj) options.where = convObj?.passData;
        counts = [await categoryModel.count(options)];
      }
      const totalCounts = counts.reduce((sum, count) => sum + count, 0);

      return totalCounts;
    } catch (error) {
      console.log(error);
      this.errorContextService.throwAndSetCtxErr(error);
      if (isLOG) console.log(error);
      return k500Error;
    }
  }

  async deleteWhereData(
    category: string,
    options: any,
    restricted: boolean,
    isForce: boolean,
  ) {
    try {
      // Check restriction
      const mode = process.env.MODE;
      if (mode != 'DEV' && restricted && mode != 'UAT') return false;

      // proceed deletion
      if (!category) return k500Error;
      const originalWhere = { ...options?.where };
      const userId = options?.where?.userId;
      const categoryModel = this.getPgRepo(userId, category, isForce);
      let results;
      if (Array.isArray(categoryModel)) {
        results = await Promise.all(
          categoryModel.map((model) => {
            let convObj;
            if (options?.where && model !== MailTrackerEntity)
              convObj = this.convertDbTypes(
                category,
                options.where,
                null,
                originalWhere,
              );
            if (convObj) options.where = convObj?.passData;
            return model.destroy({ ...options });
          }),
        );
      } else {
        let convObj;
        if (options?.where && categoryModel !== MailTrackerEntity)
          convObj = this.convertDbTypes(category, options.where);
        if (convObj) options.where = convObj?.passData;
        results = [await categoryModel.destroy({ ...options })];
      }
      return results;
    } catch (error) {
      console.log(error);
      this.errorContextService.throwAndSetCtxErr(error);
      if (isLOG) console.log(error);
      return k500Error;
    }
  }

  private convertDbTypes(
    category,
    passData,
    attributes = null,
    originalWhere = null,
    originalAttr = null,
  ) {
    if (category === alertCategory.EMAIL) return { passData };
    if (passData?.status && isNaN(passData?.status))
      passData.status = kMailTrackerStatus[passData.status];
    if (passData?.subStatus && isNaN(passData?.subStatus))
      delete passData.subStatus;
    if (passData?.statusDate) delete passData.statusDate;
    if (passData?.legalId) delete passData.legalId;
    let passType = passData?.type;
    if (passData?.type && isNaN(passData?.type)) delete passData.type;

    if (category === alertCategory.TEXT) {
      if (passData?.whatsappMsgId) delete passData.whatsappMsgId;
    } else if (category === alertCategory.NOTIFICATION) {
      if (passData?.source) delete passData.source;
      if (passData?.requestData) delete passData.requestData;
      if (passData?.service) delete passData.service;
      if (passData?.whatsappMsgId) delete passData.whatsappMsgId;
    } else if (category === alertCategory.WHATSAPP) {
      if (originalWhere?.whatsappMsgId && !passData?.whatsappMsgId)
        passData.whatsappMsgId = originalWhere?.whatsappMsgId;
    }

    // Handle Attributes
    let passAttr = attributes?.length > 0 ? attributes : null;
    if (passAttr?.length > 0) {
      let tempAttr = ['type', 'subStatus', 'statusDate', 'legalId'];
      if (category === alertCategory.TEXT)
        tempAttr = [...tempAttr, 'whatsappMsgId'];
      else if (category === alertCategory.NOTIFICATION)
        tempAttr = [
          ...tempAttr,
          'source',
          'requestData',
          'service',
          'whatsappMsgId',
        ];
      else if (category === alertCategory.WHATSAPP) {
        if (
          originalAttr?.includes('whatsappMsgId') &&
          !passAttr?.includes('whatsappMsgId')
        ) {
          passAttr.push('whatsappMsgId');
        }
        if (originalAttr?.includes('source') && !passAttr?.includes('source'))
          passAttr.push('source');
      }
      passAttr = passAttr.filter((item) => !tempAttr.includes(item));
    }
    return { passData, passType, passAttr };
  }

  private mapListType(listData, typeArr) {
    return listData.map((item, i) => {
      if (!item) return item;
      if (Array.isArray(item)) {
        let tempArr = item.map((el) => {
          let tempObj = el?.dataValues;
          tempObj = {
            ...tempObj,
            type: el?.type ? el.type : typeArr[i],
          };
          el.dataValues = tempObj;
          return el;
        });
        return tempArr;
      } else {
        let tempObj = item?.dataValues;
        tempObj = {
          ...tempObj,
          type: item?.type ? item.type : typeArr[i],
        };
        item.dataValues = tempObj;
        return item;
      }
    });
  }
}
