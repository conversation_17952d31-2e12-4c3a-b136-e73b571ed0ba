// Imports
import { Injectable } from '@nestjs/common';
import {
  ADMIN_LOGIN_CHANCE,
  ADMIN_WRONG_OTP_TIME_MINS,
  gIsPROD,
  PAGE,
  PAGE_LIMIT,
} from 'src/constants/globals';
import { k500Error } from 'src/constants/misc';
import {
  k422ErrorMessage,
  kInvalidParamValue,
  kParamMissing,
} from 'src/constants/responses';
import { kOTPIsExpired } from 'src/constants/strings';
import { AgentCallHistoryEntity } from 'src/entities/agentCallHistory.entity';
import { AdminRepository } from 'src/repositories/admin.repository';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { AllsmsService } from 'src/thirdParty/SMS/sms.service';
import { CommonService } from 'src/utils/common.service';
import { CryptService } from 'src/utils/crypt.service';
import { TypeService } from 'src/utils/type.service';
import { isUUID } from 'class-validator';
import admin from 'firebase-admin';
import { EnvConfig } from 'src/configs/env.config';
import { CRMService } from '../crm/crm.service';
import { CrmStatusRepository } from 'src/repositories/crmStatus.repository';
import { CrmDispositionRepository } from 'src/repositories/crmDisposition.repository';
import { Op, Sequelize } from 'sequelize';
import { NUMBERS } from 'src/constants/numbers';
import { RedisService } from 'src/redis/redis.service';
import { SlackService } from 'src/thirdParty/slack/slack.service';
import { EmiEntity } from 'src/entities/emi.entity';
import { loanTransaction } from 'src/entities/loan.entity';
import { FileService } from 'src/utils/file.service';
import { DateService } from 'src/utils/date.service';
import { AdminRoleRepository } from 'src/repositories/admin_role.repository';
import {
  AGENT_APP_CALL_ICONS,
  PART_OF_EMI,
  AGENT_USER_LOAN_STATUS,
  UserStage,
  kAgentAppCallType,
  REDIS_KEY,
} from 'src/constants/objects';
import { HashPhoneEntity } from 'src/entities/hashPhone.entity';
import { UserRepository } from 'src/repositories/user.repository';
import { employmentDetails } from 'src/entities/employment.entity';
import { BankingEntity } from 'src/entities/banking.entity';
import { registeredUsers } from 'src/entities/user.entity';
import { AgentUserLatestCallInfo } from 'src/entities/agentUserLatestCallInfo.entity';
import { AdminRedisSyncService } from '../admin/AdminRedisSync.service';

@Injectable()
export class AgentConnectAppService {
  firebaseDB: admin.firestore.Firestore;
  constructor(
    private readonly commonSharedService: CommonSharedService,
    private readonly commonService: CommonService,
    private readonly cryptService: CryptService,
    private readonly typeService: TypeService,
    private readonly allsmsService: AllsmsService,
    private readonly crmService: CRMService,
    private readonly redisService: RedisService,
    private readonly slackService: SlackService,
    private readonly fileService: FileService,
    private readonly dateService: DateService,
    private readonly adminRedisSyncService: AdminRedisSyncService,

    //Repository
    private readonly adminRepo: AdminRepository,
    private readonly userRepo: UserRepository,
    private readonly adminRoleRepo: AdminRoleRepository,
    private readonly crmStatusRepo: CrmStatusRepository,
    private readonly dispositionRepo: CrmDispositionRepository,
    private readonly repoManager: RepositoryManager,
  ) {
    this.firebaseDB = admin.firestore();
  }

  async sendOtp(reqData: any) {
    const companyPhone = reqData?.companyPhone;
    if (!companyPhone) return kParamMissing('companyPhone');
    if (isNaN(companyPhone) || typeof companyPhone != 'string')
      return kInvalidParamValue('companyPhone');
    if (companyPhone?.length != 10)
      return k422ErrorMessage('Please enter correct 10 digit phone number');

    let adminData = await this.checkAdminExist(companyPhone);
    if (adminData?.message) return adminData;
    adminData = await this.adminRepo.getRowWhereData(
      ['id', 'lastOtpAttemptTime', 'wrongOtpCount'],
      { where: { id: adminData?.id } },
    );
    if (!adminData || adminData == k500Error) throw new Error();
    let timeDiff = 0;
    if (adminData.lastOtpAttemptTime) {
      timeDiff = await this.typeService.dateDifference(
        new Date(),
        adminData?.lastOtpAttemptTime,
        'Minutes',
      );
    }
    if (
      adminData?.wrongOtpCount >= ADMIN_LOGIN_CHANCE &&
      timeDiff <= ADMIN_WRONG_OTP_TIME_MINS
    )
      return k422ErrorMessage(
        `Please try again after ${
          ADMIN_WRONG_OTP_TIME_MINS - timeDiff + 1
        } minutes`,
      );
    const otp = this.commonService.generateOTP();
    const encOTP = this.cryptService.getMD5Hash(otp);
    const updateData = { phoneStatusVerified: '0', otp: encOTP };
    const result = await this.adminRepo.updateRowData(
      updateData,
      adminData?.id,
    );
    if (!result || result == k500Error) throw new Error();
    await this.allsmsService.sendOtp(otp, companyPhone, null, true);
    return {};
  }

  private async validateOtp(reqData: any) {
    const companyPhone = reqData?.companyPhone;
    const otp = reqData?.otp;

    let adminData = await this.checkAdminExist(companyPhone);
    if (adminData?.message) return adminData;
    adminData = await this.adminRepo.getRowWhereData(
      ['id', 'otp', 'lastOtpAttemptTime', 'wrongOtpCount', 'updatedAt'],
      {
        where: { id: adminData?.id },
      },
    );
    if (adminData == k500Error) throw new Error();

    const otpFromDb = adminData?.otp;
    let wrongOtpCount = adminData?.wrongOtpCount;
    const currentDate = new Date();
    let timeDiff;
    if (adminData.lastOtpAttemptTime) {
      timeDiff = await this.typeService.dateDifference(
        new Date(),
        adminData.lastOtpAttemptTime,
        'Minutes',
      );
    }

    if (otpFromDb != this.cryptService.getMD5Hash(otp)) {
      wrongOtpCount = wrongOtpCount + 1;
      if (
        wrongOtpCount > ADMIN_LOGIN_CHANCE &&
        timeDiff < ADMIN_WRONG_OTP_TIME_MINS
      ) {
        return k422ErrorMessage(
          `Please try again after ${
            ADMIN_WRONG_OTP_TIME_MINS - timeDiff + 1
          } minutes`,
        );
      } else if (timeDiff >= ADMIN_WRONG_OTP_TIME_MINS) wrongOtpCount = 1;
      const updateData = {
        wrongOtpCount,
        lastOtpAttemptTime: currentDate,
      };
      const updatedAdminData = await this.adminRepo.updateRowData(
        updateData,
        adminData?.id,
      );
      if (updatedAdminData == k500Error) throw new Error();

      if (wrongOtpCount == ADMIN_LOGIN_CHANCE)
        return k422ErrorMessage(
          `Please try again after ${
            ADMIN_WRONG_OTP_TIME_MINS - timeDiff + 1
          } minutes`,
        );
      return k422ErrorMessage(
        `Incorrect OTP, Please try again only ${
          ADMIN_LOGIN_CHANCE - wrongOtpCount
        } chances left`,
      );
    }
    if (this.typeService.otpIsExpired(adminData?.updatedAt))
      return k422ErrorMessage(kOTPIsExpired);
    if (
      wrongOtpCount < ADMIN_LOGIN_CHANCE ||
      timeDiff >= ADMIN_WRONG_OTP_TIME_MINS
    ) {
      const updateData = {
        wrongOtpCount: 0,
        phoneStatusVerified: '1',
        lastOtpAttemptTime: null,
      };
      const updatedAdminData = await this.adminRepo.updateRowData(
        updateData,
        adminData?.id,
      );
      if (updatedAdminData == k500Error) throw new Error();
      return {};
    } else
      return k422ErrorMessage(
        `Please try again after ${
          ADMIN_WRONG_OTP_TIME_MINS - timeDiff + 1
        } minutes`,
      );
  }

  private async checkAdminExist(companyPhone: string) {
    let allAdminData = await this.redisService.get(REDIS_KEY.ADMINS_DATA);
    if (!allAdminData) {
      await this.adminRedisSyncService.storeAllAdminsOnRedis();
      allAdminData = await this.redisService.get(REDIS_KEY.ADMINS_DATA);
    }
    if (allAdminData) allAdminData = JSON.parse(allAdminData);
    else throw new Error();
    for (let idx = 0; idx < allAdminData.length; idx++) {
      const admin = allAdminData[idx];
      (admin.email = admin.email
        ? await this.cryptService.decryptText(admin?.email)
        : ''),
        (admin.companyPhone = admin.companyPhone
          ? await this.cryptService.decryptText(admin?.companyPhone)
          : '');
      delete admin.otherData, admin.phone, admin.roleId, admin.isLogin;
    }
    const adminData = allAdminData?.find(
      (admin) => admin?.companyPhone == companyPhone && admin?.isActive == '1',
    );
    if (!adminData)
      return k422ErrorMessage(
        'adminData not exist with give companyPhone or admin is not Active',
      );
    return adminData;
  }

  async logIn(reqData: any) {
    const companyPhone = reqData?.companyPhone;
    const otp = reqData?.otp;
    if (!companyPhone) return kParamMissing('companyPhone');
    if (isNaN(companyPhone) || typeof companyPhone != 'string')
      return kInvalidParamValue('companyPhone');
    if (companyPhone?.length != 10)
      return k422ErrorMessage('Please enter correct 10 digit phone number');
    if (!otp) return kParamMissing('otp');
    if (isNaN(otp) || typeof otp != 'string') return kInvalidParamValue('otp');
    if (otp?.length != 4)
      return k422ErrorMessage('Please enter correct 10 digit phone number');

    const isValidOtp: any = await this.validateOtp(reqData);
    if (isValidOtp?.message) return isValidOtp;

    const adminData = await this.checkAdminExist(companyPhone);
    return adminData;
  }

  async handleLogout(reqData) {
    const companyPhone = reqData?.companyPhone;
    const adminId = reqData?.adminId;
    if (!adminId) return kParamMissing('companyPhone');
    if (!companyPhone) return kParamMissing('companyPhone');
    if (isNaN(companyPhone) || typeof companyPhone != 'string')
      return kInvalidParamValue('companyPhone');
    if (companyPhone?.length != 10)
      return k422ErrorMessage('Please enter correct 10 digit phone number');

    let needLogout = true;
    const adminData = await this.checkAdminExist(companyPhone);
    if (adminData?.id == adminId) needLogout = false;
    return { needLogout };
  }

  async createCallHistory(reqData: any) {
    let agentPhone = reqData?.agentPhone;
    let customerPhone = reqData?.customerPhone;
    const adminId = reqData?.adminId;
    let userId = reqData?.userId;
    let customerFullName = reqData?.customerFullName;
    const status = reqData?.status;
    const duration = reqData?.duration;
    let callStartTime = reqData?.callStartTime;
    let callEndTime = reqData?.callEndTime;
    const hasInternetAccess =
      reqData?.hasInternetAccess === false ? false : true;
    const isCallFromAgent = reqData?.isCallFromAgent === true ? true : false;
    const isAgentOnShift = reqData?.isAgentOnShift === true ? true : false;
    if (!agentPhone) return kParamMissing('agentPhone');
    if (isNaN(agentPhone) || typeof agentPhone != 'string')
      return kInvalidParamValue('companyPhone');
    if (!customerPhone) return kParamMissing('customerPhone');
    if (isNaN(customerPhone) || typeof customerPhone != 'string')
      return kInvalidParamValue('customerPhone');
    if (!adminId) return kParamMissing('adminId');
    if (userId && !isUUID(userId))
      return k422ErrorMessage('Please enter correct value for userId');
    if (![-1, 0, 1, 2].includes(status))
      return k422ErrorMessage('Please enter correct value for status');
    if (!callStartTime) return kParamMissing('callStartTime');
    if (!callEndTime) return kParamMissing('callEndTime');
    if (status == 1 && !duration) return kParamMissing('duration');
    agentPhone = this.typeService.formatPhoneNumber(agentPhone);
    if (agentPhone?.length != 10)
      return k422ErrorMessage('Please enter correct 10 digit agentPhone');
    customerPhone = this.typeService.formatPhoneNumber(customerPhone);
    if (customerPhone?.length != 10)
      return k422ErrorMessage('Please enter correct 10 digit customerPhone');

    callEndTime = new Date(callEndTime);
    if (duration && status == 1) {
      callStartTime = new Date(callEndTime);
      callStartTime.setSeconds(callStartTime.getSeconds() - duration);
    } else callStartTime = new Date(callStartTime);

    //if user id not found and hasInternetAccess is false then get userdata
    if (!userId && !hasInternetAccess) {
      const userData = await this.repoManager.getRowWhereData(
        registeredUsers,
        ['id', 'fullName'],
        { where: { hashPhone: this.cryptService.getMD5Hash(customerPhone) } },
      );
      userId = userData?.id;
      customerFullName = userData?.fullName;
    }

    const callData: any = {
      agentPhone: this.cryptService.encryptPhone(agentPhone),
      customerPhone: this.cryptService.encryptPhone(customerPhone),
      agentHashPhone: this.cryptService.getMD5Hash(agentPhone),
      customerHashPhone: this.cryptService.getMD5Hash(customerPhone),
      callStartTime,
      callEndTime,
      duration: duration && status == 1 ? duration : null,
      adminId,
      userId: userId ? userId : null,
      customerFullName: customerFullName ? customerFullName?.trim() : null,
      isCallFromAgent,
      isAgentOnShift,
      status,
    };
    // ignore duplicates
    const redisKey = `AGENT_CALL_${adminId}_${customerPhone}_${callStartTime.getTime()}`;
    const isNewCallData = await this.redisService.setIfNotExistsWithNX(
      redisKey,
      NUMBERS.ONE_MINUTE_IN_SECONDS,
    );
    if (!isNewCallData) return {};

    const createdAgentCallData = await this.repoManager.createRowData(
      AgentCallHistoryEntity,
      callData,
    );
    if (createdAgentCallData == k500Error) throw new Error();

    callData.callHistoryId = createdAgentCallData?.id;
    const errorInUpsert = await this.repoManager.upsert(
      AgentUserLatestCallInfo,
      callData,
      {
        returning: false,
        conflictFields: ['adminId', 'agentHashPhone', 'customerHashPhone'],
      },
    );
    if (errorInUpsert == k500Error) throw new Error();

    if (!userId || !hasInternetAccess)
      return { callId: createdAgentCallData?.id };

    if (status == 1 || (isCallFromAgent && status == -1)) {
      const loanData = await this.repoManager.getRowWhereData(
        loanTransaction,
        ['loanStatus'],
        { where: { userId, loanStatus: 'Active' } },
      );
      if (loanData == k500Error) throw new Error();
      const firebaseData = {
        id: createdAgentCallData?.id,
        source: 'Phone',
        duration: callData['duration'],
        status,
        adminId,
        userId,
        isLoanActive: loanData?.loanStatus == 'Active' ? true : false,
        customerFullName: callData['customerFullName'],
        isCallFromAgent,
        isClientListened: false,
        callStartTime: callData['callStartTime'].toISOString(),
        callEndTime: callData['callEndTime'].toISOString(),
        createdAt: new Date().toISOString(),
      };

      if (isCallFromAgent && status == 1)
        firebaseData['disposition'] = 'Call received by user';
      else if (!isCallFromAgent && status == 1)
        firebaseData['disposition'] = 'Call received by agent';
      else if (isCallFromAgent && status == -1)
        firebaseData['disposition'] = 'Call not received by user';

      await this.sendEventToFireBase(firebaseData);
    } else if (status != 1 && !isCallFromAgent) {
      const crmStatusData = await this.crmStatusRepo.getRowWhereData(['id'], {
        where: { status: 'Phone' },
      });
      if (crmStatusData == k500Error || !crmStatusData) throw new Error();

      const crmDisData = await this.dispositionRepo.getRowWhereData(
        ['id', 'title'],
        { where: { title: 'Call not received by agent' } },
      );
      if (crmDisData == k500Error || !crmDisData) throw new Error();

      let crmRemark = crmDisData?.title;
      if (isAgentOnShift && status == 2)
        crmRemark = 'Call missed (Due to busy with another call)';
      else if (!isAgentOnShift) crmRemark += ' because shift is over';
      const crmData = {
        adminId,
        statusId: crmStatusData?.id,
        dispositionId: crmDisData?.id,
        userId,
        remark: crmRemark,
        callSid: createdAgentCallData?.id,
        isUsingCallService: true,
      };
      const createdCrmData = await this.crmService.createCrm(crmData);
      if (createdCrmData?.message) return createdCrmData;
    }
    return { callId: createdAgentCallData?.id };
  }

  async uploadCallRecording(reqData: any) {
    const { callId, file, extension, directory } = reqData;
    if (!callId) return kParamMissing('callId');
    if (!file) return kParamMissing('file');
    if (!extension) return kParamMissing('extension');
    if (!directory) return kParamMissing('directory');

    const recordingURL = await this.fileService.binaryToFileURL(
      file,
      extension,
      directory,
    );
    if (recordingURL == k500Error) throw new Error();

    const encRecordingURL = await this.cryptService.encryptText(recordingURL);

    let errorInUpdate = await this.repoManager.updateRowData(
      AgentCallHistoryEntity,
      { recordingURL: encRecordingURL },
      callId,
    );
    if (errorInUpdate == k500Error) throw new Error();

    errorInUpdate = await this.repoManager.updateRowWhereData(
      AgentUserLatestCallInfo,
      { recordingURL: encRecordingURL },
      { where: { callHistoryId: callId } },
    );
    if (errorInUpdate == k500Error) throw new Error();
    return {};
  }

  private async sendEventToFireBase(data: any) {
    const mode = gIsPROD ? 'PROD_' : 'UAT_';
    const collection =
      mode + EnvConfig.nbfc.nbfcShortName + '_AGENT_CALL_HISTORY';

    // Filter out undefined properties
    const filteredData = this.filterUndefinedProperties(data);

    const collectionRef = this.firebaseDB.collection(collection);

    const adminDocRef = collectionRef.doc(filteredData.adminId.toString());

    await adminDocRef.collection('CALL_HISTORY_DATA').add(filteredData);
  }

  private filterUndefinedProperties(object) {
    return Object.fromEntries(
      Object.entries(object).filter(([key, val]) => val !== undefined),
    );
  }

  private getSlackString(title, message) {
    return `*${title}* \n\n${message}`;
  }

  async getCustomerLatestCallLog(reqData) {
    const adminId = reqData?.adminId;
    let searchText = (reqData?.searchText ?? '')?.trim();
    const callType = +(reqData?.callType ?? kAgentAppCallType.all);
    if (!Object.values(kAgentAppCallType).includes(callType))
      return kInvalidParamValue('callType');
    if (searchText.includes('+'))
      searchText = searchText.replace(/\+/g, '').trim();
    let searchBy;
    if (searchText.length > 3) {
      if (!isNaN(searchText)) searchBy = 'Phone';
      else searchBy = 'Name';
    }
    if (!adminId) return kParamMissing('adminId');
    const page = +(reqData?.page ?? PAGE);
    const pageLimit = +(reqData?.pageLimit ?? PAGE_LIMIT);
    const options: any = {
      where: {},
      offset: page * pageLimit - pageLimit,
      limit: pageLimit,
      order: [['callStartTime', 'DESC']],
    };

    if (callType == kAgentAppCallType.incoming)
      options.where = {
        isCallFromAgent: { [Op.is]: false },
        status: { [Op.in]: [-1, 1] },
      };
    else if (callType == kAgentAppCallType.outgoing)
      options.where = {
        isCallFromAgent: { [Op.is]: true },
        status: { [Op.in]: [-1, 1] },
      };
    else if (callType == kAgentAppCallType.missed)
      options.where = {
        isCallFromAgent: { [Op.is]: false },
        status: { [Op.in]: [0, 2] },
      };
    options.where.adminId = adminId;

    if (searchBy == 'Phone')
      options.where.customerPhone = {
        [Op.iLike]:
          '%' +
          this.cryptService.encryptPhone(searchText).split('===')[1] +
          '%',
      };
    else if (searchBy == 'Name')
      options.where.customerFullName = {
        [Op.iLike]: '%' + searchText + '%',
      };

    const userWiseCallData = await this.repoManager.getTableCountWhereData(
      AgentUserLatestCallInfo,
      [
        'isCallFromAgent',
        'status',
        'callStartTime',
        'userId',
        'customerPhone',
        'customerFullName',
      ],
      options,
    );

    if (userWiseCallData == k500Error) throw new Error();
    if (!userWiseCallData?.rows?.length) return userWiseCallData;

    ///get user details
    let userIds: any = new Set();
    userWiseCallData?.rows.forEach((callData) => {
      if (callData?.userId) userIds.add(callData?.userId);
    });

    let userProfile: any = [];
    let favoriteUsers: any = [];
    if (userIds.size) {
      userIds = [...userIds];
      userProfile = await this.repoManager.getTableWhereData(
        registeredUsers,
        ['id', 'image'],
        { where: { id: userIds } },
      );
      if (userProfile == k500Error) throw new Error();

      const redisKey = `${adminId}_AGENT_APP_FAVORITE_CUSTOMERS`;
      favoriteUsers = await this.redisService.get(redisKey);
      if (favoriteUsers) favoriteUsers = JSON.parse(favoriteUsers);
    }

    const filteredData = await this.formatUserWiseCallData(
      userWiseCallData?.rows,
      userProfile,
      favoriteUsers,
    );

    return { count: userWiseCallData.count, rows: filteredData };
  }

  private async formatUserWiseCallData(callData, userProfile, favoriteUsers) {
    const formattedData = [];
    let tempDataObj: any = {};
    for (const ele of callData) {
      tempDataObj = {
        customerPhone: ele?.customerPhone
          ? this.cryptService.decryptPhone(ele.customerPhone)
          : null,
        callType: null,
        callStartTime: ele.callStartTime,
        selfieUrl: null,
        userId: ele?.userId ?? null,
        customerFullName: ele.customerFullName ?? null,
        isFavorite: ele?.userId ? favoriteUsers?.includes(ele?.userId) : false,
      };

      //if user id found than set user image
      if (ele?.userId) {
        const userData = userProfile.find((data) => data?.id == ele?.userId);
        tempDataObj.selfieUrl = userData?.image ?? null;
      }

      if (ele.isCallFromAgent)
        tempDataObj.callType = kAgentAppCallType.outgoing;
      else {
        if (ele.status == 0 || ele.status == 2)
          tempDataObj.callType = kAgentAppCallType.missed;
        else tempDataObj.callType = kAgentAppCallType.incoming;
      }

      formattedData.push(tempDataObj);
    }
    return formattedData;
  }

  async getCustomerCallHistory(reqData) {
    const adminId = reqData?.adminId;
    if (!adminId) return kParamMissing('adminId');
    let customerPhone = reqData?.customerPhone;
    if (!customerPhone) return kParamMissing('customerPhone');
    if (isNaN(customerPhone) || typeof customerPhone != 'string')
      return kInvalidParamValue('customerPhone');
    customerPhone = this.typeService.formatPhoneNumber(customerPhone);
    if (customerPhone?.length != 10)
      return k422ErrorMessage('Please enter correct 10 digit customerPhone');
    const page = +(reqData?.page ?? PAGE);
    const pageLimit = +(reqData?.pageLimit ?? PAGE_LIMIT);
    const options: any = {
      where: {
        adminId,
        customerHashPhone: this.cryptService.getMD5Hash(customerPhone),
      },
      offset: page * pageLimit - pageLimit,
      limit: pageLimit,
      order: [['callStartTime', 'DESC']],
    };

    const customerCallHistoryData =
      await this.repoManager.getTableCountWhereData(
        AgentCallHistoryEntity,
        [
          'isCallFromAgent',
          'status',
          'duration',
          'callStartTime',
          'recordingURL',
        ],
        options,
      );

    if (customerCallHistoryData == k500Error) throw new Error();
    if (!customerCallHistoryData?.rows?.length) return customerCallHistoryData;

    for (const ele of customerCallHistoryData?.rows) {
      if (ele?.recordingURL)
        ele.recordingURL = await this.cryptService.decryptText(
          ele.recordingURL,
        );
      if (ele.isCallFromAgent) ele.callType = kAgentAppCallType.outgoing;
      else {
        if (ele.status == 0 || ele.status == 2)
          ele.callType = kAgentAppCallType.missed;
        else ele.callType = kAgentAppCallType.incoming;
      }
      delete ele.isCallFromAgent, ele.status;
    }
    return customerCallHistoryData;
  }

  async getSuggestedCustomers(reqData) {
    const adminId = reqData?.adminId;
    if (!adminId) return kParamMissing('adminId');
    let searchText = (reqData?.searchText ?? '')?.trim();
    if (searchText?.length <= 3) return [];
    if (searchText.includes('+'))
      searchText = searchText.replace(/\+/g, '').trim();
    let searchBy;
    if (!isNaN(searchText)) searchBy = 'Phone';
    else searchBy = 'Name';

    const page = +(reqData?.page ?? PAGE);
    const pageLimit = +(reqData?.pageLimit ?? PAGE_LIMIT);
    const options: any = {
      where: {},
      offset: page * pageLimit - pageLimit,
      limit: pageLimit,
      order: [['fullName', 'ASC']],
    };

    if (searchBy == 'Phone')
      options.where.phone = {
        [Op.iLike]:
          '%' +
          this.cryptService.encryptPhone(searchText).split('===')[1] +
          '%',
      };
    else if (searchBy == 'Name')
      options.where.fullName = {
        [Op.iLike]: '%' + searchText + '%',
      };

    const customersData = await this.repoManager.getTableCountWhereData(
      registeredUsers,
      ['id', 'fullName', 'image', 'phone'],
      options,
    );
    if (customersData == k500Error) throw new Error();
    if (!customersData?.rows?.length) return customersData;

    const redisKey = `${adminId}_AGENT_APP_FAVORITE_CUSTOMERS`;
    let favoriteUsers = await this.redisService.get(redisKey);
    if (favoriteUsers) favoriteUsers = JSON.parse(favoriteUsers);
    else favoriteUsers = [];
    const formattedData: any = { count: customersData?.count, rows: [] };
    for (const ele of customersData?.rows) {
      const tempDataObj = {
        customerPhone: ele?.phone
          ? this.cryptService.decryptPhone(ele.phone)
          : null,
        selfieUrl: ele?.image ? ele.image : null,
        userId: ele?.id ?? null,
        customerFullName: ele?.fullName?.trim() ? ele.fullName?.trim() : null,
        isFavorite: ele?.id ? favoriteUsers?.includes(ele?.id) : false,
      };
      formattedData.rows.push(tempDataObj);
    }
    return formattedData;
  }

  async getFavoriteCustomers(reqData) {
    const adminId = reqData?.adminId;
    let searchText = (reqData?.searchText ?? '')?.trim();
    if (searchText.includes('+'))
      searchText = searchText.replace(/\+/g, '').trim();
    let searchBy;
    if (searchText?.length > 3) {
      if (!isNaN(searchText)) searchBy = 'Phone';
      else searchBy = 'Name';
    }
    if (!adminId) return kParamMissing('adminId');
    const redisKey = `${adminId}_AGENT_APP_FAVORITE_CUSTOMERS`;
    let favoriteUsers = await this.redisService.get(redisKey);
    if (favoriteUsers) favoriteUsers = JSON.parse(favoriteUsers);
    else favoriteUsers = [];
    if (!favoriteUsers?.length) return [];

    const options: any = {
      where: { id: favoriteUsers },
      order: [['fullName', 'ASC']],
    };

    if (searchBy == 'Phone')
      options.where.phone = {
        [Op.iLike]:
          '%' +
          this.cryptService.encryptPhone(searchText).split('===')[1] +
          '%',
      };
    else if (searchBy == 'Name')
      options.where.fullName = {
        [Op.iLike]: '%' + searchText + '%',
      };

    const customersData = await this.repoManager.getTableWhereData(
      registeredUsers,
      ['id', 'fullName', 'image', 'phone'],
      options,
    );
    if (customersData == k500Error) throw new Error();
    if (!customersData?.length) return customersData;

    const formattedData: any = [];
    for (const ele of customersData) {
      const tempDataObj = {
        customerPhone: ele?.phone
          ? this.cryptService.decryptPhone(ele.phone)
          : null,
        selfieUrl: ele?.image ? ele.image : null,
        userId: ele?.id ?? null,
        customerFullName: ele?.fullName?.trim() ? ele.fullName?.trim() : null,
        isFavorite: ele?.id ? favoriteUsers?.includes(ele?.id) : false,
      };
      formattedData.push(tempDataObj);
    }
    return formattedData;
  }

  async updateCustomerFavStatus(reqData) {
    const isFavorite = reqData?.isFavorite === true ? true : false;
    const userId = reqData?.userId;
    const adminId = reqData?.adminId;
    if (!adminId) return kParamMissing('adminId');
    if (!userId) return kParamMissing('userId');
    if (!isUUID(userId))
      return k422ErrorMessage('Please enter correct value for userId');
    const redisKey = `${adminId}_AGENT_APP_FAVORITE_CUSTOMERS`;
    let favoriteUsers = await this.redisService.get(redisKey);
    if (favoriteUsers) favoriteUsers = JSON.parse(favoriteUsers);
    else favoriteUsers = [];

    if (isFavorite) {
      if (favoriteUsers?.length >= 100)
        return k422ErrorMessage('only 100 favoriteUsers store');
      favoriteUsers.push(userId);
    } else {
      const idx = favoriteUsers.findIndex((id) => id == userId);
      if (idx == -1) throw new Error();
      favoriteUsers.splice(idx, 1);
    }
    await this.redisService.set(redisKey, JSON.stringify(favoriteUsers));
    return { isFavorite };
  }

  private formatSlackMesseageTable(slackData) {
    const { headers, callData } = slackData;
    const rows = callData.map((call) => [
      ...headers.map((header) => String(call[header])),
    ]);

    const columnWidths = headers.map((header, i) => {
      return Math.max(header.length, ...rows.map((row) => row[i].length));
    });

    const formatRow = (row) => {
      return row.map((cell, i) => cell.padEnd(columnWidths[i])).join(' | ');
    };

    const table = [
      formatRow(headers),
      '-'.repeat(columnWidths.reduce((a, b) => a + b + 3, -3)),
      ...rows.map(formatRow),
    ].join('\n');

    return `\`\`\`\n${table.trim()}\n\`\`\``;
  }

  async sendCallPerformanceReportToSlack() {
    if (EnvConfig.nbfcType == '1') return {};
    //send for cse
    let cseAdmins = await this.redisService.get('CSE_ADMIN_LIST');
    if (!cseAdmins) throw new Error();
    cseAdmins = JSON.parse(cseAdmins);
    const cseAdminIds = cseAdmins.map((ele) => ele.id);

    const cseTeamChannelId = gIsPROD
      ? EnvConfig.slack.csTeamChannelId
      : EnvConfig.slack.channelId;
    await this.sendCallSummaryReportToSlack(cseAdminIds, cseTeamChannelId);

    //send for colletion
    const colletionRole = await this.adminRoleRepo.getRoweData(['id'], {
      where: { title: 'collection' },
    });
    if (!colletionRole || colletionRole == k500Error) throw new Error();
    const options = {
      where: { roleId: colletionRole?.id, isActive: '1' },
    };
    const collectionAdminData = await this.adminRepo.getTableWhereData(
      ['id'],
      options,
    );
    if (collectionAdminData == k500Error) throw new Error();
    const collectionAdminIds = collectionAdminData.map((ele) => ele.id);
    const collectionTeamChannelId = gIsPROD
      ? EnvConfig.slack.collectionTeamChannelId
      : EnvConfig.slack.channelId;
    await this.sendCallSummaryReportToSlack(
      collectionAdminIds,
      collectionTeamChannelId,
    );
    return {};
  }

  private async sendCallSummaryReportToSlack(adminIds, channelId) {
    const endTime = new Date();
    const startTime = new Date(
      endTime.getTime() - NUMBERS.ONE_DAY_IN_SECONDS * 1000,
    );
    const [
      AdminWiseOutgoingCallReceivedData,
      AdminWiseOutgoingCallMissedData,
      AdminWiseIncomingCallReceivedData,
      AdminWiseIncomingCallMissedData,
    ] = await Promise.all([
      this.getAdminsCallInfo({ startTime, endTime, adminIds }),
      this.getAdminsCallInfo({
        startTime,
        endTime,
        isCallReceived: false,
        adminIds,
      }),
      this.getAdminsCallInfo({
        startTime,
        endTime,
        isCallFromAgent: false,
        adminIds,
      }),
      this.getAdminsCallInfo({
        startTime,
        endTime,
        isCallReceived: false,
        isCallFromAgent: false,
        adminIds,
      }),
    ]);

    if (
      AdminWiseOutgoingCallReceivedData == k500Error ||
      AdminWiseOutgoingCallMissedData == k500Error ||
      AdminWiseIncomingCallReceivedData == k500Error ||
      AdminWiseIncomingCallMissedData == k500Error
    )
      throw new Error();

    const adminWiseAllData = await this.mapAllCallDataByAdminWise({
      AdminWiseOutgoingCallReceivedData,
      AdminWiseOutgoingCallMissedData,
      AdminWiseIncomingCallReceivedData,
      AdminWiseIncomingCallMissedData,
      adminIds,
    });

    const headers = [
      'Agent',
      'Incoming Received',
      'Incoming Missed',
      'Outgoing Connected',
      'Outgoing Missed',
      'Total Calls',
      'Total Call Time',
    ];

    //send batchwise message
    for (let index = 0; index < adminWiseAllData.length; index = index + 10) {
      const batchAdminWiseCallData = adminWiseAllData.slice(index, index + 10);
      const callFormatedString = this.formatSlackMesseageTable({
        callData: batchAdminWiseCallData,
        headers,
      });

      const messageOptions: any = {
        text: `${this.getSlackString(
          'Agent Performance Report:',
          callFormatedString,
        )}`,
        sourceStr: false,
        channel: channelId,
      };
      this.slackService.sendMsg(messageOptions);
    }
    return {};
  }

  private async getAdminsCallInfo(reqData) {
    const {
      endTime,
      startTime,
      isCallReceived = true,
      isCallFromAgent = true,
      adminIds = [],
    } = reqData;

    let attributes: any = [
      'adminId',
      [Sequelize.fn('COUNT', Sequelize.col('adminId')), 'callCount'],
      [Sequelize.fn('SUM', Sequelize.col('duration')), 'totalDuration'],
    ];
    const options: any = {
      where: {
        userId: { [Op.not]: null },
        adminId: adminIds,
        status: isCallReceived ? { [Op.eq]: 1 } : { [Op.ne]: 1 },
        isCallFromAgent,
        callStartTime: {
          [Op.gte]: startTime,
          [Op.lte]: endTime,
        },
      },
      group: ['adminId'],
    };
    const countData = await this.repoManager.getTableWhereData(
      AgentCallHistoryEntity,
      attributes,
      options,
    );
    if (countData == k500Error) throw new Error();

    attributes = ['userId'];
    options.group = attributes;

    const finalizedCallData: any = [];
    let tempObj: any = {};
    for (const id of adminIds) {
      options.where.adminId = id;
      const userData = await this.repoManager.getTableWhereData(
        AgentCallHistoryEntity,
        attributes,
        options,
      );
      if (userData == k500Error) throw new Error();
      const adminCallData = countData.find((ele) => ele.adminId == id);
      tempObj = {
        adminId: id,
        callData: {
          callCount: adminCallData?.callCount ?? '0',
          totalDuration: adminCallData?.totalDuration ?? '0',
          userIds: userData.length ? userData.map((ele) => ele.userId) : [],
        },
      };
      finalizedCallData.push(tempObj);
    }
    return finalizedCallData;
  }

  private async mapAllCallDataByAdminWise(allCallData) {
    const {
      AdminWiseOutgoingCallReceivedData,
      AdminWiseOutgoingCallMissedData,
      AdminWiseIncomingCallReceivedData,
      AdminWiseIncomingCallMissedData,
      adminIds,
    } = allCallData;

    const adminData = await this.adminRepo.getTableWhereData(
      ['id', 'fullName'],
      { where: { id: adminIds } },
    );

    if (adminData == k500Error) throw new Error();

    const adminWiseAllData = {};
    for (const id of adminIds) {
      let tempObj = {
        Agent: '',
        'Incoming Received': 0,
        'Incoming Missed': 0,
        'Outgoing Connected': 0,
        'Outgoing Missed': 0,
        'Total Calls': 0,
        'Total Call Time': '',
      };

      const outgoingCallReceivedData = AdminWiseOutgoingCallReceivedData.find(
        (el) => el.adminId == id,
      );
      const outgoingCallMissedData = AdminWiseOutgoingCallMissedData.find(
        (el) => el.adminId == id,
      );
      const incomingCallReceivedData = AdminWiseIncomingCallReceivedData.find(
        (el) => el.adminId == id,
      );
      const incomingCallMissedData = AdminWiseIncomingCallMissedData.find(
        (el) => el.adminId == id,
      );

      tempObj['Incoming Received'] = +(
        incomingCallReceivedData?.callData?.callCount ?? 0
      );
      tempObj['Incoming Missed'] = +(
        incomingCallMissedData?.callData?.callCount ?? 0
      );
      tempObj['Outgoing Connected'] = +(
        outgoingCallReceivedData?.callData?.callCount ?? 0
      );
      tempObj['Outgoing Missed'] = +(
        outgoingCallMissedData?.callData?.callCount ?? 0
      );

      const totalDurationn =
        +(incomingCallReceivedData?.callData?.totalDuration ?? 0) +
        +(incomingCallMissedData?.callData?.totalDuration ?? 0) +
        +(outgoingCallReceivedData?.callData?.totalDuration ?? 0) +
        +(outgoingCallMissedData?.callData?.totalDuration ?? 0);

      tempObj['Total Calls'] =
        tempObj['Incoming Received'] +
        tempObj['Incoming Missed'] +
        tempObj['Outgoing Connected'] +
        tempObj['Outgoing Missed'];

      tempObj['Total Call Time'] =
        this.dateService.formatSecond(totalDurationn);
      tempObj['Agent'] = adminData.find((el) => el.id == id)?.fullName;
      adminWiseAllData[id] = tempObj;
    }
    return Object.values(adminWiseAllData);
  }

  async sendCollectionCallCategoryReportToSlack() {
    if (EnvConfig.nbfcType == '1') return {};
    const endTime = new Date();
    const startTime = new Date(
      endTime.getTime() - NUMBERS.ONE_DAY_IN_SECONDS * 1000,
    );

    const colletionRole = await this.adminRoleRepo.getRoweData(['id'], {
      where: { title: 'collection' },
    });
    if (!colletionRole || colletionRole == k500Error) throw new Error();
    const options = {
      where: { roleId: colletionRole?.id, isActive: '1' },
    };
    const collectionAdminData = await this.adminRepo.getTableWhereData(
      ['id'],
      options,
    );
    if (collectionAdminData == k500Error) throw new Error();
    const collectionAdminIds = collectionAdminData.map((ele) => ele.id);

    const userIds = await this.fetchCalledUserIds({
      endTime,
      startTime,
      adminIds: collectionAdminIds,
    });

    const userEmiData = await this.fetchUserEmiDataWithDelayDays(userIds);

    const categorizeUserIdsByDelayData = this.categorizeUserByDelay({
      allEmiData: userEmiData,
      userIds,
    });

    const categoryWiseCallData = await this.fetchCallDataByCategory({
      categorizeUserIdsByDelayData,
      startTime,
      endTime,
      adminIds: collectionAdminIds,
    });

    const formatedCallData = this.formatCallCategoryData(categoryWiseCallData);

    const headers = [
      'Category',
      'Incoming Received',
      'Incoming Missed',
      'Outgoing Connected',
      'Outgoing Missed',
      'Total Calls',
      'Total Call Time',
    ];

    const callFormatedString = this.formatSlackMesseageTable({
      callData: formatedCallData,
      headers,
    });

    const channelId = gIsPROD
      ? EnvConfig.slack.collectionTeamChannelId
      : EnvConfig.slack.channelId;

    const messageOptions: any = {
      text: `${this.getSlackString(
        'Call Category Report:',
        callFormatedString,
      )}`,
      sourceStr: false,
      channel: channelId,
    };
    this.slackService.sendMsg(messageOptions);
    return {};
  }

  private async fetchCalledUserIds(reqData) {
    const { endTime, startTime, adminIds = [] } = reqData;

    let attributes: any = ['userId'];
    const options: any = {
      where: {
        userId: { [Op.not]: null },
        adminId: adminIds,
        callStartTime: {
          [Op.gte]: startTime,
          [Op.lte]: endTime,
        },
      },
      group: ['userId'],
    };
    const userData = await this.repoManager.getTableWhereData(
      AgentCallHistoryEntity,
      attributes,
      options,
    );
    if (userData == k500Error) throw new Error();

    const userIds = userData.map((el) => el.userId);
    return userIds;
  }

  private async fetchUserEmiDataWithDelayDays(userIds) {
    if (!userIds.length) return [];

    const today: any = this.typeService.getGlobalDate(new Date()).toJSON();
    let yesterday: any = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    yesterday = this.typeService.getGlobalDate(yesterday).toJSON();

    const attributes: any = [
      'userId',
      [Sequelize.literal('SUM("penalty_days")'), 'penaltyDays'],
    ];

    const options = {
      where: {
        userId: userIds,
        [Op.or]: [
          {
            payment_status: '1',
            payment_done_date: [today, yesterday],
          },
          { payment_status: '0' },
        ],
      },
      group: ['userId'],
    };

    const emiData = await this.repoManager.getTableWhereData(
      EmiEntity,
      attributes,
      options,
    );

    if (emiData == k500Error) throw new Error();
    return emiData;
  }

  private categorizeUserByDelay(reqData) {
    const { allEmiData = [], userIds = [] } = reqData;
    const categoryWiseUserData = {
      'On-Time': [],
      '1-15 DPD': [],
      '16-30 DPD': [],
      '31-60 DPD': [],
      '61-90 DPD': [],
      '90+ DPD': [],
    };
    for (const id of userIds) {
      const emiData = allEmiData.find((el) => el.userId == id);
      if (!emiData) continue;
      const penaltyDays = +(emiData?.penaltyDays ?? '0');
      if (penaltyDays > 90) categoryWiseUserData['90+ DPD'].push(id);
      else if (penaltyDays >= 61 && penaltyDays <= 90)
        categoryWiseUserData['61-90 DPD'].push(id);
      else if (penaltyDays >= 31 && penaltyDays <= 60)
        categoryWiseUserData['31-60 DPD'].push(id);
      else if (penaltyDays >= 16 && penaltyDays <= 30)
        categoryWiseUserData['16-30 DPD'].push(id);
      else if (penaltyDays >= 1 && penaltyDays <= 15)
        categoryWiseUserData['1-15 DPD'].push(id);
      else categoryWiseUserData['On-Time'].push(id);
    }
    return categoryWiseUserData;
  }

  private async fetchCallDataByCategory(reqData) {
    const {
      categorizeUserIdsByDelayData,
      startTime,
      endTime,
      adminIds = [],
    } = reqData;

    const resultObj: any = {
      'On-Time': {},
      '1-15 DPD': {},
      '16-30 DPD': {},
      '31-60 DPD': {},
      '61-90 DPD': {},
      '90+ DPD': {},
    };
    for (const category in categorizeUserIdsByDelayData) {
      const userIds = categorizeUserIdsByDelayData[category];
      const [
        outgoingCallReceivedData,
        outgoingCallMissedData,
        incomingCallReceivedData,
        incomingCallMissedData,
      ] = await Promise.all([
        this.fetchCallInfo({
          startTime,
          endTime,
          adminIds,
          userIds,
        }),
        this.fetchCallInfo({
          startTime,
          endTime,
          isCallReceived: false,
          adminIds,
          userIds,
        }),
        this.fetchCallInfo({
          startTime,
          endTime,
          isCallFromAgent: false,
          adminIds,
          userIds,
        }),
        this.fetchCallInfo({
          startTime,
          endTime,
          isCallReceived: false,
          isCallFromAgent: false,
          adminIds,
          userIds,
        }),
      ]);

      if (
        outgoingCallReceivedData == k500Error ||
        outgoingCallMissedData == k500Error ||
        incomingCallReceivedData == k500Error ||
        incomingCallMissedData == k500Error
      )
        throw new Error();
      resultObj[category] = {
        incoming: {
          received: incomingCallReceivedData,
          missed: incomingCallMissedData,
        },
        outgoing: {
          received: outgoingCallReceivedData,
          missed: outgoingCallMissedData,
        },
      };
    }
    return resultObj;
  }

  private async fetchCallInfo(reqData) {
    const {
      endTime,
      startTime,
      isCallReceived = true,
      isCallFromAgent = true,
      adminIds = [],
      userIds = [],
    } = reqData;

    const attributes: any = [
      [Sequelize.fn('COUNT', Sequelize.col('*')), 'callCount'],
      [Sequelize.fn('SUM', Sequelize.col('duration')), 'totalDuration'],
    ];
    const options: any = {
      where: {
        userId: userIds,
        adminId: adminIds,
        status: isCallReceived ? { [Op.eq]: 1 } : { [Op.ne]: 1 },
        isCallFromAgent,
        callStartTime: {
          [Op.gte]: startTime,
          [Op.lte]: endTime,
        },
      },
    };
    const callData = await this.repoManager.getRowWhereData(
      AgentCallHistoryEntity,
      attributes,
      options,
    );
    if (callData == k500Error) throw new Error();
    return callData;
  }

  private formatCallCategoryData(allCallData) {
    const arr = [];
    for (const category in allCallData) {
      let tempObj = {
        Category: category,
        'Incoming Received': 0,
        'Incoming Missed': 0,
        'Outgoing Connected': 0,
        'Outgoing Missed': 0,
        'Total Calls': 0,
        'Total Call Time': '',
      };
      tempObj['Incoming Received'] = +(
        allCallData[category]?.['incoming']?.['received']?.callCount ?? '0'
      );
      tempObj['Incoming Missed'] = +(
        allCallData[category]?.['incoming']?.['missed']?.callCount ?? '0'
      );
      tempObj['Outgoing Connected'] = +(
        allCallData[category]?.['outgoing']?.['received']?.callCount ?? '0'
      );
      tempObj['Outgoing Missed'] = +(
        allCallData[category]?.['outgoing']?.['missed']?.callCount ?? '0'
      );
      tempObj['Total Calls'] =
        tempObj['Incoming Received'] +
        tempObj['Incoming Missed'] +
        tempObj['Outgoing Connected'] +
        tempObj['Outgoing Missed'];

      const duration =
        +(
          allCallData[category]?.['incoming']?.['received']?.totalDuration ??
          '0'
        ) +
        +(
          allCallData[category]?.['incoming']?.['missed']?.totalDuration ?? '0'
        ) +
        +(
          allCallData[category]?.['outgoing']?.['received']?.totalDuration ??
          '0'
        ) +
        +(
          allCallData[category]?.['outgoing']?.['missed']?.totalDuration ?? '0'
        );

      tempObj['Total Call Time'] = this.dateService.formatSecond(duration);
      arr.push(tempObj);
    }
    return arr;
  }

  //#region  --------------------------------------------------------------
  async getUserDataUsingPhone(reqData) {
    let phoneNumber = reqData?.phoneNumber;
    if (!phoneNumber) return kParamMissing('phoneNumber');
    if (isNaN(phoneNumber) || typeof phoneNumber != 'string')
      return kInvalidParamValue('phoneNumber');
    phoneNumber = this.typeService.formatPhoneNumber(phoneNumber);
    if (phoneNumber?.length != 10)
      return k422ErrorMessage('Please enter correct 10 digit phoneNumber');

    const hashPhone = this.cryptService.getMD5Hash(phoneNumber);

    const hashPhoneData = await this.repoManager.getRowWhereData(
      HashPhoneEntity,
      ['userId'],
      { where: { hashPhone } },
    );
    if (hashPhoneData == k500Error) throw new Error();
    if (!hashPhoneData) return {};

    const userData = await this.userRepo.getRowWhereData(
      [
        'id',
        'fullName',
        'image',
        'loanStatus',
        'lastLoanId',
        'stage',
        'completedLoans',
      ],
      { where: { id: hashPhoneData?.userId } },
    );
    if (userData == k500Error) throw new Error();
    if (!userData?.id) return {};

    return await this.prepareAgentAppUserData(userData);
  }
  //#endregion

  //#region
  private async prepareAgentAppUserData(userData) {
    ///prepare user stage
    const stageStatus = Object.values(UserStage)?.findIndex(
      (el) => el == userData?.stage,
    );

    const stage = stageStatus != -1 ? Object.keys(UserStage)[stageStatus] : '';

    ////prepare user data according to user stage
    const userInfo = await this.prepareUserInfoAgent(userData, stage);

    ////prepare profile text
    const isLoanInprogress = ![
      UserStage.REPAYMENT,
      UserStage.DEFAULTER,
      UserStage.NOT_ELIGIBLE,
    ].includes(userData?.stage);

    const completedLoans = userData?.completedLoans;
    let profileText;
    if (userData?.completedLoans > 0 && !isLoanInprogress) {
      profileText =
        'Repeat: ' +
        completedLoans +
        ' | ' +
        AGENT_USER_LOAN_STATUS[+userData?.loanStatus];
    } else if (userData?.completedLoans > 0 && isLoanInprogress) {
      profileText = 'Repeat: ' + completedLoans;
    } else if (!isLoanInprogress && userData?.completedLoans == 0)
      if (AGENT_USER_LOAN_STATUS[+userData?.loanStatus])
        profileText =
          'New ' + '| ' + AGENT_USER_LOAN_STATUS[+userData?.loanStatus];
      else profileText = 'New';
    else profileText = 'New';

    ////prepare chip color code
    let chipColor = '#00BA00';
    if ([2, 3].includes(+userData?.loanStatus)) chipColor = '#FF0000';
    else if (userData?.completedLoans > 0) chipColor = '#4553B4';

    return {
      id: userData?.id,
      stage,
      fullName: userData?.fullName,
      selfieUrl: userData?.image ?? null,
      profileText,
      chipColor,
      userInfo,
    };
  }
  //#endregion

  //#region
  private async prepareUserInfoAgent(userData, stage) {
    const response = [];
    const isLoanInprogress = ![
      UserStage.REPAYMENT,
      UserStage.DEFAULTER,
      UserStage.NOT_ELIGIBLE,
    ].includes(userData?.stage);

    const employmentData = await this.repoManager.getRowWhereData(
      employmentDetails,
      ['id', 'companyName', 'salary'],
      { where: { userId: userData?.id }, order: [['id', 'DESC']] },
    );

    ///if loan is in progress
    if (isLoanInprogress)
      await this.prepareAgentAppInProgress(
        response,
        userData,
        employmentData,
        stage,
      );
    else await this.prepareActiveLoanUser(response, userData, stage);

    if (employmentData?.companyName)
      response.push({
        iconURL: AGENT_APP_CALL_ICONS.COMPANY,
        header: 'Company',
        headerColor: '#A4A4A4',
        value: employmentData?.companyName || '-',
        valueColor: '#38383E',
      });

    return response;
  }
  //#endregion

  //#region
  private async prepareAgentAppInProgress(
    response,
    userData,
    employmentData,
    stage,
  ) {
    let salary = employmentData?.salary || null;

    ///if user at banking step then get latest salary
    if (userData.stage >= 8 && userData?.lastLoanId) {
      const salaryDetails = await this.repoManager.getRowWhereData(
        BankingEntity,
        ['id', 'salary'],
        {
          where: { loanId: userData?.lastLoanId },
          order: [['id', 'DESC']],
        },
      );
      salary = salaryDetails?.salary ?? salary;
    }

    response.push({
      iconURL: AGENT_APP_CALL_ICONS.STAGE,
      header: 'Stage',
      headerColor: '#A4A4A4',
      value: stage,
      valueColor: '#38383E',
    });

    if (salary)
      response.push({
        iconURL: AGENT_APP_CALL_ICONS.SALARY,
        header: 'Salary',
        headerColor: '#A4A4A4',
        value: '₹ ' + this.typeService.amountNumberWithCommas(salary) || '-',
        valueColor: '#38383E',
      });
  }
  //#endregion

  //#region
  private async prepareActiveLoanUser(response, userData, stage) {
    if (!userData?.lastLoanId) {
      response.push({
        iconURL: AGENT_APP_CALL_ICONS.STAGE,
        header: 'Stage',
        headerColor: '#A4A4A4',
        value: stage,
        valueColor: '#38383E',
      });
      return;
    }

    const emiDetails = await this.repoManager.getTableWhereData(
      EmiEntity,
      [
        'id',
        'emi_amount',
        'legalCharge',
        'legalChargeGST',
        'paidLegalCharge',
        'penalty',
        'emi_date',
        'partOfemi',
        'payment_status',
        'payment_due_status',
        'penalty_days',
        'principalCovered',
        'interestCalculate',
        'paid_principal',
        'paid_interest',
        'regInterestAmount',
        'paidRegInterestAmount',
        'bounceCharge',
        'gstOnBounceCharge',
        'paidBounceCharge',
        'dpdAmount',
        'penaltyChargesGST',
        'paidPenalCharge',
      ],
      {
        where: {
          loanId: userData?.lastLoanId,
        },
        order: [['id', 'ASC']],
      },
    );

    if (!emiDetails?.length || emiDetails == k500Error) {
      response.push({
        iconURL: AGENT_APP_CALL_ICONS.STAGE,
        header: 'Stage',
        headerColor: '#A4A4A4',
        value: stage,
        valueColor: '#38383E',
      });
      return;
    }

    const emiData = {
      amount: 0,
      upcomingDate: null,
      upcomingEmiNumber: 0,
      upcomingEmiAmount: 0,
      isDelay: false,
      delayDays: 0,
      totalDueAmount: 0,
      totalDelayEmi: 0,
    };

    for (let index = 0; index < emiDetails.length; index++) {
      const element = emiDetails[index];
      if (element?.payment_due_status == 1 && element?.payment_status == 0) {
        ///due user
        emiData.isDelay = true;
        emiData.totalDelayEmi += 1;
      }

      ///prepare total unpaid amount
      if (element?.payment_status != 1)
        emiData.totalDueAmount += this.prepareUnpaidAmount(element);

      ///manage max delay days
      if (emiData.delayDays < element?.penalty_days)
        emiData.delayDays = +element?.penalty_days;

      ///manage upcoming emi's
      if (
        !emiData.isDelay &&
        !emiData.upcomingDate &&
        element?.payment_status == 0
      ) {
        emiData.upcomingDate = element?.emi_date;
        emiData.upcomingEmiNumber =
          PART_OF_EMI[element?.partOfemi] || emiDetails?.length;
        emiData.upcomingEmiAmount =
          +element?.principalCovered + +element?.interestCalculate;
      }

      emiData.amount += +element?.principalCovered || 0;
    }

    ///Loan Amount
    response.push({
      iconURL: AGENT_APP_CALL_ICONS.MONEY,
      header: 'Loan Amount',
      headerColor: '#A4A4A4',
      value: '₹ ' + this.typeService.amountNumberWithCommas(emiData.amount),
      valueColor: '#38383E',
      trailingTwo: 'Total EMIs: ' + emiDetails?.length,
      trailingTwoColor: '#4D4D4D',
    });

    ///prepare trailingOne according to user status
    let trailingOne = '';
    if (emiData.isDelay) {
      if (emiData.totalDelayEmi > 1) trailingOne = `Total Delay EMIs: `;
      else trailingOne = `Total Delay EMI: `;
      trailingOne += emiData.totalDelayEmi;
    } else {
      trailingOne = emiData.upcomingDate
        ? this.dateService.dateToReadableFormat(
            emiData.upcomingDate,
            'DD/MM/YYYY',
          ).readableStr
        : '';
    }

    ///prepare value according to user status
    let value;
    if (emiData.isDelay) {
      value =
        '₹ ' + this.typeService.amountNumberWithCommas(emiData.totalDueAmount);
    } else {
      value =
        emiData.upcomingEmiNumber +
        ' (₹ ' +
        this.typeService.amountNumberWithCommas(emiData.upcomingEmiAmount) +
        ')';
    }

    ///Due amount
    response.push({
      iconURL: emiData.isDelay
        ? AGENT_APP_CALL_ICONS.DELAY_EMI
        : AGENT_APP_CALL_ICONS.UPCOMING_EMI,
      header:
        emiData.totalDelayEmi > 0 ? 'Total Remaining Amount' : 'Upcoming EMI',
      headerColor: '#A4A4A4',
      value,
      valueColor: !emiData.isDelay ? '#38383E' : '#FF0000',
      trailingOne,
      trailingOneColor: '#4D4D4D',
      trailingTwo:
        emiData.delayDays && emiData.totalDelayEmi > 0
          ? '(Delay days: ' + emiData.delayDays + ')'
          : '',
      trailingTwoColor: emiData.delayDays ? '#FF0000' : '#38383E',
    });
  }
  //#endregion

  //#region prepareUnpaidAmount
  private prepareUnpaidAmount(emiDetails) {
    try {
      const emiData = {
        principalCovered: emiDetails?.principalCovered
          ? this.typeService.manageAmount(emiDetails?.principalCovered)
          : 0,
        interestCalculate: emiDetails.interestCalculate
          ? this.typeService.manageAmount(emiDetails?.interestCalculate)
          : 0,
        paid_principal: emiDetails?.paid_principal
          ? this.typeService.manageAmount(emiDetails?.paid_principal)
          : 0,
        paid_interest: emiDetails?.paid_interest
          ? this.typeService.manageAmount(emiDetails?.paid_interest)
          : 0,
        regInterestAmount: emiDetails?.regInterestAmount
          ? this.typeService.manageAmount(emiDetails?.regInterestAmount)
          : 0,
        paidRegInterestAmount: emiDetails?.paidRegInterestAmount
          ? this.typeService.manageAmount(emiDetails?.paidRegInterestAmount)
          : 0,
        bounceCharge: emiDetails?.bounceCharge
          ? this.typeService.manageAmount(emiDetails?.bounceCharge)
          : 0,
        gstOnBounceCharge: emiDetails?.gstOnBounceCharge
          ? this.typeService.manageAmount(emiDetails?.gstOnBounceCharge)
          : 0,
        paidBounceCharge: emiDetails?.paidBounceCharge
          ? this.typeService.manageAmount(emiDetails?.paidBounceCharge)
          : 0,
        dpdAmount: emiDetails?.dpdAmount
          ? this.typeService.manageAmount(emiDetails?.dpdAmount)
          : 0,
        penaltyChargesGST: emiDetails?.penaltyChargesGST
          ? this.typeService.manageAmount(emiDetails?.penaltyChargesGST)
          : 0,
        paidPenalCharge: emiDetails?.paidPenalCharge
          ? this.typeService.manageAmount(emiDetails?.paidPenalCharge)
          : 0,
        penalty: emiDetails?.penalty
          ? this.typeService.manageAmount(emiDetails?.penalty)
          : 0,
        legalCharge: emiDetails?.legalCharge
          ? this.typeService.manageAmount(emiDetails?.legalCharge)
          : 0,
        legalChargeGST: emiDetails?.legalChargeGST
          ? this.typeService.manageAmount(emiDetails?.legalChargeGST)
          : 0,
        paidLegalCharge: emiDetails?.paidLegalCharge
          ? this.typeService.manageAmount(emiDetails?.paidLegalCharge)
          : 0,
      };

      let emiAmount = +(
        (emiData.principalCovered ?? 0) +
        (emiData.interestCalculate ?? 0) -
        (emiData.paid_principal ?? 0) -
        (emiData.paid_interest ?? 0)
      ).toFixed(2);
      let deferredInterest = +(
        (emiData.regInterestAmount ?? 0) - (emiData.paidRegInterestAmount ?? 0)
      ).toFixed(2);
      let bounceCharge = +(
        (emiData.bounceCharge ?? 0) +
        (emiData.gstOnBounceCharge ?? 0) -
        (emiData.paidBounceCharge ?? 0)
      ).toFixed(2);
      let penalCharge = +(
        (emiData.dpdAmount ?? 0) +
        (emiData.penaltyChargesGST ?? 0) -
        (emiData.paidPenalCharge ?? 0)
      ).toFixed(2);
      let penalty = +(emiData.penalty ?? 0).toFixed(2);
      let legalCharge = +(
        (emiData.legalCharge ?? 0) +
        (emiData.legalChargeGST ?? 0) -
        (emiData.paidLegalCharge ?? 0)
      ).toFixed(2);

      return (
        emiAmount +
        penalty +
        penalCharge +
        legalCharge +
        bounceCharge +
        deferredInterest
      );
    } catch (error) {
      return 0;
    }
  }
  //#endregion   --------------------------------------------------------------
}
