import { Body, Controller, Get, Res } from '@nestjs/common';
import { truShieldService } from './TruShield.service';
import { kInternalError, kSuccessData } from 'src/constants/responses';

@Controller('thirdParty/truShield')
export class truShielController {
  constructor(private readonly service: truShieldService) {}
  @Get('getGender')
  async getGender(@Body() body, @Res() res) {
    try {
      const name = body.name;
      const data: any = await this.service.verifyGender(name);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      console.log('Error in', error.message);
      return res.send(kInternalError);
    }
  }
}
