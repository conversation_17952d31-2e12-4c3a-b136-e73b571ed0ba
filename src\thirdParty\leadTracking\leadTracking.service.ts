import { forwardRef, Inject, Injectable, OnModuleInit } from '@nestjs/common';
import { getClientIp } from '@supercharge/request-ip/dist';
import * as fs from 'fs';
import { literal, Op } from 'sequelize';
import { ReportService } from 'src/admin/report/report.service';
import { EnvConfig } from 'src/configs/env.config';
import { BLOCK_USER, PAGE, PAGE_LIMIT } from 'src/constants/globals';
import {
  acceptedLeadModels,
  funGetLeadTableSeries,
  JOB_TYPE_NUM,
  kAlternateSelfEmployed,
  kCibilScoreInvalid,
  kCibilScoreNotExist,
  kDisplayUserStatus,
  kDOBInvalid,
  kDOBNotExist,
  // kDuplicatePAN,
  kDuplicatePhone,
  kEmailInvalid,
  kEmailNotExist,
  kFullNameInvalid,
  kFullNameNotExist,
  kGenderNotExist,
  kGenderObj,
  kJobTypeInvalid,
  kJobTypeNotExist,
  kJobTypeObj,
  // kLeadPanExists,
  kLeadPhoneExists,
  kLeadStatus,
  kLeadStatusFilterObj,
  kLeadStatusObj,
  kMaxAge,
  kMaxCibilScore,
  kMinAge,
  kMinCibilScore,
  kPANInvalid,
  kPANNotExist,
  kPhoneInvalid,
  kPhoneNotExist,
  kPincodeInvalid,
  kPincodeNotServiceable,
  kSalaryInvalid,
  kSalaryMinCriteria,
  kSalaryNotExist,
  kSelfEmployed,
  // kStatesNotServiceable,
  // kUserPanExists,
  kUserPhoneExists,
  kUserStatus,
  kUserStatusObj,
  LEAD_DATE_RANGE_LIMIT_DAYS,
  LEAD_EXPIRE_DAYS,
  LEAD_SOURCE,
  MAX_ACCEPTABLE_CIBIL,
  MIN_ACCEPTABLE_CIBIL,
  MIN_ACCEPTABLE_SALARY,
  REJECTED_STATES,
  rejectedLeadModels,
} from 'src/constants/leadTracking';
import { k500Error } from 'src/constants/misc';
import { CLOUD_FOLDER_PATH } from 'src/constants/objects';
import {
  k400ErrorMessage,
  kBadRequest,
  kInternalError,
  kInvalidParamValue,
  kNoDataFound,
  kParamMissing,
  kParamsMissing,
} from 'src/constants/responses';
import {
  checkValidString,
  regEmail,
  regPanCard,
  validateISODate,
} from 'src/constants/validation';
import { APILogger } from 'src/entities/api_logger.schema';
import { HashPhoneEntity } from 'src/entities/hashPhone.entity';
import { KYCEntity } from 'src/entities/kyc.entity';
import { LeadTrackingEntity } from 'src/entities/leadTracking.entity';
import { LeadTrackingIp } from 'src/entities/leadTrackingIp.entities';
import { loanTransaction } from 'src/entities/loan.entity';
import { MasterEntity } from 'src/entities/master.entity';
import { LeadTracking } from 'src/entities/schemas/leadTracking.schema';
import { registeredUsers } from 'src/entities/user.entity';
import { LeadTrackingRepository } from 'src/repositories/leadTracking.repository';
import { LeadTrackingFileRepository } from 'src/repositories/leadTrackingFile.repository';
import { ReportHistoryRepository } from 'src/repositories/reportHistory.repository';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { UserRepository } from 'src/repositories/user.repository';
import { CryptService } from 'src/utils/crypt.service';
import { DateService } from 'src/utils/date.service';
import { FileService } from 'src/utils/file.service';
import { StringService } from 'src/utils/string.service';
import { TypeService } from 'src/utils/type.service';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class LeadTrackingServices implements OnModuleInit {
  pincodeList: any = [];
  constructor(
    private repository: LeadTrackingFileRepository,
    private cryptServices: CryptService,
    private fileServices: FileService,
    private userRepo: UserRepository,
    private leadTrackingRepo: LeadTrackingRepository,
    private dateServices: DateService,
    private readonly repoManager: RepositoryManager,
    private readonly typeService: TypeService,
    private readonly stringService: StringService,
    @Inject(forwardRef(() => ReportService))
    private readonly reportService: ReportService,
    private readonly reportHistoryRepo: ReportHistoryRepository,
  ) {}

  onModuleInit() {
    this.managePincodeList();
  }

  async createData(reqData, file, leadSource, customCreatedAt = null) {
    try {
      let finalLeadArr = [];
      let storedFile;
      if (reqData?.lead_source != null) leadSource = +reqData.lead_source;

      // Check for excel file upload
      if (file) {
        const adminId = +reqData.adminId;
        const filename = file.filename;
        // Excel to JSON
        let readDataFromFile: any = await this.fileServices.excelToArray(
          file.filename,
        );
        if (readDataFromFile?.message) return readDataFromFile;

        //Remove empty objects from read file data if any
        readDataFromFile = readDataFromFile.filter(
          (data) => Object.keys(data).length !== 0,
        );
        finalLeadArr = readDataFromFile;

        // Upload file in Cloud and store log in DB
        storedFile = await this.uploadFileAndStoreInDB(filename, adminId);
        if (storedFile?.message) return storedFile;
      } else {
        if (!reqData || !Array.isArray(reqData)) return kBadRequest;
        finalLeadArr = reqData;
      }

      const validationRes: any = await this.validateFields(
        finalLeadArr,
        file ? true : false,
        storedFile,
        leadSource,
        customCreatedAt,
      );
      if (validationRes?.message) return validationRes;

      const storeRes: any = await this.storeLeadsInDb(validationRes.storeLeads);
      if (storeRes?.message) return storeRes;

      return validationRes.responseLeads;
    } catch (error) {
      console.log(error);
      return kInternalError;
    }
  }

  private async validateFields(
    finalLeadArr,
    isFileType,
    storedFile,
    leadSource,
    customCreatedAt,
  ) {
    let acceptedLeads = [];
    const rejectedLeads = [];

    for (let i = 0; i < finalLeadArr.length; i++) {
      let lead = finalLeadArr[i];
      if (isFileType) lead = this.convertLeadKeys(lead);

      const leadRejectReasons = [];
      //#region Check null each field

      // Full Name
      if (!lead?.fullName) leadRejectReasons.push(kFullNameNotExist);
      else if (
        typeof lead?.fullName !== 'string' ||
        !checkValidString(lead?.fullName?.trim())
      )
        leadRejectReasons.push(kFullNameInvalid);

      // Phone
      if (!lead?.phone) leadRejectReasons.push(kPhoneNotExist);
      else {
        if (lead.phone) lead.phone = lead.phone.toString();
        if (isNaN(lead.phone) || lead.phone?.length != 10)
          leadRejectReasons.push(kPhoneInvalid);
        const tempHashPhone = this.cryptServices.getMD5Hash(lead.phone);
        if (acceptedLeads.find((i) => i.hashPhone === tempHashPhone))
          leadRejectReasons.push(kDuplicatePhone);
      }

      // Check Email
      if (lead?.email && !regEmail(lead?.email))
        leadRejectReasons.push(kEmailInvalid);

      // PAN Number
      if (!lead?.pan) leadRejectReasons.push(kPANNotExist);
      else {
        if (!regPanCard(lead.pan)) leadRejectReasons.push(kPANInvalid);
      }

      // Check DOB
      if (lead?.dob) {
        if (!validateISODate(lead.dob)) {
          lead.dob = null;
          leadRejectReasons.push(kDOBInvalid);
        } else {
          lead.dob = new Date(lead.dob).toJSON().substring(0, 10);
          const derivedAge = this.typeService.getAgeFromDOB(lead.dob);
          lead.age = derivedAge;
          if (derivedAge < 21) leadRejectReasons.push(kMinAge);
          else if (derivedAge > 50) leadRejectReasons.push(kMaxAge);
        }
      }

      // check job type
      if (lead?.jobType) {
        const lowerJobType = lead.jobType?.toLowerCase();
        if (lowerJobType === kAlternateSelfEmployed.toLowerCase())
          lead.jobType = kSelfEmployed.toLowerCase();
        lead.jobType = JOB_TYPE_NUM[lead.jobType?.toLowerCase()];
        if (lead.jobType == null) leadRejectReasons.push(kJobTypeInvalid);
      }

      // Salary
      if (!lead?.salary) {
        lead.salary = -1;
        leadRejectReasons.push(kSalaryNotExist);
      } else {
        if (isNaN(lead.salary)) {
          leadRejectReasons.push(kSalaryInvalid);
          lead.salary = -1;
        } else {
          lead.salary = this.typeService.formatInteger(lead.salary);
          if (lead.salary < MIN_ACCEPTABLE_SALARY)
            leadRejectReasons.push(kSalaryMinCriteria);
        }
      }

      // CibilScore
      if (!lead?.cibilScore) {
        lead.cibilScore = -1;
        leadRejectReasons.push(kCibilScoreNotExist);
      } else {
        if (isNaN(lead.cibilScore)) {
          leadRejectReasons.push(kCibilScoreInvalid);
          lead.cibilScore = -1;
        } else {
          lead.cibilScore = this.typeService.formatInteger(lead.cibilScore);
          if (lead.cibilScore < MIN_ACCEPTABLE_CIBIL)
            leadRejectReasons.push(kMinCibilScore);
          else if (lead.cibilScore > MAX_ACCEPTABLE_CIBIL)
            leadRejectReasons.push(kMaxCibilScore);
        }
      }

      // Pincode check
      if (lead?.pincode) {
        if (isNaN(lead.pincode)) {
          lead.pincode = null;
          leadRejectReasons.push(kPincodeInvalid);
        } else lead.pincode = this.typeService.formatInteger(lead.pincode);
      }

      //#endregion
      /// FintiFi (lead source = 1)
      if (leadSource == 1) {
        // Email
        if (!lead?.email) leadRejectReasons.push(kEmailNotExist);

        // DOB
        if (!lead?.dob) leadRejectReasons.push(kDOBNotExist);

        // Gender
        if (!lead?.gender) leadRejectReasons.push(kGenderNotExist);

        // Job type
        if (lead?.jobType == null) leadRejectReasons.push(kJobTypeNotExist);
      }

      /// Encrypt lead's details
      const leadFileId = storedFile?.id;
      const phone = lead?.phone
        ? this.cryptServices.encryptPhone(lead.phone)
        : lead?.phone;
      const hashPhone = this.cryptServices.getMD5Hash(lead.phone?.toString());
      const pan = await this.cryptServices.encryptText(lead.pan);
      const hashPAN = this.cryptServices.getMD5Hash(lead.pan);
      let cibilScore = lead?.cibilScore ?? null;
      cibilScore =
        !isNaN(cibilScore) && +cibilScore >= -32768 && +cibilScore <= 32767
          ? Math.trunc(lead.cibilScore)
          : null;
      const leadTableSeries = !isNaN(lead?.phone)
        ? funGetLeadTableSeries(lead.phone)
        : 0;
      const gender =
        lead?.gender == 'Male' ? 0 : lead?.gender == 'Female' ? 1 : null;

      const id = uuidv4();

      let state = lead?.pincode
        ? this.funGetStateFromPincode(lead.pincode)
        : null;

      if (leadRejectReasons.length == 0) {
        const tempLead: any = {
          ...lead,
          id,
          leadStatus: kLeadStatus.Accepted,
          leadFileId,
          phone,
          hashPhone,
          pan,
          hashPAN,
          cibilScore,
          gender,
          leadSource,
          state,
          leadTableSeries,
        };
        if (customCreatedAt) tempLead.createdAt = customCreatedAt;
        acceptedLeads.push(tempLead);
      } else {
        const tempLead: any = {
          ...lead,
          id,
          rejectReasons: leadRejectReasons,
          leadStatus: kLeadStatus.Rejected,
          leadFileId,
          phone,
          hashPhone,
          pan,
          hashPAN,
          cibilScore,
          gender,
          leadSource,
          state,
          leadTableSeries,
        };
        if (customCreatedAt) tempLead.createdAt = customCreatedAt;
        rejectedLeads.push(tempLead);
      }
    }

    // Check if Pincode valid or not
    const mergedLeads = [...acceptedLeads, ...rejectedLeads];
    const allPins = mergedLeads.map((item, i) => {
      return { pincode: item.pincode, idx: i };
    });

    const acceptEndIdx = acceptedLeads.length;
    const invalidPinIdxA = [];
    const invalidPinIdxR = [];
    /// Validate pincode if it belongs from 7 States
    const invalidPinIdx: any[] = this.validatePincode(allPins);
    if (invalidPinIdx?.length > 0) {
      invalidPinIdx.forEach((item) => {
        if (item < acceptEndIdx) invalidPinIdxA.push(item);
        else invalidPinIdxR.push(item - acceptEndIdx);
      });
    }

    if (invalidPinIdxA?.length > 0) {
      for (let i = 0; i < invalidPinIdxA.length; i++) {
        const idx = invalidPinIdxA[i];
        const tempArr: any[] = [kPincodeNotServiceable];
        const tempAccObj = {
          ...acceptedLeads[idx],
          leadStatus: kLeadStatus.Rejected,
          rejectReasons: tempArr,
        };
        rejectedLeads.push(tempAccObj);
      }
      acceptedLeads = acceptedLeads.filter(
        (_, index) => !invalidPinIdxA.includes(index),
      );
    }

    if (invalidPinIdxR?.length > 0) {
      for (let i = 0; i < invalidPinIdxR.length; i++) {
        const idx = invalidPinIdxR[i];
        if (!rejectedLeads[idx].rejectReasons.includes(kPincodeNotServiceable))
          rejectedLeads[idx].rejectReasons.push(kPincodeNotServiceable);
      }
    }

    /// Check if lead already exists in lead table or not
    const existingIdx: any = await this.getExistingLeadsInDb(
      acceptedLeads,
      leadSource,
    );
    if (existingIdx?.message) return kInternalError;

    if (existingIdx.length > 0) {
      // Remove existing from accepted and add into rejected
      for (let i = 0; i < existingIdx.length; i++) {
        const idx = existingIdx[i];
        const tempArr: any[] = [kLeadPhoneExists];
        const tempAccObj = {
          ...acceptedLeads[idx],
          leadStatus: kLeadStatus.Rejected,
          rejectReasons: tempArr,
        };
        rejectedLeads.push(tempAccObj);
      }
      acceptedLeads = acceptedLeads.filter(
        (_, index) => !existingIdx.includes(index),
      );
    }

    /// Check if lead already exists in the Registered User table through hashPhone (for accepted)
    const acceptedHashedPhoneArr = acceptedLeads.map((item) => item.hashPhone);
    const existingAccKycIdx = await this.funCheckForExistingUsers(
      acceptedHashedPhoneArr,
    );
    if (existingAccKycIdx?.length > 0) {
      for (let i = 0; i < existingAccKycIdx.length; i++) {
        const idx = existingAccKycIdx[i];
        const tempArr: any[] = [kUserPhoneExists];
        const tempAccObj = {
          ...acceptedLeads[idx],
          leadStatus: kLeadStatus.Existing,
          rejectReasons: tempArr,
        };
        rejectedLeads.push(tempAccObj);
      }
      acceptedLeads = acceptedLeads.filter(
        (_, index) => !existingAccKycIdx.includes(index),
      );
    }

    // For Rejected add more reason
    const rejectedHashedPhoneArr = rejectedLeads.map((item) => item.hashPhone);
    const existingRejectedKycIdx = await this.funCheckForExistingUsers(
      rejectedHashedPhoneArr,
    );
    if (existingRejectedKycIdx?.length > 0) {
      for (let i = 0; i < existingRejectedKycIdx.length; i++) {
        const idx = existingRejectedKycIdx[i];
        if (!rejectedLeads[idx].rejectReasons.includes(kUserPhoneExists))
          rejectedLeads[idx].rejectReasons.push(kUserPhoneExists);
      }
    }

    let storeLeads = [...acceptedLeads, ...rejectedLeads];
    let responseLeads = {
      acceptedLeads: JSON.parse(JSON.stringify(acceptedLeads)),
      rejectedLeads: JSON.parse(JSON.stringify(rejectedLeads)),
    };
    responseLeads = await this.mapResponseArr(responseLeads);
    storeLeads = storeLeads.map((item) => {
      const tempObj = {
        ...item,
      };
      if (item?.rejectReasons)
        tempObj.rejectReasons = JSON.stringify(item.rejectReasons);
      return tempObj;
    });
    return { storeLeads, responseLeads };
  }

  private async mapResponseArr(responseLeads) {
    const { acceptedLeads, rejectedLeads } = responseLeads;
    for (let i = 0; i < acceptedLeads.length; i++) {
      const el = acceptedLeads[i];
      acceptedLeads[i] = await this.mapResponseItem(el);
    }
    for (let i = 0; i < rejectedLeads.length; i++) {
      const el = rejectedLeads[i];
      rejectedLeads[i] = await this.mapResponseItem(el);
    }
    return { acceptedLeads, rejectedLeads };
  }
  private async mapResponseItem(item) {
    item.phone =
      this.cryptServices.decryptPhone(item.phone) == k500Error
        ? '-'
        : this.cryptServices.decryptPhone(item.phone);
    item.pan = await this.cryptServices.decryptText(item.pan);
    item.leadStatus = kLeadStatusObj[item?.leadStatus] ?? '-';
    item.leadSource = LEAD_SOURCE[item?.leadSource] ?? '-';
    item.jobType = kJobTypeObj[item?.jobType] ?? '-';
    const gender =
      item?.gender == 0 ? 'Male' : item?.gender == 1 ? 'Female' : null;
    item.gender = gender;
    delete item.hashPhone;
    delete item.hashPAN;
    delete item.leadFileId;
    delete item.leadTableSeries;
    return item;
  }

  private convertLeadKeys(lead) {
    const finalObj: any = {};
    if (lead['Full Name']) finalObj.fullName = lead['Full Name'];
    if (lead['Phone']) finalObj.phone = lead['Phone']?.toString();
    if (lead['Email']) finalObj.email = lead['Email'];
    if (lead['PAN Number']) finalObj.pan = lead['PAN Number'];
    if (lead['DOB']) finalObj.dob = lead['DOB'];
    if (lead['Gender']) finalObj.gender = lead['Gender'];
    if (lead['Job Type']) finalObj.jobType = lead['Job Type'];
    if (lead['CIBIL Score']) finalObj.cibilScore = lead['CIBIL Score'];
    if (lead['Salary']) finalObj.salary = lead['Salary'];
    if (lead['Pincode']) finalObj.pincode = lead['Pincode'];

    return Object.keys(finalObj).length == 0 ? lead : finalObj;
  }

  private async getExistingLeadsInDb(acceptedLeads, leadSource) {
    /// Prepare data before grouping by table series.
    const hashPhoneArr = [];
    const prepareDataToGroup = acceptedLeads.map((item) => {
      hashPhoneArr.push(item.hashPhone);
      const readablePhone = this.cryptServices.decryptPhone(item.phone);
      const leadTableSeries = funGetLeadTableSeries(readablePhone);
      return {
        ...item,
        readablePhone,
        leadTableSeries,
      };
    });

    /// Group prepared data by table series
    const groupBySeries = {};
    for (const element of prepareDataToGroup) {
      if (!groupBySeries[element?.leadTableSeries])
        groupBySeries[element?.leadTableSeries] = [];
      groupBySeries[element?.leadTableSeries].push(element);
    }

    /// Get leads from accepted and rejected table if exists
    const existingLeads = [];
    for (const series of Object.keys(groupBySeries)) {
      const seriesDataArr = groupBySeries[series];

      const hashPhoneArr = seriesDataArr.map((item) => item.hashPhone);
      const options = {
        where: {
          hashPhone: { [Op.in]: hashPhoneArr },
        },
      };

      /// Get existing rejected leads
      const rejectedExistingLeads = await this.repoManager.getTableWhereData(
        rejectedLeadModels[series],
        ['id', 'hashPhone'],
        options,
      );
      if (rejectedExistingLeads == k500Error) return kInternalError;
      if (rejectedExistingLeads?.length)
        existingLeads.push(...rejectedExistingLeads);

      /// Get existing accepted leads
      const acceptedExistingLeads = await this.repoManager.getTableWhereData(
        acceptedLeadModels[series],
        ['id', 'hashPhone', 'leadStatus', 'leadSource'],
        options,
      );
      if (acceptedExistingLeads == k500Error) return kInternalError;

      if (acceptedExistingLeads?.length) {
        /// If lead borrowed from the same lead source,
        /// and if database's lead's lead status is not yet expired,
        /// then reject all that borrowed leads
        const sameLeadSource = acceptedExistingLeads.filter(
          (lead) =>
            lead?.leadSource == leadSource &&
            lead?.leadStatus != kLeadStatus.Expired,
        );
        if (sameLeadSource?.length) {
          existingLeads.push(...sameLeadSource);
        }

        /// Check if lead borrowed is from different lead source
        const differentLeadSource = acceptedExistingLeads.filter(
          (lead) => lead?.leadSource != leadSource,
        );
        if (differentLeadSource?.length) {
          /// Check if database's lead's status is other then 'Expired', like 'Accepted', 'In Process', or 'Disbursed'
          /// And if database's lead's lead status is not expired, then reject all that borrowed leads.
          const nonExpiredLeads = differentLeadSource.filter(
            (lead) => lead?.leadStatus != kLeadStatus.Expired,
          );
          existingLeads.push(...nonExpiredLeads);
        }
      }
    }

    /// Remove existing leads from request body leads
    const removeIndexes = [];
    if (existingLeads?.length) {
      for (let i = 0; i < existingLeads.length; i++) {
        const lead = existingLeads[i];
        const phoneIdx = hashPhoneArr.indexOf(lead.hashPhone);
        if (phoneIdx !== -1 && !removeIndexes.includes(phoneIdx))
          removeIndexes.push(phoneIdx);
      }
    }

    return removeIndexes;
  }

  private async storeLeadsInDb(leadsData) {
    if (!leadsData || !Array.isArray(leadsData)) return kInternalError;
    const rejectedLeads = [];
    const acceptedLeads = [];

    for (const element of leadsData) {
      if ([2, 4].includes(element?.leadStatus)) rejectedLeads.push(element);
      else acceptedLeads.push(element);
    }

    if (rejectedLeads?.length) {
      const response = await this.bulkLeadCreate(rejectedLeads, 'rejected');
      if (response?.message) return response;
    }

    if (acceptedLeads?.length) {
      const response = await this.bulkLeadCreate(acceptedLeads, 'accepted');
      if (response?.message) return response;
    }

    return leadsData;
  }

  private async bulkLeadCreate(leadsData, leadStatus) {
    const groupBySeries = {};
    for (const lead of leadsData) {
      if (!groupBySeries[lead.leadTableSeries])
        groupBySeries[lead.leadTableSeries] = [];

      groupBySeries[lead.leadTableSeries].push(lead);
    }

    for (const series of Object.keys(groupBySeries)) {
      const data = groupBySeries[series];
      let leadTable;
      if (leadStatus == 'accepted') leadTable = acceptedLeadModels[series];
      if (leadStatus == 'rejected') leadTable = rejectedLeadModels[series];
      const createLeadRecords = await this.repoManager.bulkCreate(
        leadTable,
        data,
      );
      if (createLeadRecords === k500Error) return kInternalError;
    }
  }

  private async uploadFileAndStoreInDB(filename, adminId) {
    if (!filename) return kInternalError;
    const uploadFile = await this.fileServices.uploadFile(
      filename,
      CLOUD_FOLDER_PATH.leadTracking,
      filename.split('.')[1],
      null,
    );
    if (uploadFile === k500Error) return kInternalError;
    const dbPayload = {
      fileUrl: uploadFile,
      uploadedBy: adminId,
    };
    const storeData = await this.repository.createRowData(dbPayload);
    if (storeData === k500Error) return kInternalError;
    return storeData;
  }

  private async funCheckForExistingUsers(hashPhoneList = []) {
    if (hashPhoneList.length === 0) return null;

    /// Check for existing leads in Registered User Entity
    const attributes = ['hashPhone'];
    const options = {
      where: { hashPhone: hashPhoneList },
      useMaster: false,
    };
    let userResults = await this.repoManager.getTableWhereData(
      HashPhoneEntity,
      attributes,
      options,
    );
    if (userResults === k500Error) return userResults;

    let existingUserIdx = [];
    if (userResults?.length) {
      /// Extract hashed Phone from userResults
      userResults = userResults.map((item) => item.hashPhone);
      /// Match users from hashPhoneList, with userResults,
      /// If user exists in userResults, then get indexes
      // of respective users from hashPhoneList
      existingUserIdx = hashPhoneList
        .map((element, index) => (userResults.includes(element) ? index : -1))
        .filter((index) => index !== -1);
    }
    return existingUserIdx;
  }

  // When registered user gets disbursement
  async handleDisbursedUser(newUser) {
    try {
      if (!newUser?.phone || newUser?.appType == null || !newUser?.userId)
        return kInternalError;

      // Check and update lead tracking status
      const hashPhone = this.cryptServices.getMD5Hash(newUser.phone);
      const leadTableSeries = funGetLeadTableSeries(newUser.phone);
      const leadData = await this.getLeadData(
        hashPhone,
        kLeadStatus.InProcess,
        leadTableSeries,
      );
      if (leadData?.message) return leadData;
      if (!leadData?.id) return false;

      /// Check, if lead is older than 30 days, then do not update disbursement details to lead table
      const isLeadExpired = this.dateServices.funCalcDaysDiff(
        leadData?.createdAt,
        new Date(),
        LEAD_EXPIRE_DAYS,
      );
      if (isLeadExpired) return;
      const updateLeadData: any = {
        leadStatus: kLeadStatus.Disbursed,
        disbursementDate: new Date(),
        disbursement_amount: newUser?.disbAmount ?? null,
      };
      await this.repoManager.updateRowData(
        acceptedLeadModels[leadTableSeries],
        updateLeadData,
        leadData.id,
      );

      return true;
    } catch (error) {
      console.log(error);
      return kInternalError;
    }
  }

  //#region Handle lead tracking status, when user registers
  async handleNewUser(newUser) {
    try {
      if (
        // !newUser?.pan ||
        !newUser?.phone ||
        newUser?.appType == null ||
        !newUser?.userId ||
        !newUser?.userCreatedAt
      )
        return kInternalError;

      // Check and update lead tracking status
      // const hashPAN = this.cryptServices.getMD5Hash(newUser.pan);
      // const leadData = await this.getLeadData(hashPAN, 0);

      /// Check and update lead tracking status
      const hashPhone = this.cryptServices.getMD5Hash(newUser.phone);
      const leadTableSeries = funGetLeadTableSeries(newUser.phone);

      /// Get accepted lead data
      const leadData = await this.getLeadData(
        hashPhone,
        kLeadStatus.Accepted,
        leadTableSeries,
      );
      if (leadData?.message) return leadData;
      if (!leadData?.id) return false;

      /// Check if the lead came first
      const leadCreatedAt = new Date(leadData.createdAt).getTime();
      const userCreatedAt = new Date(newUser.userCreatedAt).getTime();
      const isLeadFirst = userCreatedAt > leadCreatedAt;

      const updateLeadData: any = {
        leadStatus: kLeadStatus.InProcess,
        registration_date: new Date(),
      };

      /// Reject lead if appType 0 (LSP) or user come before lead
      if (newUser.appType == 0 || !isLeadFirst) {
        updateLeadData.leadStatus = kLeadStatus.Existing;

        // const rejectReasons: any[] = [kUserPanExists];
        const rejectReasons: any[] = [kUserPhoneExists];
        updateLeadData.rejectReasons = JSON.stringify(rejectReasons);

        const transferData = { ...leadData, ...updateLeadData };
        /// Transfer lead data from accepted table to rejected table
        await this.repoManager.createRowData(
          rejectedLeadModels[leadTableSeries],
          transferData,
        );
        await this.repoManager.deleteSingleData(
          acceptedLeadModels[leadTableSeries],
          leadData?.id,
        );
      }
      // await this.leadTrackingRepo.updateRowData(updateLeadData, leadData.id);
      /// Update lead status as in-process
      await this.repoManager.updateRowData(
        acceptedLeadModels[leadTableSeries],
        updateLeadData,
        leadData.id,
      );

      /// Update Lead partner if Lead InProcess
      if (newUser.appType == 1 && leadData?.leadSource != null && isLeadFirst) {
        // Update user entity
        const userUpdateData = await this.userRepo.updateRowData(
          { leadSource: leadData.leadSource },
          newUser.userId,
        );
        if (userUpdateData == k500Error) return kInternalError;
      }
      return true;
    } catch (error) {
      console.log(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region Get lead's loan status based on phone number
  async getLeadsLoanStatus(reqBody) {
    const phone = reqBody?.phone;
    if (!phone) return kParamMissing('phone');
    if (phone?.length != 10 || isNaN(phone)) return kInvalidParamValue('phone');

    const leadSource = reqBody?.leadSource;
    const leadTableSeries = funGetLeadTableSeries(phone);
    const hashPhone = this.cryptServices.getMD5Hash(phone);

    const leadOpts = {
      where: {
        hashPhone,
        leadSource,
      },
      order: [['id', 'DESC']],
    };

    /// First check if lead is rejected?
    /// 'false' param for rejected leads
    let leadData = await this.funGetLeadData(leadOpts, false, leadTableSeries);
    if (leadData == k500Error) return kInternalError;

    let leadStatus;
    if (leadData?.count) {
      leadData = leadData?.rows?.[0];
      leadStatus = leadData?.leadStatus ?? null;

      if ([kLeadStatus.Rejected, kLeadStatus.Existing].includes(leadStatus)) {
        return {
          leadStatus: kLeadStatusObj[leadStatus] ?? '-',
          rejectReasons: leadData?.rejectReasons
            ? JSON.parse(leadData.rejectReasons ?? '[]')
            : [],
        };
      }
    }

    /// If lead is not rejected, then check if lead is accepted?
    if (!leadData?.count) {
      /// 'true' param for accepted leads
      leadData = await this.funGetLeadData(leadOpts, true, leadTableSeries);
      if (leadData == k500Error) return kInternalError;
      if (!leadData?.count) return k400ErrorMessage('No data found');

      leadData = leadData?.rows?.[0];
      leadStatus = leadData?.leadStatus ?? null;

      /// If lead is 'Expired', in that case show lead status as 'Accepted'
      if ([kLeadStatus.Accepted, kLeadStatus.Expired].includes(leadStatus)) {
        return { leadStatus: kLeadStatusObj[kLeadStatus.Accepted] ?? '-' };
      }

      if (
        ![kLeadStatus.InProcess, kLeadStatus.Disbursed].includes(leadStatus)
      ) {
        return k400ErrorMessage('No data found');
      }
    }
    /// Check if lead status is in 'In Process' (1), or 'Disbursed' (3)
    const attributes = ['id', 'fullName'];
    const options = {
      where: { hashPhone },
      include: {
        model: loanTransaction,
        attributes: ['id', 'loanAmount', 'loanStatus'],
        required: false,
      },
    };
    const getUserData = await this.repoManager.getRowWhereData(
      registeredUsers,
      attributes,
      options,
    );
    if (getUserData == k500Error) return kInternalError;
    if (!getUserData) return k400ErrorMessage('No data found');

    const loanData = getUserData?.loanData ?? [];
    getUserData.loanData = loanData?.length
      ? loanData?.map((loan) => {
          return {
            ...loan,
            loanAmount: loan?.loanAmount ?? '-',
          };
        })
      : [];
    getUserData.leadStatus = kLeadStatusObj[leadStatus] ?? '-';
    return getUserData;
  }

  async getLeadData(hashPhone: string, checkStatus: number, leadTableSeries) {
    if (!hashPhone) return kParamMissing('hashPhone');

    const getLead = await this.repoManager.getRowWhereData(
      acceptedLeadModels[leadTableSeries],
      null,
      {
        where: {
          hashPhone,
          leadStatus: checkStatus,
        },
      },
    );
    if (!getLead) return {};
    if (getLead === k500Error) return kInternalError;

    return getLead;
  }

  //#region Get lead tracking tab dashboard data
  async getDashboardData(queryParams) {
    try {
      queryParams.isDownload = (queryParams?.download ?? false) == 'true';

      /// Query parameter validations
      if (!queryParams?.isDownload) {
        if (!queryParams?.page) return kParamMissing('page');
        if (isNaN(queryParams?.page)) return kInvalidParamValue('page');
      }

      const lead_status = queryParams?.lead_status ?? null;
      if (!lead_status) return kParamMissing('lead_status');

      if (
        isNaN(+lead_status) ||
        !Object.keys(kLeadStatusFilterObj).includes(String(lead_status))
      ) {
        return kInvalidParamValue('lead_status');
      }

      /// Manages lead status as request params passed by frontend side.
      queryParams.leadStatusObj = {
        /// Lead Progress (frontend option label)
        1: [
          kLeadStatus.Accepted,
          kLeadStatus.InProcess,
          kLeadStatus.Disbursed,
          kLeadStatus.Expired,
        ], /// All(LP) (frontend option label)
        2: [kLeadStatus.Accepted], /// Accepted (frontend option label)
        3: [kLeadStatus.InProcess], /// In Process (frontend option label)
        4: [kLeadStatus.Disbursed], /// Disbursed (frontend option label)
        8: [kLeadStatus.Expired], /// Expired (frontend option label)
        /// Closed Leads (frontend option label)
        5: [kLeadStatus.Rejected, kLeadStatus.Existing], /// All(CL) (frontend option label)
        6: [kLeadStatus.Rejected], /// Rejected (frontend option label)
        7: [kLeadStatus.Existing], /// Existing (frontend option label)
      };

      /// Check for lead status: All (LP), Accepted, In Process, and Disbursed
      if ([1, 2, 3, 4, 8].includes(+queryParams.lead_status))
        queryParams.isAccept = true;

      /// Check for lead status: All (CL), Rejected, and Existing
      if ([5, 6, 7].includes(+queryParams.lead_status))
        queryParams.isAccept = false;

      /// Apply date range
      const defaultDate = this.dateServices.getGlobalDate(new Date()).toJSON();
      const startDate = queryParams?.startDate ?? defaultDate;
      const endDate = queryParams?.endDate ?? defaultDate;
      queryParams.dateRange = this.dateServices.utcDateRange(
        startDate,
        endDate,
      );

      if (queryParams.isDownload && !queryParams.reportCronDownloadReq)
        return await this.funCreateDownloadReq(queryParams, defaultDate);

      let data: any = await this.funGetAllLeads(queryParams);
      if (data == k500Error) return kInternalError;

      if (data?.count)
        data = await this.funPrepareLeadResponse(queryParams, data);

      if (queryParams.isDownload && queryParams.reportCronDownloadReq)
        return await this.funReportCronDownloadReq(queryParams, data);

      return data?.count ? data : { count: 0, rows: [] };
    } catch (error) {
      return kInternalError;
    }
  }
  //#endregion Get Dashboard data

  //#region Get all leads data
  private async funGetAllLeads(queryParams) {
    const options: any = {
      where: {
        createdAt: {
          [Op.and]: {
            [Op.gte]: queryParams?.dateRange?.minRange,
            [Op.lt]: queryParams?.dateRange?.maxRange,
          },
        },
      },
      order: [['createdAt', 'DESC']],
      offset: (+queryParams.page - 1) * PAGE_LIMIT,
      limit: PAGE_LIMIT,
    };

    if (+queryParams.lead_status)
      options.where.leadStatus =
        queryParams?.leadStatusObj[+queryParams.lead_status] || [];

    if (+queryParams?.lead_source && !isNaN(+queryParams?.lead_source))
      options.where.leadSource = +queryParams.lead_source;

    /// Prepare search based on full phone and full PAN
    const searchText = queryParams?.searchText?.toUpperCase()?.trim();
    if (searchText?.length == 10) {
      const hashText = this.cryptServices.getMD5Hash(searchText);
      /// Search phone
      if (!isNaN(searchText)) {
        const searchSeries = funGetLeadTableSeries(searchText);
        options.where.hashPhone = hashText;
        return await this.funGetLeadData(
          options,
          queryParams.isAccept,
          searchSeries,
        );
      }
      /// Search PAN
      else if (regPanCard(searchText)) options.where.hashPAN = hashText;
    } else if (queryParams?.searchText) return [];

    /// Get data from 0 table series (for both accepted or rejected leads)
    if (!queryParams?.isDownload && !queryParams?.searchText)
      return await this.funGetLeadData(options, queryParams.isAccept, 0);
    /// Get data for search and download purpose
    else return await this.funGetBulkData(options, queryParams);
  }
  //#endregion

  //#region Get data for search and download purpose
  private async funGetBulkData(options, queryParams) {
    if (queryParams?.isDownload) {
      delete options.offset;
      delete options.limit;
    }

    /// Get data from all 10 table partitions, (both accepted and rejected)
    const leadPromises = [];
    for (let i = 0; i < 10; i++)
      leadPromises.push(this.funGetLeadData(options, queryParams?.isAccept, i));
    let rows = await Promise.all(leadPromises);

    /// Manage data for pagination
    let count = 0;
    rows = rows.map((item) => {
      count = count + item?.count;
      return item?.rows;
    });
    rows = rows.flat();
    return { count, rows };
  }
  //#endregion

  //#region Get Lead data from accepted and rejected partitions
  private async funGetLeadData(options, isAccept, leadTableSeries) {
    let leadTable;
    if (isAccept) leadTable = acceptedLeadModels[leadTableSeries];
    if (!isAccept) leadTable = rejectedLeadModels[leadTableSeries];
    return await this.repoManager.getTableCountWhereData(
      leadTable,
      null,
      options,
    );
  }
  //#endregion

  //#region Prepare response for lead tracking tab dashboard
  private async funPrepareLeadResponse(queryParams, data) {
    let userStatusData;
    if ([1, 3].includes(+queryParams?.lead_status)) {
      const inProcessLeads = data?.rows?.filter(
        (lead) => lead?.leadStatus == kLeadStatus.InProcess,
      );
      if (inProcessLeads?.length) {
        userStatusData = await this.funGetProcessedLeadsData(inProcessLeads);
      }
    }

    data.rows = await Promise.all(
      data?.rows?.map(async (lead) => {
        const formattedDate = lead?.dob
          ? this.dateServices.dateToReadableFormat(lead?.dob, 'DD/MM/YYYY')
          : lead?.dob;
        const formattedDisbursedDate = lead?.disbursementDate
          ? this.dateServices.dateToReadableFormat(
              lead?.disbursementDate,
              'DD/MM/YYYY',
            )
          : lead?.disbursementDate;
        /// Set reject reason as single string in API response.
        let rejectionReason = '';
        if (lead?.rejectReasons) {
          const rejectReasons = JSON.parse(lead?.rejectReasons);
          rejectionReason = rejectReasons.join(',');
        }

        let leadStatus = lead?.leadStatus ?? '-';
        if (queryParams?.isDownload) {
          leadStatus = kLeadStatusObj[lead?.leadStatus] ?? '-';
        }

        const response: any = {
          'Full Name': lead?.fullName ?? '-',
          Email: lead?.email ?? '-',
          'Phone Number':
            this.cryptServices.decryptPhone(lead?.phone) === k500Error
              ? '-'
              : this.cryptServices.decryptPhone(lead.phone),
          'Job Type': kJobTypeObj[lead?.jobType] ?? '-',
          'CIBIL Score': lead?.cibilScore ?? '-',
          Pincode: lead?.pincode ?? '-',
          'Lead Status': leadStatus ?? '-',
          'Lead Source': LEAD_SOURCE[lead?.leadSource] ?? '-',
          Salary: lead?.salary ?? '-',
          Age: lead?.age ?? '-',
          DOB: formattedDate ? formattedDate?.readableStr : '-',
          Gender: kGenderObj[lead?.gender] ?? '-',
          State: lead?.state ?? '-',
          'Created At': lead?.createdAt
            ? this.dateServices.readableDate(lead.createdAt)
            : '-',
        };

        const PAN = lead?.pan
          ? await this.cryptServices.decryptText(lead.pan)
          : '-';

        /// If not download, then include PAN field in response
        if (!queryParams.isDownload) response.PAN = PAN;
        /// If download is true and lead status is 'Disbursed',
        /// then include PAN field in report
        else if (queryParams.isDownload && +queryParams.lead_status == 4)
          response.PAN = PAN;

        /// Show all data related to lead status 'Accepted'
        if (queryParams.isAccept) {
          response['Disbursed Date'] = formattedDisbursedDate
            ? formattedDisbursedDate?.readableStr
            : '-';
          response['Disbursement Amount'] = lead?.disbursement_amount
            ? this.stringService.readableAmount(lead.disbursement_amount)
            : '-';

          response['User Status'] =
            kUserStatusObj?.[userStatusData?.[lead?.hashPhone]?.status] ??
            kDisplayUserStatus?.[lead?.leadStatus] ??
            '-';
          response['Registration Date'] = lead?.registration_date
            ? this.dateServices.readableDate(lead.registration_date)
            : '-';
          response['Decline Reason'] =
            userStatusData?.[lead?.hashPhone]?.reason ?? '-';
        }

        /// Show all data related to lead status 'Rejected'
        if (!queryParams.isAccept)
          response['Rejection Reasons'] = rejectionReason ?? '-';

        return response;
      }),
    );
    return data;
  }
  //#endregion

  private async funGetProcessedLeadsData(leads) {
    /// Prepare an object of user_phone = { lead's user status, loan reject reason (admin/user) }
    const userStatusData = {};

    /// A common object if no data available
    const commonObj = { status: '-', reason: '-' };

    const hashPhoneArr = [];
    /// Loop over lead's data and prepare it for response
    for (let index = 0; index < leads?.length; index++) {
      const lead = leads[index];

      /// Collect hashPhones to fetch user data
      hashPhoneArr.push(lead?.hashPhone);

      /// Set all lead's user status as 'In Process' if lead's user status is not 'Cool Off', 'Blacklist', or 'Rejected'.
      userStatusData[lead?.hashPhone] = {
        ...commonObj,
        status: kUserStatus.InProcess,
      };
    }

    /// Get user data to check, if user is blacklisted
    const userData = await this.repoManager.getTableWhereData(
      registeredUsers,
      ['hashPhone', 'isBlacklist', 'masterId', 'lastLoanId', 'otp'],
      {
        where: {
          hashPhone: hashPhoneArr,
        },
      },
    );
    if (userData == k500Error) return kInternalError;

    const coolOffs = [];
    const rejectedLoans = [];
    /// Loop over user data
    for (let index = 0; index < userData?.length; index++) {
      const user = userData[index];

      /// Check, if user just entered phone number to start registration
      if (!user?.otp) {
        userStatusData[user?.hashPhone] = {
          ...commonObj,
          status: kUserStatus.Registered,
        };
      }
      /// Check, if user is blocked/blacklisted
      if (user?.isBlacklist == BLOCK_USER.BLOCK) {
        userStatusData[user?.hashPhone] = {
          ...commonObj,
          status: kUserStatus.Blacklist,
        };
      }
      /// If user is not blocked/blacklisted, then collect that relevant data to search for cool off and rejected loan
      else if (user?.isBlacklist == BLOCK_USER.ACTIVE) {
        coolOffs.push(user?.masterId);
        if (user?.lastLoanId) rejectedLoans.push(user?.lastLoanId);
      }
    }

    /// Check for cool off users
    let isCoolOffUser = false;
    const masterData = await this.repoManager.getTableWhereData(
      MasterEntity,
      ['id', 'coolOffData'],
      { where: { id: coolOffs } },
    );
    if (masterData == k500Error) return kInternalError;

    /// Loop over master data to check for cool off users and others
    for (let index = 0; index < masterData?.length; index++) {
      const coolOffUser = masterData[index];

      let coolOffEndsOn = coolOffUser?.coolOffData?.coolOffEndsOn;
      if (coolOffUser?.id && coolOffEndsOn) {
        const todayDate = this.typeService.getGlobalDate(new Date());
        coolOffEndsOn = this.typeService.getGlobalDate(coolOffEndsOn);
        isCoolOffUser = coolOffEndsOn > todayDate;
        if (isCoolOffUser) {
          const coolOffUsers = userData.find(
            (user) => user?.masterId == coolOffUser?.id,
          );
          userStatusData[coolOffUsers?.hashPhone] = {
            ...commonObj,
            status: kUserStatus.CoolOff,
          };
        }
      }
    }

    /// Check if lead's loan is 'Rejected'
    const loanData = await this.repoManager.getTableWhereData(
      loanTransaction,
      ['hashPhone', 'loanStatus', 'remark', 'loanRejectReason'],
      {
        where: {
          loanStatus: 'Rejected',
          id: rejectedLoans,
        },
      },
    );
    if (loanData == k500Error) return kInternalError;

    /// Loop over loan data to prepare lead's loan 'Rejected' reason
    for (let index = 0; index < loanData?.length; index++) {
      const rejectedLoan = loanData[index];
      const reasons = [];
      if (rejectedLoan?.remark) reasons.push(rejectedLoan?.remark);
      if (rejectedLoan?.loanRejectReason)
        reasons.push(rejectedLoan?.loanRejectReason);

      userStatusData[rejectedLoan?.hashPhone] = {
        status: kUserStatus.Rejected,
        reason: reasons.join(' / ') ?? '-',
      };
    }

    return userStatusData;
  }

  //#region Create Report download request
  private async funCreateDownloadReq(queryParams, defaultDate) {
    /// Query parameter validations
    if (!queryParams?.adminId) return kParamMissing('adminId');
    if (isNaN(queryParams?.adminId)) return kInvalidParamValue('adminId');

    queryParams.reportName = 'Lead tracking report';
    queryParams.apiUrl = 'report/leadReport';
    queryParams.fromDate = queryParams?.startDate ?? defaultDate;
    queryParams.toDate = queryParams?.endDate ?? defaultDate;

    queryParams.extraparms = {};
    if (!isNaN(queryParams?.lead_status))
      queryParams.extraparms.lead_status = queryParams?.lead_status;
    if (!isNaN(queryParams?.lead_source))
      queryParams.extraparms.lead_source = queryParams?.lead_source;
    queryParams.extraparms.reportCronDownloadReq = true;
    queryParams.extraparms.type =
      kLeadStatusFilterObj[+queryParams.lead_status] ?? '-';

    return await this.reportService.createReportHistory(queryParams);
  }
  //#endregion

  //#region Report CRON Download Request
  private async funReportCronDownloadReq(queryParams, data) {
    /// Prepare excel sheet name
    const leadStatus = kLeadStatusFilterObj[queryParams.lead_status] ?? '';
    const sheetName = leadStatus
      ? `Lead Tracking ${leadStatus}.xlsx`
      : 'Lead Tracking.xlsx';

    const rawExcelData = {
      sheets: ['local-reports'],
      data: [data?.rows],
      sheetName,
      needFindTuneKey: false,
      reportStore: true,
      startDate: queryParams?.startDate,
      endDate: queryParams?.endDate,
    };
    const url: any = await this.fileServices.objectToExcelURL(rawExcelData);
    if (url?.message) return url;

    const updatedData = { downloadUrl: url, status: '1' };
    await this.reportHistoryRepo.updateRowData(
      updatedData,
      queryParams.downloadId,
    );
    return { fileUrl: url };
  }
  //#endregion

  async getLeadCountsReport(queryParams) {
    const { startDate, endDate } = this.getStartEndDate(
      queryParams?.startDate,
      queryParams?.endDate,
    );
    const options: any = {
      where: {
        createdAt: {
          [Op.and]: {
            [Op.gte]: startDate,
            [Op.lt]: endDate,
          },
        },
      },
    };
    if (queryParams?.leadSource && !isNaN(queryParams?.leadSource)) {
      options.where = {
        ...options.where,
        leadSource: queryParams?.leadSource,
      };
    }

    const totalReceivedLeads = await this.getCountFromStatus(options, null);
    const totalAcceptedLeads = await this.getCountFromStatus(options, '0');
    const totalInProcessLeads = await this.getCountFromStatus(options, '1');
    const totalRejectedLeads = await this.getCountFromStatus(options, '2');
    const totalDisbursedLeads = await this.getCountFromStatus(options, '3');
    return {
      totalReceivedLeads,
      totalAcceptedLeads,
      totalInProcessLeads,
      totalRejectedLeads,
      totalDisbursedLeads,
    };
  }

  private async getCountFromStatus(countsOptions, status) {
    if (status == null) return null;
    countsOptions.where.leadStatus = status;
    const data = await this.leadTrackingRepo.countOfRowData(countsOptions);
    if (!data || data === k500Error) return null;
    return data;
  }

  private getStartEndDate(startDate, endDate) {
    startDate = startDate || new Date();
    endDate = endDate || new Date();
    const startObj = this.dateServices.getStartOfDay(startDate);
    const endObj = this.dateServices.getEndOfDay(endDate);
    startDate = startObj.startOfDay;
    endDate = endObj.endOfDay;
    return { startDate, endDate };
  }

  private getIp(logParams) {
    let ip: string = getClientIp(logParams?.req);
    if (ip) ip = ip.replace(/f/g, '').replace(/:/g, '');
    if (ip == '1') ip = '110.227.999.999';

    if (logParams?.headers?.lspip) {
      try {
        if (EnvConfig.whiteListedIPs.includes(ip))
          ip = logParams?.headers?.lspip?.split(',')[0]?.trim();
      } catch (error) {}
    }

    if (ip == '::1') ip = '**************';
    if (typeof ip == 'string') ip = ip.replace('::ffff:', '');

    return ip;
  }

  async createMongoLeadsLog(reqData) {
    const { logParams, ip } = reqData;
    const body = logParams.req.body;
    const headers = logParams.req.headers;
    // Create MongoDB log first
    return await this.repoManager.createRowData(LeadTracking, {
      source: LEAD_SOURCE[logParams.req.headers['lead-source']],
      body: body ? JSON.stringify(body) : '',
      headers: JSON.stringify(headers),
      ip,
      adminId: '',
      apiEndpoint: `${logParams?.req?.protocol}://${logParams?.req.get(
        'host',
      )}${logParams?.req.originalUrl}`,
    });
  }

  async authFunction(logParams) {
    try {
      const reqOrigin =
        logParams?.headers?.origin ?? logParams?.headers?.referer;
      const exceptionOrigin = [
        EnvConfig.nbfc.nbfcShortName?.toLowerCase(),
        'devuatapi',
      ];
      const isExceptionHost = exceptionOrigin.some((i) =>
        reqOrigin?.includes(i),
      );
      // Get IP
      const ip = this.getIp(logParams);

      const checkIpFromLeadTracking = await this.repoManager.getRowWhereData(
        LeadTrackingIp,
        ['id', 'ip', 'isBlackListed', 'ipSource'],
        { where: { ip } },
      );

      if (!isExceptionHost) {
        if (!checkIpFromLeadTracking) return false;
        if (checkIpFromLeadTracking?.isBlackListed === 1) return false;
        logParams.req.headers['lead-source'] =
          checkIpFromLeadTracking?.ipSource;
      } else logParams.req.headers['lead-source'] = 0;

      // Log Mongo
      const mongoPayload = {
        logParams,
        ip,
      };
      const data = await this.createMongoLeadsLog(mongoPayload);
      logParams.req.headers['mongodb-logId'] = data?.id ?? null;
      return data;
    } catch (error) {
      console.log(error);
      return false;
    }
  }

  private validatePincode(pincodes) {
    if (!Array.isArray(pincodes)) return null;
    const invalidIndex = [];
    const pincodeRes = pincodes.map((item) => {
      return {
        ...item,
        pincode: item?.pincode
          ? this.funGetStateFromPincode(item.pincode)
          : item.pincode,
      };
    });
    if (!pincodeRes || !Array.isArray(pincodeRes)) return null;
    pincodeRes.forEach((item) => {
      const state = item?.pincode;
      if (REJECTED_STATES.includes(state)) invalidIndex.push(item.idx);
    });
    return invalidIndex;
  }

  async getPhoneNumberByUserId(reqData) {
    try {
      const { userList } = reqData;
      if (!userList || userList.length === 0) return kParamMissing('userList');
      const userData = await this.leadTrackingRepo.getTableWhereData(
        ['id', 'phone', 'pan'],
        {
          where: {
            id: { [Op.in]: userList },
          },
        },
      );
      let finalData = [];

      for (let i = 0; i < userData.length; i++) {
        const id = userData[i].id;
        const encryptedPhone = userData[i].phone;
        const encryptedPan = userData[i].pan;
        const phone = this.cryptServices.decryptPhone(encryptedPhone);
        const pan = await this.cryptServices.decryptText(encryptedPan);
        finalData.push({ id, phone, pan });
      }

      return finalData;
    } catch (error) {
      console.log(error);
      return kInternalError;
    }
  }

  //#region Lead tracking report
  async funGetLeadTrackingReport(bodyParams) {
    if (!bodyParams.startDate) return kParamMissing('startDate');
    if (!bodyParams.endDate) return kParamMissing('endDate');

    const { startDate, endDate } = this.getStartEndDate(
      bodyParams?.startDate,
      bodyParams?.endDate,
    );

    const leadSource = bodyParams?.lead_source;

    /// Get lead source number array
    const leadSources = Object.keys(LEAD_SOURCE);

    /// Check if lead source passed in params is number (optional param)
    const isSourcePassed = leadSource != '' && !isNaN(leadSource);

    /// Validate lead source number if passed in params
    if (isSourcePassed && !leadSources.includes(String(leadSource)))
      return k400ErrorMessage('Invalid Lead Source');

    const rejectedAttr: any = [
      'leadSource',
      [literal('COUNT("id")'), 'TotalRejectedLeads'],
    ]; /// Rejected leads (lead status = 2 and 4)

    const acceptedAttr: any = [
      'leadSource',
      [
        literal('COUNT(CASE WHEN "leadStatus" = 0 THEN 1 END)'),
        'TotalAcceptedLeads',
      ], /// Accepted leads (lead status = 0)
      [
        literal('COUNT(CASE WHEN "leadStatus" = 1 THEN 1 END)'),
        'TotalProcessedLeads',
      ], /// Processed leads (lead status = 1)
      [
        literal('COUNT(CASE WHEN "leadStatus" = 3 THEN 1 END)'),
        'TotalDisbursedLeads',
      ], /// Disbursed leads (lead status = 3)
    ];

    const options: any = {
      where: {
        createdAt: {
          [Op.and]: {
            [Op.gte]: startDate,
            [Op.lt]: endDate,
          },
        },
        leadSource: leadSources,
      },
      group: ['leadSource'],
    };

    if (isSourcePassed) options.where.leadSource = leadSource;

    /// Get rejected leads data from all the 10 series rejected tables
    const rejectedLeadsPromiseArr = [];
    for (let index = 0; index < 10; index++)
      rejectedLeadsPromiseArr.push(
        this.funGetLeadCount(options, false, index, rejectedAttr),
      );
    const allRejectedData = await Promise.all(rejectedLeadsPromiseArr);

    /// Get accepted leads data from all the 10 series accepted tables
    const acceptedLeadsPromiseArr = [];
    for (let index = 0; index < 10; index++)
      acceptedLeadsPromiseArr.push(
        this.funGetLeadCount(options, true, index, acceptedAttr),
      );
    const allAcceptedData = await Promise.all(acceptedLeadsPromiseArr);

    /// Get array of object(s), where each object contains lead source and their lead status wise count summary
    const reportObj = this.funCalcLeadCount([
      ...allRejectedData,
      ...allAcceptedData,
    ]);

    const finalReport = [];

    for (const leadSource in reportObj) {
      /// Get lead source name
      const source = LEAD_SOURCE[leadSource] ?? '-';

      /// Count total received leads
      let totalReceivedLeads =
        reportObj[leadSource]?.rejectedLeadsCount +
        reportObj[leadSource]?.acceptedLeadsCount +
        reportObj[leadSource]?.processedLeadsCount +
        reportObj[leadSource]?.disbursedLeadsCount;
      totalReceivedLeads = totalReceivedLeads
        ? this.typeService.amountNumberWithCommas(totalReceivedLeads)
        : '-';

      /// Count total rejected leads
      let totalRejectedLeads = reportObj[leadSource]?.rejectedLeadsCount;
      totalRejectedLeads = totalRejectedLeads
        ? this.typeService.amountNumberWithCommas(totalRejectedLeads)
        : '-';

      /// Count total accepted leads
      let totalAcceptedLeads = reportObj[leadSource]?.acceptedLeadsCount;
      totalAcceptedLeads = totalAcceptedLeads
        ? this.typeService.amountNumberWithCommas(totalAcceptedLeads)
        : '-';

      /// Count total processed leads
      let totalProcessedLeads = reportObj[leadSource]?.processedLeadsCount;
      totalProcessedLeads = totalProcessedLeads
        ? this.typeService.amountNumberWithCommas(totalProcessedLeads)
        : '-';

      /// Count total disbursed leads
      let totalDisbursedLeads = reportObj[leadSource]?.disbursedLeadsCount;
      totalDisbursedLeads = totalDisbursedLeads
        ? this.typeService.amountNumberWithCommas(totalDisbursedLeads)
        : '-';

      finalReport.push({
        'Lead Source': source,
        'Total Received Leads': totalReceivedLeads,
        'Total Rejected Leads': totalRejectedLeads,
        'Total Accepted Leads': totalAcceptedLeads,
        'Total Processed Leads': totalProcessedLeads,
        'NBFC Disbursed Leads': totalDisbursedLeads,
      });
    }
    return finalReport;
  }
  //#endregion Lead tracking report

  //#region Get lead count data from accepted and rejected tables
  async funGetLeadCount(options, isAccept, index, attributes) {
    let leadTable;
    if (isAccept) leadTable = acceptedLeadModels[index];
    if (!isAccept) leadTable = rejectedLeadModels[index];
    return await this.repoManager.getTableWhereData(
      leadTable,
      attributes,
      options,
    );
  }
  //#endregion

  //#region Calculate lead count for all lead status
  private funCalcLeadCount(dataArr) {
    let leadSourceSums = {};
    dataArr.forEach((arrItem) => {
      arrItem.forEach((objItem) => {
        const leadSource = objItem?.leadSource;
        const rejectedLeads = parseInt(objItem?.TotalRejectedLeads ?? 0, 10);
        const acceptedLeads = parseInt(objItem?.TotalAcceptedLeads ?? 0, 10);
        const processedLeads = parseInt(objItem?.TotalProcessedLeads ?? 0, 10);
        const disbursedLeads = parseInt(objItem?.TotalDisbursedLeads ?? 0, 10);

        /// Initialize count if does not exists for that particular lead source
        if (!leadSourceSums[leadSource]) {
          leadSourceSums[leadSource] = {};
          leadSourceSums[leadSource].rejectedLeadsCount = 0;
          leadSourceSums[leadSource].acceptedLeadsCount = 0;
          leadSourceSums[leadSource].processedLeadsCount = 0;
          leadSourceSums[leadSource].disbursedLeadsCount = 0;
        }
        /// Calculate sum of count, lead status wise for that particular lead source
        leadSourceSums[leadSource].rejectedLeadsCount += rejectedLeads;
        leadSourceSums[leadSource].acceptedLeadsCount += acceptedLeads;
        leadSourceSums[leadSource].processedLeadsCount += processedLeads;
        leadSourceSums[leadSource].disbursedLeadsCount += disbursedLeads;
      });
    });
    return leadSourceSums;
  }
  //#endregion

  //#region Lead tracking report -> Get lead source wise count
  // private async funGetLeadSourceWiseCount(leadSource) {
  //   const response = { leadSource, leadCount: 0 };

  //   /// Get lead list based on conditions
  //   const disbOpts: any = {
  //     where: {
  //       leadStatus: 4, /// Existing
  //       rejectReasons: '["A user with same PAN already exists."]',
  //       leadSource,
  //     },
  //     useMaster: false,
  //   };
  //   const disburedLeads = await this.repoManager.getTableWhereData(
  //     LeadTrackingEntity,
  //     ['hashPAN', 'createdAt'],
  //     disbOpts,
  //   );
  //   if (disburedLeads === k500Error) return kInternalError;
  //   if (!disburedLeads?.length) return response;

  //   const hashPANList = disburedLeads.map((item) => item.hashPAN);

  //   const kycOptions1 = {
  //     where: {
  //       hashPan: hashPANList,
  //     },
  //     useMaster: false,
  //   };
  //   const kycList1 = await this.repoManager.getTableWhereData(
  //     KYCEntity,
  //     ['userId', 'hashPan'],
  //     kycOptions1,
  //   );
  //   if (kycList1 === k500Error) return kInternalError;
  //   if (!kycList1?.length) return response;

  //   kycList1.forEach((el, i) => {
  //     const leadMatch = disburedLeads.find((i) => i.hashPAN == el.hashPan);
  //     kycList1[i]['leadCreatedAt'] = leadMatch?.createdAt
  //       ? leadMatch.createdAt
  //       : null;
  //   });

  //   const userArr = kycList1.map((i) => i.userId);

  //   /// Get lead list
  //   const userOptions1 = {
  //     where: {
  //       id: userArr,
  //     },
  //     useMaster: false,
  //   };
  //   const userList1 = await this.repoManager.getTableWhereData(
  //     registeredUsers,
  //     ['id', 'createdAt'],
  //     userOptions1,
  //   );
  //   if (userList1 === k500Error) return kInternalError;
  //   if (!userList1?.length) return response;

  //   const userIdList = [];
  //   userList1.forEach((el) => {
  //     const kycData = kycList1.find((i) => i.userId == el.id);

  //     const leadDate = new Date(kycData.leadCreatedAt);
  //     const userDate = new Date(el.createdAt);

  //     if (leadDate < userDate) userIdList.push(el.id);
  //   });

  //   /// Get loan list
  //   const loanOptions = {
  //     where: {
  //       userId: userIdList,
  //       appType: 0, /// LSP App
  //       loanStatus: ['Complete', 'Active'],
  //     },
  //     group: ['userId'],
  //     useMaster: false,
  //   };
  //   const leadCount = await this.repoManager.getTableWhereData(
  //     loanTransaction,
  //     ['userId'],
  //     loanOptions,
  //   );
  //   if (leadCount === k500Error) return kInternalError;
  //   if (!leadCount?.length) return response;
  //   response.leadCount = leadCount?.length;

  //   return response;
  // }
  //#endregion

  //#region Migrate from Mongo - Archived
  async migrateLostDataFromMongo(reqData) {
    try {
      const { startDate, endDate } = reqData;
      if (!startDate || !endDate) return kParamsMissing;
      const dateRange = this.dateServices.utcDateRange(startDate, endDate);
      console.log(2, dateRange);
      // Fetch and Insert each record created after 22 Oct from Mongo
      const options = {
        where: {
          createdAt: {
            $gte: new Date(dateRange.minRange),
            $lte: new Date(dateRange.maxRange),
          },
          apiEndpoint: '/thirdparty/leadtracking/createrecords',
          statusCode: 500,
        },
      };
      const fetchedData: any = await this.repoManager.fetchDataFromMongoDB(
        APILogger,
        options,
      );

      if (!fetchedData || fetchedData === k500Error) return kNoDataFound;

      console.log('TOTAL LENGTH:', fetchedData.length);

      for (let i = 0; i < fetchedData.length; i++) {
        try {
          console.log(i);
          const el = fetchedData[i];
          const ip = el?.ip;
          if (!ip) continue;
          if (el.body?.length === 2) continue;
          let parsedBody = el.body ? JSON.parse(el.body) : {};
          if (Object.keys(parsedBody).length === 0) continue;
          const createdAt = el.createdAt ?? new Date();
          parsedBody = Object.values(parsedBody);
          if (parsedBody?.length === 0) continue;

          // get Lead source
          const checkIpFromLeadTracking =
            await this.repoManager.getRowWhereData(
              LeadTrackingIp,
              ['id', 'ip', 'isBlackListed', 'ipSource'],
              { where: { ip, isBlackListed: 0 } },
            );
          if (!checkIpFromLeadTracking || checkIpFromLeadTracking === k500Error)
            continue;

          const leadSource = checkIpFromLeadTracking?.ipSource;
          if (leadSource === null) continue;
          await this.createData(parsedBody, null, leadSource, createdAt);
        } catch (error) {
          console.log(error);
        }
      }

      return fetchedData;
    } catch (error) {
      console.log(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region Manage Pincode List
  private async managePincodeList() {
    const data: any = fs.readFileSync(
      './upload/misc/PINCODE_LIST.json',
      'utf-8',
    );
    if (!data) return;
    this.pincodeList = JSON.parse(data);
  }
  //#endregion

  //#region Get State Name based on pincode
  private funGetStateFromPincode(pincode) {
    if (!pincode) return null;
    const pincodeList = this.pincodeList ?? [];
    const pincodeData = pincodeList.find((item) => item.Pincode == pincode);
    const state = pincodeData?.State ?? null;
    if (!state) return null;
    return state.toLowerCase();
  }
  //#endregion

  //#region Get Lead Table Series
  funGetLeadTrackingTableSeries(reqData) {
    const phone = reqData?.phone?.trim();
    if (isNaN(phone) || phone?.length != 10) return kInvalidParamValue('phone');
    const series = funGetLeadTableSeries(phone);
    return { 'Lead Table Series': series };
  }
  //#endregion

  //#region Get report of lead's loan status, on base of filters of date range and lead's IP address.
  async funGetLeadStatusReport(query) {
    try {
      if (query?.fromDate && query?.toDate) {
        const from: any = new Date(query.fromDate);
        const to: any = new Date(query.toDate);

        if (from > to || from == 'Invalid Date' || to == 'Invalid Date') {
          return k400ErrorMessage('Invalid date range');
        }
      }
      /// Validate and apply date range
      const defaultDate = this.dateServices.getGlobalDate(new Date()).toJSON();
      const startDate = query?.fromDate ?? defaultDate;
      const endDate = query?.toDate ?? defaultDate;
      const dateRange = this.dateServices.utcDateRange(startDate, endDate);
      const invalidRange = this.dateServices.funCalcDaysDiff(
        dateRange.minRange,
        dateRange.maxRange,
        LEAD_DATE_RANGE_LIMIT_DAYS,
      );
      if (invalidRange) {
        return k400ErrorMessage(
          `Date range should be within ${LEAD_DATE_RANGE_LIMIT_DAYS} days.`,
        );
      }

      let data = await this.funGetAllLeadsData(dateRange, query);
      data = data.flat();
      if (!data?.length) {
        return kNoDataFound;
      }

      const finalReponse = [];
      const processedLeads = data.filter((item) =>
        [kLeadStatus.InProcess, kLeadStatus.Disbursed].includes(
          item?.leadStatus,
        ),
      );
      if (processedLeads?.length) {
        const leadUserList = await this.funGetLeadUserData(processedLeads);
        finalReponse.push(...leadUserList);
      }

      const notProcessedLeads = data.filter((item) =>
        [
          kLeadStatus.Accepted,
          kLeadStatus.Rejected,
          kLeadStatus.Existing,
          kLeadStatus.Expired,
        ].includes(item?.leadStatus),
      );
      if (notProcessedLeads?.length) {
        const leadUserList = await this.funPrepareResponseForReport(
          notProcessedLeads,
        );
        finalReponse.push(...leadUserList);
      }

      return finalReponse;
    } catch (error) {
      return kInternalError;
    }
  }

  private funPrepareResponseForReport(list) {
    return list.map((item) => {
      let decryptedPhone =
        this.cryptServices.decryptPhone(item?.phone) == k500Error
          ? '-'
          : this.cryptServices.decryptPhone(item?.phone);
      const rejectReasons = item?.rejectReasons
        ? JSON.parse(item.rejectReasons ?? '[]')
        : [];
      let finalObj: any = {};
      finalObj.phone = decryptedPhone;
      /// If lead is 'Expired', in that case show lead status as 'Accepted'
      finalObj.leadStatus =
        item?.leadStatus == kLeadStatus.Expired
          ? kLeadStatusObj[kLeadStatus.Accepted]
          : kLeadStatusObj[item?.leadStatus] ?? '-';
      if (
        [kLeadStatus.Rejected, kLeadStatus.Existing].includes(item?.leadStatus)
      ) {
        finalObj.rejectReasons = rejectReasons;
      }
      return finalObj;
    });
  }
  //#endregion

  private async funGetLeadUserData(leadUserList) {
    const hashPhoneArr = leadUserList.map((item) => item?.hashPhone);

    const attributes = ['id', 'fullName', 'phone', 'hashPhone'];
    const options = {
      where: { hashPhone: hashPhoneArr },
      include: {
        model: loanTransaction,
        attributes: ['id', 'loanAmount', 'loanStatus'],
        required: false,
      },
    };
    const getUserList = await this.repoManager.getTableWhereData(
      registeredUsers,
      attributes,
      options,
    );

    return getUserList?.map((item) => {
      let loanData = item?.loanData ?? [];
      loanData = loanData?.length
        ? loanData?.map((loan) => {
            return {
              ...loan,
              loanAmount: loan?.loanAmount ?? '-',
            };
          })
        : [];

      const leadUserData = leadUserList.find(
        (leadUser) => leadUser?.hashPhone == item?.hashPhone,
      );

      let decryptedPhone =
        this.cryptServices.decryptPhone(item?.phone) == k500Error
          ? '-'
          : this.cryptServices.decryptPhone(item?.phone);
      delete item?.hashPhone;
      return {
        ...item,
        fullName: item?.fullName ?? '-',
        phone: decryptedPhone,
        leadStatus: kLeadStatusObj[leadUserData?.leadStatus] ?? '-',
        loanData,
      };
    });
  }

  private async funGetAllLeadsData(dateRange, query) {
    const promiseArr = [];
    const attributesObj = {
      0: ['hashPhone', 'leadStatus', 'fullName', 'phone'], /// For accepted leads table
      1: ['leadStatus', 'rejectReasons', 'fullName', 'phone'], /// For rejected leads table
    };

    for (let category = 0; category < 2; category++) {
      const isAccept = category == 0 ? true : false;
      const attributes = attributesObj[category];
      for (let series = 0; series < 10; series++) {
        const options: any = {
          where: {
            createdAt: {
              [Op.and]: {
                [Op.gte]: dateRange?.minRange,
                [Op.lt]: dateRange?.maxRange,
              },
            },
            leadSource: query?.leadSource,
          },
          order: [['createdAt', 'DESC']],
        };

        promiseArr.push(
          this.funGetLeadCount(options, isAccept, series, attributes),
        );
      }
    }

    return await Promise.all(promiseArr);
  }
}
