// Imports
import { Document } from 'mongoose';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';

export type GetAPILoggerDocument = GetAPILogger & Document;

@Schema({ timestamps: true, strict: false })
export class GetAPILogger {
  @Prop({ required: true })
  apiEndpoint: string;

  @Prop({ required: false })
  type: string;

  @Prop({ required: false })
  userId: string;

  @Prop({ required: false })
  adminId: number;

  @Prop({ required: false })
  loanId: number;

  @Prop({ required: false })
  body: string;

  @Prop({ required: false })
  headers: string;

  @Prop({ required: false })
  data: string;

  @Prop({ required: false })
  ip: string;

  @Prop({ required: false })
  sourceId: string;

  @Prop({ required: false })
  traceId: number;

  @Prop({ type: Date, required: false })
  expireAt: Date;
}

export const GetAPILoggerSchema = SchemaFactory.createForClass(GetAPILogger);
