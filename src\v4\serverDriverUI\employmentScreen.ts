import { HOST_URL } from 'src/constants/globals';
const calculateDate = (offset = 0) => {
  const date = new Date();
  date.setDate(date.getDate() + offset); // Adjust the date by the offset
  return date;
};

export const kEmploymentDetailsScreen: any = {
  showAppBar: true,
  appBarColor: '0xffF1F1F1',
  appBarElevation: 0,
  appBarLeading: {
    type: 9,
    key: 'back',
    iconCodePoint: '62832',
    color: '0x7f040404',
    size: 18,
    clickEvent: { route: 'dashboardRoute', routingType: 3 },
  },
  surfaceTintColor: '0xffF1F1F1',
  backgroundColor: '0xffF1F1F1',
  body: {
    type: 17,
    key: 'body',
    padding: { left: 16, top: 15, right: 16, bottom: 15 },
    child: {
      type: 4,
      key: 'view',
      crossAxisAlignment: 2,
      children: [
        {
          type: 10,
          key: 'picture',
          svgURL:
            'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Jan-2025/*************.svg',
        },
        {
          type: 19,
          key: 'title',
          padding: { top: 15 },
          texts: [
            {
              text: 'Employment details',
              style: { color: '0xff000000', fontSize: 18, fontWeight: 5 },
            },
          ],
        },
        {
          type: 19,
          key: 'description',
          padding: { top: 8 },
          textAlign: 1,
          texts: [
            {
              text: 'Provide your professional details to know your employment.',
              style: { color: '0x99000000', fontSize: 12, fontWeight: 4 },
            },
          ],
        },
        {
          type: 5,
          key: 'fields',
          color: '0xffffffff',
          borderRadius: {
            bottomLeft: 15,
            bottomRight: 15,
            topLeft: 15,
            topRight: 15,
          },
          padding: { left: 15, top: 15, right: 15, bottom: 15 },
          margin: { top: 25 },
          boxShadow: {
            color: '0x66000000',
            blurRadius: 0.0,
            spreadRadius: 0.0,
            x: 1.0,
            y: 1.0,
          },
          child: {
            type: 4,
            key: 'widgets',
            children: [
              {
                type: 1,
                key: 'companyName',
                isSearch: true,
                labelText: "Company's name",
                title: "Your company's name",
                description: 'Where are you currently working?',
                options: [],
                searchApiData: {
                  endpoint: `${HOST_URL}v4/employment/searchCompany`,
                  requestType: 'GET',
                },
              },
              {
                type: 1,
                key: 'sectorId',
                padding: { top: 8 },
                isSearch: true,
                labelText: 'Employment sector',
                title: 'Employment sector',
                description: 'Sector that best describes your work',
                options: [
                  { id: 3, value: 'Homemaker' },
                  { id: 4, value: 'Hospital Natural Resources' },
                  { id: 5, value: 'Retail Sales' },
                  { id: 6, value: 'Telecom & Media' },
                  { id: 7, value: 'Agriculture & Farming' },
                  { id: 8, value: 'Leisure/Entertainment' },
                  { id: 9, value: 'Transportation' },
                  { id: 10, value: 'Home Business' },
                  { id: 11, value: 'Retired' },
                  { id: 12, value: 'Education' },
                  { id: 13, value: 'Manufaturing' },
                  { id: 14, value: 'Government' },
                  { id: 15, value: 'Information technology' },
                  { id: 16, value: 'Health care' },
                  { id: 17, value: 'Other' },
                  { id: 18, value: 'Banking & Finance' },
                  { id: 19, value: 'Services' },
                  { id: 20, value: 'Unemployed' },
                ],
              },
              {
                type: 1,
                key: 'designationId',
                padding: { top: 8 },
                isSearch: true,
                labelText: 'Designation',
                title: 'Your designation',
                description: "What's your job role?",
                options: [
                  { id: 1, value: 'Professional' },
                  { id: 2, value: 'Manager' },
                  { id: 3, value: 'Software Engineer' },
                  { id: 4, value: 'Sales' },
                  { id: 5, value: 'Driver' },
                  { id: 6, value: 'Executive' },
                  { id: 7, value: 'Office Staff' },
                  { id: 8, value: 'Skilled' },
                  { id: 9, value: 'Services' },
                  { id: 10, value: 'Construction worker' },
                  { id: 11, value: 'Factory Worker' },
                  { id: 12, value: 'Guard' },
                  { id: 13, value: 'Unskilled worker' },
                  { id: 14, value: 'Self-Employed' },
                  { id: 15, value: 'Other Occupations' },
                ],
              },
              {
                type: 6,
                key: 'empStartDate',
                padding: { top: 8 },
                labelText: 'Employment start date',
                helpText: 'EMPLOYMENT START DATE',
                firstDay: '1900-01-01 00:00:00.000',
                lastDay: '',
              },
              {
                type: 11,
                key: 'netPaySalary',
                padding: { top: 8 },
                labelText: 'Salary amount',
                hintText: 'Enter your monthly in-hand salary',
                formatters: [6],
              },
              {
                type: 6,
                key: 'lastPayDate',
                padding: { top: 8 },
                labelText: 'Last salary date',
                helpText: 'LAST SALARY DATE',
                firstDay: calculateDate(-90),
                lastDay: '',
              },
              {
                type: 6,
                key: 'nextPayDate',
                padding: { top: 8 },
                labelText: 'Next salary date',
                helpText: 'NEXT SALARY DATE',
                firstDay: '',
                lastDay: calculateDate(90),
              },
            ],
          },
        },
        {
          type: 2,
          key: 'continue',
          title: 'Continue',
          padding: { top: 20 },
          apiData: {
            endpoint: `${HOST_URL}v4/employment/submitDetails`,
            requestType: 'POST',
            ansKeys: [
              { key: 'companyName' },
              { key: 'sectorId' },
              { key: 'designationId' },
              { key: 'empStartDate' },
              { key: 'netPaySalary' },
              { key: 'lastPayDate' },
              { key: 'nextPayDate' },
            ],
            syncUserData: true,
            submitCurrentQuestion: true,
            isRedirect: true,
            canClearState: true,
          },
        },
      ],
    },
  },
};

export const kLspEmploymentDetailsScreen: any = {
  showAppBar: true,
  appBarActions: [
    {
      type: 17,
      key: 'helpButton',
      padding: { right: 16 },
      texts: [
        {
          text: 'HELP',
          style: {
            color: '0xff128391',
            fontSize: 14,
            fontWeight: 5,
            decoration: 4,
            decorationColor: '0xff128391',
          },
          clickEvent: { route: 'helpAndSupportRoute', routingType: 1 },
        },
      ],
    },
  ],
  appBarColor: '0xffFFFFFF',
  appBarElevation: 0,
  appBarLeading: {
    type: 7,
    key: 'back',
    iconCodePoint: '62834',
    color: '0xff020C0D',
    size: 28,
    clickEvent: { route: 'dashboardRoute', routingType: 3 },
  },
  surfaceTintColor: '0xffFFFFFF',
  backgroundColor: '0xffFFFFFF',
  body: {
    type: 15,
    key: 'body',
    physics: 3,
    padding: { left: 16, top: 15, right: 16, bottom: 15 },
    child: {
      type: 3,
      key: 'view',
      crossAxisAlignment: 4,
      children: [
        {
          type: 17,
          key: 'title',
          texts: [
            {
              text: 'Employment details',
              style: { color: '0xff020C0D', fontSize: 26, fontWeight: 6 },
            },
          ],
        },
        {
          type: 17,
          key: 'description',
          texts: [
            {
              text: 'This step is quick and helps you get the best loan terms based on your employment.',
              style: { color: '0xff718589', fontSize: 14, fontWeight: 5 },
            },
          ],
        },
        {
          type: 18,
          key: 'separator',
          padding: { top: 30 },
          physics: 5,
          fields: [
            {
              type: 10,
              key: 'companyName',
              isSearch: true,
              svgIcon:
                'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/company_name.svg',
              labelText: "Company's Name",
              title: "Your Company's Name",
              description: 'Where are you currently working?',
              options: [],
              searchApiData: {
                endpoint: `${HOST_URL}v4/employment/searchCompany`,
                requestType: 'GET',
              },
            },
            {
              type: 10,
              key: 'sectorId',
              padding: { top: 15 },
              isSearch: true,
              svgIcon:
                'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/company_name.svg',
              labelText: 'Employment sector',
              title: 'Employment sector',
              description: 'Sector that best describes your work',
              options: [
                { id: 3, value: 'Homemaker' },
                { id: 4, value: 'Hospital Natural Resources' },
                { id: 5, value: 'Retail Sales' },
                { id: 6, value: 'Telecom & Media' },
                { id: 7, value: 'Agriculture & Farming' },
                { id: 8, value: 'Leisure/Entertainment' },
                { id: 9, value: 'Transportation' },
                { id: 10, value: 'Home Business' },
                { id: 11, value: 'Retired' },
                { id: 12, value: 'Education' },
                { id: 13, value: 'Manufaturing' },
                { id: 14, value: 'Government' },
                { id: 15, value: 'Information technology' },
                { id: 16, value: 'Health care' },
                { id: 17, value: 'Other' },
                { id: 18, value: 'Banking & Finance' },
                { id: 19, value: 'Services' },
                { id: 20, value: 'Unemployed' },
              ],
            },
            {
              type: 10,
              key: 'designationId',
              padding: { top: 15 },
              isSearch: true,
              svgIcon:
                'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/job_type.svg',
              labelText: 'Designation',
              title: 'Your designation',
              description: "What's your job role?",
              options: [
                { id: 1, value: 'Professional' },
                { id: 2, value: 'Manager' },
                { id: 3, value: 'Software Engineer' },
                { id: 4, value: 'Sales' },
                { id: 5, value: 'Driver' },
                { id: 6, value: 'Executive' },
                { id: 7, value: 'Office Staff' },
                { id: 8, value: 'Skilled' },
                { id: 9, value: 'Services' },
                { id: 10, value: 'Construction worker' },
                { id: 11, value: 'Factory Worker' },
                { id: 12, value: 'Guard' },
                { id: 13, value: 'Unskilled worker' },
                { id: 14, value: 'Self-Employed' },
                { id: 15, value: 'Other Occupations' },
              ],
            },
            {
              type: 5,
              key: 'empStartDate',
              padding: { top: 15 },
              svgIcon:
                'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/calendar.svg',
              labelText: 'Employment start date',
              title: 'Employment start date',
              description: 'When did you join the current company?',
              firstDay: '1900-01-01 00:00:00.000',
              lastDay: '',
            },
            {
              type: 9,
              key: 'netPaySalary',
              padding: { top: 15 },
              labelText: 'Salary amount',
              hintText: 'Enter your monthly in-hand salary',
              formatters: [6],
            },
            {
              type: 5,
              key: 'lastPayDate',
              padding: { top: 15 },
              svgIcon:
                'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/calendar.svg',
              labelText: 'Last salary date',
              title: 'Most recent salary date',
              description: 'When did you get your last salary?',
              firstDay: calculateDate(-90),
              lastDay: '',
            },
            {
              type: 5,
              key: 'nextPayDate',
              padding: { top: 15 },
              svgIcon:
                'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/calendar.svg',
              labelText: 'Next salary date',
              title: 'Next salary date',
              description: 'What is your next salary date?',
              firstDay: '',
              lastDay: calculateDate(90),
            },
          ],
        },
      ],
    },
  },
  bottomNavigationBar: {
    type: 4,
    key: 'bottomBar',
    height: 90,
    rWidth: 1.0,
    color: '0xffFFFFFF',
    border: {
      top: { color: '0x8095B0B5', width: 0.5 },
    },
    child: {
      type: 1,
      key: 'continue',
      padding: { top: 16, left: 16, right: 16, bottom: 16 },
      title: 'Continue',
      apiData: {
        endpoint: `${HOST_URL}v4/employment/submitDetails`,
        requestType: 'POST',
        ansKeys: [
          { key: 'companyName' },
          { key: 'sectorId' },
          { key: 'designationId' },
          { key: 'empStartDate' },
          { key: 'netPaySalary' },
          { key: 'lastPayDate' },
          { key: 'nextPayDate' },
        ],
        syncUserData: true,
        submitCurrentQuestion: true,
        isRedirect: true,
        canClearState: true,
      },
    },
  },
};

export const kReapplyEmploymentDetailsScreen: any = {
  showAppBar: true,
  appBarColor: '0xffF1F1F1',
  appBarElevation: 0,
  appBarLeading: {
    type: 9,
    key: 'back',
    iconCodePoint: '62832',
    color: '0x7f040404',
    size: 18,
    clickEvent: { route: 'dashboardRoute', routingType: 3 },
  },
  surfaceTintColor: '0xffF1F1F1',
  backgroundColor: '0xffF1F1F1',
  body: {
    type: 17,
    key: 'body',
    padding: { left: 16, top: 15, right: 16, bottom: 15 },
    child: {
      type: 4,
      key: 'view',
      crossAxisAlignment: 2,
      children: [
        {
          type: 10,
          key: 'picture',
          svgURL:
            'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Jan-2025/*************.svg',
        },
        {
          type: 19,
          key: 'title',
          padding: { top: 15 },
          texts: [
            {
              text: 'Welcome back',
              style: { color: '0xff000000', fontSize: 18, fontWeight: 5 },
            },
          ],
        },
        {
          type: 19,
          key: 'description',
          padding: { top: 8 },
          textAlign: 1,
          texts: [
            {
              text: 'Fill the below mentioned details to get a loan',
              style: { color: '0x99000000', fontSize: 12, fontWeight: 4 },
            },
          ],
        },
        {
          type: 5,
          key: 'basicDetails',
          color: '0xffffffff',
          borderRadius: {
            bottomLeft: 12,
            bottomRight: 12,
            topLeft: 12,
            topRight: 12,
          },
          margin: { top: 25 },
          boxShadow: {
            color: '0x66000000',
            blurRadius: 0.0,
            spreadRadius: 0.0,
            x: 1.0,
            y: 1.0,
          },
          child: {
            type: 4,
            key: 'widgets',
            children: [
              {
                type: 15,
                key: 'detailRow',
                padding: { left: 25, top: 15, right: 25, bottom: 15 },
                crossAxisAlignment: 2,
                mainAxisAlignment: 4,
                children: [
                  {
                    type: 15,
                    key: 'firstRow',
                    isFlexible: true,
                    crossAxisAlignment: 2,
                    mainAxisAlignment: 6,
                    children: [
                      {
                        type: 19,
                        key: 'number',
                        texts: [
                          {
                            text: '1',
                            style: {
                              color: '0xff000000',
                              fontSize: 20,
                              fontWeight: 5,
                            },
                          },
                        ],
                      },
                      {
                        type: 19,
                        key: 'title',
                        isFlexible: true,
                        padding: { left: 25 },
                        texts: [
                          {
                            text: 'Basic details',
                            style: {
                              color: '0xff000000',
                              fontSize: 14,
                              fontWeight: 5,
                            },
                          },
                        ],
                      },
                    ],
                  },
                  {
                    type: 9,
                    key: 'check',
                    iconCodePoint: '57686',
                    color: '0xff000000',
                  },
                ],
              },
            ],
          },
        },
        {
          type: 5,
          key: 'employment',
          color: '0xffffffff',
          borderRadius: {
            bottomLeft: 12,
            bottomRight: 12,
            topLeft: 12,
            topRight: 12,
          },
          margin: { top: 15 },
          boxShadow: {
            color: '0x66000000',
            blurRadius: 0.0,
            spreadRadius: 0.0,
            x: 1.0,
            y: 1.0,
          },
          child: {
            type: 4,
            key: 'widgets',
            children: [
              {
                type: 15,
                key: 'detailRow',
                padding: { left: 25, top: 15, right: 25, bottom: 15 },
                crossAxisAlignment: 2,
                mainAxisAlignment: 6,
                children: [
                  {
                    type: 19,
                    key: 'number',
                    texts: [
                      {
                        text: '2',
                        style: {
                          color: '0xff000000',
                          fontSize: 20,
                          fontWeight: 5,
                        },
                      },
                    ],
                  },
                  {
                    type: 4,
                    key: 'detailsColumn',
                    padding: { left: 25 },
                    crossAxisAlignment: 4,
                    children: [
                      {
                        type: 19,
                        key: 'title',
                        texts: [
                          {
                            text: 'Employment',
                            style: {
                              color: '0xff000000',
                              fontSize: 14,
                              fontWeight: 5,
                            },
                          },
                        ],
                      },
                      {
                        type: 19,
                        key: 'subtitle',
                        texts: [
                          {
                            text: 'Status: Pending',
                            style: {
                              color: '0xffFF0000',
                              fontSize: 12,
                              fontWeight: 5,
                            },
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
              {
                type: 5,
                key: 'divider',
                height: 1.5,
                rWidth: 1.0,
                color: '0xffEBEBEB',
              },
              {
                type: 12,
                key: 'isSameCompany',
                padding: { bottom: 5 },
                fillColor: '0xffffffff',
                labelText: 'Are you still working at',
                hintText: 'Previous Company Name',
                options: [
                  { id: 0, value: 'Yes' },
                  { id: 1, value: 'No' },
                ],
              },
            ],
          },
        },
      ],
    },
  },
  bottomNavigationBar: {
    type: 5,
    key: 'bottomBar',
    height: 90,
    rWidth: 1.0,
    color: '0xffF1F1F1',
    child: {
      type: 2,
      key: 'continue',
      padding: { top: 16, left: 16, right: 16, bottom: 16 },
      title: 'Continue',
      apiData: {
        endpoint: `${HOST_URL}v4/employment/submitDetails`,
        requestType: 'POST',
        ansKeys: [{ key: 'isSameCompany' }],
        syncUserData: true,
        submitCurrentQuestion: true,
        isRedirect: true,
        canClearState: true,
      },
    },
  },
};
