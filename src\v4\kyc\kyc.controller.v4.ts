// Imports
import {
  kInternalError,
  kParamMissing,
  kSuccessData,
} from 'src/constants/responses';
import { KycServiceV4 } from './kyc.service.v4';
import { UserServiceV4 } from '../user/user.service.v4';
import {
  Body,
  Controller,
  Get,
  Headers,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { ErrorContextService } from 'src/utils/error.context.service';

@Controller('kyc')
export class KycControllerV4 {
  constructor(
    private readonly service: KycServiceV4,
    private readonly userService: UserServiceV4,
    private readonly errorContextService: ErrorContextService,
  ) {}

  @Post('validatemAadhaar')
  async funValidatemAadhaar(@Body() body, @Res() res) {
    try {
      const aadhaarData: any = await this.service.validatemAadhaar(body);
      if (aadhaarData?.message) return res.send(aadhaarData);
      let data: any = {};
      if (aadhaarData?.needUserInfo) {
        data = await this.userService.routeDetails({ id: body.userId });
        if (data?.message) return res.send(data);
        delete aadhaarData.needUserInfo;
      }

      return res.send({ ...kSuccessData, data: { ...data, ...aadhaarData } });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }

  @Post('aadhaarOtpRequest')
  async funAadhaarOtpRequest(@Headers() headers, @Body() body, @Res() res) {
    try {
      body.appType = headers.apptype || headers.appType;
      const data: any = await this.service.aadhaarOtpRequest(body);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Post('aadhaarOtpVerify')
  async funAadhaarOtpVerify(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.aadhaarOtpVerify(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }

  @Post('validatePan')
  async funValidatePan(@Body() body, @Res() res) {
    try {
      let data: any = await this.service.validatePan(body);
      if (data?.message) return res.send(data);
      if (data?.needUserInfo) {
        data = await this.userService.routeDetails({ id: body.userId });
        if (data?.message) return res.send(data);
      }
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }

  // In case system failed to validate pan card during reference submission
  @Post('validateStuckPanUsers')
  async funValidateStuckPanUsers(@Res() res) {
    try {
      const data: any = await this.service.validateStuckPanUsers();
      if (data?.message) return res.send(data);
      if (data.userIds) {
        for (let index = 0; index < data.userIds?.length; index++) {
          const userId = data.userIds[index];
          await this.userService.routeDetails({ id: userId });
        }
      }
      return res.send(kSuccessData);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }

  @Post('submitPanDetails')
  async funSubmitPanDetails(@Body() body, @Res() res) {
    try {
      let data: any = await this.service.submitPanDetails(body);
      if (data?.message) return res.send(data);
      if (data?.needUserInfo) {
        data = await this.userService.routeDetails({ id: body.userId });
        if (data?.message) return res.send(data);
      }
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }

  @Post('changeAadhaarService')
  async changeAadhaarService(@Body() body, @Res() res) {
    try {
      if (!body?.userId) return kParamMissing('userId');
      if (!body?.aadhaar_service) return kParamMissing('aadhaar_service');
      let data = await this.userService.routeDetails({
        id: body.userId,
        aadhaar_service: body.aadhaar_service,
      });
      if (data?.message) return res.send(data);
      data = await this.service.changeAadhaarService(body, data);

      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }
  @Get('profileImage')
  async funProfileImage(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.profileImage(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }

  //#region migrate kyc address to lat log
  @Get('migrateKYCLatLong')
  async migrateKYCLatLong(@Res() res) {
    try {
      const data: any = await this.service.updateLatLngkycData();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }

  @Post('zoopDigiRepoPrep')
  async funZoopDigiRepoPrep(@Res() res, @Body() body) {
    const aadhaarData: any = await this.service.zoopDigiRepoPrep(body);
    if (aadhaarData?.message) return res.send(aadhaarData);
    return res.send({ ...kSuccessData, aadhaarData });
  }

  @Post('checkAddharStatus')
  async funCheckAddharStatus(@Res() res, @Body() body) {
    const aadhaarStatus: any = await this.service.checkAdhaarStatus(body);
    let data: any = {};
    if (aadhaarStatus?.needUserInfo) {
      data = await this.userService.routeDetails({ id: aadhaarStatus.userId });
      if (data?.message) return res.send(data);
      delete aadhaarStatus.needUserInfo;
    }
    return res.send({ ...kSuccessData, data });
  }

  @Post('validateAadhaarKycInWA')
  async validateAadhaarKycInWA(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.validateAadhaarKycInWA(body);
      return res.send({ data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send({ data: false });
    }
  }

  @Post('aadhaarOtpReqInWA')
  async aadhaarOtpReqInWA(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.aadhaarOtpReqInWA(body);
      return res.send({ data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send({ data: false });
    }
  }
  //#endregion
  @Post('panToName')
  async funPanToName(@Res() res, @Body() body) {
    const data: any = await this.service.panToName(body);
    return res.send({ ...kSuccessData, data });
  }
}
