// Imports
import axios from 'axios';
import { Injectable } from '@nestjs/common';
import { EnvConfig } from 'src/configs/env.config';
import { k500Error } from 'src/constants/misc';
import { kParamMissing } from 'src/constants/responses';
import {
  MICRO_ALERT_TOPIC,
  slackNbfcBlock,
  slackSourceBlock,
} from 'src/constants/objects';
import { nGetSlackMsg, slackApiError } from 'src/constants/network';
import { KafkaService } from 'src/microservice/kafka/kafka.service';

export const errorExecptionUrls = [
  'https://www.googleapis.com/oauth2/v1/tokeninfo?access_token=',
  'https://ifsc.razorpay.com',
];

@Injectable()
export class SlackService {
  constructor(private readonly kafkaService: KafkaService) {}

  async sendMsg(reqData) {
    if (EnvConfig.isDev) return {};
    if (reqData?.sourceStr !== false && !reqData?.sourceStr)
      reqData.sourceStr = EnvConfig.server.sourceId;
    reqData.text = `*NBFC :* \`${EnvConfig.bureauReportCreds.nbfcShortForm}\` \n ${reqData.text}`;
    this.kafkaService.send(MICRO_ALERT_TOPIC.SEND_SLACK_MSG, reqData);
    return {};
  }

  async sendAPIErrorMsg(error, url) {
    if (EnvConfig.isDev) return {};
    const API_EXEPTION = [slackApiError];
    const sourceStr = EnvConfig.server.sourceId;
    let payload: any = {
      error,
      url,
      sourceStr,
      platformName: `*NBFC :* \`${EnvConfig.bureauReportCreds.nbfcShortForm}\``,
    };

    if (error?.error || error?.body || error?.option) {
      payload.error = error?.error || {};
      payload.body = error?.body || {};
      payload.option = error?.option || {};
    }

    if (!API_EXEPTION.includes(url)) {
      this.kafkaService.send(MICRO_ALERT_TOPIC.SEND_API_ERROR_MESSAGE, payload);
    }
    return {};
  }

  async sendSlackCronAlert(reqData) {
    const { url, fieldObj } = reqData;
    let fieldsArr: any = [];
    if (url == 'admin/emi/updateAllEmiDues') {
      const updatedEmi = Array.isArray(fieldObj?.updatedRes)
        ? fieldObj?.updatedRes?.[0]
        : null;
      fieldsArr = [
        {
          type: 'mrkdwn',
          text: `\`\`\`Total EMI Data: ${fieldObj?.id?.length}\`\`\``,
        },
        {
          type: 'mrkdwn',
          text: `\`\`\`Updated EMI Data: ${updatedEmi}\`\`\``,
        },
      ];
    } else if (url == 'admin/emi/updateAllEmiDuesPenalty') {
      fieldsArr = [
        {
          type: 'mrkdwn',
          text: `\`\`\`Defaulters Data: ${fieldObj?.defaultersLength}\`\`\``,
        },
        {
          type: 'mrkdwn',
          text: `\`\`\`Total Emi Dues Data: ${fieldObj?.totalEmiDues}\`\`\``,
        },
        {
          type: 'mrkdwn',
          text: `\`\`\`Total Updated Data: ${fieldObj?.dataUpdated}\`\`\``,
        },
      ];
    } else if (url == 'admin/transaction/placeAutoDebitForEMIDues') {
      fieldsArr = [
        {
          type: 'mrkdwn',
          text: `\`\`\`Target Emi Data: ${fieldObj?.targetEMIs}\`\`\``,
        },
        {
          type: 'mrkdwn',
          text: `\`\`\`Total Payment Data: ${fieldObj?.totalTransactionId}\`\`\``,
        },
      ];
    } else if (url == 'admin/disbursement/markDisbursementAsComplete') {
      fieldsArr = [
        {
          type: 'mrkdwn',
          text: `\`\`\`Loan Id: ${fieldObj?.loanId}\`\`\``,
        },
        {
          type: 'mrkdwn',
          text: `\`\`\`Process: Done\`\`\``,
        },
      ];
    } else if (url == 'admin/loan/autoDeclineLoanAfter7Day') {
      fieldsArr = [
        {
          type: 'mrkdwn',
          text: `\`\`\`Total Loan Data: ${fieldObj?.loanData?.length}\`\`\``,
        },
        {
          type: 'mrkdwn',
          text: `\`\`\`Loan Decline: ${fieldObj?.loanRejectCount}\`\`\``,
        },
      ];
    } else if (url == 'admin/admin/updateUserLoanStatus') {
      fieldsArr = [
        {
          type: 'mrkdwn',
          text: `\`\`\`Total User Data: ${fieldObj?.total}\`\`\``,
        },
        {
          type: 'mrkdwn',
          text: `\`\`\`OnTime User Data: ${fieldObj?.onTime}\`\`\``,
        },
        {
          type: 'mrkdwn',
          text: `\`\`\`Delayed User Data: ${fieldObj?.delayed}\`\`\``,
        },
        {
          type: 'mrkdwn',
          text: `\`\`\`Default User Data: ${fieldObj?.defaulted}\`\`\``,
        },
      ];
    } else if (url == 'admin/transaction/refundAutomation') {
      fieldsArr = [
        {
          type: 'mrkdwn',
          text: `\`\`\`Total Refund Data: ${fieldObj?.refundList?.length}\`\`\``,
        },
        {
          type: 'mrkdwn',
          text: `\`\`\`Refund Initialized : ${fieldObj?.totalCount}\`\`\``,
        },
      ];
    } else if (url == 'admin/emi/getLoanDueAlert') {
      fieldsArr = [
        {
          type: 'mrkdwn',
          text: `\`\`\`Missed EMI due: ${
            fieldObj?.missedEmi.length > 0 ? fieldObj.missedEmi : 0
          }\`\`\``,
        },
        {
          type: 'mrkdwn',
          text: `\`\`\`Missed bounce charge: ${
            fieldObj?.missedBounce.length > 0 ? fieldObj.missedBounce : 0
          }\`\`\``,
        },
        {
          type: 'mrkdwn',
          text: `\`\`\`Missed demand letter: ${
            fieldObj?.missedDemand.length > 0 ? fieldObj.missedDemand : 0
          }\`\`\``,
        },
        {
          type: 'mrkdwn',
          text: `\`\`\`Missed legal notice: ${
            fieldObj?.missedLegal.length > 0 ? fieldObj.missedLegal : 0
          }\`\`\``,
        },
      ];
    } else if (url == 'admin/loan/loanPaymentStatusAlert') {
      fieldsArr = [
        {
          type: 'mrkdwn',
          text: `\`\`\`Loan not update: ${
            fieldObj?.loanNotUpdated.length > 0 ? fieldObj.loanNotUpdated : 0
          }\`\`\``,
        },
        {
          type: 'mrkdwn',
          text: `\`\`\`EMI not update: ${
            fieldObj?.emiNotUpdated.length > 0 ? fieldObj.emiNotUpdated : 0
          }\`\`\``,
        },
      ];
    }

    const slackPayload = {
      blocks: [
        {
          type: 'divider',
        },
        slackNbfcBlock,
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Api Url*: \`${url}\``,
          },
        },
        {
          type: 'section',
          fields: fieldsArr,
        },
        slackSourceBlock,
      ],
      channel: EnvConfig.slack.cronAlertChannelId,
    };

    this.kafkaService.send(
      MICRO_ALERT_TOPIC.SEND_SLACK_CRON_ALERT,
      slackPayload,
    );
  }

  async getMsg(reqData) {
    // Caution -> Do not remove try catch
    try {
      const channel = reqData.channel;
      if (!channel) return kParamMissing('channel');
      const headers = { Authorization: 'Bearer ' + EnvConfig.slack.botToken };
      const params = { channel: channel };
      const response: any = await axios
        .get(nGetSlackMsg, { headers, params })
        // Caution -> Do not remove catch
        .catch((err) => {
          console.log(err);
          return k500Error;
        });
      if (!response) return k500Error;
      return response.data;
    } catch (error) {
      return k500Error;
    }
  }
}
