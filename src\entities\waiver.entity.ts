import {
  <PERSON><PERSON>sTo,
  Column,
  DataType,
  Foreign<PERSON>ey,
  HasOne,
  Model,
  Table,
} from 'sequelize-typescript';
import { registeredUsers as user } from './user.entity';
import { EmiEntity as emi } from './emi.entity';
import { admin } from './admin.entity';
import { loanTransaction } from './loan.entity';

@Table({})
export class WaiverEntity extends Model<WaiverEntity> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.JSONB,
    allowNull: false,
    defaultValue: {},
  })
  oldBifurcation: {};

  @Column({
    type: DataType.JSONB,
    allowNull: false,
    defaultValue: {},
  })
  newBifurcation: {};

  @Column({
    type: DataType.JSONB,
    allowNull: false,
    defaultValue: {},
    comment: 'Bifurcation of Waiver Given',
  })
  waiverBifurcation: {};

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  waiverAmount: number;

  @ForeignKey(() => loanTransaction)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  loanId: number;

  @ForeignKey(() => user)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId: string;

  @ForeignKey(() => emi)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  emiId: number;

  @ForeignKey(() => admin)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  adminId: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  followerId: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    values: [
      'EMIPAY',
      'FULLPAY',
      'WAIVER_PAID',
      'WAIVER_REVERSED',
      'EMIPAY_REVERSE_SETTLEMENT',
    ],
  })
  type: string;

  @BelongsTo(() => loanTransaction)
  loanData: loanTransaction;

  @BelongsTo(() => admin, {
    foreignKey: 'followerId',
    targetKey: 'id',
    constraints: false,
  })
  followerData: admin;
}
