import { Injectable } from '@nestjs/common';
import { nConvertBase64ToPdf, nInsertLog } from 'src/constants/network';
import { DateService } from 'src/utils/date.service';

@Injectable()
export class BOBNetbankingServiceV4 {
  constructor(private dateService: DateService) {}

  getBOBNetbankingData(reqData) {
    const todayDateInfo = this.dateService.dateToReadableFormat(
      new Date(),
      'DD/MM/YYYY',
    );
    const today = new Date();
    today.setDate(today.getDate() - 120);
    const fromDate = new Date(today);
    const fromDateInfo = this.dateService.dateToReadableFormat(
      fromDate,
      'DD/MM/YYYY',
    );
    return {
      title: 'Verify your bank',
      initialURL:
        'https://feba.bobibanking.com/corp/AuthenticationController?__START_TRAN_FLAG__=Y&FORMSGROUP_ID__=AuthenticationFG&__EVENT_ID__=LOAD&FG_BUTTONS__=LOAD&ACTION.LOAD=Y&AuthenticationFG.LOGIN_FLAG=1&BANK_ID=012&LANGUAGE_ID=001',
      initialLoader: true,
      type: 'BANK',
      jsTriggers: {
        'https://feba.bobibanking.com/corp/AuthenticationController': {
          onLoadStart: {
            state: { isLoader: false, isProcessing: true },
          },
          onLoadStop: {
            state: { isLoader: false, isProcessing: false },
            triggers: [
              `console.log("LOADER-> #01");
              (function() {
                    document.querySelector('.botRightNav1').remove();
                    document.querySelector('.botRightNav').remove();
                    document.querySelector('.topRow.custrow').remove();
                    document.getElementById('sliderImage_1').remove();
                    document.getElementById('LoginHDisplay.SubSection3').remove();
                    document.getElementById('popupMainDiv').remove();
                    document.getElementById('footer').remove();
                    const linkSection = document.querySelector('.linkSection');
                    if(linkSection) linkSection.remove();
                        const listItem = document.querySelector('ul.autocomplete-values-list li[value="0"]');
                    if(listItem) listItem.click();
                    const navigateTo = document.getElementById('LoginHDisplay.Ra24');
                    if(navigateTo) navigateTo.style.opacity = '0';
                    console.log("stopProcessing");
                })();`,
              `const homePageInterval = setInterval(() => {
                  const menuBar = document.querySelector('.icon-menu.menuBarIcon');
                  if(menuBar) {
                      clearInterval(homePageInterval);
                      console.log("startProcessing");
                  }
              }, 300);`,
              `let accountInterval = setInterval(() => {
                  let newAccountButton = document.evaluate(
                    "//a[text()='Operative Accounts']",
                    document,
                    null,
                    XPathResult.FIRST_ORDERED_NODE_TYPE,
                    null,
                  ).singleNodeValue;
                  if (newAccountButton) {
                    console.log("LOADER-> #02");
                    console.log("startProcessing");
                    newAccountButton.click();
                    clearInterval(accountInterval);
                  }
                }, 1000);`,
            ],
          },
          consoles: [
            {
              combinations: ['stopProcessing'],
              state: { isProcessing: false },
            },
            {
              combinations: ['startProcessing'],
              state: { isProcessing: true },
            },
            {
              // Metrics #01
              apiTriggers: [
                {
                  url: nInsertLog,
                  method: 'POST',
                  needWebResponse: false,
                  body: {
                    loanId: reqData.loanId,
                    userId: reqData.userId,
                    type: 1,
                    subType: 1,
                    status: 2,
                    values: { bankCode: 'BANK_OF_BARODA', step: 1 },
                  },
                },
              ],
              // Loader #2
              combinations: ['LOADER-> #01'],
            },
            {
              // Metrics #02
              apiTriggers: [
                {
                  url: nInsertLog,
                  method: 'POST',
                  needWebResponse: false,
                  body: {
                    loanId: reqData.loanId,
                    userId: reqData.userId,
                    type: 1,
                    subType: 1,
                    status: 2,
                    values: { bankCode: 'BANK_OF_BARODA', step: 2 },
                  },
                },
              ],
              // Loader #2
              combinations: ['LOADER-> #02'],
            },
          ],
        },
        'https://feba.bobibanking.com/corp/Finacle': {
          allowContains: true,
          onLoadStart: {
            state: { isProcessing: true },
          },
          onLoadStop: {
            state: { isProcessing: true },
            triggers: [
              `let statementInterval = setInterval(() => {
                    let statementGenerateBtn = document.evaluate(
                      "//a[text()='Generate Account Statement']",
                      document,
                      null,
                      XPathResult.FIRST_ORDERED_NODE_TYPE,
                      null,
                    ).singleNodeValue;
                    if (statementGenerateBtn) {
                      statementGenerateBtn.click();
                      clearInterval(statementInterval);
                    }
                  }, 1000);`,
              `let searchTransactionInterval = setInterval(() => {
                    let serachStatementBtn = document.evaluate(
                      "//span[text()='Search Transactions']",
                      document,
                      null,
                      XPathResult.FIRST_ORDERED_NODE_TYPE,
                      null,
                    ).singleNodeValue;
                    if (serachStatementBtn) {
                      serachStatementBtn.click();
                      clearInterval(searchTransactionInterval);
                      console.log("LOADER-> #03");
                      document.querySelector('input[name="TransactionHistoryFG.SELECTED_RADIO_INDEX"][value="0"]').checked = true;
                      document.querySelector('input[name="TransactionHistoryFG.FROM_TXN_DATE_submit"]').value = '${fromDateInfo.readableStr}';
                      document.querySelector('input[name="TransactionHistoryFG.TO_TXN_DATE_submit"]').value = '${todayDateInfo.readableStr}';
                      document.querySelector('input[name="TransactionHistoryFG.FROM_TXN_DATE"]').value = '${fromDateInfo.readableStr}';
                      document.querySelector('input[name="TransactionHistoryFG.TO_TXN_DATE"]').value = '${todayDateInfo.readableStr}';
                      document.querySelector('input[name="Action.SEARCH"][value="Search"]').click();
                    }
                }, 1000);`,
              `async function fetchTransactionData() {
                  const transactionData = {
                      step: "TRANSACTION",
                      status: "PENDING",
                      accountNumber: "",
                      transactions: "",
                  };

                  const transactionInterval = setInterval(async () => {
                      let accountStatement = document.evaluate(
                          "//h1[text()='Account Statement']",
                          document,
                          null,
                          XPathResult.FIRST_ORDERED_NODE_TYPE,
                          null
                      ).singleNodeValue;

                      if (accountStatement) {
                          clearInterval(transactionInterval);
                          console.log("LOADER-> #04");
                          const transactionElement = document.evaluate(
                                "//span[starts-with(text(), 'Transactions List')]",
                                document,
                                null,
                                XPathResult.FIRST_ORDERED_NODE_TYPE,
                                null
                            ).singleNodeValue;

                          let accountNumber = transactionElement.textContent.split('- ');
                          accountNumber = accountNumber[accountNumber.length - 1];
                          transactionData.accountNumber = accountNumber;
                          await new Promise((resolve) => setTimeout(resolve, 10000));
                          document.getElementById('PageConfigurationMaster_ROAUX3W__1:GENERATE_REPORT5').click();
                          await new Promise((resolve) => setTimeout(resolve, 10000));

                          let jSessionToken = document.querySelectorAll('input[id="PageMasterFG.null"]')[1].value.split('!');
                          jSessionToken = jSessionToken[0] + '!' + jSessionToken[1];
                          let cookies = document.cookie + '; JSESSIONID=' + jSessionToken;

                          const pdfUrl = document.PageMasterFG.action.replace('Finacle', 'FinacleRiaRequest');
                          const requestId =  document.getElementById('PageConfigurationMaster_ROAUX3W__1:Requestid').value

                          const headers = {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'Cookie': cookies
                          };

                          const body = new URLSearchParams({
                            'TransactionHistoryFG.FROM_TXN_DATE': '${fromDateInfo.readableStr}',
                            'TransactionHistoryFG.FROM_TXN_DATE_submit': '${fromDateInfo.readableStr}',
                            'TransactionHistoryFG.TO_TXN_DATE': '${fromDateInfo.readableStr}',
                            'TransactionHistoryFG.TO_TXN_DATE_submit': '${fromDateInfo.readableStr}',
                            'TransactionHistoryFG.OUTFORMAT': '5',
                            'GROUPLET_FORMSGROUP_ID__': 'TransactionHistoryFG',
                            'TransactionHistoryFG.REPORTTITLE': 'OpTransactionHistoryUX5',
                            'Requestid': requestId,
                            'TransactionHistoryFG.__COLLAPSIBLE_IDS__': 'PageConfigurationMaster_ROAUX3W__1:SearchPanel_Stage3_Extended_midAligned19.SubSectionHeader1,PageConfigurationMaster_ROAUX3W__1:SearchPanel_Stage3_Extended_midAligned19#PageConfigurationMaster_ROAUX3W__1:SearchPanel_Stage3_Extended_midAligned19.SubSection1,C|',
                            '__GROUPLET_NAME__': 'PageConfigurationMaster_ROAUX3W__1',
                            '__RIA__': 'GROUPLET',
                            'Action.GENERATE_REPORT': 'OK',
                            'X-Requested-With': 'RIAUploadRequest'
                          });
                          try {
                            const response = await fetch(pdfUrl, {
                              method: 'POST',
                              headers: headers,
                              body: body
                            });
                            console.log("LOADER-> #05");
                            let arrayBuffer = await response.arrayBuffer();
                            let binary = '';
                            let bytes = new Uint8Array(arrayBuffer);
                            let len = bytes.byteLength;
                            for (let i = 0; i < len; i++) {
                              binary += String.fromCharCode(bytes[i]);
                            }
                            let base64Str = window.btoa(binary);
                            transactionData.transactions = base64Str;
                            transactionData.status = "COMPLETED";
                            console.log(JSON.stringify(transactionData));
                          } catch (error) {
                            console.error('Error:', error);
                          }
                      }
                  }, 1000);
              }
              fetchTransactionData();`,
            ],
          },
          consoles: [
            {
              // Metrics #03
              apiTriggers: [
                {
                  url: nInsertLog,
                  method: 'POST',
                  needWebResponse: false,
                  body: {
                    loanId: reqData.loanId,
                    userId: reqData.userId,
                    type: 1,
                    subType: 1,
                    status: 2,
                    values: { bankCode: 'BANK_OF_BARODA', step: 3 },
                  },
                },
              ],
              // Loader #3
              combinations: ['LOADER-> #03'],
            },
            {
              // Metrics #04
              apiTriggers: [
                {
                  url: nInsertLog,
                  method: 'POST',
                  needWebResponse: false,
                  body: {
                    loanId: reqData.loanId,
                    userId: reqData.userId,
                    type: 1,
                    subType: 1,
                    status: 2,
                    values: { bankCode: 'BANK_OF_BARODA', step: 4 },
                  },
                },
              ],
              // Loader #4
              combinations: ['LOADER-> #04'],
            },
            {
              // Metrics #05
              apiTriggers: [
                {
                  url: nInsertLog,
                  method: 'POST',
                  needWebResponse: false,
                  body: {
                    loanId: reqData.loanId,
                    userId: reqData.userId,
                    type: 1,
                    subType: 1,
                    status: 2,
                    values: { bankCode: 'BANK_OF_BARODA', step: 5 },
                  },
                },
              ],
              // Loader #5
              combinations: ['LOADER-> #05'],
            },
            {
              combinations: ['TRANSACTION', 'COMPLETED'],
              apiTriggers: [
                {
                  url: nInsertLog,
                  method: 'POST',
                  needWebResponse: false,
                  body: {
                    loanId: reqData.loanId,
                    userId: reqData.userId,
                    type: 1,
                    subType: 1,
                    status: 2,
                    values: { bankCode: 'BANK_OF_BARODA', step: 6 },
                  },
                },
                {
                  url: nConvertBase64ToPdf,
                  method: 'POST',
                  body: {
                    bankCode: 'BANK_OF_BARODA',
                    loanId: reqData.loanId,
                    userId: reqData.userId,
                  },
                  needWebResponse: true,
                },
              ],
            },
          ],
        },
      },
    };
  }
}
