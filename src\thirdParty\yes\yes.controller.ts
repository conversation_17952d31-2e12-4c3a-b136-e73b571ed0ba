// Imports
import { Body, Controller, Post, Req, Res } from '@nestjs/common';
import {
  k422ErrorMessage,
  kInternalError,
  kSuccessData,
} from 'src/constants/responses';
import { YESService } from './yes.service';
import { ErrorContextService } from 'src/utils/error.context.service';

@Controller('yes')
export class YESController {
  constructor(
    private readonly service: YESService,
    private readonly errorContextService: ErrorContextService,
  ) {}

  @Post('callback')
  async callback(@Body() body, @Res() res, @Req() req) {
    try {
      const decryptedResponse = await this.service.decryptCallbackResponse({
        body,
        headers: req.headers,
      });
      if (decryptedResponse === true) return res.json({ ...kSuccessData });
      const data: any = await this.service.callback(decryptedResponse);
      if (data.message) return res.send(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Post('intentRegister')
  async intent(@Body() body, @Res() res) {
    try {
      body.isDirect = true;
      const data: any = await this.service.intentRegister(body);
      if (data.message) return res.send(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Post('intentStatus')
  async intentStatus(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.intentStatus(body);
      if (data.message) return res.send(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Post('decryption')
  async decryption(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.decryptResponse(
        body?.hash,
        body?.iv,
        body?.secretKey,
        body?.data,
        body?.callbacktype,
      );
      if (data.message) return res.send(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Post('encryption')
  async encryption(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.encryptRequest(body);
      if (data.message) return res.send(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Post('refund')
  async refund(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.refund(body);
      if (data.message) return res.send(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Post('refundStatus')
  async refundStatus(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.refundStatus(body);
      if (data.message) return res.send(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }
  @Post('settlement')
  async settlement(@Res() res) {
    try {
      const data: any = await this.service.settlement();
      if (data.message) return res.send(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }
  @Post('settlementStatus')
  async settlementStatus(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.settlementStatus(body);
      if (data.message) return res.send(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }
}
