// Imports
import { k500Error, topBanks } from 'src/constants/misc';
import { Key } from 'src/authentication/auth.guard';
import {
  Body,
  Controller,
  Get,
  Query,
  Res,
  Headers,
  Post,
} from '@nestjs/common';
import { kInternalError, kSuccessData } from 'src/constants/responses';
import { MiscServiceV4 } from './misc.service.v4';
import { AdminRedisSyncService } from 'src/admin/admin/AdminRedisSync.service';
import { ErrorContextService } from 'src/utils/error.context.service';

@Controller('misc')
export class MiscControllerV4 {
  constructor(
    private readonly service: MiscServiceV4,
    private readonly adminRedisSyncService: AdminRedisSyncService,
    private readonly errorContextService: ErrorContextService,
  ) {}

  @Get('/getConfigs')
  async funGetConfigs(@Headers() headers, @Res() res, @Query() query) {
    try {
      if (headers.apptype && query) query.apptype = headers.apptype;
      const data: any = await this.service.getConfigs(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }

  @Get('/getTimeStamp')
  async funGetTimeStamp(@Res() res) {
    const timeStamp = this.service.getTimeStamp();
    return res.send({
      ...kSuccessData,
      data: { timeStamp },
    });
  }

  @Get('/getLoanPurposeList')
  async funGetLoanPurposeList(@Res() res) {
    try {
      const data: any = await this.service.getLoanPurposeList();
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }

  @Get('/getAvailableBankList')
  async funGetAvailableBankList(@Res() res) {
    try {
      const data: any = await this.service.getAvailableBankList();
      if (data?.message) return res.send(data);

      //To show top banks at the top of list
      const topBanksFromData = [];
      const remainingBanks = [];

      data.forEach((bank: any) => {
        if (topBanks.includes(bank.bankCode)) {
          topBanksFromData.push(bank);
        } else {
          remainingBanks.push(bank);
        }
      });

      // return res.send({ ...kSuccessData, data });
      return res.send({
        ...kSuccessData,
        data: [...topBanksFromData, ...remainingBanks],
      });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }

  @Get('getSettingsData')
  async funGetSettingsData(
    @Key() userId,
    @Query() query,
    @Res() res,
    @Headers() headers,
  ) {
    try {
      if (userId && !query?.userId) query.userId = userId;
      if (headers?.apptype) query.appType = headers.apptype;
      const data: any = await this.service.getSettingsData(query);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      // Silent error response to user in case of error
      return res.json(kSuccessData);
    }
  }

  @Get('userLoanDeclineReasons')
  async funGetUserloanDeclineReasons(@Res() res) {
    try {
      const result = await this.service.userLoanDeclineReasons();
      if (result == k500Error) return res.json(kInternalError);
      return res.json({ ...kSuccessData, data: result });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  @Get('/getBlackListReason')
  async getBlackListReason(@Res() res) {
    try {
      const data: any = await this.service.getBlackListReason();
      if (data?.message) return res.send(data);
      return res.json({ ...kSuccessData, data: data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Get('shareText')
  async shareTextApp(@Headers() headers, @Res() res) {
    try {
      const data: any = await this.service.shareTextApp(headers);
      if (data?.message) return res.send(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Post('saveUserRating')
  async funSaveUserRating(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.storeUserRating(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }

  @Post('uploadFile')
  async funUploadFile(@Body() body, @Res() res) {
    try {
      const data = await this.service.uploadFile(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }

  @Post('redisSetConfig')
  async funRedisSetConfig(@Body() Body, @Res() res) {
    try {
      const data: any = await this.adminRedisSyncService.storeConfigData();
      if (!data) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Get('getUserFAQs')
  async funGetUserFAQs(
    @Key() userId,
    @Query() query,
    @Res() res,
    @Headers() headers,
  ) {
    try {
      console.log({ headers });
      if (userId && !query?.userId) query.userId = userId;
      if (headers?.apptype) query.appType = headers.apptype;
      if (headers?.typeOfDevice) query.typeOfDevice = headers.typeOfDevice;
      const data: any = await this.service.getFAQDetails(query);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      // Silent error response to user in case of error
      return res.json(kSuccessData);
    }
  }
}
