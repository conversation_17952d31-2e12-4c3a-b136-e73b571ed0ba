// Imports
import { Document } from 'mongoose';
import { <PERSON><PERSON>, <PERSON>hema, SchemaFactory } from '@nestjs/mongoose';

export type ExperimentDocument = Experiment & Document;

@Schema({ timestamps: true, strict: false })
export class Experiment {
  @Prop({ required: true })
  type: string;

  @Prop({ required: false })
  userId: string;

  @Prop({ required: false })
  value: string;
}

export const ExperimentSchema = SchemaFactory.createForClass(Experiment);
