// Imports
import { Op } from 'sequelize';
import { Injectable } from '@nestjs/common';
import { k500Error } from 'src/constants/misc';
import { kfinvu } from 'src/constants/strings';
import { CryptService } from 'src/utils/crypt.service';
import { registeredUsers } from 'src/entities/user.entity';
import { MasterEntity } from 'src/entities/master.entity';
import { BankingEntity } from 'src/entities/banking.entity';
import { AALogs } from 'src/entities/schemas/aa_logs_schema';
import { SequelOptions } from 'src/interfaces/include.options';
import { FinvuService } from 'src/thirdParty/finvu/finvu.service';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { PeriodicEntity } from 'src/entities/periodic.entity';
import { LoanDetails } from 'src/entities/loan_details.entity';
import { LeadEntitiy } from 'src/entities/leads.entity';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { UserStage } from 'src/constants/objects';
import { FileService } from 'src/utils/file.service';

@Injectable()
export class LeadService {
  constructor(
    private readonly crypt: CryptService,
    private readonly finvu: FinvuService,
    private readonly repo: RepositoryManager,
    private readonly fileService: FileService,
    private readonly common: CommonSharedService,
  ) {}

  async sendAALoanCompletedUsers(reqData) {
    const targetLoanIds = reqData.loanIds ?? [];
    const readOnly = reqData.readOnly == 'true';
    const limit = reqData.limit ?? 10;
    const targetUserIds = reqData.userIds ?? [];

    const rawQuery = `SELECT "userId" FROM "LoanDetails" AS "loan"
    INNER JOIN "registeredUsers" AS "user" ON "user"."id" = "loan"."userId"
    WHERE "loan"."loanStatus" = '1' AND "closing_date" != '-1'
    AND "lastIndex" = 'true' AND "transactions_source" = '1'
    AND "user"."isBlacklist" != '1' AND "user"."stage" IN (19,21)
    LIMIT ${limit}`;
    const outputList = await this.repo.injectRawQuery(LoanDetails, rawQuery);
    if (outputList == k500Error) throw new Error();
    const userIds = outputList.map((el) => el.userId);

    const masterInclude: SequelOptions = { model: MasterEntity };
    masterInclude.attributes = ['loanId'];
    if (targetLoanIds?.length > 0)
      masterInclude.where = { loanId: targetLoanIds };

    const include = [masterInclude];

    const userAttr = ['id'];
    const userOptions: any = {
      include,
      useMaster: false,
      where: { id: userIds },
    };
    if (targetUserIds?.length > 0) userOptions.where.id = targetUserIds;

    const userList = await this.repo.getTableWhereData(
      registeredUsers,
      userAttr,
      userOptions,
    );
    if (userList == k500Error) throw new Error();

    const loanIds = userList.map((el) => el.masterData.loanId);

    const bankAttr = [
      'consentId',
      'consentPhone',
      'consentHandleId',
      'id',
      'loanId',
      'userId',
    ];
    const bankOptions = {
      limit,
      where: {
        consentMode: kfinvu,
        consentStatus: 'ACCEPTED',
        consentId: { [Op.ne]: null },
        salaryVerification: { [Op.in]: ['1', '3'] },
        loanId: loanIds,
      },
    };
    const bankList = await this.repo.getTableWhereData(
      BankingEntity,
      bankAttr,
      bankOptions,
    );
    if (bankList == k500Error) throw new Error();
    if (readOnly) return { count: bankList.length, rows: bankList };

    for (let index = 0; index < bankList.length; index++) {
      try {
        const bankingData = bankList[index];

        const consentId = bankingData.consentId;
        const consentHandleId = bankingData.consentHandleId;
        const custId = await this.crypt.decryptPhone(bankingData.consentPhone);

        // Save the entry of request
        const creationData = {
          source: 'FINVU',
          data: { custId, consentId, consentHandleId },
          loanId: bankingData.loanId,
          userId: bankingData.userId,
          type: 1,
          subType: 4,
          createdAt: new Date().toJSON(),
        };
        await this.repo.createRowData(AALogs, creationData);

        const sessionId = await this.finvu.fetchDataRequest(
          custId,
          consentId,
          consentHandleId,
        );
        if (!sessionId) continue;

        // update session Id
        await this.repo.updateRowData(
          BankingEntity,
          { sessionId },
          bankingData.id,
          true,
        );
        await this.repo.createRowData(PeriodicEntity, {
          sessionId,
          source: 1,
          type: 1,
          userId: bankingData.userId,
        });
      } catch (error) {}
    }

    return { count: bankList.length, rows: bankList };
  }

  async qualityLead(reqData) {
    const isDownload = reqData.download == 'true';

    const leadAttr = ['userId', 'data'];
    const leadOptions = { useMaster: false, where: { type: { [Op.ne]: 2 } } };
    const leadList = await this.repo.getTableWhereData(
      LeadEntitiy,
      leadAttr,
      leadOptions,
    );
    if (leadList == k500Error) throw new Error();

    const userIds = leadList.map((el) => el.userId);
    const masterInclude: SequelOptions = { model: MasterEntity };
    masterInclude.attributes = ['coolOffData'];
    const include = [masterInclude];
    const userAttr = [
      'fullName',
      'createdAt',
      'id',
      'phone',
      'stage',
      'stageTime',
      'lastCrm',
    ];
    const userOptions = {
      include,
      where: { id: userIds, isBlacklist: { [Op.ne]: '1' } },
    };
    const userList = await this.repo.getTableWhereData(
      registeredUsers,
      userAttr,
      userOptions,
    );
    if (userList == k500Error) throw new Error();

    const finalizedList = [];
    const today = new Date();
    const todayTime = today.getTime();
    for (let index = 0; index < userList.length; index++) {
      const userData = userList[index];
      const crm = userData?.lastCrm ?? {};
      if (
        [
          UserStage.MANDATE,
          UserStage.ESIGN,
          UserStage.DISBURSEMENT,
          UserStage.REPAYMENT,
        ].includes(userData.stage)
      ) {
        continue;
      }

      // Cool off period
      const masterData = userData.masterData ?? {};
      const coolOffData = masterData?.coolOffData ?? {};
      const coolOffEndsOn = coolOffData?.coolOffEndsOn ?? '';
      if (coolOffEndsOn && coolOffEndsOn != '') {
        const endDate = new Date(coolOffEndsOn);
        if (endDate.getTime() > todayTime) continue;
      }

      const finalizedData: any = {
        Name: userData.fullName ?? '-',
        userId: userData.id,
        Phone: this.crypt.decryptPhone(userData.phone),
        Salary: '-',
        LoanCredits: '-',
        Crm: crm?.remark ?? '-',
      };

      const matchData =
        (leadList.find((el) => el.userId == userData.id) ?? {}).data ?? {};
      if (matchData?.avgSalaryAmount > 0)
        finalizedData.Salary = Math.floor(matchData?.avgSalaryAmount);
      if (matchData?.avgIncomeAmount > 0)
        finalizedData.Salary = Math.floor(matchData?.avgIncomeAmount);
      finalizedData.Stage = this.common.stageNumberToStr(userData.stage);
      finalizedData.LoanCredits = matchData?.loanCreditCount ?? '-';

      finalizedList.push(finalizedData);
    }

    finalizedList.sort(
      (b, a) =>
        (a.Salary == '-' ? 0 : a.Salary) - (b.Salary == '-' ? 0 : b.Salary),
    );

    if (isDownload) {
      const rawExcelData = {
        sheets: ['Legal quality report'],
        data: [finalizedList],
        sheetName: 'legal_quality.xlsx',
      };
      const fileUrl = await this.fileService.objectToExcelURL(rawExcelData);
      if (fileUrl?.message) return fileUrl;
      return { fileUrl };
    }

    return { rows: finalizedList, count: finalizedList.length };
  }
}
