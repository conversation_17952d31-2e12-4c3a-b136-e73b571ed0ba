import { Injectable } from '@nestjs/common';
import { k422ErrorMessage } from 'src/constants/responses';
import { kErrorMsgs } from 'src/constants/strings';
// import { HTTPerror } from 'src/errors/exception.errors';

type trigger = { primary: string; foreign: string };
@Injectable()
export class TriggerService {
  constructor() {}

  createTrigger(body) {
    try {
      const name: string = body?.name ?? '';
      const entity: trigger = body?.entity ?? '';
      const on: string[] = body?.on ?? [];
      const updates: any[] = body?.updates ?? [];
      const creates: any[] = body?.creates ?? [];
      let columns: string[] = body?.columns ?? [];

      if (on.findIndex((i) => i.toLowerCase() === 'insert') !== -1)
        columns = [];

      if (!name || !on.length || !entity)
        return { message: 'required params missing' };

      const funName: string = `FUN_${name}`;

      // defining function for trigger
      let triggerFun: string = '';

      // initial line and return line for trigger function
      const initialLine: string = `CREATE OR REPLACE FUNCTION ${funName}() RETURNS TRIGGER AS $${name}_AUDIT$ `; // initial function declaration
      const returnLine: string = `RETURN NULL; END; $${name}_AUDIT$ LANGUAGE PLPGSQL;`;
      let declareString: string = '';
      let assignmentString: string = '';
      let updatesStrings: string = '';
      let fullInsertsStrings: string = '';
      let conditionString: string = '';

      // updating condition string as for which column update need to trigger function
      if (columns.length) conditionString += ` WHEN (`;
      for (let i = 0; i < columns.length; i++) {
        try {
          conditionString += `${i > 0 ? ' OR ' : ''}OLD."${
            columns[i]
          }" IS DISTINCT FROM NEW."${columns[i]}"${
            i + 1 === columns.length ? ')' : ''
          }`;
        } catch (e) {
          throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
        }
      }

      // defining trigger
      const trigger: string = `CREATE OR REPLACE TRIGGER ${name} AFTER ${on.join(
        ' OR ',
      )} ON PUBLIC."${entity}" FOR EACH ROW${conditionString} EXECUTE FUNCTION ${funName}(); `;

      // adding initial line in triggerFun
      triggerFun += initialLine;

      // updating all entities given
      for (let i = 0; i < updates.length; i++) {
        try {
          const item = updates[i];
          const entityUpdate: string = item.entity;
          const entitySelect: any[] = item?.select ?? [];
          const updateFields: any[] = item?.fields ?? [];
          const where: object = item.where ?? '';
          if (!where)
            return k422ErrorMessage(
              'Where condition is required in all updates',
            );

          // from select array declare all as fields above BEGIN
          for (let j = 0; j < entitySelect.length; j++) {
            try {
              const current = entitySelect[j];
              const fromEntity = current.entity;
              const selectWhere: string = current.where;
              const fromAttributes: any[] = current.attributes;
              let attributesString = '';
              let valuesString = '';

              for (let k = 0; k < fromAttributes.length; k++) {
                try {
                  const attributeType = fromAttributes[k].type;
                  const attributeAs = fromAttributes[k].as;
                  const attributeField: string = fromAttributes[k].field;
                  declareString += `DECLARE ${attributeAs} ${attributeType}; `;
                  const isFun =
                    attributeField.includes('(') &&
                    attributeField.includes(')');
                  if (isFun) attributesString += attributeField;
                  else attributesString += `"${attributeField}"`;
                  valuesString += `${attributeAs}`;
                  if (k !== fromAttributes.length - 1) {
                    attributesString += ',';
                    valuesString += ',';
                  }
                } catch (e) {}
              }
              assignmentString += `SELECT ${attributesString} INTO ${valuesString} FROM PUBLIC."${fromEntity}" WHERE ${selectWhere}; `;
            } catch (e) {
              throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
            }
          }

          if (updateFields.length)
            updatesStrings += `UPDATE PUBLIC."${entityUpdate}" SET `;
          for (let j = 0; j < updateFields.length; j++) {
            try {
              const updateItem = updateFields[j];
              updatesStrings += `"${updateItem.foreign}"=${updateItem.primary} `;
              // if last field add ; else add ,
              if (j + 1 === updateFields.length) {
                updatesStrings += `WHERE ${where}; `;
              } else {
                updatesStrings += ', ';
              }
            } catch (e) {
              throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
            }
          }
        } catch (e) {
          throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
        }
      }

      // add query for inserting rows
      for (let i = 0; i < creates.length; i++) {
        try {
          const inserted = creates[i];
          const insertEntity = inserted.entity;
          const insertedFields: any[] = inserted.fields ?? [];
          let insertString: string = '';
          let fields: string = '';
          let values: string = '';

          // inserted fields are present
          if (insertedFields.length) {
            insertString += `INSERT INTO PUBLIC."${insertEntity}"`;
            fields = '(';
            values = '(';
          }
          for (let j = 0; j < insertedFields.length; j++) {
            try {
              const field = insertedFields[j];
              if (j + 1 === insertedFields.length) {
                fields += `"${field.foreign}") `;
                values += `${field.primary}); `;
                insertString += fields;
                insertString += ' VALUES ';
                insertString += values;
              } else {
                fields += `"${field.foreign}",`;
                values += `${field.primary},`;
              }
            } catch (e) {
              throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
            }
          }
          if (insertedFields.length) fullInsertsStrings += insertString;
        } catch (e) {
          throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
        }
      }

      // add declarations on top before function BEGIN
      triggerFun += declareString;
      // add BEGIN
      triggerFun += ' BEGIN ';

      // add assignment string i.e. get data from other tables
      triggerFun += assignmentString;

      // add Update string
      triggerFun += updatesStrings;

      // add create strings
      triggerFun += fullInsertsStrings;

      // add end lines for function
      triggerFun += returnLine;
      return triggerFun + ' ' + trigger;
    } catch (e) {
      console.log({ e });
      throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
    }
  }
}

// update hash Phone in HashPhoneentities
const updateHashPhone = `CREATE OR REPLACE FUNCTION insert_or_update_hashPhone()
RETURNS TRIGGER AS $$
BEGIN
    IF EXISTS (SELECT 1 FROM public."HashPhoneEntities" WHERE "userId" = NEW."id") THEN
        UPDATE public."HashPhoneEntities"
        SET "hashPhone" = NEW."hashPhone"
        WHERE "userId" = NEW."id";
    ELSE
        INSERT INTO public."HashPhoneEntities" ("hashPhone", "userId", "createdAt", "updatedAt")
        VALUES (NEW."hashPhone", NEW."id", NOW(), NOW());
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER insert_or_update_hashPhone
AFTER INSERT OR UPDATE ON public."registeredUsers"
FOR EACH ROW
WHEN (NEW."hashPhone" IS DISTINCT FROM OLD."hashPhone")
EXECUTE FUNCTION insert_or_update_hashPhone();`;

const updateLoanUserDetails = `
CREATE OR REPLACE FUNCTION update_loan_user_details()
RETURNS TRIGGER AS $$
BEGIN
	UPDATE PUBLIC."loanTransactions"
	SET "fullName"= NEW."fullName", "email" = NEW."email", "phone" = NEW."phone", "hashPhone" = NEW."hashPhone"
	WHERE "userId"=NEW."id";
	RETURN NULL;
END;

$$ LANGUAGE PLPGSQL; 

CREATE OR REPLACE TRIGGER update_loan_user_details
AFTER UPDATE ON PUBLIC."registeredUsers"
FOR EACH ROW
WHEN (NEW."fullName" IS DISTINCT FROM OLD."fullName" OR
NEW."email" IS DISTINCT FROM OLD."email" OR
NEW."phone" IS DISTINCT FROM OLD."phone" OR
NEW."hashPhone" IS DISTINCT FROM OLD."hashPhone") 
EXECUTE FUNCTION update_loan_user_details();
`;

const updateUserUniqueId = `CREATE OR REPLACE FUNCTION nullify_user_unique_id_while_complete_reject()
RETURNS TRIGGER AS $$

BEGIN
    IF NEW."userUniqueId" IS NOT NULL THEN
        UPDATE public."loanTransactions"
        SET "userUniqueId" = NULL
        WHERE "id" = NEW."id";
    END IF;

    RETURN NULL;
END;

$$ LANGUAGE plpgsql;


CREATE OR REPLACE TRIGGER nullify_user_unique_id_while_complete_reject
AFTER UPDATE ON public."loanTransactions"
FOR EACH ROW
WHEN (NEW."loanStatus" IN ('Complete', 'Rejected'))
EXECUTE FUNCTION nullify_user_unique_id_while_complete_reject();
`;

const loanStatusCompleteTrigger = `CREATE OR REPLACE FUNCTION public.loan_status_complete_trigger()
RETURNS TRIGGER AS $$
BEGIN
        UPDATE public."MasterEntities"
        SET status = jsonb_set(
            status,
            '{loan}',
            to_jsonb(7),
            true
        )
        WHERE "loanId" = NEW.id;
		IF NEW."userUniqueId" IS NOT NULL THEN
            UPDATE public."loanTransactions"
            SET "userUniqueId" = NULL
            WHERE "id" = NEW."id";
        END IF;
	
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
CREATE OR REPLACE TRIGGER loan_status_complete_trigger
    AFTER UPDATE OF "loanStatus"
    ON public."loanTransactions"
    FOR EACH ROW
    WHEN (new."loanStatus" = 'Complete'::"enum_loanTransactions_loanStatus")
    EXECUTE FUNCTION public.loan_status_complete_trigger();
`;

const loanStatusRejectedTrigger = `CREATE OR REPLACE FUNCTION public.loan_status_rejected_trigger()
RETURNS TRIGGER AS $$
BEGIN
		IF NEW."userUniqueId" IS NOT NULL THEN
            UPDATE public."loanTransactions"
            SET "userUniqueId" = NULL
            WHERE "id" = NEW."id";
        END IF;
	
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
CREATE OR REPLACE TRIGGER loan_status_rejected_trigger
    AFTER UPDATE OF "loanStatus"
    ON public."loanTransactions"
    FOR EACH ROW
    WHEN (new."loanStatus" = 'Rejected'::"enum_loanTransactions_loanStatus")
    EXECUTE FUNCTION public.loan_status_rejected_trigger();
`;

const loanStatusActiveTrigger = `CREATE OR REPLACE FUNCTION public.loan_status_active_trigger()
RETURNS TRIGGER AS $$
BEGIN
UPDATE public."MasterEntities"
    SET status = 
                jsonb_set(
                    jsonb_set(
                        status,                      
                        '{loan}',                             
                        to_jsonb(6),                          
                        true                                  
                    ),
                    '{disbursement}',                      
                    to_jsonb(1),                             
                    true                                     
                ),                                      
			dates = jsonb_set(
                        dates,                      
                        '{disbursement}',                             
                        to_jsonb((extract(epoch FROM now()) * 1000)::bigint),                         
                        true                                  
                    )
    WHERE "loanId" = NEW.id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
CREATE OR REPLACE TRIGGER loan_status_active_trigger
    AFTER UPDATE OF "loanStatus"
    ON public."loanTransactions"
    FOR EACH ROW
    WHEN (new."loanStatus" = 'Active'::"enum_loanTransactions_loanStatus")
    EXECUTE FUNCTION public.loan_status_active_trigger();
`;

export const allTriggers = [
  updateHashPhone,
  updateLoanUserDetails,
  // updateUserUniqueId,
  loanStatusCompleteTrigger,
  loanStatusRejectedTrigger,
  loanStatusActiveTrigger,
];
