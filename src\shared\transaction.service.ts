// Imports
import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { fn, literal, Op, Sequelize } from 'sequelize';
import { LegalService } from 'src/admin/legal/legal.service';
import { k500Error } from 'src/constants/misc';
import * as fs from 'fs';
import {
  k422ErrorMessage,
  kBadRequest,
  kInternalError,
  kParamMissing,
  kParamsMissing,
} from 'src/constants/responses';
import {
  KICICIUPI,
  KICICIUPI2,
  KYESUPI,
  k1159Tail,
  kAmountGreaterThan,
  kAmountLessThanPIF,
  kAmountLessThanPrincipalAndInt,
  kApp,
  kAutoDebit,
  kAutoDebitFailedSubject,
  kAutoDebitInitiatedSubject,
  kCAMS,
  kCalBySystem,
  kCashfree,
  kCollectionEmail,
  kCollectionPhone,
  kCompleted,
  kCredit,
  kCreditPay,
  kCreditTransaction,
  kCreditTransactionDirect,
  kDirectBankPay,
  kEMIPay,
  kErrorMsgs,
  kFailed,
  kFullPay,
  kGlobalTrail,
  kInitiated,
  kLoanClosureStr,
  kLoanSettled,
  kNoDataFound,
  kNoReplyMail,
  kPartPay,
  kPleaceEnterValidSubmissionDate,
  kPleaseProvideFutureDue,
  kRazorpay,
  kRefund,
  kSDK,
  kSomthinfWentWrong,
  kSplitRefundable,
  kTransactionIdExists,
  kUpi,
  kWeb,
  kWrongSourceType,
  kYouReachedAutoDebitLimit,
  kfinvu,
  kPaymentLinkTitle,
  kPaymentWaiverTitle,
  kHelpContact,
} from 'src/constants/strings';
import {
  HOST_URL,
  ECS_BOUNCE_CHARGE,
  Latest_Version,
  penaltyChargesObj,
  ICICI_CREDS,
  MSG91,
  REKYCDAYS,
  CASE_FILLED,
  SUMMONS,
  WARRENT,
} from 'src/constants/globals';
import { EmiEntity } from 'src/entities/emi.entity';
import { TransactionEntity } from 'src/entities/transaction.entity';
import { registeredUsers } from 'src/entities/user.entity';
import { AdminRepository } from 'src/repositories/admin.repository';
import { ChangeLogsRepository } from 'src/repositories/changeLogs.repository';
import { CrmRepository } from 'src/repositories/crm.repository';
import { EMIRepository } from 'src/repositories/emi.repository';
import { LoanRepository } from 'src/repositories/loan.repository';
import { MasterRepository } from 'src/repositories/master.repository';
import { StampRepository } from 'src/repositories/stamp.repository';
import { TransactionRepository } from 'src/repositories/transaction.repository';
import { UserActivityRepository } from 'src/repositories/user.activity.repository';
import { UserRepository } from 'src/repositories/user.repository';
import { CashFreeService } from 'src/thirdParty/cashfree/cashfree.service';
import { RazorpoayService } from 'src/thirdParty/razorpay/razorpay.service';
import { CryptService } from 'src/utils/crypt.service';
import { DateService } from 'src/utils/date.service';
import { RazorpayService } from 'src/utils/razorpay.service';
import { TypeService } from 'src/utils/type.service';
import { CalculationSharedService } from './calculation.service';
import { CommonSharedService } from './common.shared.service';
import { SharedNotificationService } from './services/notification.service';
import {
  GLOBAL_RANGES,
  SYSTEM_ADMIN_ID,
  UPI_SERVICE,
  MAX_AUTO_DEBIT_COUNT,
  promoCodeRemark,
  gIsPROD,
  UAT_PHONE_NUMBER,
  disburseAmt,
} from 'src/constants/globals';
import {
  CASHFREE_HEADERS,
  kLspMsg91Templates,
  kMsg91Templates,
  ptpCrmIds,
  kPaymentMode,
  CLOUD_FOLDER_PATH,
} from 'src/constants/objects';
import { mandateEntity } from 'src/entities/mandate.entity';
import { SubScriptionEntity } from 'src/entities/subscription.entity';
import { loanTransaction } from 'src/entities/loan.entity';
import { esignEntity } from 'src/entities/esign.entity';
import { BankingEntity } from 'src/entities/banking.entity';
import {
  CF_RETURN_URL,
  CF_SUBSCRIPTION,
  nPaymentRedirect,
} from 'src/constants/network';
import { SigndeskService } from 'src/thirdParty/signdesk/signdesk.service';
import { PromoCodeService } from './promo.code.service';
import { WhatsAppService } from 'src/thirdParty/whatsApp/whatsApp.service';
import { ICICIThirdParty } from 'src/thirdParty/icici/icici.service';
import { APIService } from 'src/utils/api.service';
import { StringService } from 'src/utils/string.service';
import { SystemTraceEntity } from 'src/entities/system_trace.entity';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { ChangeLogsEntity } from 'src/entities/change.logs.entity';
import { v4 as uuidv4 } from 'uuid';
import { LogsSharedService } from './logs.service';
import { UserSharedLogTrackerMiddleware } from './logtracker.middleware';
import { UserLogTrackerRepository } from 'src/repositories/userLogTracker.repository';
import { EnvConfig } from 'src/configs/env.config';
import { FileService } from 'src/utils/file.service';
import { SequelOptions } from 'src/interfaces/include.options';
import { UserServiceV4 } from 'src/v4/user/user.service.v4';
import {
  kAutoDebitFailed,
  kAutoDebitInitiated,
  kLoanClosure,
  kSettlementLoan,
  tLoanSettlementTemplate,
} from 'src/constants/directories';
import { PaymentLinkEntity } from 'src/entities/paymentLink.entity';
import { ActiveLoanAddressesEntity } from 'src/entities/activeLoanAddress.entity';
import { TransactionInitializedArchiveEntity } from 'src/entities/transactionInitializedArchive.entity';
import { AllsmsService } from 'src/thirdParty/SMS/sms.service';
import { WaiverEntity } from 'src/entities/waiver.entity';
import { FinvuService } from 'src/thirdParty/finvu/finvu.service';
import { PeriodicEntity } from 'src/entities/periodic.entity';
import { SlackService } from 'src/thirdParty/slack/slack.service';
import { ErrorContextService } from 'src/utils/error.context.service';
import { MasterEntity } from 'src/entities/master.entity';
import { BankingSharedService } from './banking.service';
import { YESService } from 'src/thirdParty/yes/yes.service';
import { ReportService } from 'src/admin/report/report.service';
import { EMIService } from 'src/admin/emi/emi.service';
import { CamsServiceThirdParty } from 'src/thirdParty/cams/cams.service.thirdParty';
import { RedisService } from 'src/redis/redis.service';
import { NUMBERS } from 'src/constants/numbers';
import { LegalCollectionEntity } from 'src/entities/legal.collection.entity';
import { CRYPT_PATH } from 'src/constants/paths';
import { creditTransactionService } from './creditTransactions.shared.service';

@Injectable()
export class SharedTransactionService {
  constructor(
    // Database
    private readonly repoManager: RepositoryManager,
    private readonly emiRepo: EMIRepository,
    private readonly typeService: TypeService,
    private readonly strService: StringService,
    private readonly crmRepo: CrmRepository,
    private readonly dateService: DateService,
    private readonly emiService: EMIService,
    private readonly transactionRepo: TransactionRepository,
    private readonly userRepo: UserRepository,
    private readonly cryptService: CryptService,
    private readonly razorpaySer: RazorpayService,
    private readonly adminRepo: AdminRepository,
    private readonly stampRepo: StampRepository,
    private readonly changeLogsRepo: ChangeLogsRepository,
    private readonly masterRepository: MasterRepository,
    @Inject(forwardRef(() => CalculationSharedService))
    private readonly sharedCalculation: CalculationSharedService,
    private readonly legalService: LegalService,
    private readonly userActivityRepo: UserActivityRepository,
    private readonly sharedNotification: SharedNotificationService,
    private readonly cashFreeService: CashFreeService,
    private readonly loanRepo: LoanRepository,
    private readonly commonSharedService: CommonSharedService,
    private readonly razorpayService: RazorpoayService,
    private readonly signDeskService: SigndeskService,
    private readonly whatsappService: WhatsAppService,
    private readonly apiService: APIService,
    private readonly fileService: FileService,
    @Inject(forwardRef(() => UserServiceV4))
    private readonly userServiceV4: UserServiceV4,
    // Shared
    private readonly promoCodeService: PromoCodeService,
    @Inject(forwardRef(() => ICICIThirdParty))
    private readonly iciciService: ICICIThirdParty,
    @Inject(forwardRef(() => LogsSharedService))
    private readonly logsSharedService: LogsSharedService,
    @Inject(forwardRef(() => UserSharedLogTrackerMiddleware))
    private readonly userSharedLogTrackerMiddleware: UserSharedLogTrackerMiddleware,
    private readonly userLogTrackerRepository: UserLogTrackerRepository,
    private readonly allsmsService: AllsmsService,
    @Inject(forwardRef(() => FinvuService))
    private readonly finvuService: FinvuService,
    private readonly slackService: SlackService,
    private readonly errorContextService: ErrorContextService,
    @Inject(forwardRef(() => BankingSharedService))
    private readonly bankingSharedService: BankingSharedService,
    private readonly changeLogRepo: ChangeLogsRepository,
    @Inject(forwardRef(() => YESService))
    private readonly yesUpiService: YESService,
    @Inject(forwardRef(() => ReportService))
    private readonly reportService: ReportService,
    @Inject(forwardRef(() => CamsServiceThirdParty))
    private readonly camsService: CamsServiceThirdParty,
    private readonly redisService: RedisService,
    @Inject(forwardRef(() => creditTransactionService))
    private readonly creditTransService: creditTransactionService,
  ) {}

  async splitTransaction(paidAmount, loanId, iGst = false) {
    if (!loanId || isNaN(+loanId)) return kParamMissing('loanId');

    const transInclude: SequelOptions = { model: TransactionEntity };
    transInclude.attributes = [
      'principalAmount',
      'interestAmount',
      'penaltyAmount',
      'cgstOnBounceCharge',
      'sgstOnBounceCharge',
      'igstOnBounceCharge',
      'bounceCharge',
      'penalCharge',
      'regInterestAmount',
      'cgstOnPenalCharge',
      'igstOnPenalCharge',
      'sgstOnPenalCharge',
      'legalCharge',
      'sgstOnLegalCharge',
      'cgstOnLegalCharge',
      'igstOnLegalCharge',
    ];
    transInclude.required = false;
    transInclude.where = { status: kCompleted };
    const include = [transInclude];

    const emiAttributes = [
      'id',
      'partPaymentPenaltyAmount',
      'principalCovered',
      'interestCalculate',
      'penalty',
      'bounceCharge',
      'gstOnBounceCharge',
      'dpdAmount',
      'penaltyChargesGST',
      'regInterestAmount',
      'legalCharge',
      'legalChargeGST',
    ];
    const emiOptions = {
      include,
      where: { loanId, payment_status: '0', payment_due_status: '1' },
    };

    const emiList = await this.repoManager.getTableWhereData(
      EmiEntity,
      emiAttributes,
      emiOptions,
    );
    if (!iGst) {
      for (let i = 0; i < emiList.length; i++) {
        try {
          let ele = emiList[i];
          let cGstOnPenal = this.typeService.manageAmount(
            (ele.penaltyChargesGST ?? 0) / 2,
          );
          let sGstOnPenal = this.typeService.manageAmount(
            (ele.penaltyChargesGST ?? 0) / 2,
          );
          ele.penaltyChargesGST = cGstOnPenal + sGstOnPenal;
        } catch (error) {}
      }
    }
    const splitResult = this.sharedCalculation.splitPaymentsforPI({
      paidAmount,
      emiList,
      loanId,
      iGst,
    });
    return splitResult;
  }

  async checkCFOrder(
    loanId: number,
    checkAllPending = false,
    returnAffected = false,
  ) {
    try {
      const minDate = new Date();
      minDate.setDate(minDate.getDate() - 180);

      // Last 2 Days pending
      const maxLastDate = new Date();
      maxLastDate.setDate(maxLastDate.getDate() - 2880);

      const attributes = [
        'emiId',
        'id',
        'paidAmount',
        'transactionId',
        'type',
        'userId',
        'source',
        'response',
        'subSource',
      ];
      const where: any = {
        loanId,
        status: 'INITIALIZED',
        source: checkAllPending
          ? ['CASHFREE', 'RAZORPAY', 'ICICI_UPI']
          : [kCashfree],
        subSource: { [Op.ne]: kAutoDebit },
        transactionId: { [Op.ne]: null },
      };
      if (!checkAllPending) where.updatedAt = { [Op.gte]: minDate };
      else where.updatedAt = { [Op.gte]: maxLastDate };
      const options: any = { where };
      options.order = [['id', 'DESC']];

      const transactionList = await this.transactionRepo.getTableWhereData(
        attributes,
        options,
      );
      if (transactionList == k500Error) return kInternalError;
      else if (transactionList.length == 0) return false;
      const returnData = [];
      let paymentResponse;
      for (let index = 0; index < transactionList.length; index++) {
        try {
          const data = transactionList[index];
          let response;
          // Cashfree
          if (data.source === kCashfree)
            response = await this.cashFreeService.checkPayment(
              data.transactionId,
              data?.response,
            );
          // Razorpay
          else if (data.source === kRazorpay)
            response = await this.razorpaySer.checkPayment(data.transactionId);
          // UPIs
          else if (data?.source === KICICIUPI || data?.source === KICICIUPI2) {
            const transactionId = data?.transactionId;
            const transactionType = 'C';
            response = await this.iciciService.CallbackStatus({
              transactionId,
              transactionType,
            });
          }
          if (response != k500Error && response != false) {
            if (
              response.status == 'COMPLETED' ||
              ((data?.source === KICICIUPI || data?.source === KICICIUPI2) &&
                response.status == 'FAILED')
            ) {
              const paymentData: any = { id: data.id, status: response.status };
              paymentData.response = response?.response;
              paymentData.utr = response?.utr;
              if (data?.source === KICICIUPI || data?.source === KICICIUPI2)
                paymentData.utr = response?.OriginalBankRRN;
              paymentData.completionDate = response?.paymentDate.toJSON();
              paymentData.paymentTime = response?.paymentTime;
              paymentData.type = data.type;
              paymentData.loanId = loanId;
              paymentData.userId = data.userId;
              paymentData.subSource = data.subSource;
              if (data.emiId) paymentData.emiId = data.emiId;
              if (returnAffected)
                returnData.push({
                  emiId: data.emiId,
                  loanId,
                  userId: data.userId,
                  status: response.status,
                  transactionId: data.transactionId,
                });
              else {
                await this.markTransactionAsComplete(paymentData);
                return {
                  amount: data.paidAmount,
                  status: paymentData.status,
                  transactionId: data.transactionId.replace('CFORDER', ''),
                };
              }
            } else if (response.status == 'INITIALIZED') {
              try {
                let update;
                if (response.response) update = { response: response.response };
                else if ((data?.response ?? '').includes('IP_LIMIT')) {
                  const tempRes = JSON.parse(data?.response);
                  delete tempRes.iplimit;
                  update = { response: JSON.stringify(tempRes) };
                }
                if (update)
                  await this.transactionRepo.updateRowData(update, data.id);
              } catch (er) {}
            }
            paymentResponse = response;
          }
        } catch (error) {}
      }
      if (returnAffected) return returnData;
      else return paymentResponse;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region markTransactionAsComplete
  async markTransactionAsComplete(paymentData: any) {
    try {
      // Params preparation
      const id = paymentData.id;
      const loanId = paymentData.loanId;
      const userId = paymentData.userId;
      const status = paymentData.status;
      const isBifurcated = paymentData.isBifurcated ?? false;

      // Skipping Transaction Operations If Already Done
      if (!isBifurcated) {
        const previousStatus = await this.redisService.setIfNotExistsWithNX(
          `${userId}_${id}_${status}`,
          NUMBERS.ONE_MINUTE_IN_SECONDS,
        );
        if (!previousStatus) return {};
      }

      let isLoanClosure = false;
      let isLoanSettled = false;
      let isCreditTrans = false;
      let isDirectCreditTrans = false;
      const updatedData: any = { status };
      updatedData.completionDate = paymentData.completionDate;
      updatedData.paymentTime = paymentData.paymentTime;
      updatedData.response = paymentData.response;
      if (paymentData.utr) updatedData.utr = paymentData.utr;
      let dataWaiver;
      let adminId;
      let updateWaiver = false;
      let needNotify = true;
      let subSource = paymentData?.subSource;
      const ip: any = await this.logsSharedService.getRecentIPData(loanId);

      // Successful payment (COMPLETED)
      if (status === kCompleted) {
        const options: any = { where: { id } };
        let attributes = [
          'id',
          'source',
          'subSource',
          'subStatus',
          'settled_type',
          'usedCreditAmount',
        ];
        // Get All The Attributes Only For Active Ontime Bucket Users
        let isUserFromActiveOntimeBucket =
          await this.isUserFromActiveOntimeBucket(userId);
        if (isUserFromActiveOntimeBucket) attributes = null;
        // Get INITIALIZED Transaction
        const trans = await this.transactionRepo.getRowWhereData(
          attributes,
          options,
        );
        if (trans === k500Error || trans === null) return kInternalError;
        subSource = trans?.subSource;
        // Handling Transactions Where User Used Credit
        let subStatus = trans?.subStatus;
        if (
          (subStatus == kLoanClosureStr || subStatus == kLoanSettled) &&
          (trans?.usedCreditAmount ?? 0) > 0
        )
          subStatus = kCreditTransaction;
        isCreditTrans =
          trans?.usedCreditAmount > 0 && subStatus == kCreditTransaction;
        isDirectCreditTrans = subStatus === kCreditTransactionDirect;

        if (isUserFromActiveOntimeBucket) {
          if (
            trans?.settled_type != 'WAIVER_SETTLED' &&
            !paymentData?.dummyPayment &&
            !isBifurcated &&
            subStatus === kCreditTransaction
          ) {
            if (
              (trans?.type == kPartPay &&
                (trans?.adminId == SYSTEM_ADMIN_ID ||
                  trans?.adminId == null)) ||
              trans?.type == kFullPay
            ) {
              const coverWaiver: any =
                await this.creditTransService.coverWaiverByCredit({
                  userId,
                  loanId,
                });
              if (coverWaiver?.mesasge) coverWaiver;
            }
          }

          if (isCreditTrans || isDirectCreditTrans) {
            const creditTransPaymentData = {
              transactionData: trans,
              thisTransCreditAmount: trans?.usedCreditAmount,
              paymentData,
            };
            // This will cover waiver and use credit as a payment
            const creditTrans = await this.creditTransService.useCredit(
              creditTransPaymentData,
              isDirectCreditTrans,
            );
            if (creditTrans?.message || creditTrans === false)
              return creditTrans;
          }
        }
        // Checking for Waiver Transaction only When settled_type == WAIVER_SETTLED
        if (trans.settled_type == 'WAIVER_SETTLED')
          updateWaiver = await this.updateWaiverTransaction(id, paymentData);

        if (updateWaiver != true) {
          await this.checkAutoDebitResAndUpdatePenalty(id);

          // Validate -> Bifurcation
          const reCalculation = await this.reCalculateBifurcation(id, {
            ...updatedData,
            isBifurcated,
          });
          if (reCalculation.message) return reCalculation;
          if (reCalculation.recalculated) return {};
          if (reCalculation.updatedData) {
            Object.assign(updatedData, reCalculation.updatedData);
            paymentData.emiId = reCalculation.updatedData.emiId;
          }
        }

        // Param for Loan Closure Transactions
        if (
          paymentData?.status === kCompleted &&
          trans?.subStatus === kLoanClosureStr &&
          paymentData?.type === kFullPay
        )
          isLoanClosure = true;
        // Param for Loan Settlement Transactions
        if (
          paymentData?.status === kCompleted &&
          trans?.subStatus === kLoanSettled &&
          paymentData?.type === kFullPay
        )
          isLoanSettled = true;
        let repayAccount = '-';
        if (
          trans.source === kRazorpay &&
          (subSource === kApp || subSource === kWeb)
        ) {
          repayAccount = 'RAZORPAY-1';
        } else if (trans.source === kRazorpay && subSource === kAutoDebit) {
          repayAccount = 'RAZORPAY-2';
        } else if (trans.source === kCashfree) {
          repayAccount = kCashfree;
        } else if (trans.source === KICICIUPI) {
          repayAccount = 'UPI [ICICI Bank - 753]';
        } else if (trans.source === KICICIUPI2) {
          repayAccount = 'UPI [ICICI Bank - 400]';
        } else if (trans.source === KYESUPI)
          repayAccount = 'UPI [Yes Bank - 556]';
        else if (
          subSource === kDirectBankPay ||
          subSource === 'DIRECT ICICI' ||
          subSource === 'ICICI DIRECT - CASH' ||
          subSource === 'ICICI MANUAL' ||
          trans.source === kUpi
        ) {
          repayAccount = 'BANK TRANSFER[ICICI BANK - 30400]';
        }

        const city = await this.userSharedLogTrackerMiddleware.getUserData(
          userId,
        );
        const deviceId = await this.logsSharedService.getDeviceId(userId);
        let brand: any = '';
        let model = '';
        if (deviceId?.deviceInfo == null) {
          deviceId.webDeviceInfo = deviceId?.webDeviceInfo
            ? JSON.parse(deviceId?.webDeviceInfo)
            : {};
          brand = deviceId?.webDeviceInfo?.user_agent ?? '-';
          model = '';
        } else {
          deviceId.deviceInfo = deviceId?.deviceInfo
            ? JSON.parse(deviceId?.deviceInfo)
            : {};
          brand =
            deviceId?.deviceInfo?.brand ?? deviceId?.deviceInfo?.name ?? '';
          model = deviceId?.deviceInfo?.model ?? '-';
        }
        if (!brand) {
          brand = '-';
        }
        if (model === '-') {
          model = '';
        }
        const device = [brand, model].filter((part) => part !== '-').join(' ');

        const IPData = await this.logsSharedService.getLocationByIp(ip);
        const otherDetails = {
          utr: paymentData?.utr ?? '-',
          repayAccount,
          modeOfPayment: subSource ?? '-',
          accountDeducted: '-',
          device: device || '-',
        };
        const passData = {
          userId,
          stage: `${paymentData.type} - ${'Payment successful'} `,
          loanId,
          ip: ip ?? '-',
          deviceId: '-',
          city: city?.city ?? '-',
          ipLocation: IPData?.ipLocation ?? '-',
          ipCountry: IPData?.ipCountry ?? '-',
          otherDetails,
        };
        await this.userLogTrackerRepository.create(passData);
      }

      // Update transaction data
      const updateResponse = await this.transactionRepo.updateRowData(
        updatedData,
        id,
      );

      if (updateResponse == k500Error) return kInternalError;
      //check case assing tobe collectin or not
      if (updatedData.status == kFailed) {
        await this.addEcsBounceCharge(paymentData);
        await this.legalService.makeLegalEligible(
          id,
          loanId,
          userId,
          paymentData.type == kFullPay,
        );
        //send Failed notification
        // await this.sendFailedAutoPaySms(userId, id);
      }
      if (
        status == kFailed &&
        paymentData.type == 'PARTPAY' &&
        subSource == kAutoDebit
      )
        needNotify = false;
      // send payment notification
      if (needNotify)
        await this.sendPaymentSuccessNotificationToUser(userId, id);

      if (paymentData.status != kCompleted) return;

      // Update full pay data
      if (paymentData.type == kFullPay && updateWaiver != true) {
        const data = await this.calculationPIAfterComplited(id);
        if (data?.dueAmount) {
          dataWaiver = data.dataWaiver;
          if (data?.adminId) adminId = data.adminId;
        }
        if (dataWaiver && !isCreditTrans && !isDirectCreditTrans)
          await this.updateWaiver(dataWaiver, adminId, paymentData, ip, data);
        const update = await this.sharedCalculation.getFullPayDataByTransId(
          id,
          isCreditTrans,
          dataWaiver,
        );
        if (update?.message) return update;
      }
      // Update EMI
      else if (paymentData.type == kPartPay || paymentData.type == kEMIPay) {
        const result = await this.checkAndUpdateEMIPartPay(
          id,
          paymentData.emiId,
        );
        if (result === false) return false;
      } else if (updateWaiver == true) {
        await this.createUserNoc({ loanId });
        return false;
      }

      await this.checkUserStatus(loanId);
      // Check details with PTP for defaulter users
      await this.checkPTPTransactionStatus(loanId, id);

      // update paid bifurcation in EMI (paid Principal, paid Interest, paid Penalty)
      await this.reCalculatePaidAmounts({ loanId, isCreditTrans });

      // Sync function -> Calculate CLTV after transaction completion
      this.sharedCalculation
        .calculateCLTV({ loanIds: [loanId] })
        .catch((err) => {});

      // Close the loan if all expected amount recovered
      const canCompleteLoan = await this.isEligibleForLoanClose(loanId);
      if (canCompleteLoan) {
        const data = await this.closeTheLoan({
          loanId,
          userId,
          isLoanClosure,
          isLoanSettled,
        });
        await this.validateTwoStepResponse(userId).catch((error) => {});
        return data;
      }
      if (status == kCompleted)
        await this.legalService.funCheckLegalAssingToCollection({ loanId });

      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  async validateTwoStepResponse(userId) {
    const createdAt = new Date();
    createdAt.setHours(createdAt.getHours() - 24);
    const toDate = new Date();
    const periodicData = await this.repoManager.getRowWhereData(
      PeriodicEntity,
      ['sessionId'],
      {
        where: {
          type: 2,
          status: 3,
          userId,
          createdAt: { [Op.gte]: createdAt, [Op.lte]: toDate },
        },
        order: [['createdAt', 'DESC']],
      },
    );
    if (periodicData === k500Error) throw new Error();

    if (periodicData) {
      const attr = [
        'id',
        'accountDetails',
        'salaryDate',
        'salary',
        'otherDetails',
      ];
      const options = { where: { userId }, order: [['id', 'DESC']] };
      const bankData = await this.repoManager.getRowWhereData(
        BankingEntity,
        attr,
        options,
      );
      if (bankData == k500Error) throw new Error();

      const prepareObj = {
        accountDetails: bankData?.accountDetails
          ? JSON.parse(bankData?.accountDetails)
          : null,
        salaryDate: bankData?.salaryDate,
        userId,
        salary: bankData?.salary,
        otherDetails: bankData?.otherDetails,
      };
      await this.bankingSharedService.checkLatestTransactions(prepareObj);
    }
    return {};
  }

  async addEcsBounceCharge(paymentData) {
    try {
      if (paymentData.subSource != kAutoDebit) return;
      if (!paymentData.emiId) return;
      const loanInclude = {
        model: loanTransaction,
        attributes: ['penaltyCharges'],
      };
      const attributes = ['penalty', 'totalPenalty'];
      const options = {
        where: {
          id: paymentData.emiId,
          payment_status: '0',
          bounceCharge: 0,
        },
        include: [loanInclude],
      };

      const emiData = await this.emiRepo.getRowWhereData(attributes, options);
      if (!emiData) return {};
      let gstOnBounceCharge = 0;
      let penalty = emiData.penalty ?? 0;
      let totalPenalty = emiData?.totalPenalty ?? 0;
      let bounceCharge = emiData?.bounceCharge ?? 0;

      if (emiData?.loan?.penaltyCharges?.MODIFICATION_CALCULATION) {
        gstOnBounceCharge = 0;
      } else {
        penalty = +(penalty + ECS_BOUNCE_CHARGE).toFixed(2);
        totalPenalty = +(totalPenalty + ECS_BOUNCE_CHARGE).toFixed(2);
      }
      bounceCharge = ECS_BOUNCE_CHARGE;
      const sessionId = uuidv4();
      const systemCreationData = {
        sessionId,
        type: 1,
        emiId: paymentData.emiId,
        loanId: paymentData.loanId,
        userId: paymentData.userId,
        uniqueId: `TYPE=${1}=EMI=` + paymentData.emiId,
      };
      const createdData = await this.repoManager.createRowData(
        SystemTraceEntity,
        systemCreationData,
      );
      if (createdData === k500Error) return kInternalError;

      // Update -> EMI row data
      await this.repoManager.updateRowData(
        EmiEntity,
        {
          penalty,
          totalPenalty,
          gstOnBounceCharge,
          bounceCharge,
        },
        paymentData.emiId,
      );
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region update transaction
  private async updateWaiverTransaction(id, paymentData) {
    try {
      const att = [
        'loanId',
        'paidAmount',
        'type',
        'userId',
        'adminId',
        'source',
      ];
      const settled_type = 'WAIVER_SETTLED';
      const where = {
        id,
        settled_type,
        status: 'INITIALIZED',
        transactionId: { [Op.ne]: null },
      };
      const find = await this.transactionRepo.getRowWhereData(att, { where });
      if (!find) return;
      if (find === k500Error) return kInternalError;
      const body = { loanId: find.loanId, amount: find.paidAmount };
      /// find loan Data
      const loanData: any = await this.getLoanDataWaiver(body);
      const isCreditTrans = find?.source === kCredit ? true : false;
      if (loanData?.message) return loanData;
      const prePareData: any = isCreditTrans
        ? await this.creditTransService.prePareAmountForWaiver(loanData, body)
        : await this.prePareAmountForWaiver(loanData, body);
      if (prePareData?.message) return prePareData;
      const followerId = loanData?.followerId ?? SYSTEM_ADMIN_ID;
      const type = prePareData?.finalData.type;
      const iGst = loanData?.penaltyCharges?.I_GST ?? false;
      for (let index = 0; index < prePareData.emiList.length; index++) {
        try {
          const emiData = prePareData.emiList[index];
          const emi = prePareData.emiList[index]['emi'];
          const coverAmount =
            emiData.coverPrincipal +
            emiData.coverInterest +
            emiData.coverPenalty +
            emiData.coverRegInterest +
            emiData.coverBounce +
            emiData.coverPenal +
            emiData.coverLegal +
            emiData.coverForeclose;
          if (coverAmount > 0) {
            let waiver =
              (emi?.waiver ?? 0) +
              (emi?.paid_waiver ?? 0) +
              (emi?.unpaid_waiver ?? 0);
            let penalty = emi.penalty;
            let emi_amount = +emi.emi_amount;
            emi_amount += emiData.coverPrincipal;
            emi_amount += emiData.coverInterest;
            let fullPayPrincipal = emi.fullPayPrincipal;
            let fullPayInterest = emi.fullPayInterest;
            let fullPayPenalty = emi.fullPayPenalty;
            let fullPayRegInterest = emi.fullPayRegInterest;
            let fullPayBounce = emi.fullPayBounce;
            let fullPayPenal = emi.fullPayPenal;
            let fullPayLegalCharge = emi.fullPayLegalCharge;
            let fullPayForeclose =
              (emi.forClosureAmount ?? 0) +
              (emi.sgstForClosureCharge ?? 0) +
              (emi.cgstForClosureCharge ?? 0) +
              (emi.igstForClosureCharge ?? 0);
            let pay_type = emi.pay_type;
            if (type === 'FULLPAY') {
              fullPayPrincipal += emiData.coverPrincipal;
              fullPayInterest += emiData.coverInterest;
              fullPayPenalty += emiData.coverPenalty;
              fullPayRegInterest += emiData.coverRegInterest;
              fullPayBounce += emiData.coverBounce;
              fullPayPenal += emiData.coverPenal;
              fullPayLegalCharge += emiData.coverLegal;
              fullPayForeclose += emiData.coverForeclose;
              pay_type = 'FULLPAY';
            }

            if (coverAmount >= waiver) waiver = 0;
            else waiver -= coverAmount;
            if (waiver < 10) waiver = 0;
            if (emi_amount > emi.principalCovered + emi.interestCalculate)
              emi_amount = emi.principalCovered + emi.interestCalculate;
            // Adding Charges Back to EMIs

            // Deferred Int
            let regInterestAmount =
              (emi?.regInterestAmount ?? 0) + emiData?.coverRegInterest;
            // Bounce Charge
            let bounceCharge = emi?.bounceCharge ?? 0;
            let gstOnBounceCharge = 0;
            if (emiData?.coverBounce > 0) {
              bounceCharge =
                (emi?.bounceCharge ?? 0) +
                (emi?.gstOnBounceCharge ?? 0) +
                emiData?.coverBounce;

              bounceCharge = +(bounceCharge - gstOnBounceCharge).toFixed(2);
            }
            // Penalty
            penalty += emiData.coverPenalty;
            // Penal
            let dpdAmount =
              (emi?.dpdAmount ?? 0) +
              (emi?.penaltyChargesGST ?? 0) +
              emiData?.coverPenal;
            let penaltyChargesGST = 0;
            dpdAmount = +(dpdAmount - penaltyChargesGST).toFixed(2);
            // Legal
            let legalCharge =
              (emi?.legalCharge ?? 0) +
              (emi?.legalChargeGST ?? 0) +
              emiData?.coverLegal;
            let legalChargeGST = +(legalCharge - legalCharge / 1.18).toFixed(2);
            legalCharge = legalCharge - legalChargeGST;

            // Foreclose
            let forecloseCharge =
              (emi?.forClosureAmount ?? 0) +
              (emi?.sgstForClosureCharge ?? 0) +
              (emi?.cgstForClosureCharge ?? 0) +
              (emi?.igstForClosureCharge ?? 0) +
              emiData?.coverForeclose;
            let fetchGst = this.fetchGstFromAmount(forecloseCharge, !iGst);
            let forClosureAmount = fetchGst?.pure ?? 0;
            let sgstForClosureCharge = fetchGst?.sgst ?? 0;
            let cgstForClosureCharge = fetchGst?.cgst ?? 0;
            let igstForClosureCharge = fetchGst?.gst ?? 0;

            // Reducing Waived Amts in EMI
            let waived_regInterest =
              (emi?.waived_regInterest ?? 0) - (emiData?.coverRegInterest ?? 0);
            if (waived_regInterest < 1) waived_regInterest = 0;
            let waived_bounce =
              (emi?.waived_bounce ?? 0) - emiData?.coverBounce;
            if (waived_bounce < 1) waived_bounce = 0;
            let waived_penal = (emi?.waived_penal ?? 0) - emiData?.coverPenal;
            if (waived_penal < 1) waived_penal = 0;
            let waived_legal = (emi?.waived_legal ?? 0) - emiData?.coverLegal;
            if (waived_legal < 1) waived_legal = 0;
            let waived_foreclose =
              (emi?.waived_foreclose ?? 0) - emiData?.coverForeclose;
            if (waived_foreclose < 1) waived_foreclose = 0;

            // Adding Paid Charges
            let paidRegInterestAmount =
              (emi?.paidRegInterestAmount ?? 0) + emiData?.coverRegInterest;
            let paidBounceCharge =
              (emi?.paidBounceCharge ?? 0) + emiData?.coverBounce;
            let paidPenalCharge =
              (emi?.paidPenalCharge ?? 0) + emiData?.coverPenal;
            let paidLegalCharge =
              (emi?.paidLegalCharge ?? 0) + emiData?.coverLegal;

            let updateData = {
              penalty: this.typeService.manageAmount(penalty),
              waiver: this.typeService.manageAmount(waiver, disburseAmt),
              emi_amount: this.typeService.manageAmount(emi_amount),
              paid_waiver: 0,
              unpaid_waiver: 0,
              fullPayPrincipal: this.typeService.manageAmount(fullPayPrincipal),
              fullPayInterest: this.typeService.manageAmount(fullPayInterest),
              fullPayPenalty: this.typeService.manageAmount(fullPayPenalty),
              fullPayRegInterest,
              fullPayBounce,
              fullPayPenal,
              fullPayLegalCharge,
              pay_type,
              regInterestAmount,
              bounceCharge,
              gstOnBounceCharge,
              dpdAmount,
              penaltyChargesGST,
              legalCharge,
              legalChargeGST,
              waived_regInterest,
              waived_bounce,
              waived_penal,
              waived_legal,
              paidRegInterestAmount,
              paidBounceCharge,
              paidPenalCharge,
              paidLegalCharge,
              waived_foreclose,
              forClosureAmount,
              sgstForClosureCharge,
              cgstForClosureCharge,
              igstForClosureCharge,
            };
            if (
              updateData.legalCharge + updateData.legalChargeGST > 5902 ||
              updateData.bounceCharge + updateData.gstOnBounceCharge > 592
            ) {
              const text = '*Extra Legal/Bounce Adding While Waiver Trans.*';
              const bodyDetails = {
                emiData,
              };
              const threads = [
                `Body details -> ${JSON.stringify(bodyDetails)}`,
                `Prepared Data -> ${JSON.stringify(prePareData)}`,
                `Lona Data -> ${JSON.stringify({ loanData })}`,
              ];
              this.slackService.sendMsg({ text, threads });
            }
            if (gstOnBounceCharge == 0) delete updateData.gstOnBounceCharge;
            await this.emiRepo.updateRowData(updateData, emiData.id);

            // Update Transaction for Waiver Payment
            const splitWaiverTransaction: any =
              await this.splitWaiverTransaction({
                emiData,
                transaction: find,
                index,
                id,
                iGst,
                utr: paymentData.utr,
                completionDate: paymentData.completionDate,
                paymentTime: paymentData.paymentTime,
                coverAmount,
              });
            if (splitWaiverTransaction == k500Error)
              throw new Error('Error in Updating Transaction for Waiver');

            if (coverAmount > 0) {
              let coveredEmiAmount =
                emiData?.coverPrincipal + emiData?.coverInterest;
              let coveredDiff = emiData?.coverRegInterest;
              let waiverObj = {
                emiId: emiData.id,
                emiAmount: coveredEmiAmount > 0 ? -coveredEmiAmount : 0,
                deferredInterest: coveredDiff > 0 ? -coveredDiff : 0,
                bounceCharge:
                  emiData.coverBounce > 0 ? -emiData.coverBounce : 0,
                penalty: emiData?.coverPenalty > 0 ? -emiData?.coverPenalty : 0,
                penalCharge: emiData?.coverPenal > 0 ? -emiData?.coverPenal : 0,
                legalCharge: emiData?.coverLegal > 0 ? -emiData?.coverLegal : 0,
                forecloseCharge:
                  emiData?.coverForeclose > 0 ? -emiData?.coverForeclose : 0,
              };
              let data = {
                oldBifurcation: {},
                newBifurcation: {},
                waiverBifurcation: waiverObj,
                waiverAmount: -coverAmount,
                loanId: find.loanId,
                userId: find.userId,
                emiId: emiData.id,
                adminId: find.adminId ?? SYSTEM_ADMIN_ID,
                type: 'WAIVER_PAID',
                followerId,
              };
              let updateWaiverEntry = await this.repoManager.createRowData(
                WaiverEntity,
                data,
              );
              if (updateWaiverEntry == k500Error) return kInternalError;
            }
          }
        } catch (error) {}
      }
      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region  get loan Data for waiver
  async getLoanDataWaiver(body) {
    try {
      const loanId = body.loanId;
      /// transaction
      const tranInclude: any = { model: TransactionEntity };
      tranInclude.attributes = [
        'id',
        'paidAmount',
        'status',
        'principalAmount',
        'interestAmount',
        'penaltyAmount',
        'regInterestAmount',
        'bounceCharge',
        'sgstOnBounceCharge',
        'cgstOnBounceCharge',
        'igstOnBounceCharge',
        'penalCharge',
        'sgstOnPenalCharge',
        'cgstOnPenalCharge',
        'igstOnPenalCharge',
        'legalCharge',
        'sgstOnLegalCharge',
        'cgstOnLegalCharge',
        'igstOnLegalCharge',
        'emiId',
        'forClosureAmount',
        'sgstForClosureCharge',
        'cgstForClosureCharge',
        'igstForClosureCharge',
      ];
      tranInclude.where = { status: 'COMPLETED' };
      tranInclude.required = false;
      // Emi
      const attributes = [
        'id',
        'emi_amount',
        'emi_date',
        'payment_status',
        'penalty',
        'principalCovered',
        'interestCalculate',
        'regInterestAmount',
        'bounceCharge',
        'gstOnBounceCharge',
        'dpdAmount',
        'penaltyChargesGST',
        'legalCharge',
        'legalChargeGST',
        'partPaymentPenaltyAmount',
        'pay_type',
        'waiver',
        'paid_waiver',
        'unpaid_waiver',
        'fullPayPrincipal',
        'fullPayInterest',
        'fullPayPenalty',
        'fullPayRegInterest',
        'fullPayBounce',
        'fullPayPenal',
        'fullPayLegalCharge',
        'waived_regInterest',
        'waived_bounce',
        'waived_penal',
        'waived_legal',
        'paidRegInterestAmount',
        'paidBounceCharge',
        'paidPenalCharge',
        'paidLegalCharge',
        'waived_foreclose',
        'forClosureAmount',
        'sgstForClosureCharge',
        'cgstForClosureCharge',
        'igstForClosureCharge',
        'payment_due_status',
      ];

      const emiInclude: any = { model: EmiEntity, attributes };
      emiInclude.where = {
        [Op.or]: {
          waiver: { [Op.gt]: 0 },
          paid_waiver: { [Op.gt]: 0 },
          unpaid_waiver: { [Op.gt]: 0 },
        },
      };
      // user
      const userInclude: any = { model: registeredUsers };
      userInclude.attributes = [
        'id',
        'email',
        'phone',
        'fullName',
        'allPhone',
        'appType',
      ];
      // Mandate
      const include = [userInclude, emiInclude, tranInclude];

      // Loan
      const att = [
        'id',
        'loanStatus',
        'loan_disbursement_date',
        'interestRate',
        'netApprovedAmount',
        'followerId',
        'penaltyCharges',
        'appType',
      ];
      const where: any = { id: loanId };
      const options = { include, where };
      const loanData = await this.loanRepo.getRowWhereData(att, options);
      if (!loanData || loanData === k500Error) return kInternalError;
      loanData.registeredUsers.phone = this.cryptService.decryptPhone(
        loanData.registeredUsers?.phone,
      );
      return loanData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  async checkAutoDebitResAndUpdatePenalty(id) {
    try {
      // Preparation -> Query
      const where = { payment_due_status: '1' };
      const emiModel = {
        model: EmiEntity,
        where,
        attributes: ['id', 'emi_date'],
      };
      const whereOps = {
        id,
        subSource: kAutoDebit,
        status: kInitiated,
        type: kEMIPay,
        transactionId: { [Op.ne]: null },
      };
      const options = { where: whereOps, include: [emiModel] };
      const att = ['id', 'createdAt'];
      // Hit -> Query
      const result = await this.transactionRepo.getRowWhereData(att, options);
      // Validation -> Query data
      if (!result || result == k500Error) return kInternalError;
      // update
      const date = this.typeService.getGlobalDate(result.createdAt);
      const emiDate = this.typeService.getGlobalDate(result.emiData.emi_date);
      if (date.getTime() <= emiDate.getTime()) {
        // Create -> System trace row data
        const systemCreationData = {
          sessionId: uuidv4(),
          type: 2,
          emiId: result.emiData?.id,
        };
        const createdData = await this.repoManager.createRowData(
          SystemTraceEntity,
          systemCreationData,
        );
        if (createdData === k500Error) throw new Error();

        const emiId = result.emiData.id;
        const update = {
          penalty: null,
          penalty_days: null,
          payment_due_status: '0',
          totalPenalty: 0,
          dpdAmount: 0,
          regInterestAmount: 0,
          penaltyChargesGST: 0,
          penaltyCharges: penaltyChargesObj,
        };
        const emiWhere = { emi_date: { [Op.gt]: date }, ...where };

        return await this.emiRepo.updateRowDataWithOptions(
          update,
          emiWhere,
          emiId,
        );
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // Re-calculation of bifurcation (Principal, Interest, Penalty)
  async reCalculateBifurcation(id, paymentData: any = {}) {
    if (paymentData == kFullPay) return {};
    if (paymentData?.type == kFullPay) return {};
    if (paymentData.isBifurcated) return {};

    // Transaction validation
    const att = [
      'adminId',
      'paidAmount',
      'loanId',
      'emiId',
      'source',
      'status',
      'subSource',
      'subscriptionDate',
      'transactionId',
      'userId',
      'maxDPD',
      'utr',
    ];
    const options = { where: { id } };
    const transData = await this.transactionRepo.getRowWhereData(att, options);

    if (!transData) return k422ErrorMessage(kNoDataFound);
    if (transData == k500Error) throw new Error();
    if (transData.status == kCompleted)
      return k422ErrorMessage('Transaction already completed');

    // Loan validation
    const data = { ...transData, amount: transData?.paidAmount };
    if (!data?.emiId) return {};
    const canBifurcate = await this.canCheckBifurcation(data?.emiId); // Only delayed payments needs to check for re calculation

    if (!canBifurcate) return {};
    // Loan data
    const loanData = await this.getLoanData(data, true);
    if (!loanData) return k422ErrorMessage(kNoDataFound);
    if (loanData == k500Error) throw new Error();
    const iGst = loanData?.penaltyCharges?.I_GST ?? false;

    // Re-calculation validation
    const status = paymentData.status;
    if (status != kCompleted)
      return k422ErrorMessage('Transaction is not completed');

    // Bifurcation logic
    const bifurcationList: any = await this.splitTransaction(
      transData?.paidAmount,
      loanData.id,
      iGst,
    );
    if (bifurcationList.message) return bifurcationList;
    // No split required
    if (bifurcationList.length == 1) {
      const updatedData = bifurcationList[0];
      if (updatedData?.principalAmount)
        updatedData.principalAmount = this.typeService.manageAmount(
          updatedData?.principalAmount,
        );
      if (updatedData?.interestAmount)
        updatedData.interestAmount = this.typeService.manageAmount(
          updatedData?.interestAmount,
        );
      if (updatedData?.penaltyAmount)
        updatedData.penaltyAmount = this.typeService.manageAmount(
          updatedData?.penaltyAmount,
        );
      if (updatedData?.paidAmount)
        updatedData.paidAmount = this.typeService.manageAmount(
          updatedData?.paidAmount,
        );
      if (updatedData?.roundOff) updatedData.roundOff = updatedData?.roundOff;
      delete updatedData.dummyPayment;
      if (
        updatedData?.principalAmount == 0 &&
        updatedData?.interestAmount == 0 &&
        Math.abs(updatedData?.paidAmount - updatedData?.penaltyAmount) < 5
      )
        return { recalculated: true };

      return { reCalculated: false, updatedData };
    }
    // Check for duplication
    else if (bifurcationList.length > 1) {
      try {
        const isRefundedAmt = bifurcationList.find(
          (el) => el.isRefundable === true,
        );
        if (isRefundedAmt) {
          if (transData.transactionId) {
            const transAttr = ['id'];
            const transOptions = {
              where: {
                status: kCompleted,
                transactionId: transData.transactionId,
              },
            };
            const postTransData = await this.repoManager.getRowWhereData(
              TransactionEntity,
              transAttr,
              transOptions,
            );
            if (postTransData && postTransData != k500Error) {
              return k422ErrorMessage('Transaction already completed');
            }
          }
        }
      } catch (error) {}
    }
    /// this for update refund in last
    bifurcationList.sort(
      (a, b) =>
        ((a?.isRefundable ?? false) === true ? 1 : 0) -
        ((b?.isRefundable ?? false) === true ? 1 : 0),
    );

    // Split required
    let bifurcatedPaidAmount = 0;
    for (let index = 0; index < bifurcationList.length; index++) {
      try {
        const creationData = bifurcationList[index];
        bifurcatedPaidAmount += creationData?.paidAmount;
        if (bifurcatedPaidAmount > transData?.paidAmount) {
          const text = '*Error For Transction Bifurcation - Wrong Split*';
          const body = {
            transData,
            loanData,
            paymentData,
          };
          const threads = [
            `Loan And Transaction Data -> ${JSON.stringify(body)}`,
            `Bifurcated Transaction Data  -> ${JSON.stringify(
              bifurcationList,
            )}`,
          ];
          this.slackService.sendMsg({ text, threads });
          continue;
        }

        const isRefundable = creationData.isRefundable ?? false;
        delete creationData.isRefundable;
        creationData.accStatus = kCalBySystem;
        creationData.adminId = transData.adminId;
        creationData.followerId =
          loanData?.followerId ??
          (transData?.adminId != SYSTEM_ADMIN_ID ? transData?.adminId : null);
        creationData.type = kPartPay;
        creationData.completionDate = paymentData.completionDate;
        creationData.paymentTime = paymentData.paymentTime;
        creationData.loanId = transData.loanId;
        creationData.userId = transData.userId;
        creationData.status = isRefundable ? kCompleted : kInitiated;
        creationData.source = transData.source;
        creationData.subSource = transData.subSource;
        creationData.subscriptionDate = transData.subscriptionDate;
        creationData.response = { corePayment: transData };
        creationData.response = JSON.stringify(creationData.response);
        creationData.maxDPD = transData?.maxDPD;

        if (isRefundable) {
          creationData.subStatus = kSplitRefundable;
          creationData.emiId = data?.emiId;
        }

        let updatedData;
        // Update core payment (converting into dummy payment)
        if (index == 0) {
          creationData.utr = paymentData.utr ?? transData?.utr;
          updatedData = await this.transactionRepo.updateRowData(
            creationData,
            id,
          );
          if (updatedData == k500Error) return kInternalError;
          creationData.status = status;
          creationData.id = id;

          // Transaction should not iterate again if dummy payment is refundable else it will go in loop for too many times
          if (!isRefundable) {
            creationData.status = kCompleted;
            creationData.isBifurcated = true;
            await this.markTransactionAsComplete(creationData);
          }
        }
        // Create remaining dummy payments
        else {
          const tail = `DMY${index + 1}`;
          creationData.transactionId = transData.transactionId + tail;
          creationData.utr = (paymentData.utr ?? transData?.utr) + tail;

          updatedData = await this.transactionRepo.createRowData(creationData);
          if (updatedData == k500Error) return kInternalError;
          updatedData.status = status;
          // Transaction should not iterate again if dummy payment is refundable else it will go in loop for too many times
          if (!isRefundable) {
            updatedData.status = kCompleted;
            updatedData.isBifurcated = true;
            updatedData.subSource = creationData.subSource;
            await this.markTransactionAsComplete(updatedData);
          }
        }
      } catch (error) {
        console.log(error);
        this.errorContextService.throwAndSetCtxErr(error);
      }
    }
    return { recalculated: true };
  }

  // private async sendFailedAutoPaySms(userId, paymentId) {
  //   try {
  //     const attributes = [
  //       'paidAmount',
  //       'utr',
  //       'loanId',
  //       'completionDate',
  //       'createdAt',
  //       'adminId',
  //       'status',
  //       'subSource',
  //     ];
  //     const loanInclude = {
  //       model: loanTransaction,
  //       attributes: ['appType'],
  //     };
  //     const options = { where: { id: paymentId }, include: [loanInclude] };
  //     const transData = await this.transactionRepo.getRowWhereData(
  //       attributes,
  //       options,
  //     );
  //     if (transData == k500Error) return kInternalError;
  //     if (!transData) return k422ErrorMessage(kNoDataFound);

  //     const paidAmount = (transData.paidAmount ?? 0).toFixed(2);
  //     const paidDate = transData?.completionDate ?? transData?.createdAt;
  //     const date = this.typeService.getDateFormatted(paidDate);
  //     const loanId = transData?.loanId;
  //     const status = transData?.status;
  //     const subSource = transData?.subSource;
  //     const appType = transData?.loanData?.appType;
  //     const title = 'Auto Debit Response Failed';
  //     const failedSMSId =
  //       appType == 1
  //         ? kMsg91Templates.PaymentFailedSMSId
  //         : kLspMsg91Templates.PaymentFailedSMSId;

  //     //send whatsapp
  //     if (status === kFailed && subSource == 'AUTODEBIT') {
  //       return await this.sharedNotification.sendLegalSMS(
  //         userId,
  //         appType,
  //         failedSMSId,
  //         title,
  //         493,
  //       );
  //     }
  //     return null;
  //   } catch (error) {
  //
  //     return kInternalError;
  //   }
  // }

  async sendPaymentSuccessNotificationToUser(userId, paymentId) {
    try {
      const attributes = [
        'paidAmount',
        'utr',
        'loanId',
        'completionDate',
        'subscriptionDate',
        'createdAt',
        'adminId',
        'status',
        'subSource',
        'response',
        'type',
      ];
      const loanInclude = {
        model: loanTransaction,
        attributes: ['appType'],
      };
      const options = { where: { id: paymentId }, include: [loanInclude] };
      const transData = await this.transactionRepo.getRowWhereData(
        attributes,
        options,
      );
      if (transData == k500Error) return kInternalError;
      if (!transData) return k422ErrorMessage(kNoDataFound);

      const paidAmount = (transData.paidAmount ?? 0).toFixed(2);
      const paidDate = transData?.completionDate ?? transData?.createdAt;
      const scheduledDate = transData.subscriptionDate ?? paidDate;
      const date = this.typeService.getDateFormatted(paidDate);
      const adminId = transData?.adminId;
      const loanId = transData?.loanId;
      const utr = transData.utr ?? '';
      const type = transData?.type;
      const status = transData?.status;
      const subSource = transData?.subSource;
      const appType = transData?.loanData?.appType;
      const response = transData?.response
        ? JSON.parse(transData?.response)
        : {};

      const autoDebitFailedResponse =
        response?.payment?.failureReason ??
        response?.response?.data?.message ??
        response?.error_description ??
        response?.error_message ??
        response?.reason ??
        '';
      // THE FAILED AUTODEBIT SMS ARE SENT THROUGH THIS FUN : sendFailedAutoPaySms()
      // IN CASE YOU UNCOMMENT OR USE THIS FUNCTION MAKE SURE TO REMOVE THE FAILED AUTODEBIT SMS OPTION
      const title =
        status == kCompleted
          ? 'Payment successful'
          : subSource == 'AUTODEBIT'
          ? 'Auto Debit Response Failed'
          : 'Payment failed!';
      let content = '';
      if (status == kCompleted)
        content = `We have received Rs. ${paidAmount} on ${date} towards your loan ${loanId}, login into the app to check the repayment details!`;
      else if (status == kFailed)
        content = `Repayment of Rs.${paidAmount} towards your loan ${loanId} is failed!`;
      const data = {
        // forceRoute: 'dashboardRoute',
        paidAmount,
        utr,
        paymentSuccess: status == kCompleted ? true : false,
      };
      const smsOptions =
        status == kCompleted
          ? { var1: `${paidAmount} on ${date}`, var2: loanId }
          : { var1: kCollectionPhone };
      const successSMSId =
        appType == 1
          ? kMsg91Templates.PaymentSuccessSMSId
          : kLspMsg91Templates.PaymentSuccessSMSId;
      const failedSMSId =
        status == kFailed && subSource == 'AUTODEBIT'
          ? kMsg91Templates.PaymentFailedSMSId
          : kMsg91Templates.PaymentFailedSMS;
      const smsId = status == kCompleted ? successSMSId : failedSMSId;
      const primaryId =
        status == kFailed && subSource == 'AUTODEBIT' ? 493 : null;

      //send whatsapp
      if (
        status === kCompleted ||
        (status === kFailed && subSource == 'AUTODEBIT')
      ) {
        const userData = await this.userRepo.getRowWhereData(
          ['fullName', 'email', 'phone', 'hashPhone'],
          { where: { id: userId } },
        );
        if (userData == k500Error) return kInternalError;
        const key = loanId * 484848;
        const paymentLink = `${nPaymentRedirect}${key}`;

        // check whatsapp number register
        const hashPhones = [userData?.hashPhone];
        const nonWhatsAppHashPhone =
          await this.whatsappService.getNonWhatsAppUsers(hashPhones);

        if (!nonWhatsAppHashPhone.includes(userData?.hashPhone)) {
          let number = this.cryptService.decryptPhone(userData?.phone);
          const whatsappOption = {
            customerName: userData?.fullName,
            email: userData?.email,
            number,
            loanId,
            userId,
            paidAmount: paidAmount,
            paidDate: date,
            adminId,
            title: title,
            requestData: title,
            paymentLink,
            appType,
          };
          this.whatsappService.sendWhatsAppMessageMicroService(whatsappOption);
        }
        if (status === kFailed && type === kEMIPay) {
          const prepareMailData = {
            name: userData?.fullName,
            email: userData?.email,
            loanId,
            paidAmount,
            scheduledDate,
            failedResponse: autoDebitFailedResponse,
            status,
            appType,
            userId,
          };
          await this.sendAutoDebitMail(prepareMailData);
        }
      }
      const userData = [];
      userData.push({ userId, appType });
      const body = {
        userData,
        title,
        content,
        adminId,
        data,
        smsOptions,
        isMsgSent: true,
        smsId,
        id: primaryId,
      };
      return await this.sharedNotification.sendNotificationToUser(body);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region calculation PI After COMPLETED
  async calculationPIAfterComplited(id) {
    try {
      const att = ['paidAmount', 'loanId', 'emiId', 'settled_type', 'adminId'];
      const options = { where: { id } };
      const result = await this.transactionRepo.getRowWhereData(att, options);
      if (!result || result === k500Error) return false;
      const data: any = { ...result, amount: result?.paidAmount };
      if (result?.settled_type === 'FULLPAY_SETTLED') data.isCloseLoan = true;
      if (!data?.emiId) data.emiId = -1;
      const loanData = await this.getLoanData(data, true);
      if (!loanData || loanData === k500Error) return false;
      try {
        loanData?.emiData.forEach((e) => {
          try {
            if (e?.payment_status === '1' && e?.pay_type === 'FULLPAY')
              e.payment_status = '0';
            e?.transactionData.forEach((element) => {
              if (element.id == id) {
                element.status = 'INITIALIZED';
                e.payment_status = '0';
              }
            });
          } catch (error) {}
        });
      } catch (error) {}
      const prePareAmount: any = await this.prePareAmount(data, loanData);

      if (prePareAmount?.dueAmount) {
        if (prePareAmount?.dataWaiver) prePareAmount.adminId = result?.adminId;
        return prePareAmount;
      }
      return false;
    } catch (error) {
      return false;
    }
  }
  //#endregion

  //#region update data Waiver this function use for full pay setteld amount
  private async updateWaiver(dataWaiver, adminId, paymentData, ip, data) {
    try {
      const keys = Object.keys(dataWaiver);
      let waiverAmount = 0;
      let userId = paymentData?.userId;
      let loanId = paymentData.loanId;
      let waiverEmiWise = {};
      let fullpayData = await this.sharedCalculation.getFullPaymentData({
        loanId,
      });
      let oldFullPay = this.prepareFullPayWaiverObj(fullpayData, 'OLD');
      let newFullPay = this.prepareFullPayWaiverObj(data, 'NEW');
      for (let index = 0; index < keys.length; index++) {
        try {
          const key = keys[index];
          const id = +key;
          const value = dataWaiver[key];
          const waiverEMIamount =
            (value['fullPayPrincipal'] ?? 0) + (value['fullPayInterest'] ?? 0);
          const waiverPenalty = value['fullPayPenalty'] ?? 0;
          const waiverRegInterest = value['fullPayRegInterest'] ?? 0;
          const waiverBounce = value['fullPayBounce'] ?? 0;
          const waiverPenal = value['fullPayPenal'] ?? 0;
          const waiverLegal = value['fullPayLegalCharge'] ?? 0;
          const waiverForeclose = value['fullPayForecloseCharge'] ?? 0;
          waiverAmount +=
            waiverEMIamount +
            waiverPenalty +
            waiverRegInterest +
            waiverBounce +
            waiverPenal +
            waiverLegal +
            waiverForeclose;
          if (
            waiverEMIamount +
              waiverPenalty +
              waiverRegInterest +
              waiverBounce +
              waiverPenal +
              waiverLegal +
              waiverForeclose >
            0
          ) {
            const att = [
              'id',
              'emi_amount',
              'principalCovered',
              'interestCalculate',
              'penalty',
              'regInterestAmount',
              'bounceCharge',
              'gstOnBounceCharge',
              'dpdAmount',
              'penaltyChargesGST',
              'legalCharge',
              'legalChargeGST',
              'paid_principal',
              'paid_interest',
              'paidRegInterestAmount',
              'paidBounceCharge',
              'paidPenalCharge',
              'paidLegalCharge',
              'waiver',
              'userId',
              'loanId',
            ];
            const options = { where: { id, payment_status: '0' } };
            const emiData = await this.emiRepo.getRowWhereData(att, options);
            if (!emiData || emiData === k500Error) continue;
            userId = emiData?.userId;
            /// emi amount calculation
            let emi_amount: any = +(emiData?.emi_amount ?? 0) - waiverEMIamount;
            if (emi_amount < 0) emi_amount = 0;
            emi_amount = emi_amount.toFixed(2);
            // penalty amount calculation
            let penalty: any = +(emiData?.penalty ?? 0) - waiverPenalty;
            if (penalty < 0) penalty = 0;
            penalty = +penalty.toFixed(2);
            // Deffered amount calculation
            let regInterestAmount: any =
              +(emiData?.regInterestAmount ?? 0) - waiverRegInterest;
            if (regInterestAmount < 0) regInterestAmount = 0;
            regInterestAmount = +regInterestAmount.toFixed(2);
            // bounceCharge amount calculation
            let bounceCharge: any =
              (emiData?.bounceCharge ?? 0) +
              (emiData?.gstOnBounceCharge ?? 0) -
              waiverBounce;
            if (bounceCharge < 0) bounceCharge = 0;
            let gstOnBounceCharge = 0;
            bounceCharge = +(bounceCharge - gstOnBounceCharge).toFixed(2);
            // penalCharge amount calculation
            let dpdAmount: any =
              (emiData?.dpdAmount ?? 0) +
              (emiData?.penaltyChargesGST ?? 0) -
              waiverPenal;
            if (dpdAmount < 0) dpdAmount = 0;
            let penaltyChargesGST = 0;
            dpdAmount = +(dpdAmount - penaltyChargesGST).toFixed(2);
            // legalcharge amount calculation
            let legalCharge: any =
              (emiData?.legalCharge ?? 0) +
              (emiData?.legalChargeGST ?? 0) -
              waiverLegal;
            if (legalCharge < 0) legalCharge = 0;
            let legalChargeGST = +(legalCharge - legalCharge / 1.18).toFixed(2);
            legalCharge = +(legalCharge - legalChargeGST).toFixed(2);
            // waiver amount calculation
            let waiver =
              (emiData?.waiver ?? 0) +
              waiverEMIamount +
              waiverPenalty +
              waiverRegInterest +
              waiverBounce +
              waiverPenal +
              waiverLegal +
              waiverForeclose;
            waiver = this.typeService.manageAmount(waiver);
            const updateData = {
              emi_amount,
              penalty,
              waiver,
              regInterestAmount,
              bounceCharge,
              gstOnBounceCharge,
              dpdAmount,
              penaltyChargesGST,
              legalCharge,
              legalChargeGST,
            };
            const userActivity = {
              emiId: id,
              waiver_emiAmount: waiverEMIamount,
              waiver_penalty: waiverPenalty,
              waiver_regIntAmount: waiverRegInterest,
              waiver_bounce: waiverBounce,
              waiver_penal: waiverPenal,
              waiver_legal: waiverLegal,
              waiver_foreclose: waiverForeclose,
            };
            let preparedData = this.prepareWaiverObj(emiData, userActivity);
            waiverEmiWise[id] = preparedData.waiverBifurcation;
            const createAcitivity = {
              loanId: emiData?.loanId,
              userId: emiData?.userId,
              type: 'WAIVER_PAID',
              date: this.typeService.getGlobalDate(new Date()).toJSON(),
              respons: JSON.stringify(userActivity),
            };
            this.userActivityRepo.createRowData(createAcitivity);
            await this.sharedCalculation.updateWaivedBifurcation(
              id,
              userActivity,
            );
            if (
              updateData.legalCharge + updateData.legalChargeGST > 5902 ||
              updateData.bounceCharge + updateData.gstOnBounceCharge > 592
            ) {
              const text = '*Extra Legal/Bounce Adding While Fullpay Waiver.*';
              const bodyDetails = {
                emiData,
              };
              const threads = [
                `Body details -> ${JSON.stringify(bodyDetails)}`,
                `Updating Data -> ${JSON.stringify(updateData)}`,
                `emiData -> ${JSON.stringify({ emiData })}`,
              ];
              this.slackService.sendMsg({ text, threads });
            }
            await this.emiRepo.updateRowData(updateData, id);
            const logCreateObj: any = {
              userId: paymentData.userId,
              loanId: paymentData.loanId,
              type: 'Waiver',
              subType: 'Waiver',
              oldData: 0,
              newData:
                waiverEMIamount +
                waiverPenalty +
                waiverRegInterest +
                waiverBounce +
                waiverPenal +
                waiverLegal +
                waiverForeclose,
              adminId,
              ip,
            };
            this.changeLogsRepo.create(logCreateObj);
          }
        } catch (error) {}
      }
      // Updating Waiver Entry with Effected Changes
      const followerId = data?.followerId;
      if (waiverAmount > 0) {
        let preparedObj = {
          oldBifurcation: oldFullPay,
          newBifurcation: newFullPay,
          waiverBifurcation: { ...waiverEmiWise },
          waiverAmount,
          loanId,
          userId,
          emiId: null,
          adminId,
          type: 'FULLPAY',
          followerId,
        };
        let updateWaiverEntry = await this.repoManager.createRowData(
          WaiverEntity,
          preparedObj,
        );
        if (updateWaiverEntry == k500Error) return kInternalError;
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region prepareWaiverObj
  prepareWaiverObj(emiData, waiverData) {
    let emiId = waiverData.emiId;
    let emiAmount = +(
      (emiData.principalCovered ?? 0) +
      (emiData.interestCalculate ?? 0) -
      (emiData.paid_principal ?? 0) -
      (emiData.paid_interest ?? 0)
    ).toFixed(2);
    let deferredInterest = +(
      (emiData.regInterestAmount ?? 0) - (emiData.paidRegInterestAmount ?? 0)
    ).toFixed(2);
    let bounceCharge = +(
      (emiData.bounceCharge ?? 0) +
      (emiData.gstOnBounceCharge ?? 0) -
      (emiData.paidBounceCharge ?? 0)
    ).toFixed(2);
    let penalCharge = +(
      (emiData.dpdAmount ?? 0) +
      (emiData.penaltyChargesGST ?? 0) -
      (emiData.paidPenalCharge ?? 0)
    ).toFixed(2);
    let penalty = +(emiData.penalty ?? 0).toFixed(2);
    let legalCharge = +(
      (emiData.legalCharge ?? 0) +
      (emiData.legalChargeGST ?? 0) -
      (emiData.paidLegalCharge ?? 0)
    ).toFixed(2);
    let forecloseCharge = +(
      (emiData.forClosureAmount ?? 0) +
      (emiData.sgstForClosureCharge ?? 0) +
      (emiData.cgstForClosureCharge ?? 0) +
      (emiData.igstForClosureCharge ?? 0)
    ).toFixed(2);

    let obj = {
      // Bifurcation Before Waiver
      oldBifurcation: {
        emiId,
        emiAmount: Math.max(emiAmount, 0),
        deferredInterest: Math.max(deferredInterest, 0),
        bounceCharge: Math.max(bounceCharge, 0),
        penalCharge: Math.max(penalCharge, 0),
        penalty: Math.max(penalty, 0),
        legalCharge: Math.max(legalCharge, 0),
        forecloseCharge: Math.max(forecloseCharge, 0),
      },
      // Bifurcation After Waiver
      newBifurcation: {
        emiId,
        emiAmount: Math.max(
          +(emiAmount - waiverData.waiver_emiAmount).toFixed(2),
          0,
        ),
        deferredInterest: Math.max(
          +(deferredInterest - waiverData.waiver_regIntAmount).toFixed(2),
          0,
        ),
        bounceCharge: Math.max(
          +(bounceCharge - waiverData.waiver_bounce).toFixed(2),
          0,
        ),
        penalCharge: Math.max(
          +(penalCharge - waiverData.waiver_penal).toFixed(2),
          0,
        ),
        penalty: Math.max(+(penalty - waiverData.waiver_penalty).toFixed(2), 0),
        legalCharge: Math.max(
          +(legalCharge - waiverData.waiver_legal).toFixed(2),
          0,
        ),
        forecloseCharge: Math.max(
          +(forecloseCharge - (waiverData.waiver_foreclose ?? 0)).toFixed(2),
          0,
        ),
      },
      // Bifurcation Of Waiver Given
      waiverBifurcation: {
        emiId,
        emiAmount: Math.max(+waiverData.waiver_emiAmount.toFixed(2), 0),
        deferredInterest: Math.max(
          +waiverData.waiver_regIntAmount.toFixed(2),
          0,
        ),
        bounceCharge: Math.max(+waiverData.waiver_bounce.toFixed(2), 0),
        penalCharge: Math.max(+waiverData.waiver_penal.toFixed(2), 0),
        penalty: Math.max(+waiverData.waiver_penalty.toFixed(2), 0),
        legalCharge: Math.max(+waiverData.waiver_legal.toFixed(2), 0),
        forecloseCharge: Math.max(
          +(waiverData.waiver_foreclose ?? 0).toFixed(2),
          0,
        ),
      },
    };
    return obj;
  }
  //#endregion

  //#region prepareFullPayWaiverObj
  prepareFullPayWaiverObj(data, type) {
    if (!type) return {};
    // Bifurcation of Payment Before Waiver Given
    if (type == 'OLD') {
      return {
        emiAmount:
          (data?.remainingPrincipal ?? 0) +
          (data?.overduePrincipalAmount ?? 0) +
          (data?.remainingInterest ?? 0) +
          (data?.overdueInterestAmount ?? 0),
        deferredInterest: data?.regInterestAmount ?? 0,
        bounceCharge:
          (data?.bounceCharge ?? 0) +
          (data?.sgstBounceCharge ?? 0) +
          (data?.cgstBounceCharge ?? 0),
        penalCharge:
          (data?.penalCharge ?? 0) +
          (data?.remainingPenalty ?? 0) +
          (data?.cgstPenalCharges ?? 0) +
          (data?.sgstPenalCharges ?? 0),
        legalCharge:
          (data?.legalCharges ?? 0) +
          (data?.sgstLegalCharges ?? 0) +
          (data?.cgstLegalCharges ?? 0),
        foreclosureCharge:
          (data?.forClosureAmount ?? 0) +
          (data?.sgstForClosureCharge ?? 0) +
          (data?.cgstForClosureCharge ?? 0),
        totalAmount: data.totalAmount ?? 0,
      };
    }
    // Bifurcation of Payment User Made After Waiver
    if (type == 'NEW') {
      return {
        emiAmount: data.principalAmount + data.interestAmount,
        deferredInterest: data.regInterestAmount,
        bounceCharge:
          data.bounceCharge + data.sgstOnBounceCharge + data.cgstOnBounceCharge,
        penalCharge:
          data.penalCharge + data.sgstOnPenalCharge + data.cgstOnPenalCharge,
        legalCharge:
          data.legalCharge + data.sgstOnLegalCharge + data.cgstOnLegalCharge,
        foreclosureCharge:
          data.forClosureAmount +
          data.sgstForClosureCharge +
          data.cgstForClosureCharge,
        totalAmount: data.dueAmount,
      };
    }
  }
  //#endregion

  //#region update part pay
  private async checkAndUpdateEMIPartPay(id: number, emiId: number) {
    try {
      const loanInclude = {
        model: loanTransaction,
        attributes: ['penaltyCharges', 'isPartPayment'],
      };
      const emiAttr = [
        'id',
        'emi_amount',
        'penalty',
        'partPaymentPenaltyAmount',
        'settledId',
        'pay_type',
        'principalCovered',
        'interestCalculate',
        'regInterestAmount',
        'bounceCharge',
        'gstOnBounceCharge',
        'dpdAmount',
        'penaltyChargesGST',
        'legalCharge',
        'legalChargeGST',
        'payment_status',
        'payment_due_status',
        'loanId',
      ];
      const emiOptions: any = { where: { id: emiId } };
      emiOptions.include = loanInclude;
      const emiData = await this.emiRepo.getRowWhereData(emiAttr, emiOptions);
      if (!emiData || emiData == k500Error) return false;

      // for old users bounce charge is alreafy included in penalty
      if (!emiData?.loan?.penaltyCharges?.MODIFICATION_CALCULATION)
        emiData.bounceCharge = 0;

      if (emiData?.payment_status == '1') return true;
      const att = [
        'id',
        'paidAmount',
        'principalAmount',
        'interestAmount',
        'penaltyAmount',
        'regInterestAmount',
        'bounceCharge',
        'cgstOnBounceCharge',
        'sgstOnBounceCharge',
        'igstOnBounceCharge',
        'penalCharge',
        'cgstOnPenalCharge',
        'sgstOnPenalCharge',
        'igstOnPenalCharge',
        'legalCharge',
        'cgstOnLegalCharge',
        'sgstOnLegalCharge',
        'igstOnLegalCharge',
        'completionDate',
      ];
      const option = { where: { emiId, status: kCompleted } };
      const transactionData = await this.transactionRepo.getTableWhereData(
        att,
        option,
      );
      if (!transactionData || transactionData == k500Error) return false;
      let emiPaidAmount = 0;
      let emiPaidPenalty = 0;
      let emiPaidCharges = 0;
      let idPaidPenaltyAmount = 0;
      let payment_done_date;
      transactionData.forEach((element) => {
        try {
          emiPaidAmount += element?.principalAmount ?? 0;
          emiPaidAmount += element?.interestAmount ?? 0;
          emiPaidPenalty += element?.penaltyAmount ?? 0;
          emiPaidCharges += element?.regInterestAmount ?? 0;
          emiPaidCharges += element?.bounceCharge ?? 0;
          emiPaidCharges += element?.sgstOnBounceCharge ?? 0;
          emiPaidCharges += element?.cgstOnBounceCharge ?? 0;
          emiPaidCharges += element?.igstOnBounceCharge ?? 0;
          emiPaidCharges += element?.penalCharge ?? 0;
          emiPaidCharges += element?.sgstOnPenalCharge ?? 0;
          emiPaidCharges += element?.cgstOnPenalCharge ?? 0;
          emiPaidCharges += element?.igstOnPenalCharge ?? 0;
          emiPaidCharges += element?.legalCharge ?? 0;
          emiPaidCharges += element?.cgstOnLegalCharge ?? 0;
          emiPaidCharges += element?.sgstOnLegalCharge ?? 0;
          emiPaidCharges += element?.igstOnLegalCharge ?? 0;
          if (element.id === id) {
            idPaidPenaltyAmount = element?.penaltyAmount ?? 0;
            payment_done_date = element.completionDate;
          }
        } catch (error) {}
      });

      let emiAmount =
        (emiData?.principalCovered ?? 0) + (emiData?.interestCalculate ?? 0);
      emiAmount -= emiPaidAmount;
      let emiCharges =
        this.typeService.manageAmount(emiData?.regInterestAmount ?? 0) +
        this.typeService.manageAmount(emiData?.bounceCharge ?? 0) +
        this.typeService.manageAmount(emiData?.gstOnBounceCharge ?? 0) +
        this.typeService.manageAmount(emiData?.dpdAmount ?? 0) +
        this.typeService.manageAmount(emiData?.penaltyChargesGST ?? 0) +
        this.typeService.manageAmount(emiData?.legalCharge ?? 0) +
        this.typeService.manageAmount(emiData?.legalChargeGST ?? 0);
      emiCharges -= emiPaidCharges;
      const partPenaltyAmount = +(emiData?.partPaymentPenaltyAmount ?? 0);
      let emi_amount = +(emiData.emi_amount ?? 0);
      if (emi_amount > emiAmount) emi_amount = emiAmount;
      let penalty = +(emiData.penalty ?? 0);
      const partPaymentPenaltyAmount = emiPaidPenalty;
      if (
        idPaidPenaltyAmount &&
        !(
          partPenaltyAmount + 10 > emiPaidPenalty &&
          partPenaltyAmount - 10 < emiPaidPenalty
        )
      )
        penalty -= idPaidPenaltyAmount;

      if (emi_amount < 0) emi_amount = 0;
      if (penalty < 0) penalty = 0;
      if (emiCharges < 0) emiCharges = 0;

      const updatedData: any = {};
      if (emi_amount <= 10 && penalty <= 10 && emiCharges <= 10) {
        emi_amount = +(emiData.emi_amount ?? 0);
        updatedData.payment_done_date = payment_done_date;
        updatedData.payment_status = '1';
      }
      updatedData.emi_amount = emi_amount;
      updatedData.penalty = penalty;
      updatedData.partPaymentPenaltyAmount = partPaymentPenaltyAmount;
      if (updatedData.payment_status === '1')
        updatedData.pay_type = emiData?.pay_type ?? 'EMIPAY';
      const updateResponse = await this.emiRepo.updateRowData(
        updatedData,
        emiId,
      );
      if (updateResponse == k500Error) return false;
      // Disabling The Part Pay Option (if it's enabled by admin)
      if (
        emiData?.payment_due_status === '1' &&
        updatedData.payment_status === '1' &&
        emiData?.loan?.isPartPayment
      ) {
        const emiList = await this.emiRepo.getTableWhereData(
          ['payment_status', 'payment_due_status'],
          {
            where: { loanId: emiData?.loanId },
          },
        );
        if (emiList == k500Error) return kInternalError;
        let canUpdate = true;
        emiList.forEach((ele) => {
          if (ele?.payment_status == '0' && ele?.payment_due_status == '1')
            canUpdate = false;
        });
        if (canUpdate) {
          const update = await this.loanRepo.updateRowData(
            { isPartPayment: 0, partPayEnabledBy: SYSTEM_ADMIN_ID },
            emiData?.loanId,
          );
          if (update == k500Error) return kInternalError;
        }
      }
    } catch (error) {
      return false;
    }
  }
  //#endregion

  async isEligibleForLoanClose(loanId: number) {
    try {
      if (!loanId) return false;
      let options: any = { where: { loanId } };
      let attributes = [
        'payment_done_date',
        'payment_status',
        'interestCalculate',
        'principalCovered',
        'fullPayInterest',
        'pay_type',
      ];
      const emiList = await this.emiRepo.getTableWhereData(attributes, options);
      let totalExpectedAmount = 0;

      for (let index = 0; index < emiList.length; index++) {
        try {
          const emi = emiList[index];
          const principalAmount = +(emi?.principalCovered ?? 0);
          const interestAmount = +(emi?.interestCalculate ?? 0);
          if (emi?.pay_type == 'FULLPAY') {
            const fullPayInterest = Math.ceil(+emi?.fullPayInterest);
            totalExpectedAmount += principalAmount + fullPayInterest;
          } else totalExpectedAmount += principalAmount + interestAmount;
        } catch (error) {}
      }
      totalExpectedAmount = Math.ceil(totalExpectedAmount);

      if (emiList == k500Error || emiList.length === 0) return false;

      const paidEMIs = emiList.filter(
        (el) => el.payment_status == '1' && el.payment_done_date != null,
      );
      const isAllEMIPaid = paidEMIs.length === emiList.length;
      // Calculate paid amount
      attributes = ['paidAmount'];
      options = { where: { loanId, status: kCompleted } };
      const transList = await this.transactionRepo.getTableWhereData(
        attributes,
        options,
      );
      if (transList == k500Error) return false;

      const paidAmount = transList.reduce(
        (prev, curr) => prev + curr.paidAmount,
        0,
      );
      // Calculate raw EMI amount
      const isPrincipalPaid = paidAmount >= totalExpectedAmount;

      return isAllEMIPaid && isPrincipalPaid;
    } catch (error) {
      return false;
    }
  }

  async closeTheLoan(targetData: any) {
    try {
      const loanId = targetData.loanId;
      const userId = targetData.userId;
      const isLoanClosure = targetData?.isLoanClosure ?? false;
      const isLoanSettled = targetData?.isLoanSettled ?? false;
      const loanCompletionDate = this.typeService
        .getGlobalDate(new Date())
        .toJSON();

      const loanData: any = {
        loanStatus: 'Complete',
        loanCompletionDate,
        check_total_principal_paid_date: new Date(),
        isLoanClosure,
        isLoanSettled,
      };

      // Updating Credit in Loan Only For Active Ontime Bucket Users
      let isUserFromActiveOntimeBucket =
        await this.isUserFromActiveOntimeBucket(userId);
      let loanStatus = false; // false Means  -> Defaulter
      if (isUserFromActiveOntimeBucket) {
        const creditUsed: any = await this.getUsedCredit(loanId, userId);
        if (creditUsed?.mesasge) return creditUsed;
        loanData.penaltyCharges = creditUsed?.penaltyCharges ?? {};
        if (creditUsed?.loanStaus == 1) loanStatus = true; // 1 -> Ontime
      }
      if (isUserFromActiveOntimeBucket && loanStatus)
        isUserFromActiveOntimeBucket = false;

      // Sending Slack Message for Credit Pay Users
      if (isUserFromActiveOntimeBucket) {
        const text = `*CREDIT USER - LOAN CLOSING l-${loanId}*`;
        const body = {
          isUserFromActiveOntimeBucket,
          loanData,
          targetData,
        };
        const threads = [`Body details -> ${JSON.stringify(body)}`];
        this.slackService.sendMsg({ text, threads });
      }

      const updateLoanData = await this.loanRepo.updateRowData(
        loanData,
        loanId,
      );
      if (updateLoanData == k500Error) return kInternalError;
      await this.legalService.legalCloseLegalAndCloseLoan({
        loanIds: [loanId],
      });
      if (!isUserFromActiveOntimeBucket) await this.createUserNoc({ loanId });
      const loanInclude = {
        model: loanTransaction,
        attributes: ['appType', 'loanClosureEnabledBy', 'isLoanClosure'],
      };
      const masterData = await this.masterRepository.getRowWhereData(
        ['id', 'status'],
        { where: { loanId }, order: [['id', 'DESC']], include: [loanInclude] },
      );
      if (masterData === k500Error) return kInternalError;

      // checking for loan is closed with partial payment then add entry in change log Entity
      // comment it because set it in trigger
      // masterData.status.loan = 7;
      // const updateMaster = await this.masterRepository.updateRowData(
      //   masterData,
      //   masterData.id,
      // );
      // if (updateMaster == k500Error) return kInternalError;
      const userData = await this.userRepo.getRowWhereData(
        ['completedLoans', 'loanStatus', 'phone', 'typeOfDevice'],
        { where: { id: userId } },
      );
      if (userData == k500Error || !userData) return kInternalError;

      const updateUserData = await this.userRepo.updateRowData(
        { pinCrm: null },
        userId,
      );
      if (updateUserData == k500Error) return kInternalError;
      // temporary redis code commented due to PROD issue
      // const key = `${userId}_USER_BASIC_DETAILS`;
      // await this.redisService.del(key);

      // sent review link to ontime loan completion users
      const appType = masterData.loanData?.appType;
      if (userData?.loanStatus === 1 && loanData?.loanStatus === 'Complete') {
        await this.sharedNotification.sendReviewMessage({
          phoneNumber: userData?.phone,
          loanId,
          appType,
          typeOfDevice: userData?.typeOfDevice,
        });
      }

      await this.userServiceV4.routeDetails({ id: userId });

      // update status of active loan address
      try {
        await this.repoManager.updateRowWhereData(
          ActiveLoanAddressesEntity,
          { isActive: false },
          { where: { loanId } },
        );
      } catch (error) {}
      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region getUsedCredit
  async getUsedCredit(loanId, userId) {
    if (!userId) return kParamMissing('userId');
    if (!loanId) return kParamMissing('loanId');

    // Covering Waiver for Full Pay Case
    const coverWaiver: any = await this.creditTransService.coverWaiverByCredit({
      userId,
      loanId,
    });
    if (coverWaiver?.mesasge) coverWaiver;

    // Get User Data
    const user = await this.userRepo.getRowWhereData(
      ['remainingCredit', 'loanStatus'],
      {
        where: {
          id: userId,
        },
      },
    );
    if (user == k500Error)
      throw new Error('Error in Getting remainingCredit from User Table');
    const remainingAmount = user?.remainingCredit;

    // Get Loan Data
    let loan = await this.loanRepo.getRowWhereData(['penaltyCharges'], {
      where: { id: loanId },
    });
    if (loan == k500Error) throw new Error('Error in Getting Loan Data');

    // Get Trans. Data
    let attributes = ['paidAmount'];
    let options = {
      where: { loanId, status: kCompleted, subSource: kCreditPay },
    };
    const transList = await this.transactionRepo.getTableWhereData(
      attributes,
      options,
    );
    let paidCredit = transList.reduce(
      (prev, curr) => prev + curr.paidAmount,
      0,
    );

    let data = {};
    if (paidCredit < 0) paidCredit = 0;
    if (loan?.penaltyCharges) {
      data = { ...loan?.penaltyCharges, creditUsed: paidCredit };
    } else {
      data = {
        creditUsed: paidCredit,
      };
    }
    return { penaltyCharges: data, loanStaus: user?.loanStatus ?? 3 };
  }

  //#region closeTheLoanForCreditUsers
  async closeTheLoanForCreditUsers(targetData: any) {
    try {
      const isLoanClosure = targetData?.isLoanClosure ?? false;
      const isLoanSettled = targetData?.isLoanSettled ?? false;
      const loanCompletionDate = this.typeService
        .getGlobalDate(new Date())
        .toJSON();
      const loanData = {
        loanStatus: 'Complete',
        loanCompletionDate,
        check_total_principal_paid_date: new Date(),
        isLoanClosure,
        isLoanSettled,
      };
      const loanId = targetData.loanId;
      const updateLoanData = await this.loanRepo.updateRowData(
        loanData,
        loanId,
      );
      if (updateLoanData == k500Error) return kInternalError;
      await this.legalService.legalCloseLegalAndCloseLoan({
        loanIds: [loanId],
      });
      const userId = targetData.userId;
      const loanInclude = {
        model: loanTransaction,
        attributes: ['appType', 'loanClosureEnabledBy', 'isLoanClosure'],
      };
      const masterData = await this.masterRepository.getRowWhereData(
        ['id', 'status'],
        { where: { loanId }, order: [['id', 'DESC']], include: [loanInclude] },
      );
      if (masterData === k500Error) return kInternalError;

      // checking for loan is closed with partial payment then add entry in change log Entity
      // comment it because set it in trigger
      // masterData.status.loan = 7;
      // const updateMaster = await this.masterRepository.updateRowData(
      //   masterData,
      //   masterData.id,
      // );
      // if (updateMaster == k500Error) return kInternalError;
      const userData = await this.userRepo.getRowWhereData(
        ['completedLoans', 'loanStatus', 'phone', 'typeOfDevice'],
        { where: { id: userId } },
      );
      if (userData == k500Error || !userData) return kInternalError;

      const updateUserData = await this.userRepo.updateRowData(
        { pinCrm: null },
        userId,
      );
      if (updateUserData == k500Error) return kInternalError;
      // temporary redis code commented due to PROD issue
      // const key = `${userId}_USER_BASIC_DETAILS`;
      // await this.redisService.del(key);

      // sent review link to ontime loan completion users
      // const appType = masterData.loanData?.appType;
      // if (userData?.loanStatus === 1 && loanData?.loanStatus === 'Complete') {
      //   await this.sharedNotification.sendReviewMessage({
      //     phoneNumber: userData?.phone,
      //     loanId,
      //     appType,
      //     typeOfDevice: userData?.typeOfDevice,
      //   });
      // }

      await this.userServiceV4.routeDetails({ id: userId });

      // update status of active loan address
      try {
        await this.repoManager.updateRowWhereData(
          ActiveLoanAddressesEntity,
          { isActive: false },
          { where: { loanId } },
        );
      } catch (error) {}
      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async checkPTPTransactionStatus(loanId, transId) {
    try {
      // Params validation
      if (!loanId) return kParamMissing('loanId');
      if (!transId) return kParamMissing('transId');

      // Get payment data
      let attributes: any = ['completionDate', 'paidAmount'];
      let options: any = {
        where: { id: transId, status: kCompleted },
      };
      // Query
      const transData = await this.transactionRepo.getRowWhereData(
        attributes,
        options,
      );
      if (!transData) return k422ErrorMessage(kNoDataFound);
      if (transData == k500Error) return kInternalError;

      const completionDate = this.typeService.getGlobalDate(
        transData.completionDate,
      );
      const rangeData = this.dateService.utcDateRange(
        new Date(completionDate),
        new Date(completionDate),
      );

      // Query preparation
      const userInclude: { model; attributes? } = { model: registeredUsers };
      userInclude.attributes = ['id', 'lastCrm'];
      const include = [userInclude];
      attributes = ['id', 'totalReceivedAmount', 'createdAt', 'due_date'];
      options = {
        include,
        order: [['id', 'DESC']],
        where: {
          loanId,
          due_date: { [Op.gte]: completionDate.toJSON().substring(0, 10) },
          relationData: {
            [Op.or]: [
              { titleId: { [Op.in]: ptpCrmIds } },
              { reasonId: { [Op.in]: ptpCrmIds } },
            ],
          },
        },
      };
      // Query
      const crmData: any = await this.crmRepo.getRowWhereData(
        attributes,
        options,
      );
      if (crmData === k500Error) return kInternalError;
      else if (!crmData) return k422ErrorMessage(kNoDataFound);
      let sDate = this.typeService
        .getGlobalDate(new Date(crmData?.createdAt))
        .toJSON();
      let eDate = this.typeService
        .getGlobalDate(new Date(crmData?.due_date))
        .toJSON();

      attributes = [
        [Sequelize.fn('SUM', Sequelize.col('paidAmount')), 'amount'],
      ];
      options = {
        where: {
          loanId,
          completionDate: {
            [Op.gte]: sDate,
            [Op.lte]: eDate,
          },
          status: 'COMPLETED',
          type: { [Op.ne]: 'REFUND' },
        },
      };
      const allTransData = await this.transactionRepo.getRowWhereData(
        attributes,
        options,
      );
      if (allTransData === k500Error) return kInternalError;

      // Update data
      const crmId = crmData.id;
      const dueDate = await this.typeService.getGlobalDate(
        crmData?.registeredUsers?.lastCrm?.due_date,
      );
      const paidAmount = allTransData?.amount ?? 0;
      const totalReceived = Math.round(paidAmount);

      // trans_status = 1: Ontime Close, 2: Pre Close
      const updatedData = {
        transactionId: transId,
        trans_status: 2,
        totalReceivedAmount: totalReceived,
      };

      // Convert dueDate and completionDate to date strings with only the date part to Match
      const dueDateString = dueDate.toISOString().split('T')[0];
      const completionDateString = completionDate.toISOString().split('T')[0];

      if (dueDateString === completionDateString) {
        updatedData.trans_status = 1;
      }
      const updatedResponse = await this.crmRepo.updateRowData(
        updatedData,
        crmId,
      );
      if (updatedResponse == k500Error) return kInternalError;

      const userData: any = crmData.registeredUsers ?? {};
      const lastCrmData = userData.lastCrm ?? {};
      if (
        lastCrmData &&
        lastCrmData.due_date &&
        ptpCrmIds.includes(lastCrmData.titleId)
      ) {
        const dueDate = new Date(lastCrmData.due_date);
        if (
          rangeData.minRange.getTime() <= dueDate.getTime() &&
          rangeData.maxRange.getTime() >= dueDate.getTime()
        ) {
          lastCrmData.isPTPPaid = true;
          await this.userRepo.updateRowData(
            { lastCrm: lastCrmData },
            userData.id,
          );
        }
        return {};
      }
      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async checkUserStatus(loanId) {
    try {
      // Table joins
      const emiInclude: { model; attributes? } = { model: EmiEntity };
      emiInclude.attributes = ['payment_due_status', 'payment_status'];
      // Query preparation
      const include = [emiInclude];
      const attributes = ['userId'];
      let options: any = { limit: 1, include, where: { id: loanId } };
      // Query
      const loanList = await this.loanRepo.getTableWhereData(
        attributes,
        options,
      );
      if (loanList == k500Error) return kInternalError;

      const onTimeUsers = [];
      const delayedUsers = [];
      const defaultedUsers = [];
      loanList.forEach((el) => {
        try {
          const userId = el.userId;
          const emiList: any = el.emiData ?? {};
          const isDelayed = emiList.find((el) => el.payment_due_status == '1');
          // On time
          if (!isDelayed) onTimeUsers.push(userId);
          else {
            const isDefaulter = emiList.find(
              (el) => el.payment_due_status == '1' && el.payment_status == '0',
            );
            if (isDefaulter) defaultedUsers.push(userId);
            else delayedUsers.push(userId);
          }
        } catch (error) {}
      });
      // Update defaulter users
      if (defaultedUsers.length > 0) {
        const updatedData = { loanStatus: 3 };
        options = { where: { id: defaultedUsers } };
        await this.userRepo.updateRowWhereData(updatedData, options);
      }
      // Update delayed users
      if (delayedUsers.length > 0) {
        const updatedData = { loanStatus: 2 };
        options = { where: { id: delayedUsers } };
        await this.userRepo.updateRowWhereData(updatedData, options);
      }
      // Update on time users
      if (onTimeUsers.length > 0) {
        const updatedData = { loanStatus: 1 };
        options = { where: { id: onTimeUsers } };
        await this.userRepo.updateRowWhereData(updatedData, options);
      }
    } catch (error) {}
  }

  //#region  prePare amount for waiver
  private async prePareAmountForWaiver(loanData, body) {
    try {
      let amount = body?.amount ?? 0;
      let unPaidWaiveAmount = 0;
      let loanId = body?.loanId;
      const emiList = [];
      let maxDPD = 0;
      const iGst = loanData?.penaltyCharges?.I_GST ?? false;

      const emiData = await this.emiRepo.getTableWhereData(['penalty_days'], {
        where: { loanId, payment_due_status: '1' },
      });
      if (emiData === k500Error) return kInternalError;

      emiData.forEach((ele) => {
        if ((ele.penalty_days ?? 0) > 0 && ele.penalty_days > maxDPD)
          maxDPD = +ele.penalty_days;
      });
      /// pre pare emi amount paid and remanige
      loanData?.emiData.sort((a, b) => a.id - b.id);
      loanData?.emiData.forEach((ele) => {
        if ((ele?.totalPenalty ?? 0) > 0) ele.bounceCharge = 0;
        let waiveAmount = 0;
        waiveAmount += ele?.waiver;
        waiveAmount += ele?.paid_waiver;
        waiveAmount += ele?.unpaid_waiver;
        let paidPrincipal = ele?.fullPayPrincipal ?? 0;
        let paidInterest = ele?.fullPayInterest ?? 0;
        let paidPenalty = ele?.fullPayPenalty ?? 0;
        let paidRegInterest = ele?.fullPayRegInterest ?? 0;
        let paidBounce = ele?.fullPayBounce ?? 0;
        let paidPenal = ele?.fullPayPenal ?? 0;
        let paidLegal = ele?.fullPayLegalCharge ?? 0;
        let paidForeclose =
          (ele?.forClosureAmount ?? 0) +
          (ele?.sgstForClosureCharge ?? 0) +
          (ele?.cgstForClosureCharge ?? 0) +
          (ele?.igstForClosureCharge ?? 0);

        try {
          const filter = loanData.transactionData.filter(
            (f) => f.emiId == ele.id,
          );
          filter.forEach((tran) => {
            paidPrincipal += tran?.principalAmount ?? 0;
            paidInterest += tran?.interestAmount ?? 0;
            paidPenalty += tran?.penaltyAmount ?? 0;
            paidRegInterest += tran?.regInterestAmount ?? 0;
            paidBounce += tran?.bounceCharge ?? 0;
            paidBounce += tran?.sgstOnBounceCharge ?? 0;
            paidBounce += tran?.cgstOnBounceCharge ?? 0;
            paidBounce += tran?.igstOnBounceCharge ?? 0;
            paidPenal += tran?.penalCharge ?? 0;
            paidPenal += tran?.sgstOnPenalCharge ?? 0;
            paidPenal += tran?.cgstOnPenalCharge ?? 0;
            paidPenal += tran?.igstOnPenalCharge ?? 0;
            paidLegal += tran?.legalCharge ?? 0;
            paidLegal += tran?.cgstOnLegalCharge ?? 0;
            paidLegal += tran?.sgstOnLegalCharge ?? 0;
            paidLegal += tran?.igstOnLegalCharge ?? 0;
            paidForeclose += tran?.forClosureAmount ?? 0;
            paidForeclose += tran?.sgstForClosureCharge ?? 0;
            paidForeclose += tran?.cgstForClosureCharge ?? 0;
            paidForeclose += tran?.igstForClosureCharge ?? 0;
          });
        } catch (error) {}
        paidPrincipal = this.typeService.manageAmount(paidPrincipal);
        paidInterest = this.typeService.manageAmount(paidInterest);
        paidPenalty = this.typeService.manageAmount(paidPenalty);
        unPaidWaiveAmount += waiveAmount;
        let diffPrincipal = ele.principalCovered - paidPrincipal;
        if (diffPrincipal < 0) {
          paidPrincipal += diffPrincipal;
          paidInterest += -1 * diffPrincipal;
          diffPrincipal = 0;
        }
        if (ele?.payment_due_status != '1' && ele?.pay_type == 'FULLPAY')
          ele.interestCalculate = ele?.fullPayInterest;
        let diffInterest = ele.interestCalculate - paidInterest;
        if (diffPrincipal > 0) waiveAmount -= diffPrincipal;
        if (diffInterest > 0) waiveAmount -= diffInterest;
        if (diffInterest < 0) {
          paidInterest += diffInterest;
          paidPenalty += -1 * diffInterest;
          diffInterest = 0;
        }

        let diffPenalty = waiveAmount;

        let diffRegInterest = this.typeService.manageAmount(
          ele?.waived_regInterest ?? 0,
        );
        if (diffRegInterest < 0) diffRegInterest = 0;
        diffPenalty -= diffRegInterest;

        let diffBounce = this.typeService.manageAmount(ele?.waived_bounce ?? 0);
        if (diffBounce < 0) diffBounce = 0;
        diffPenalty -= diffBounce;

        let diffPenal = this.typeService.manageAmount(ele?.waived_penal ?? 0);
        if (diffPenal < 0) diffPenal = 0;
        diffPenalty -= diffPenal;

        let diffLegal = this.typeService.manageAmount(ele?.waived_legal ?? 0);
        if (diffLegal < 0) diffLegal = 0;
        diffPenalty -= diffLegal;

        let diffForeclose = this.typeService.manageAmount(
          ele?.waived_foreclose ?? 0,
        );
        if (diffForeclose < 0) diffForeclose = 0;
        diffPenalty -= diffForeclose;

        waiveAmount = 0;
        diffPrincipal = Math.round(diffPrincipal);
        diffInterest = Math.round(diffInterest);
        diffPenalty = Math.round(diffPenalty);

        if (diffPenalty < 0) diffPenalty = 0;
        emiList.push({
          id: ele.id,
          paidPrincipal,
          paidInterest,
          paidPenalty,
          paidRegInterest,
          paidBounce,
          paidPenal,
          paidLegal,
          diffPrincipal,
          diffInterest,
          diffPenalty,
          diffRegInterest,
          diffBounce,
          diffPenal,
          diffLegal,
          waiveAmount,
          coverPrincipal: 0,
          coverInterest: 0,
          coverPenalty: 0,
          coverRegInterest: 0,
          coverBounce: 0,
          coverPenal: 0,
          coverLegal: 0,
          emi: ele,
          diffForeclose,
          paidForeclose,
        });
      });

      if (amount == 0 || amount > unPaidWaiveAmount) amount = unPaidWaiveAmount;
      let finalData = {
        paidAmount: +amount,
        principalAmount: 0,
        interestAmount: 0,
        penaltyAmount: 0,
        regInterestAmount: 0,
        bounceCharge: 0,
        penalCharge: 0,
        legalCharge: 0,
        cgstOnBounceCharge: 0,
        sgstOnBounceCharge: 0,
        igstOnBounceCharge: 0,
        cgstOnPenalCharge: 0,
        sgstOnPenalCharge: 0,
        igstOnPenalCharge: 0,
        cgstOnLegalCharge: 0,
        sgstOnLegalCharge: 0,
        igstOnLegalCharge: 0,
        emiId: null,
        type: 'FULLPAY',
        settled_type: 'WAIVER_SETTLED',
        maxDPD,
        forClosureAmount: 0,
        sgstForClosureCharge: 0,
        cgstForClosureCharge: 0,
        igstForClosureCharge: 0,
      };
      let principalAmount = 0;
      let interestAmount = 0;
      let penaltyAmount = 0;
      let regInterestAmount = 0;
      let bounceCharge = 0;
      let penalCharge = 0;
      let legalCharge = 0;
      let sgstOnBounceCharge = 0;
      let cgstOnBounceCharge = 0;
      let igstOnBounceCharge = 0;
      let sgstOnPenalCharge = 0;
      let cgstOnPenalCharge = 0;
      let igstOnPenalCharge = 0;
      let sgstOnLegalCharge = 0;
      let cgstOnLegalCharge = 0;
      let igstOnLegalCharge = 0;
      let deductedBounceCharge = 0;
      let deductedPenalCharge = 0;
      let deductedLegalCharge = 0;
      let forecloseAmount = 0;
      let sgstForClosureCharge = 0;
      let cgstForClosureCharge = 0;
      let igstForClosureCharge = 0;
      let deductedForecloseAmount = 0;
      if (amount != 0 && amount <= unPaidWaiveAmount) {
        emiList.forEach((emi) => {
          // interest Amount update
          if (emi?.diffInterest >= amount) {
            interestAmount = amount;
            emi.coverInterest = amount;
            amount = 0;
          } else {
            interestAmount = emi?.diffInterest;
            emi.coverInterest = emi.diffInterest;
            amount -= emi?.diffInterest;
          }

          // principal Amount update
          if (emi?.diffPrincipal >= amount) {
            principalAmount = amount;
            emi.coverPrincipal = amount;
            amount = 0;
          } else {
            principalAmount = emi?.diffPrincipal;
            emi.coverPrincipal = emi.diffPrincipal;
            amount -= emi?.diffPrincipal;
          }

          // foreclose Amount update
          if (emi?.diffForeclose >= amount) {
            forecloseAmount = amount;
            emi.coverForeclose = amount;
            amount = 0;
          } else {
            forecloseAmount = emi?.diffForeclose;
            emi.coverForeclose = emi?.diffForeclose;
            amount -= emi?.diffForeclose;
          }
          deductedForecloseAmount = forecloseAmount;

          // penalty Amount update
          if (emi?.diffPenalty >= amount) {
            penaltyAmount = amount;
            emi.coverPenalty = amount;
            amount = 0;
          } else {
            penaltyAmount = emi?.diffPenalty;
            emi.coverPenalty = emi?.diffPenalty;
            amount -= emi?.diffPenalty;
          }

          // Deffered Amount update
          if (emi?.diffRegInterest >= amount) {
            regInterestAmount = amount;
            emi.coverRegInterest = amount;
            amount = 0;
          } else {
            regInterestAmount = emi?.diffRegInterest;
            emi.coverRegInterest = emi?.diffRegInterest;
            amount -= emi?.diffRegInterest;
          }

          //bounce charge update
          if (emi?.diffBounce >= amount) {
            bounceCharge = amount;
            emi.coverBounce = amount;
            amount = 0;
          } else {
            bounceCharge = emi?.diffBounce;
            emi.coverBounce = emi?.diffBounce;
            amount -= emi?.diffBounce;
          }
          deductedBounceCharge = bounceCharge;

          //penal charge update
          if (emi?.diffPenal >= amount) {
            penalCharge = amount;
            emi.coverPenal = amount;
            amount = 0;
          } else {
            penalCharge = emi?.diffPenal;
            emi.coverPenal = emi?.diffPenal;
            amount -= emi?.diffPenal;
          }
          deductedPenalCharge = penalCharge;

          //legal charge update
          if (emi?.diffLegal >= amount) {
            legalCharge = amount;
            emi.coverLegal = amount;
            amount = 0;
          } else {
            legalCharge = emi?.diffLegal;
            emi.coverLegal = emi?.diffLegal;
            amount -= emi?.diffLegal;
          }
          deductedLegalCharge = legalCharge;

          //gst calculation
          const gstBounceChargeAmount = 0;
          let gstPenalChargeAmount = 0;
          sgstOnPenalCharge = iGst
            ? 0
            : this.typeService.manageAmount(gstPenalChargeAmount / 2);
          cgstOnPenalCharge = sgstOnPenalCharge;
          igstOnPenalCharge = !iGst
            ? 0
            : this.typeService.manageAmount(gstPenalChargeAmount);
          gstPenalChargeAmount =
            sgstOnPenalCharge + cgstOnPenalCharge + igstOnPenalCharge;

          const gstLegalChargeAmount = legalCharge - legalCharge / 1.18;
          const gstForecloseAmount = forecloseAmount - forecloseAmount / 1.18;

          bounceCharge = bounceCharge - gstBounceChargeAmount;
          penalCharge = penalCharge - gstPenalChargeAmount;
          legalCharge = legalCharge - gstLegalChargeAmount;
          forecloseAmount = forecloseAmount - gstForecloseAmount;

          sgstOnBounceCharge = iGst
            ? 0
            : this.typeService.manageAmount(gstBounceChargeAmount / 2);
          cgstOnBounceCharge = sgstOnBounceCharge;
          igstOnBounceCharge = !iGst
            ? 0
            : this.typeService.manageAmount(gstBounceChargeAmount);

          sgstOnLegalCharge = iGst
            ? 0
            : this.typeService.manageAmount(gstLegalChargeAmount / 2);
          cgstOnLegalCharge = sgstOnLegalCharge;
          igstOnLegalCharge = !iGst
            ? 0
            : this.typeService.manageAmount(gstLegalChargeAmount);

          sgstForClosureCharge = iGst
            ? 0
            : this.typeService.manageAmount(gstForecloseAmount / 2);
          cgstForClosureCharge = sgstForClosureCharge;
          igstForClosureCharge = !iGst
            ? 0
            : this.typeService.manageAmount(gstForecloseAmount);

          //Add difference back to charges
          bounceCharge +=
            deductedBounceCharge -
            (bounceCharge +
              sgstOnBounceCharge +
              cgstOnBounceCharge +
              igstOnBounceCharge);

          penalCharge +=
            deductedPenalCharge -
            (penalCharge +
              sgstOnPenalCharge +
              cgstOnPenalCharge +
              igstOnPenalCharge);

          legalCharge +=
            deductedLegalCharge -
            (legalCharge +
              sgstOnLegalCharge +
              cgstOnLegalCharge +
              igstOnLegalCharge);

          forecloseAmount +=
            deductedForecloseAmount -
            (forecloseAmount +
              sgstForClosureCharge +
              cgstForClosureCharge +
              igstForClosureCharge);

          regInterestAmount = +regInterestAmount.toFixed(2);
          bounceCharge = +bounceCharge.toFixed(2);
          penalCharge = +penalCharge.toFixed(2);
          legalCharge = +legalCharge.toFixed(2);
          if (
            bounceCharge +
              cgstOnBounceCharge +
              sgstOnBounceCharge +
              igstOnBounceCharge >
            590
          )
            bounceCharge -=
              bounceCharge + cgstOnBounceCharge + sgstOnBounceCharge - 590;

          finalData.principalAmount += principalAmount;
          finalData.interestAmount += interestAmount;
          finalData.penaltyAmount += penaltyAmount;
          finalData.regInterestAmount += regInterestAmount;
          finalData.bounceCharge += bounceCharge;
          finalData.penalCharge += penalCharge;
          finalData.legalCharge += legalCharge;
          finalData.cgstOnBounceCharge += cgstOnBounceCharge;
          finalData.igstOnBounceCharge += igstOnBounceCharge;
          finalData.sgstOnBounceCharge += sgstOnBounceCharge;
          finalData.cgstOnPenalCharge += cgstOnPenalCharge;
          finalData.sgstOnPenalCharge += sgstOnPenalCharge;
          finalData.igstOnPenalCharge += igstOnPenalCharge;
          finalData.cgstOnLegalCharge += cgstOnLegalCharge;
          finalData.sgstOnLegalCharge += sgstOnLegalCharge;
          finalData.igstOnLegalCharge += igstOnLegalCharge;
          finalData.forClosureAmount += forecloseAmount;
          finalData.sgstForClosureCharge += sgstForClosureCharge;
          finalData.cgstForClosureCharge += cgstForClosureCharge;
          finalData.igstForClosureCharge += igstForClosureCharge;
        });
      } else return k422ErrorMessage(kSomthinfWentWrong);
      return { finalData, emiList };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  // Bifurcation only works for delayed emis
  private async canCheckBifurcation(emiId) {
    try {
      // Param validation
      if (!emiId || emiId == -1) return false;
      if (isNaN(emiId)) return false;
      // Query preparation
      const attributes = ['payment_status', 'payment_due_status'];
      const options = { where: { id: emiId } };
      // Query
      const emiData = await this.emiRepo.getRowWhereData(attributes, options);
      if (!emiData) return false;
      if (emiData == k500Error) return true;
      if (emiData.payment_due_status == '1' && emiData.payment_status == '0')
        return true;
      return false;
    } catch (error) {
      return true;
    }
  }

  /// getBoth this veribale use for get data active and complited loan
  async getLoanData(body, getBoth = false) {
    try {
      const loanId = +body.loanId;
      const emiId = +body.emiId;
      const settledId = body.settledId;
      /// transaction
      const tranInclude: any = { model: TransactionEntity };
      tranInclude.attributes = [
        'id',
        'paidAmount',
        'status',
        'principalAmount',
        'interestAmount',
        'penaltyAmount',
        'regInterestAmount',
        'bounceCharge',
        'cgstOnBounceCharge',
        'sgstOnBounceCharge',
        'igstOnBounceCharge',
        'penalCharge',
        'cgstOnPenalCharge',
        'sgstOnPenalCharge',
        'igstOnPenalCharge',
        'legalCharge',
        'cgstOnLegalCharge',
        'sgstOnLegalCharge',
        'igstOnLegalCharge',
        'subStatus',
      ];
      if (getBoth !== true) tranInclude.where = { status: 'COMPLETED' };
      tranInclude.required = false;
      // Emi
      const attributes = [
        'id',
        'emi_amount',
        'emi_date',
        'payment_status',
        'penalty',
        'penalty_days',
        'userId',
        'settledId',
        'payment_due_status',
        'principalCovered',
        'interestCalculate',
        'regInterestAmount',
        'bounceCharge',
        'dpdAmount',
        'legalCharge',
        'gstOnBounceCharge',
        'penaltyChargesGST',
        'legalChargeGST',
        'partPaymentPenaltyAmount',
        'pay_type',
        'paid_principal',
        'paid_interest',
        'paidRegInterestAmount',
        'paidBounceCharge',
        'paidPenalCharge',
        'paidLegalCharge',
        'partOfemi',
      ];
      getBoth = true;
      const emiWhere: any = getBoth === true ? {} : { payment_status: '0' };
      if (emiId != -1) emiWhere.id = emiId;
      let option: any = { where: { loanId, ...emiWhere } };
      /// if settled id then update
      if (settledId) {
        const updateData = await this.emiRepo.updateRowData(
          { settledId },
          option,
        );
        if (!updateData || updateData == k500Error) return kInternalError;
      }
      const emiInclude: any = { model: EmiEntity, attributes };
      emiInclude.include = [tranInclude];
      // user
      const userInclude: any = { model: registeredUsers };
      userInclude.attributes = [
        'id',
        'email',
        'phone',
        'fullName',
        'appType',
        'loanStatus',
        'fcmToken',
        'isBlacklist',
        'totalCredit',
        'remainingCredit',
        'allPhone',
      ];
      // subScription
      const subScription: any = { model: SubScriptionEntity };
      subScription.attributes = ['mode', 'referenceId', 'status', 'umrn'];
      // Mandate
      const mandate = { model: mandateEntity, attributes: ['emandate_id'] };
      const include = [userInclude, emiInclude, subScription, mandate];
      // Loan
      const att = [
        'id',
        'loanStatus',
        'loan_disbursement_date',
        'interestRate',
        'netApprovedAmount',
        'followerId',
        'feesIncome',
        'calculationDetails',
        'penaltyCharges',
        'appType',
        'loanClosureMailCount',
        'settlementMailCount',
      ];
      const where: any = { id: loanId, loanStatus: 'Active' };
      if (getBoth === true)
        where.loanStatus = { [Op.or]: ['Active', 'Complete'] };
      const options = { include, where };
      const loanData = await this.loanRepo.getRowWhereData(att, options);
      if (loanData === k500Error) return kInternalError;
      if (!loanData) return k422ErrorMessage(kNoDataFound);
      const phone = await this.cryptService.decryptPhone(
        loanData.registeredUsers.phone,
      );
      if (phone == k500Error) return kInternalError;
      loanData.registeredUsers.phone = phone;

      if (emiId != -1 && getBoth !== true) {
        const find = loanData.emiData.find(
          (e) => e.id === emiId && e.payment_status === '0',
        );
        if (!find) return;
      }
      const MODIFICATION_CALCULATION =
        loanData?.penaltyCharges?.MODIFICATION_CALCULATION;
      const iGst = loanData?.penaltyCharges?.I_GST ?? false;
      const emiList = loanData?.emiData;
      if (!iGst) {
        for (let i = 0; i < emiList.length; i++) {
          try {
            let ele = emiList[i];
            let cGstOnPenal = this.typeService.manageAmount(
              (ele.penaltyChargesGST ?? 0) / 2,
            );
            let sGstOnPenal = this.typeService.manageAmount(
              (ele.penaltyChargesGST ?? 0) / 2,
            );
            ele.penaltyChargesGST = cGstOnPenal + sGstOnPenal;
            if (!MODIFICATION_CALCULATION) ele.bounceCharge = 0;
          } catch (error) {}
        }
        // for old users bounce charge is already included in penalty
      } else if (!MODIFICATION_CALCULATION) {
        for (let emi of emiList) {
          emi.bounceCharge = 0;
        }
      }
      return loanData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region prepare Amount
  private async prePareAmount(body: any, loanData: loanTransaction) {
    let emiId = body.emiId;
    let amount = +(body?.amount ?? 0);
    amount = +amount.toFixed(2);
    amount = isNaN(amount) ? 0 : amount;
    let dueAmount = 0;
    let principalAmount = 0;
    let interestAmount = 0;
    let penaltyAmount = 0;
    let forClosureAmount = 0;
    let regInterestAmount = 0;
    let bounceCharge = 0;
    let penalCharge = 0;
    let legalCharge = 0;
    let sgstOnBounceCharge = 0;
    let cgstOnBounceCharge = 0;
    let igstOnBounceCharge = 0;
    let sgstOnPenalCharge = 0;
    let cgstOnPenalCharge = 0;
    let igstOnPenalCharge = 0;
    let sgstOnLegalCharge = 0;
    let cgstOnLegalCharge = 0;
    let igstOnLegalCharge = 0;
    let sgstForClosureCharge = 0;
    let cgstForClosureCharge = 0;
    let igstForClosureCharge = 0;
    let type = '';
    let settled_type = '';
    let promoCodeRemark = '';
    let forClosureDays = 0;
    let roundOff = 0;
    let dataWaiver = {};
    let isLoanClosure = false;
    const loanId = body.loanId;
    const followerId = loanData.followerId;
    let creditAmount = body?.usedCreditAmount ?? 0;
    let isUsingCredit = creditAmount > 0 ? true : false;
    let isDirectLoanClose = false;
    const iGst = loanData?.penaltyCharges?.I_GST ?? false;

    let loanTotalRemainingAmount = 0;
    let fullPay;
    if (body.payment_date) {
      const targetDate = this.typeService.getGlobalDate(body.payment_date);
      fullPay = await this.sharedCalculation.getFullPaymentData({
        loanId,
        targetDate,
      });
      if (fullPay?.message) return fullPay;
    } else
      fullPay = await this.sharedCalculation.getFullPaymentData({ loanId });
    if (fullPay === k500Error) return kInternalError;

    // Get User's Remaining Credits Only For Active Ontime Bucket Users
    let diff = 0;
    if (
      body?.source != 'PAYMENT_LINK' &&
      emiId == -1 &&
      body?.subSource != 'AUTODEBIT'
    ) {
      let remainingCredit = loanData?.registeredUsers?.remainingCredit ?? 0;
      diff = (fullPay?.totalAmount ?? 0) - (body?.amount ?? 0);
      if (diff < 0 || (fullPay?.totalAmount ?? 0) == diff) diff = 0;
      if (diff > 0) remainingCredit -= diff;
      const totalWaiver = fullPay?.totalWaiver ?? 0;
      if (totalWaiver > 0) remainingCredit -= totalWaiver;
      if (remainingCredit < 0) remainingCredit = 0;
      if (remainingCredit > 0) {
        creditAmount = remainingCredit;
        isUsingCredit = true;
      } else {
        creditAmount = 0;
        isUsingCredit = false;
      }
    }

    // Changing Body for Credit Transaction, All EMI Pay -> Full Pay
    if (
      isUsingCredit &&
      body?.subSource == kApp &&
      body?.amount &&
      emiId != -1
    ) {
      if (fullPay?.totalAmount - creditAmount === amount) {
        delete body?.amount;
        body.emiId = -1;
        emiId = -1;
      }
    }
    // Full pay
    if (emiId == -1) {
      dueAmount = fullPay.totalAmount;
      if (creditAmount > 0) {
        body.isCloseLoan = true;
        if (body?.subSource != kApp) body.amount = body.amount - creditAmount;
        else body.amount = fullPay.totalAmount - creditAmount;
      }
      loanTotalRemainingAmount = fullPay.totalAmount;

      if (
        creditAmount >= dueAmount &&
        body.isCloseLoan === true &&
        body?.source === kCredit
      ) {
        isDirectLoanClose = true;
        body.amount = dueAmount;
      }

      // Check max amount limit for auto debit
      const maxAutoDebitAmount = GLOBAL_RANGES.MAX_AUTODEBIT_PLACE_AMOUNT;
      if (dueAmount > maxAutoDebitAmount && body.subSource == kAutoDebit) {
        dueAmount = maxAutoDebitAmount;
        body.amount = dueAmount;
      }

      if ((body?.amount ?? 0) > dueAmount) body.amount = dueAmount;
      principalAmount =
        fullPay.remainingPrincipal + fullPay.overduePrincipalAmount;
      interestAmount =
        fullPay.remainingInterest + fullPay.overdueInterestAmount;
      penaltyAmount = fullPay.remainingPenalty;
      forClosureAmount = fullPay.forClosureAmount;
      cgstForClosureCharge = fullPay.cgstForClosureCharge;
      sgstForClosureCharge = fullPay.sgstForClosureCharge;
      igstForClosureCharge = fullPay.igstForClosureCharge;
      forClosureDays = fullPay.forClosureDays;
      bounceCharge = fullPay.bounceCharge;
      penalCharge = fullPay.penalCharge;
      legalCharge = fullPay.legalCharges;
      sgstOnBounceCharge = fullPay.sgstBounceCharge;
      cgstOnBounceCharge = fullPay.cgstBounceCharge;
      igstOnBounceCharge = fullPay.igstBounceCharge;
      sgstOnPenalCharge = fullPay.sgstPenalCharges;
      cgstOnPenalCharge = fullPay.cgstPenalCharges;
      igstOnPenalCharge = fullPay.igstPenalCharges;
      sgstOnLegalCharge = fullPay.sgstLegalCharges;
      cgstOnLegalCharge = fullPay.cgstLegalCharges;
      igstOnLegalCharge = fullPay.igstLegalCharges;
      regInterestAmount = fullPay.regInterestAmount;
      if (
        body?.isCloseLoan != true &&
        amount < fullPay.totalAmount &&
        body.adminId &&
        body?.adminId != SYSTEM_ADMIN_ID &&
        !isUsingCredit
      )
        return k422ErrorMessage(kErrorMsgs.LESS_FULLPAY_AMOUNT);
      if (
        body?.isCloseLoan === true ||
        body?.isLoanClosure ||
        body?.isLoanSettled
      ) {
        // Minimun Amount Required -> Principal, Interest and Foreclosure Charges
        let minimumAmount = principalAmount + interestAmount;
        if (forClosureAmount > 0) {
          minimumAmount +=
            forClosureAmount +
            cgstForClosureCharge +
            sgstForClosureCharge +
            igstForClosureCharge;
          if (body.amount < minimumAmount && !isUsingCredit)
            return k422ErrorMessage(kAmountLessThanPIF);
        }
        if (body.amount < minimumAmount && !isUsingCredit)
          return k422ErrorMessage(kAmountLessThanPrincipalAndInt);

        const calFullPay: any =
          isUsingCredit && !isDirectLoanClose
            ? await this.getCalculationOfFullPayAmountCredit(
                fullPay,
                body,
                loanId,
                creditAmount,
                iGst,
              )
            : this.getCalculationOfFullPayAmount(fullPay, body);

        if (calFullPay?.message) return calFullPay;
        dueAmount = calFullPay.dueAmount;
        principalAmount = +calFullPay.principalAmount.toFixed(2);
        interestAmount = +calFullPay.interestAmount.toFixed(2);
        penaltyAmount = +calFullPay.penaltyAmount.toFixed(2);
        regInterestAmount = +calFullPay.regInterestAmount.toFixed(2);
        bounceCharge = +calFullPay.bounceCharge.toFixed(2);
        cgstOnBounceCharge = +calFullPay.cgstOnBounceCharge.toFixed(2);
        sgstOnBounceCharge = +calFullPay.sgstOnBounceCharge.toFixed(2);
        igstOnBounceCharge = +calFullPay.igstOnBounceCharge.toFixed(2);
        cgstOnPenalCharge = +calFullPay.cgstOnPenalCharge.toFixed(2);
        igstOnPenalCharge = +calFullPay.igstOnPenalCharge.toFixed(2);
        sgstOnPenalCharge = +calFullPay.sgstOnPenalCharge.toFixed(2);
        cgstOnLegalCharge = +calFullPay.cgstOnLegalCharge.toFixed(2);
        igstOnLegalCharge = +calFullPay.igstOnLegalCharge.toFixed(2);
        sgstOnLegalCharge = +calFullPay.sgstOnLegalCharge.toFixed(2);
        forClosureAmount = +calFullPay.forecloseAmount.toFixed(2);
        sgstForClosureCharge = +calFullPay.sgstOnForecloseCharge.toFixed(2);
        cgstForClosureCharge = +calFullPay.cgstOnForecloseCharge.toFixed(2);
        igstForClosureCharge = +calFullPay.igstOnForecloseCharge.toFixed(2);
        penalCharge = +calFullPay.penalCharge.toFixed(2);
        legalCharge = +calFullPay.legalCharge.toFixed(2);
        dataWaiver = calFullPay?.dataWaiver;
        roundOff = calFullPay.roundOff ?? 0;
        settled_type = 'FULLPAY_SETTLED';
        isLoanClosure = fullPay?.totalAmount - calFullPay?.dueAmount > 10;
      }
      type = 'FULLPAY';
    }
    // Emi pay or Part pay
    else {
      const emiData = loanData.emiData.find((e) => e.id == emiId);
      // Prevention -> Excessive payment
      if (emiData.payment_status != '0') return false;

      // const fullPay = await this.sharedCalculation.getFullPaymentData({
      //   loanId,
      // });

      let emiAmount =
        this.typeService.manageAmount(emiData?.principalCovered ?? 0) +
        this.typeService.manageAmount(emiData?.interestCalculate ?? 0) -
        this.typeService.manageAmount(emiData?.paid_principal ?? 0) -
        this.typeService.manageAmount(emiData?.paid_interest ?? 0) +
        this.typeService.manageAmount(emiData?.penalty ?? 0) +
        this.typeService.manageAmount(emiData?.regInterestAmount ?? 0) -
        this.typeService.manageAmount(emiData?.paidRegInterestAmount ?? 0) +
        this.typeService.manageAmount(emiData?.bounceCharge ?? 0) +
        this.typeService.manageAmount(emiData?.gstOnBounceCharge ?? 0) -
        this.typeService.manageAmount(emiData?.paidBounceCharge ?? 0) +
        this.typeService.manageAmount(emiData?.dpdAmount ?? 0) +
        this.typeService.manageAmount(emiData?.penaltyChargesGST ?? 0) -
        this.typeService.manageAmount(emiData?.paidPenalCharge ?? 0) +
        this.typeService.manageAmount(emiData?.legalCharge ?? 0) +
        this.typeService.manageAmount(emiData?.legalChargeGST ?? 0) -
        this.typeService.manageAmount(emiData?.paidLegalCharge ?? 0);
      const actualEMIAmount = emiAmount;
      emiAmount = this.typeService.manageAmount(emiAmount); // Payment should not be payable in decimals

      const isEMIPay =
        amount == 0 || (amount >= emiAmount - 5 && amount <= emiAmount + 5);

      if (isEMIPay) {
        amount = emiAmount;
        roundOff = parseFloat((emiAmount - actualEMIAmount).toFixed(2)); // Payment should not be payable in decimals so extra payment get considered as round off
        if (roundOff > 5) return kInternalError; // Prevention
      }
      // check if is emi pay then
      const emiDate = new Date(emiData.emi_date);
      emiDate.setDate(emiDate.getDate() - 3);

      let iGst = loanData?.penaltyCharges?.I_GST ?? false;
      const data: any = this.getEMiAmount(emiData, amount, iGst);
      if (data === k500Error) return kInternalError;
      else if (
        data.isGreaterEMI == true &&
        body.source != 'UPI' &&
        body.source != 'UPI2' &&
        body.subSource != 'APP' &&
        body.subSource != 'WEB' &&
        body.isGreaterEMI != true
      ) {
        return k422ErrorMessage(kAmountGreaterThan);
      }

      const isDirectTransferPayment =
        body?.adminId &&
        body?.transactionId &&
        body?.utr &&
        (body?.source == 'UPI' || body?.source == 'UPI2');

      if (isEMIPay) type = 'EMIPAY';
      else type = 'PARTPAY';
      // Prevention -> To Avoid Part Payment Less Than 1000
      if (type == kPartPay && !isUsingCredit) {
        if (
          body?.amount < 1000 &&
          fullPay?.totalAmount > 1000 &&
          !isDirectTransferPayment
        )
          return k422ErrorMessage(`Part Payment Less Than 1000 is Not Allowed`);
      }

      // Prevention -> Excessive payment
      if (
        body?.amount > fullPay?.totalAmount + 10 &&
        type == 'PARTPAY' &&
        !isUsingCredit
      )
        return k422ErrorMessage('Enter amount is greater than fullpay amount');
      principalAmount = data.principalAmount;
      interestAmount = data.interestAmount;
      penaltyAmount = data.penaltyAmount;
      regInterestAmount = data.regInterestAmount;
      bounceCharge = data.bounceChargeAmount;
      penalCharge = data.penalChargesAmount;
      sgstOnBounceCharge = data.sgstBounceChargeAmount;
      cgstOnBounceCharge = data.cgstBounceChargeAmount;
      igstOnBounceCharge = data.igstBounceChargeAmount;
      sgstOnPenalCharge = data.sgstPenalChargeAmount;
      cgstOnPenalCharge = data.cgstPenalChargeAmount;
      igstOnPenalCharge = data.igstPenalChargeAmount;
      legalCharge = data.legalChargeAmount;
      sgstOnLegalCharge = data.sgstLegalChargeAmount;
      cgstOnLegalCharge = data.cgstLegalChargeAmount;
      igstOnLegalCharge = data.igstLegalChargeAmount;
      dueAmount = amount;
      if (data.isGreaterEMI == true) settled_type = 'PARTPAY_GREATER';

      // Manging Amount for Credit Trans.
      // dueAmount = fullPay.totalAmount;
      if (
        creditAmount > 0 &&
        (creditAmount >= dueAmount || creditAmount >= amount) &&
        body?.source === kCredit
      ) {
        isDirectLoanClose = true;
        dueAmount = creditAmount >= dueAmount ? creditAmount : amount;
      }
    }
    if (dueAmount < 1 && !isUsingCredit) return false;

    return {
      principalAmount: this.typeService.manageAmount(principalAmount),
      interestAmount: this.typeService.manageAmount(interestAmount),
      penaltyAmount: this.typeService.manageAmount(penaltyAmount),
      dueAmount: this.typeService.manageAmount(dueAmount),
      roundOff: roundOff,
      forClosureAmount: this.typeService.manageAmount(forClosureAmount),
      regInterestAmount: this.typeService.manageAmount(regInterestAmount),
      penalCharge: this.typeService.manageAmount(penalCharge),
      bounceCharge: this.typeService.manageAmount(bounceCharge),
      legalCharge: this.typeService.manageAmount(legalCharge),
      sgstForClosureCharge: this.typeService.manageAmount(sgstForClosureCharge),
      cgstForClosureCharge: this.typeService.manageAmount(cgstForClosureCharge),
      igstForClosureCharge: this.typeService.manageAmount(igstForClosureCharge),
      sgstOnBounceCharge: this.typeService.manageAmount(sgstOnBounceCharge),
      cgstOnBounceCharge: this.typeService.manageAmount(cgstOnBounceCharge),
      igstOnBounceCharge: this.typeService.manageAmount(igstOnBounceCharge),
      sgstOnPenalCharge: this.typeService.manageAmount(sgstOnPenalCharge),
      cgstOnPenalCharge: this.typeService.manageAmount(cgstOnPenalCharge),
      igstOnPenalCharge: this.typeService.manageAmount(igstOnPenalCharge),
      sgstOnLegalCharge: this.typeService.manageAmount(sgstOnLegalCharge),
      cgstOnLegalCharge: this.typeService.manageAmount(cgstOnLegalCharge),
      igstOnLegalCharge: this.typeService.manageAmount(igstOnLegalCharge),
      type,
      settled_type,
      dataWaiver,
      promoCodeRemark,
      forClosureDays,
      isLoanClosure,
      followerId,
      loanTotalRemainingAmount,
      creditAmount,
      isDirectLoanClose,
    };
  }
  //#endregion

  async sendLoanClosureEmail(query) {
    const loanClosureAmount = +query?.amount;
    if (!loanClosureAmount) return kParamMissing('amount');
    if (loanClosureAmount) {
      const isInDecimal = Number.isInteger(loanClosureAmount);
      if (!isInDecimal)
        return k422ErrorMessage('Amount In Decimal is Not Allowed');
    }
    const loanId = query?.loanId;
    if (!loanId) return kParamMissing('loanId');
    const adminId = query?.adminId;
    if (!adminId) return kParamMissing('adminId');
    let date = query?.date;
    if (!date) return kParamMissing('date');
    let time = query?.time ?? k1159Tail;
    if (!time) return kParamMissing('time');
    const mailType = query?.mailType;
    if (!mailType) return kParamMissing('mailType');
    let type = '';
    if (mailType == 'SETTLEMENT') type = '1';
    else if (mailType == 'LOAN_CLOSURE_OFFER') type = '2';

    let subStatus = null;
    if (type == '1') subStatus = kLoanSettled;
    else if (type == '2') subStatus = kLoanClosureStr;

    const body = {
      loanId,
      emiId: -1,
      amount: +loanClosureAmount,
      isCloseLoan: true,
      source: 'PAYMENT_LINK',
      subStatus,
      adminId,
    };

    const loanData = await this.getLoanData(body);
    if (loanData === k500Error) throw new Error();
    if (loanData?.message) return loanData;

    const prePareAmount: any = await this.prePareAmount(body, loanData);
    if (prePareAmount === false) throw new Error();
    if (!prePareAmount?.dueAmount) return prePareAmount;
    const { loanTotalRemainingAmount } = prePareAmount;

    const result: any = await this.prePareTransaction(
      body,
      prePareAmount,
      loanData,
    );
    if (!result || result == k500Error) throw new Error();
    if (result?.statusCode) return result;

    const loanClosureMailCount = loanData.loanClosureMailCount + 1;
    const settlementMailCount = loanData.settlementMailCount + 1;
    const appType = loanData?.appType;
    let dataToUpdate: any = {};
    if (type == '1') {
      dataToUpdate.settlementMailCount = settlementMailCount;
      dataToUpdate.loanSettlementEnabledBy = adminId;
    } else if (type == '2') {
      dataToUpdate.loanClosureMailCount = loanClosureMailCount;
      dataToUpdate.loanClosureEnabledBy = adminId;
    }
    const loanUpdate = await this.loanRepo.updateRowData(dataToUpdate, loanId);
    if (loanUpdate == k500Error) throw new Error();

    // Adding Entry of Link In DB
    const Date1 = date.split('-')[0];
    const Month = date.split('-')[1];
    const Year = date.split('-')[2];
    let newDate = `${Year}-${Month}-${Date1}`;
    let expiryDate = `${newDate}T10:00:00.000Z`;
    const isLinkCreated = await this.repoManager.createRowData(
      PaymentLinkEntity,
      {
        link: result.paymentLink,
        loanId,
        userId: loanData.registeredUsers.id,
        mailType: type,
        isActive: true,
        expiryDate,
        amount: prePareAmount?.dueAmount,
      },
    );
    if (isLinkCreated == k500Error) return kInternalError;

    // Preparing Template
    let templatePath;
    if (type == '1') templatePath = kSettlementLoan;
    else if (type == '2') templatePath = kLoanClosure;
    const template = await this.commonSharedService.getEmailTemplatePath(
      templatePath,
      appType,
    );
    const html: any = this.setValues(
      template,
      this.typeService.amountNumberWithCommas(loanClosureAmount),
      this.typeService.amountNumberWithCommas(loanTotalRemainingAmount),
      this.typeService.getAmountInWords(loanClosureAmount),
      this.typeService.getAmountInWords(loanTotalRemainingAmount),
      date,
      time,
      result.paymentLink,
      loanId,
      loanData.registeredUsers.fullName,
    );
    if (html?.message) return kInternalError;

    // Sending Mail
    let email = loanData.registeredUsers.email;
    const subject =
      mailType == 'SETTLEMENT'
        ? 'Settlement Letter for your Overdue Loan'
        : 'Loan Closure Offer';

    const attachments: any = [];
    let filePath: string = '';
    if (type == '1') {
      const hbsData = await this.fileService.hbsHandlebars(
        tLoanSettlementTemplate,
        {
          loanId,
          date,
          time,
          amount: this.typeService.amountNumberWithCommas(loanClosureAmount),
          amountInWord: this.typeService.getAmountInWords(loanClosureAmount),
          totalRemainingAmount: this.typeService.amountNumberWithCommas(
            loanTotalRemainingAmount,
          ),
          totalRemainingAmountInWord: this.typeService.getAmountInWords(
            loanTotalRemainingAmount,
          ),
          todayDate: this.typeService.getDateFormatted(new Date()),
          nbfcName: EnvConfig.nbfc.nbfcName,
          nbfcLogo: EnvConfig.url.nbfcLogo,
          nbfcRegistrationNumber: EnvConfig.nbfc.nbfcRegistrationNumber,
          cinNumber: EnvConfig.nbfc.nbfcCINNumber,
          nbfcAddress: EnvConfig.nbfc.nbfcAddress,
          name: loanData.registeredUsers.fullName,
        },
      );

      filePath = await this.fileService.dataToPDF(hbsData);
      if (filePath == k500Error) return kInternalError;
      attachments.push({
        filename: `loan settlement.pdf`,
        path: filePath,
        contentType: 'application/pdf',
      });
    }

    await this.sharedNotification.sendMailFromSendinBlue(
      email,
      subject,
      html,
      loanData.registeredUsers.id,
      null,
      attachments,
      null,
      kCollectionEmail,
    );
    await this.fileService.removeFile(filePath);
  }

  // Function for Adjusting Excess Amount In to Upcoming EMIs
  async adjustExcessAmount(data, loanId, userId, adminId) {
    if (!loanId) return kParamMissing('loanId');
    if (!data) return kParamMissing('data');
    if (!userId) return kParamMissing('userId');
    if (!adminId) return kParamMissing('userId');

    let isRefundable = data?.isRefund ?? false;
    if (isRefundable == true) {
      // Transaction data
      const options = { where: { loanId } };
      const emiData = await this.emiRepo.getTableWhereData(
        [
          'id',
          'payment_status',
          'principalCovered',
          'interestCalculate',
          'payment_due_status',
          'emi_date',
        ],
        options,
      );
      if (emiData == k500Error) return kInternalError;
      let unpaidEmis = emiData.filter((ele) => ele.payment_status == '0');
      if (unpaidEmis.length == 0)
        return k422ErrorMessage('Unpaid EMI not found!!');
      unpaidEmis.sort((a, b) => a.id - b.id);

      let emiToAdd = unpaidEmis[0].id;
      let principalCovered = unpaidEmis[0].principalCovered ?? 0;
      let interestCalculate = unpaidEmis[0].interestCalculate ?? 0;
      let emiAmount = principalCovered + interestCalculate;
      let transId: any = 0;

      transId = await this.selectTransAndUpdateEmi(
        loanId,
        data,
        emiAmount,
        emiToAdd,
        emiData,
      );
      if (transId.message) return kInternalError;

      if (transId != 0 && transId) {
        const refundAmt = data?.filteredData[0]?.amount ?? 0;
        const logCreateObj: any = {
          userId,
          loanId,
          type: 'Adjusting Excess Amount',
          subType: 'Adjusting Excess Amount',
          oldData: refundAmt,
          newData: 0,
          adminId,
        };
        await this.changeLogRepo.create(logCreateObj);
        const updateTrans = await this.transactionRepo.updateRowData(
          {
            status: kInitiated,
            emiId: emiToAdd,
            principalAmount: principalCovered,
            interestAmount: interestCalculate,
          },
          transId,
        );
        if (updateTrans == k500Error) return kInternalError;
        const checkPaymentOrder = await this.checkPaymentOrder({
          loanId,
          userId,
        });
        if (checkPaymentOrder.message) return kInternalError;
        return true;
      }
    }
    return k422ErrorMessage('User is Not Eligible For Amt. Adjustment');
  }

  async selectTransAndUpdateEmi(loanId, data, emiAmount, emiToAdd, emiData) {
    let isRefundable = data?.isRefund ?? false;
    let refundArrayLength = data.filteredData.length > 0 ? true : false;

    let transId = 0;
    let emiId = data?.filteredData[0]?.emiId;
    let emiToUpdate = emiData.filter((e) => e.id == emiToAdd);
    let emiDate = new Date(emiToUpdate[0].emi_date);
    let isEmiDelay = emiToUpdate[0].payment_due_status == '1' ? true : false;

    if (isRefundable && refundArrayLength) {
      let transData = data?.filteredData[0]?.tranData;
      transData = transData.sort((a, b) => b.id - a.id);
      for (let i = 0; i < transData.length; i++) {
        let ele = transData[i];
        let isEqual =
          ele?.paidAmount >= emiAmount - 5 && ele?.paidAmount <= emiAmount + 5;
        if (
          ele?.emiId == emiId &&
          ele?.subSource != 'AUTODEBIT' &&
          ele?.type == 'EMIPAY' &&
          isEqual
        ) {
          transId = ele?.id;
          break;
        }
      }
      return transId;
    } else if (isRefundable && !refundArrayLength && isEmiDelay) {
      let attr = [
        'id',
        'paidAmount',
        'completionDate',
        'type',
        'status',
        'source',
        'subSource',
        'emiId',
      ];
      let opt = { where: { status: kCompleted, loanId } };
      let transactions = await this.transactionRepo.getTableWhereData(
        attr,
        opt,
      );
      if (transactions == k500Error) return kInternalError;
      let transData = transactions.sort((a, b) => b.id - a.id);

      const emiIdCountMap = {};
      // Getting EMIPAY Trans
      transData.forEach((ele) => {
        if (ele?.type === 'EMIPAY') {
          emiIdCountMap[ele?.emiId] = (emiIdCountMap[ele?.emiId] || 0) + 1;
        }
      });
      // Getting Id of EMI of Which Refund is Generated
      for (const id in emiIdCountMap) {
        if (emiIdCountMap[id] >= 2) {
          emiId = parseInt(id);
          break;
        }
      }
      let transCompletionDate;
      for (let i = 0; i < transData.length; i++) {
        let ele = transData[i];
        let isEqual =
          ele?.paidAmount >= emiAmount - 5 && ele?.paidAmount <= emiAmount + 5;
        if (
          ele?.emiId == emiId &&
          ele?.subSource != 'AUTODEBIT' &&
          ele?.type == 'EMIPAY' &&
          isEqual
        ) {
          transCompletionDate = new Date(ele?.completionDate);
          transId = ele?.id;
          break;
        }
      }
      if (transCompletionDate < emiDate && isEmiDelay) {
        let emiUpdate = await this.emiRepo.updateRowData(
          {
            payment_due_status: 0,
            penalty_days: 0,
            regInterestAmount: 0,
            bounceCharge: 0,
            gstOnBounceCharge: 0,
            dpdAmount: 0,
            penaltyChargesGST: 0,
            legalCharge: 0,
            legalChargeGST: 0,
          },
          emiToAdd,
        );
        if (emiUpdate == k500Error) return kInternalError;
      }
    }
    return transId;
  }

  private replaceAll(str, find, replace) {
    return str.replace(new RegExp(this.escapeRegExp(find), 'g'), replace);
  }

  private escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // $& means the whole matched string
  }

  private setValues(
    template: string,
    amount: string,
    totalRemainingAmount: string,
    amountInWord: string,
    totalRemainingAmountInWord: string,
    date: string,
    time: string,
    link: string,
    loanId: string,
    name: string,
  ) {
    try {
      let html = fs.readFileSync(template, 'utf-8');

      html = html.replace('##LOANID##', loanId);
      html = html.replace('##TOTALREMAININGAMOUNT##', totalRemainingAmount);
      html = html.replace('##AMOUNTINWORD##', amountInWord);
      html = html.replace(
        '##TOTALREMAININGAMOUNTINWORD##',
        totalRemainingAmountInWord,
      );
      html = this.replaceAll(html, '##AMOUNT##', amount);
      html = this.replaceAll(html, '##DATE##', date);
      html = this.replaceAll(html, '##TIME##', time);
      html = html.replace('##link##', link);
      html = html.replace('##NBFCNAME##', EnvConfig.nbfc.nbfcName);
      html = html.replace(
        '##NBFCREGISTRATIONNUMBER##',
        EnvConfig.nbfc.nbfcRegistrationNumber,
      );
      html = html.replace('##NAME##', name);
      html = html.replace('##NBFCSHORTNAME##', EnvConfig.nbfc.nbfcShortName);
      html = html.replace('##INFOMAIL##', EnvConfig.mail.infoMail);
      html = html.replace(
        '##PRIVACY_POLICY_LINK##',
        EnvConfig.permissionsLink.nbfc[2],
      );
      html = html.replace(
        '##TERM_AND_CONDITION_LINK##',
        EnvConfig.permissionsLink.nbfc[1],
      );
      html = this.replaceAll(html, '##NBFCLINK##', EnvConfig.url.nbfcWebUrl);

      return html;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async createUserNoc(reqData) {
    try {
      const loanId = reqData?.loanId;
      if (!loanId) return kParamMissing('loanId');
      const type = reqData?.type;
      const adminId = reqData?.adminId ?? SYSTEM_ADMIN_ID;

      // Get NOC data based on loanId
      const result = await this.funNOCData(loanId);
      if (!result || result === k500Error) return kInternalError;
      // Operations For Closing Loan Normally
      if (type == 'NORMAL') result.isLoanClosure = true;
      if (type == 'NORMAL' || result.isLoanClosure == true) {
        // ChangeLogEntity Opertaions(Changing Type Waiver to Normal)
        const logOpts = { where: { loanId, type: 'Waiver' } };
        const logUpdatedData = { type: 'NORMAL', oldData: '0', newData: '0' };
        const logsUpdation = await this.repoManager.updateRowWhereData(
          ChangeLogsEntity,
          logUpdatedData,
          logOpts,
        );
        if (logsUpdation === k500Error) throw new Error();
        // Adding Entry in ChangeLogEntity to Report Cibil Normal
        const closureAdminId = result?.loanClosureEnabledBy ?? adminId;
        const userId = result?.userId;
        const creationData = {
          loanId,
          adminId: closureAdminId,
          userId,
          type: 'CIBIL_SETTLED_AMOUNT',
          subType: 'Cibil',
          newData: '',
          oldData: '',
        };
        const createdData = await this.changeLogsRepo.create(creationData);
        if (createdData == k500Error) return kInternalError;
      }
      const adminData = await this.commonSharedService.getAdminData(adminId);
      if (adminData === k500Error) return kInternalError;
      const stampId = result?.eSignData?.stampId;
      let aggrementNo = '-';
      if (stampId) {
        const stampNumber = await this.stampRepo.getRowWhereData(
          ['certificateNo'],
          { where: { id: result?.eSignData?.stampId } },
        );
        if (stampNumber === k500Error) return kInternalError;
        aggrementNo = stampNumber?.certificateNo ?? '-';
      }

      const transactionData = result.transactionData.sort(
        (a, b) => b?.id - a?.id,
      );
      const lastPaymentDate = this.typeService.getGlobalDate(
        new Date(transactionData[0]?.completionDate),
      );
      let totalAmount = 0;
      transactionData.forEach((el) => {
        totalAmount += +el?.paidAmount;
      });
      totalAmount = this.typeService.manageAmount(totalAmount);
      let loanType = 'ONTIME';
      let totalWaiver: any = 0;
      if (!result?.isLoanClosure) {
        result.emiData.forEach((el) => {
          const waiver =
            (el.waiver ?? 0) + (el.paid_waiver ?? 0) + (el.unpaid_waiver ?? 0);
          if (waiver > 0) {
            totalWaiver += waiver;
            loanType = 'SETTLED';
          }
        });
      }
      // Commented to Send Noc By System
      // if (loanType == 'SETTLED' && adminId == SYSTEM_ADMIN_ID)
      //   return kInternalError;
      const accountNumber = result?.bankingData?.accountNumber;
      const caseDetails = result?.caseDetails;
      totalWaiver = Number(totalWaiver).toFixed(2);
      const sendData = {
        userData: result.registeredUsers,
        totalAmount: this.strService.readableAmount(totalAmount),
        loanAmount: result?.netApprovedAmount,
        lastPaymentDate,
        loanId,
        aggrementNo,
        adminEmail: adminData?.email,
        loanType,
        appType: result?.appType,
        accountNumber,
        totalWaiver: this.strService.readableAmount(totalWaiver),
        adminId,
        caseDetails,
      };
      const mailResult = await this.sharedNotification.sendNocBymail(
        sendData.userData?.email,
        sendData,
      );
      if (mailResult === k500Error) return kInternalError;
      await this.loanRepo.updateRowData(
        { nocSentBy: adminId, isLoanClosure: result?.isLoanClosure },
        loanId,
      );
      return mailResult;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region Create user NOC
  private async funNOCData(loanId) {
    try {
      const loanData = await this.funLoanData(loanId);
      if (!loanData?.id) return;
      loanData.transactionData = await this.funTransactionData(loanId);
      loanData.emiData = await this.funEMIData(loanId);
      loanData.eSignData = await this.funESignData(loanId);
      loanData.bankingData = await this.funBankingData(loanData?.bankingId);
      const legalData = await this.funLegalData(loanId);
      loanData.caseDetails = legalData?.caseDetails;
      loanData.registeredUsers = {
        id: loanData?.userId,
        fullName: loanData?.fullName,
        email: loanData?.email,
      };
      delete loanData?.bankingId;
      delete loanData?.fullName;
      delete loanData?.email;
      return loanData;
    } catch (error) {
      return k500Error;
    }
  }
  //#endregion

  //#region Create user NOC
  private async funLoanData(id) {
    const attributes = [
      'id',
      'userId',
      'netApprovedAmount',
      'loanStatus',
      'appType',
      'isLoanClosure',
      'loanClosureEnabledBy',
      'bankingId',
      'email',
      'fullName',
    ];
    const options: any = {
      where: { id, loanStatus: 'Complete' },
      useMaster: false,
    };
    const data = await this.repoManager.getRowWhereData(
      loanTransaction,
      attributes,
      options,
    );
    if (data == k500Error) return k500Error;
    return data;
  }
  //#endregion

  //#region Create user NOC
  private async funLegalData(loanId) {
    const attributes = ['caseDetails'];
    const options = {
      where: { loanId, type: [CASE_FILLED, SUMMONS, WARRENT] },
      order: [['id', 'DESC']],
    };
    const data = await this.repoManager.getRowWhereData(
      LegalCollectionEntity,
      attributes,
      options,
    );
    if (data == k500Error) return k500Error;
    return data;
  }
  //#endregion

  //#region Create user NOC
  private async funTransactionData(loanId) {
    const attributes = ['id', 'paidAmount', 'completionDate'];
    const options = {
      where: { loanId, status: 'COMPLETED' },
      useMaster: false,
    };
    const data = await this.repoManager.getTableWhereData(
      TransactionEntity,
      attributes,
      options,
    );
    if (data == k500Error) return k500Error;
    return data;
  }
  //#endregion

  //#region Create user NOC
  private async funEMIData(loanId) {
    const attributes = ['waiver', 'paid_waiver', 'unpaid_waiver'];
    const options = { where: { loanId }, useMaster: false };
    const data = await this.repoManager.getTableWhereData(
      EmiEntity,
      attributes,
      options,
    );
    if (data == k500Error) return k500Error;
    return data;
  }
  //#endregion

  //#region Create user NOC
  private async funESignData(loanId) {
    const attributes = ['id', 'stampId'];
    const options = { where: { loanId }, useMaster: false };
    const data = await this.repoManager.getRowWhereData(
      esignEntity,
      attributes,
      options,
    );
    if (data == k500Error) return k500Error;
    return data;
  }
  //#endregion

  //#region Create user NOC
  private async funBankingData(id) {
    const attributes = ['accountNumber'];
    const options = { where: { id }, useMaster: false };
    const data = await this.repoManager.getRowWhereData(
      BankingEntity,
      attributes,
      options,
    );
    if (data == k500Error) return k500Error;
    return data;
  }
  //#endregion

  async createNormalNoc(loanId) {
    try {
      if (!loanId) return kParamMissing('loanId');
      const options: any = {
        where: {
          loanId,
          [Op.or]: [
            { waiver: { [Op.ne]: null } },
            { paid_waiver: { [Op.ne]: null } },
            { unpaid_waiver: { [Op.ne]: null } },
          ],
        },
      };
      const emiData = await this.emiRepo.getTableWhereData(['id'], options);
      if (emiData === k500Error) throw new Error();
      if (emiData.length == 0) return k422ErrorMessage(kNoDataFound);
      // EMI Operations
      const emiIds = emiData.map((ele) => ele?.id);
      const updateDataForEmi = {
        waiver: null,
        paid_waiver: null,
        unpaid_waiver: null,
        waived_principal: 0,
        waived_interest: 0,
        waived_penalty: 0,
      };
      const emiUpdation = await this.emiRepo.updateRowData(
        updateDataForEmi,
        emiIds,
      );
      if (emiUpdation === k500Error) throw new Error();
      // ChangeLogEntity Opertaions
      const logOpts = { where: { loanId, type: 'Waiver' } };
      const logUpdatedData = { type: 'NORMAL', oldData: '0', newData: '0' };
      const logsUpdation = await this.repoManager.updateRowWhereData(
        ChangeLogsEntity,
        logUpdatedData,
        logOpts,
      );
      if (logsUpdation === k500Error) throw new Error();
      // Loan Operations
      const loanUpdation = await this.loanRepo.updateRowData(
        { nocURL: null, nocSentBy: null },
        loanId,
      );
      if (loanUpdation === k500Error) throw new Error();
      return true;
    } catch (error) {}
  }

  //#region get min amount paid of loanId
  private async getRemainingPrincipalAmountOfLoan(body) {
    try {
      if (body?.isCloseLoan === true) {
        const loanId = body.loanId;
        /// emi model
        const emiAtt = [
          'id',
          'emi_amount',
          'payment_status',
          'payment_due_status',
          'principalCovered',
        ];
        const emiModel = { model: EmiEntity, attributes: emiAtt };
        /// transaction Model
        const tranAtt = ['id', 'paidAmount', 'emiId', 'principalAmount'];
        const tranModel: any = {
          model: TransactionEntity,
          attributes: tranAtt,
        };
        tranModel.where = { status: 'COMPLETED' };
        tranModel.required = false;
        /// loan Options
        const options = {
          where: { id: loanId, loanStatus: 'Active' },
          include: [tranModel, emiModel],
        };
        const att = ['id'];
        const result = await this.loanRepo.getRowWhereData(att, options);
        if (!result || result === k500Error) return kInternalError;
        const emiData = result.emiData;
        const transaction = result.transactionData;
        let remainingPrincipal = 0;
        let amount = 0;
        emiData.forEach((emi) => {
          try {
            if (!(emi.payment_status == '1' && emi.payment_due_status == '0')) {
              remainingPrincipal += emi.principalCovered;
              const tempTrans = transaction.filter(
                (tran) => tran.emiId == emi.id,
              );
              tempTrans.forEach((tran) => {
                amount += tran.paidAmount;
              });
            }
          } catch (error) {}
        });

        remainingPrincipal -= amount;
        if (remainingPrincipal < 0) remainingPrincipal = 0;
        if ((body?.amount ?? 0) < remainingPrincipal + body.interestAmount)
          return k422ErrorMessage(kAmountLessThanPrincipalAndInt);
        return { result, remainingPrincipal };
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region get calculation of full pay amount
  async getCalculationOfFullPayAmountCredit(
    fullPay,
    body,
    loanId?,
    creditAmount?,
    gstType?,
  ) {
    try {
      let dueAmount = fullPay.totalAmount;
      let iGst = fullPay.I_GST;
      let principalAmount = this.typeService.manageAmount(
        fullPay.remainingPrincipal + fullPay.overduePrincipalAmount,
      );
      let interestAmount = this.typeService.manageAmount(
        fullPay.remainingInterest + fullPay.overdueInterestAmount,
      );
      let penaltyAmount = this.typeService.manageAmount(
        fullPay.remainingPenalty,
      );
      let regInterestAmount = this.typeService.manageAmount(
        fullPay.regInterestAmount,
      );
      let bounceCharge = this.typeService.manageAmount(
        fullPay.bounceCharge +
          fullPay.sgstBounceCharge +
          fullPay.cgstBounceCharge +
          fullPay.igstBounceCharge,
      );
      let penalCharge = this.typeService.manageAmount(
        fullPay.penalCharge +
          fullPay.sgstPenalCharges +
          fullPay.cgstPenalCharges +
          fullPay.igstPenalCharges,
      );
      let forecloseAmount = this.typeService.manageAmount(
        fullPay.forClosureAmount +
          fullPay.sgstForClosureCharge +
          fullPay.cgstForClosureCharge +
          fullPay.igstForClosureCharge,
      );
      let legalCharge = this.typeService.manageAmount(
        fullPay.legalCharges +
          fullPay.sgstLegalCharges +
          fullPay.cgstLegalCharges +
          fullPay.igstLegalCharges,
      );

      // Reducing Amts for Credit Transaction
      const amts = await this.reduceAmountsForCreditPay(
        principalAmount,
        interestAmount,
        penaltyAmount,
        regInterestAmount,
        bounceCharge,
        penalCharge,
        forecloseAmount,
        legalCharge,
        fullPay,
        { loanId, creditAmount, iGst: gstType },
      );
      principalAmount = amts.principalAmount;
      interestAmount = amts.interestAmount;
      penaltyAmount = amts.penaltyAmount;
      regInterestAmount = amts.regInterestAmount;
      bounceCharge = amts.bounceCharge;
      penalCharge = amts.penalCharge;
      forecloseAmount = amts.forecloseAmount;
      legalCharge = amts.legalCharge;

      let deductedBounceCharge = 0;
      let deductedPenalCharge = 0;
      let deductedLegalCharge = 0;
      let deductedForecloseAmt = 0;
      let amount = +body?.amount;
      if (body?.isLoanClosure) amount = +body?.loanClosureAmt;
      if (body?.isLoanSettled) amount = +body?.loanSettlementAmt;
      dueAmount = amount;
      if (principalAmount >= amount) {
        principalAmount = amount;
        amount = 0;
      } else amount -= principalAmount;

      if (interestAmount >= amount) {
        interestAmount = amount;
        amount = 0;
      } else amount -= interestAmount;

      if (forecloseAmount >= amount) {
        forecloseAmount = amount;
        amount = 0;
      } else amount -= forecloseAmount;
      deductedForecloseAmt = forecloseAmount;

      if (penaltyAmount >= amount) {
        penaltyAmount = amount;
        amount = 0;
      } else amount -= penaltyAmount;

      if (regInterestAmount >= amount) {
        regInterestAmount = amount;
        amount = 0;
      } else amount -= regInterestAmount;

      if (bounceCharge >= amount) {
        bounceCharge = amount;
        amount = 0;
      } else amount -= bounceCharge;
      deductedBounceCharge = bounceCharge;
      if (penalCharge >= amount) {
        penalCharge = amount;
        amount = 0;
      } else amount -= penalCharge;
      deductedPenalCharge = penalCharge;

      if (legalCharge >= amount) {
        legalCharge = amount;
        amount = 0;
      } else amount -= legalCharge;
      deductedLegalCharge = legalCharge;

      // Bounce GST
      let tempBounce = bounceCharge;
      let gstBounceChargeAmount = 0;
      // Penal GST
      let tempPenal = penalCharge;
      let gstPenalChargeAmount = 0;

      // Legal GST
      let tempLegal = legalCharge;
      let gstLegalChargeAmount = legalCharge - legalCharge / 1.18;
      gstLegalChargeAmount =
        this.typeService.manageAmount(gstLegalChargeAmount);
      if (gstLegalChargeAmount % 2 == 1) {
        gstLegalChargeAmount = gstLegalChargeAmount - 1;
      }
      // Foreclose GST
      let tempForeclose = forecloseAmount;
      let gstForecloseAmount = forecloseAmount - forecloseAmount / 1.18;
      gstForecloseAmount = this.typeService.manageAmount(gstForecloseAmount);
      if (gstForecloseAmount % 2 == 1) {
        gstForecloseAmount = gstForecloseAmount - 1;
      }
      let roundOff = 0;
      bounceCharge = bounceCharge - gstBounceChargeAmount;
      if (tempBounce > bounceCharge + gstBounceChargeAmount)
        roundOff += tempBounce - (bounceCharge + gstBounceChargeAmount);
      penalCharge = penalCharge - gstPenalChargeAmount;
      if (tempPenal > penalCharge + gstPenalChargeAmount)
        roundOff += tempPenal - (penalCharge + gstPenalChargeAmount);
      legalCharge = legalCharge - gstLegalChargeAmount;
      if (tempLegal > legalCharge + gstLegalChargeAmount)
        roundOff += tempLegal - (legalCharge + gstLegalChargeAmount);
      forecloseAmount = forecloseAmount - gstForecloseAmount;
      if (tempForeclose > forecloseAmount + gstForecloseAmount)
        roundOff += tempForeclose - (forecloseAmount + gstForecloseAmount);

      const sgstOnBounceCharge = iGst
        ? 0
        : +(gstBounceChargeAmount / 2).toFixed(2);
      const cgstOnBounceCharge = sgstOnBounceCharge;
      const igstOnBounceCharge = !iGst ? 0 : +gstBounceChargeAmount.toFixed(2);

      const sgstOnPenalCharge = iGst
        ? 0
        : +(gstPenalChargeAmount / 2).toFixed(2);
      const cgstOnPenalCharge = sgstOnPenalCharge;
      const igstOnPenalCharge = !iGst ? 0 : +gstPenalChargeAmount.toFixed(2);

      const sgstOnLegalCharge = iGst
        ? 0
        : +(gstLegalChargeAmount / 2).toFixed(2);
      const cgstOnLegalCharge = sgstOnLegalCharge;
      const igstOnLegalCharge = !iGst ? 0 : +gstLegalChargeAmount.toFixed(2);

      const sgstOnForecloseCharge = iGst
        ? 0
        : +(gstForecloseAmount / 2).toFixed(2);
      const cgstOnForecloseCharge = sgstOnForecloseCharge;
      const igstOnForecloseCharge = !iGst ? 0 : +gstForecloseAmount.toFixed(2);
      //Add difference back to charges
      bounceCharge +=
        deductedBounceCharge -
        (bounceCharge +
          sgstOnBounceCharge +
          cgstOnBounceCharge +
          igstOnBounceCharge);

      penalCharge +=
        deductedPenalCharge -
        (penalCharge +
          sgstOnPenalCharge +
          cgstOnPenalCharge +
          igstOnPenalCharge);

      legalCharge +=
        deductedLegalCharge -
        (legalCharge +
          sgstOnLegalCharge +
          cgstOnLegalCharge +
          igstOnLegalCharge);

      forecloseAmount +=
        deductedForecloseAmt -
        (forecloseAmount +
          sgstOnForecloseCharge +
          cgstOnForecloseCharge +
          igstOnForecloseCharge);

      const waiverAmount = fullPay.totalAmount - dueAmount;
      let dataWaiver = {};
      if (waiverAmount > 0) {
        try {
          dataWaiver = this.getWaiverAmountInfullpaysetted(
            fullPay?.fullPIPData,
            waiverAmount,
          );
        } catch (error) {}
      }

      return {
        dueAmount,
        principalAmount,
        interestAmount,
        penaltyAmount,
        regInterestAmount,
        bounceCharge,
        penalCharge,
        legalCharge,
        cgstOnBounceCharge,
        sgstOnBounceCharge,
        cgstOnPenalCharge,
        sgstOnPenalCharge,
        cgstOnLegalCharge,
        sgstOnLegalCharge,
        forecloseAmount,
        sgstOnForecloseCharge,
        cgstOnForecloseCharge,
        dataWaiver,
        roundOff,
        igstOnBounceCharge,
        igstOnPenalCharge,
        igstOnLegalCharge,
        igstOnForecloseCharge,
      };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region get calculation of full pay amount
  getCalculationOfFullPayAmount(fullPay, body): any {
    try {
      let dueAmount = fullPay.totalAmount;
      let iGst = fullPay.I_GST;
      let principalAmount = this.typeService.manageAmount(
        fullPay.remainingPrincipal + fullPay.overduePrincipalAmount,
      );
      let interestAmount = this.typeService.manageAmount(
        fullPay.remainingInterest + fullPay.overdueInterestAmount,
      );
      let penaltyAmount = this.typeService.manageAmount(
        fullPay.remainingPenalty,
      );
      let regInterestAmount = this.typeService.manageAmount(
        fullPay.regInterestAmount,
      );
      let bounceCharge = this.typeService.manageAmount(
        fullPay.bounceCharge +
          fullPay.sgstBounceCharge +
          fullPay.cgstBounceCharge +
          fullPay.igstBounceCharge,
      );
      let penalCharge = this.typeService.manageAmount(
        fullPay.penalCharge +
          fullPay.sgstPenalCharges +
          fullPay.cgstPenalCharges +
          fullPay.igstPenalCharges,
      );
      let forecloseAmount = this.typeService.manageAmount(
        fullPay.forClosureAmount +
          fullPay.sgstForClosureCharge +
          fullPay.cgstForClosureCharge +
          fullPay.igstForClosureCharge,
      );
      let legalCharge = this.typeService.manageAmount(
        fullPay.legalCharges +
          fullPay.sgstLegalCharges +
          fullPay.cgstLegalCharges +
          fullPay.igstLegalCharges,
      );
      let deductedBounceCharge = 0;
      let deductedPenalCharge = 0;
      let deductedLegalCharge = 0;
      let deductedForecloseAmt = 0;
      let amount = +body?.amount;
      if (body?.isLoanClosure) amount = +body?.loanClosureAmt;
      if (body?.isLoanSettled) amount = +body?.loanSettlementAmt;
      dueAmount = amount;
      if (principalAmount >= amount) {
        principalAmount = amount;
        amount = 0;
      } else amount -= principalAmount;

      if (interestAmount >= amount) {
        interestAmount = amount;
        amount = 0;
      } else amount -= interestAmount;

      if (forecloseAmount >= amount) {
        forecloseAmount = amount;
        amount = 0;
      } else amount -= forecloseAmount;
      deductedForecloseAmt = forecloseAmount;

      if (penaltyAmount >= amount) {
        penaltyAmount = amount;
        amount = 0;
      } else amount -= penaltyAmount;

      if (regInterestAmount >= amount) {
        regInterestAmount = amount;
        amount = 0;
      } else amount -= regInterestAmount;

      if (bounceCharge >= amount) {
        bounceCharge = amount;
        amount = 0;
      } else amount -= bounceCharge;
      deductedBounceCharge = bounceCharge;
      if (penalCharge >= amount) {
        penalCharge = amount;
        amount = 0;
      } else amount -= penalCharge;
      deductedPenalCharge = penalCharge;

      if (legalCharge >= amount) {
        legalCharge = amount;
        amount = 0;
      } else amount -= legalCharge;
      deductedLegalCharge = legalCharge;

      // Bounce GST
      let tempBounce = bounceCharge;
      let gstBounceChargeAmount = 0;
      // Penal GST
      let tempPenal = penalCharge;
      let gstPenalChargeAmount = 0;

      // Legal GST
      let tempLegal = legalCharge;
      let gstLegalChargeAmount = legalCharge - legalCharge / 1.18;
      gstLegalChargeAmount =
        this.typeService.manageAmount(gstLegalChargeAmount);
      if (gstLegalChargeAmount % 2 == 1) {
        gstLegalChargeAmount = gstLegalChargeAmount - 1;
      }
      // Foreclose GST
      let tempForeclose = forecloseAmount;
      let gstForecloseAmount = forecloseAmount - forecloseAmount / 1.18;
      gstForecloseAmount = this.typeService.manageAmount(gstForecloseAmount);
      if (gstForecloseAmount % 2 == 1) {
        gstForecloseAmount = gstForecloseAmount - 1;
      }
      let roundOff = 0;
      bounceCharge = bounceCharge - gstBounceChargeAmount;
      if (tempBounce > bounceCharge + gstBounceChargeAmount)
        roundOff += tempBounce - (bounceCharge + gstBounceChargeAmount);
      penalCharge = penalCharge - gstPenalChargeAmount;
      if (tempPenal > penalCharge + gstPenalChargeAmount)
        roundOff += tempPenal - (penalCharge + gstPenalChargeAmount);
      legalCharge = legalCharge - gstLegalChargeAmount;
      if (tempLegal > legalCharge + gstLegalChargeAmount)
        roundOff += tempLegal - (legalCharge + gstLegalChargeAmount);
      forecloseAmount = forecloseAmount - gstForecloseAmount;
      if (tempForeclose > forecloseAmount + gstForecloseAmount)
        roundOff += tempForeclose - (forecloseAmount + gstForecloseAmount);

      const sgstOnBounceCharge = iGst
        ? 0
        : +(gstBounceChargeAmount / 2).toFixed(2);
      const cgstOnBounceCharge = sgstOnBounceCharge;
      const igstOnBounceCharge = !iGst ? 0 : +gstBounceChargeAmount.toFixed(2);

      const sgstOnPenalCharge = iGst
        ? 0
        : +(gstPenalChargeAmount / 2).toFixed(2);
      const cgstOnPenalCharge = sgstOnPenalCharge;
      const igstOnPenalCharge = !iGst ? 0 : +gstPenalChargeAmount.toFixed(2);

      const sgstOnLegalCharge = iGst
        ? 0
        : +(gstLegalChargeAmount / 2).toFixed(2);
      const cgstOnLegalCharge = sgstOnLegalCharge;
      const igstOnLegalCharge = !iGst ? 0 : +gstLegalChargeAmount.toFixed(2);

      const sgstOnForecloseCharge = iGst
        ? 0
        : +(gstForecloseAmount / 2).toFixed(2);
      const cgstOnForecloseCharge = sgstOnForecloseCharge;
      const igstOnForecloseCharge = !iGst ? 0 : +gstForecloseAmount.toFixed(2);
      //Add difference back to charges
      bounceCharge +=
        deductedBounceCharge -
        (bounceCharge +
          sgstOnBounceCharge +
          cgstOnBounceCharge +
          igstOnBounceCharge);

      penalCharge +=
        deductedPenalCharge -
        (penalCharge +
          sgstOnPenalCharge +
          cgstOnPenalCharge +
          igstOnPenalCharge);

      legalCharge +=
        deductedLegalCharge -
        (legalCharge +
          sgstOnLegalCharge +
          cgstOnLegalCharge +
          igstOnLegalCharge);

      forecloseAmount +=
        deductedForecloseAmt -
        (forecloseAmount +
          sgstOnForecloseCharge +
          cgstOnForecloseCharge +
          igstOnForecloseCharge);

      const waiverAmount = fullPay.totalAmount - dueAmount;
      let dataWaiver = {};
      if (waiverAmount > 0) {
        try {
          dataWaiver = this.getWaiverAmountInfullpaysetted(
            fullPay?.fullPIPData,
            waiverAmount,
          );
        } catch (error) {}
      }

      return {
        dueAmount,
        principalAmount,
        interestAmount,
        penaltyAmount,
        regInterestAmount,
        bounceCharge,
        penalCharge,
        legalCharge,
        cgstOnBounceCharge,
        sgstOnBounceCharge,
        cgstOnPenalCharge,
        sgstOnPenalCharge,
        cgstOnLegalCharge,
        sgstOnLegalCharge,
        forecloseAmount,
        sgstOnForecloseCharge,
        cgstOnForecloseCharge,
        dataWaiver,
        roundOff,
        igstOnBounceCharge,
        igstOnPenalCharge,
        igstOnLegalCharge,
        igstOnForecloseCharge,
      };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  private getEMiAmount(emiData: EmiEntity, amount = 0, iGst = false) {
    let principalAmount = 0;
    let interestAmount = 0;
    let penaltyAmount = 0;
    let regInterestAmount = 0;
    let bounceChargeAmount = 0;
    let penalChargesAmount = 0;
    let legalChargeAmount = 0;
    let sgstBounceChargeAmount = 0;
    let cgstBounceChargeAmount = 0;
    let igstBounceChargeAmount = 0;
    let sgstPenalChargeAmount = 0;
    let cgstPenalChargeAmount = 0;
    let igstPenalChargeAmount = 0;
    let sgstLegalChargeAmount = 0;
    let cgstLegalChargeAmount = 0;
    let igstLegalChargeAmount = 0;
    let deductedBounceCharge = 0;
    let deductedPenalCharge = 0;
    let deductedLegalCharge = 0;
    let bounceCharge = 0;
    let penalCharges = 0;
    let legalCharge = 0;
    if (amount < 1) return kInternalError;
    if (!iGst) {
      let cGstOnPenal = this.typeService.manageAmount(
        (emiData.penaltyChargesGST ?? 0) / 2,
      );
      let sGstOnPenal = this.typeService.manageAmount(
        (emiData.penaltyChargesGST ?? 0) / 2,
      );
      emiData.penaltyChargesGST = cGstOnPenal + sGstOnPenal;
    }

    bounceCharge =
      +(emiData?.bounceCharge ?? 0) + +(emiData?.gstOnBounceCharge ?? 0);
    bounceCharge = this.typeService.manageAmount(bounceCharge);
    penalCharges = +(emiData?.dpdAmount ?? 0 + emiData?.penaltyChargesGST ?? 0);
    penalCharges = this.typeService.manageAmount(penalCharges);
    legalCharge = +(emiData?.legalCharge ?? 0 + emiData?.legalChargeGST ?? 0);
    legalCharge = this.typeService.manageAmount(legalCharge);

    let principalEMI = emiData?.principalCovered ?? 0;
    let interestEMI = emiData?.interestCalculate ?? 0;
    let penaltyEMI = this.typeService.manageAmount(emiData?.penalty ?? 0);
    let regIntAmount = this.typeService.manageAmount(
      emiData?.regInterestAmount ?? 0,
    );
    let paidAmount = 0;
    let paidInterest = 0;
    let paidPrincipal = 0;
    let paidPenalty = 0;
    let paidRegInt = 0;
    let paidBounceCharge = 0;
    let paidPenalCharge = 0;
    let paidLegalCharge = 0;
    (emiData?.transactionData ?? []).forEach((e) => {
      try {
        if (e?.status === kCompleted) {
          paidAmount += e?.paidAmount ?? 0;
          paidInterest += e?.interestAmount ?? 0;
          paidPrincipal += e?.principalAmount ?? 0;
          paidPenalty += e?.penaltyAmount ?? 0;
          paidRegInt += e?.regInterestAmount ?? 0;
          paidBounceCharge +=
            (e?.bounceCharge ?? 0) +
            (e?.sgstOnBounceCharge ?? 0) +
            (e?.cgstOnBounceCharge ?? 0) +
            (e?.igstOnBounceCharge ?? 0);
          paidPenalCharge +=
            (e?.penalCharge ?? 0) +
            (e?.sgstOnPenalCharge ?? 0) +
            (e?.cgstOnPenalCharge ?? 0) +
            (e?.igstOnPenalCharge ?? 0);
          paidLegalCharge +=
            (e?.legalCharge ?? 0) +
            (e?.sgstOnLegalCharge ?? 0) +
            (e?.cgstOnLegalCharge ?? 0) +
            (e?.igstOnLegalCharge ?? 0);
        }
      } catch (error) {}
    });
    paidAmount -= emiData?.partPaymentPenaltyAmount ?? 0;

    interestEMI -= paidInterest;
    principalEMI -= paidPrincipal;
    penaltyEMI -= paidPenalty;
    bounceCharge -= paidBounceCharge;
    penalCharges -= paidPenalCharge;
    legalCharge -= paidLegalCharge;
    regIntAmount -= paidRegInt;

    // interest Amount update
    if (interestEMI >= amount) {
      interestAmount = amount;
      amount = 0;
    } else {
      interestAmount = interestEMI;
      amount -= interestEMI;
    }

    // principal Amount update
    if (principalEMI >= amount) {
      principalAmount = amount;
      amount = 0;
    } else {
      principalAmount = principalEMI;
      amount -= principalEMI;
    }

    // penalty Amount update
    if (penaltyEMI >= amount) {
      penaltyAmount = amount;
      amount = 0;
    } else {
      penaltyAmount = penaltyEMI;
      amount -= penaltyEMI;
    }

    // Deffered Amount update
    if (regIntAmount >= amount) {
      regInterestAmount = amount;
      amount = 0;
    } else {
      regInterestAmount = regIntAmount;
      amount -= regIntAmount;
    }

    //bounce charge update
    if (bounceCharge >= amount) {
      bounceChargeAmount = amount;
      amount = 0;
    } else {
      bounceChargeAmount = bounceCharge;
      amount -= bounceCharge;
    }
    deductedBounceCharge = bounceChargeAmount;

    //penal charge update
    if (penalCharges >= amount) {
      penalChargesAmount = amount;
      amount = 0;
    } else {
      penalChargesAmount = penalCharges;
      amount -= penalCharges;
    }
    deductedPenalCharge = penalChargesAmount;

    //legal charge update
    if (legalCharge >= amount) {
      legalChargeAmount = amount;
      amount = 0;
    } else {
      legalChargeAmount = legalCharge;
      amount -= legalCharge;
    }
    deductedLegalCharge = legalChargeAmount;

    //gst calculation
    const gstBounceChargeAmount = bounceChargeAmount - bounceChargeAmount;
    let gstPenalChargeAmount = penalChargesAmount - penalChargesAmount;
    sgstPenalChargeAmount = iGst
      ? 0
      : this.typeService.manageAmount(gstPenalChargeAmount / 2);
    cgstPenalChargeAmount = sgstPenalChargeAmount;
    igstPenalChargeAmount = !iGst
      ? 0
      : this.typeService.manageAmount(gstPenalChargeAmount);
    gstPenalChargeAmount =
      sgstPenalChargeAmount + cgstPenalChargeAmount + igstPenalChargeAmount;

    const gstLegalChargeAmount = legalChargeAmount - legalChargeAmount / 1.18;

    bounceChargeAmount = bounceChargeAmount - gstBounceChargeAmount;
    penalChargesAmount = penalChargesAmount - gstPenalChargeAmount;
    legalChargeAmount = legalChargeAmount - gstLegalChargeAmount;

    sgstBounceChargeAmount = iGst ? 0 : +(gstBounceChargeAmount / 2).toFixed(2);
    cgstBounceChargeAmount = sgstBounceChargeAmount;
    igstBounceChargeAmount = !iGst ? 0 : +gstBounceChargeAmount.toFixed(2);

    sgstLegalChargeAmount = iGst ? 0 : +(gstLegalChargeAmount / 2).toFixed(2);
    cgstLegalChargeAmount = sgstLegalChargeAmount;
    igstLegalChargeAmount = !iGst ? 0 : +gstLegalChargeAmount.toFixed(2);

    //Add difference back to charges
    bounceChargeAmount +=
      deductedBounceCharge -
      (bounceChargeAmount +
        sgstBounceChargeAmount +
        cgstBounceChargeAmount +
        igstBounceChargeAmount);

    penalChargesAmount +=
      deductedPenalCharge -
      (penalChargesAmount +
        sgstPenalChargeAmount +
        cgstPenalChargeAmount +
        igstPenalChargeAmount);

    legalChargeAmount +=
      deductedLegalCharge -
      (legalChargeAmount +
        sgstLegalChargeAmount +
        cgstLegalChargeAmount +
        igstLegalChargeAmount);

    regInterestAmount = +regInterestAmount.toFixed(2);
    bounceChargeAmount = +bounceChargeAmount.toFixed(2);
    penalChargesAmount = +penalChargesAmount.toFixed(2);
    legalChargeAmount = +legalChargeAmount.toFixed(2);
    //#endregion

    let isGreaterEMI = false;
    if (
      principalAmount == 0 &&
      interestAmount == 0 &&
      penaltyAmount == 0 &&
      regInterestAmount == 0 &&
      bounceCharge == 0 &&
      penalCharges == 0 &&
      legalCharge == 0
    )
      return kInternalError;
    else if (amount > 20) isGreaterEMI = true;
    return {
      principalAmount,
      interestAmount,
      penaltyAmount,
      regInterestAmount,
      bounceChargeAmount,
      penalChargesAmount,
      legalChargeAmount,
      cgstBounceChargeAmount,
      sgstBounceChargeAmount,
      igstBounceChargeAmount,
      cgstPenalChargeAmount,
      sgstPenalChargeAmount,
      igstPenalChargeAmount,
      cgstLegalChargeAmount,
      igstLegalChargeAmount,
      sgstLegalChargeAmount,
      isGreaterEMI,
    };
  }

  //#region get waiver amount in full pay
  private getWaiverAmountInfullpaysetted(fullPIPData, waiverAmount) {
    const data = {};
    try {
      if (fullPIPData) {
        // Putting Foreclosure Charge into Single Key
        const keys = Object.keys(fullPIPData);
        for (let index = 0; index < keys.length; index++) {
          const value = fullPIPData[keys[index]];
          fullPIPData[keys[index]].fullPayForecloseCharge =
            (value?.forClosureAmount ?? 0) +
            (value?.sgstForClosureCharge ?? 0) +
            (value.cgstForClosureCharge ?? 0) +
            (value?.igstForClosureCharge ?? 0);
        }
      }
      const list = [
        'fullPayLegalCharge',
        'fullPayPenal',
        'fullPayBounce',
        'fullPayRegInterest',
        'fullPayPenalty',
        'fullPayForecloseCharge',
        'fullPayInterest',
        'fullPayPrincipal',
      ];
      for (let index = 0; index < list.length; index++) {
        const key = list[index];
        const keys = Object.keys(fullPIPData);
        keys.forEach((emiId) => {
          try {
            const amount = fullPIPData[emiId][key];
            let tempAmount = 0;
            if (waiverAmount >= amount) {
              waiverAmount -= amount;
              tempAmount = amount;
            } else {
              tempAmount = waiverAmount;
              waiverAmount = 0;
            }
            const tempData = {};
            tempData[key] = tempAmount;
            if (!data[emiId]) data[emiId] = {};
            data[emiId][key] = tempAmount;
          } catch (error) {}
        });
      }
    } catch (error) {}
    return data;
  }
  //#endregion

  //#region create payment
  async funCreatePaymentOrder(body) {
    try {
      const loanId = body?.loanId;
      if (!loanId) return kParamMissing('loanId');
      const amount = +body?.amount;
      if (amount) {
        const isInDecimal = Number.isInteger(amount);
        if (!isInDecimal)
          return k422ErrorMessage('Amount In Decimal is Not Allowed');
      }
      if (body?.dueDate && body.settledId) {
        const toDay = this.typeService.getGlobalDate(new Date());
        const dueDate = this.typeService.getGlobalDate(new Date(body.dueDate));
        if (dueDate.getTime() < toDay.getTime())
          return k422ErrorMessage(kPleaseProvideFutureDue);
      }

      // if payments go update then check rights
      if (body?.adminId && body?.transactionId && body?.utr) {
        const checkAccess = await this.checkAdminAccessToUpdatePayments(
          'payment update',
          body?.adminId,
        );
        if (checkAccess !== true) return checkAccess;
      }
      // if check waive off access
      if (
        (body?.adminId && body?.isCloseLoan === true) ||
        body?.isPromoCode === true
      ) {
        if (body?.isPromoCode === true) {
          // check eligibility of promo code
          let isEligible =
            await this.promoCodeService.getUserWaiveOffEligibility({
              userId: null,
              loanId: body?.loanId,
            });
          if (isEligible?.emiDetails) {
            body.remarks = promoCodeRemark;
            body.isCloseLoan = true;
            body.amount = isEligible?.emiDetails?.rePayAmount;
          }
        }
        body.adminId = body?.adminId ?? SYSTEM_ADMIN_ID;
        body.isCloseLoan = true;
        if (body?.source == 'PAYMENT_LINK') {
          const checkAccess = await this.checkAdminAccessToUpdatePayments(
            'waive off',
            body?.adminId,
          );
          if (checkAccess !== true) return checkAccess;
        }
      }

      // find loan Data
      const loanData = await this.getLoanData(body);

      if (loanData === k500Error) return kInternalError;
      if (loanData?.message) return loanData;
      const prePareAmount: any = await this.prePareAmount(body, loanData);
      if (prePareAmount?.isDirectLoanClose) {
        const addInitializedEntry: any =
          await this.creditTransService.closeLoanDirectly(
            prePareAmount,
            body,
            loanData,
          );
        if (addInitializedEntry?.message) return addInitializedEntry;
        return addInitializedEntry;
      }

      if (prePareAmount === false) return k422ErrorMessage(kSomthinfWentWrong);
      if (!prePareAmount?.dueAmount) return prePareAmount;
      const result: any = await this.prePareTransaction(
        body,
        prePareAmount,
        loanData,
      );
      if (!result || result === k500Error || result?.statusCode) return result;
      if (
        body?.adminId &&
        body?.transactionId &&
        body?.utr &&
        (body?.source == 'UPI' || body?.source == 'UPI2')
      )
        await this.checkAndUpdatePaymenst(body);

      const isLastEmi = loanData?.emiData.some(
        (el) => el.id == body?.emiId && el.partOfemi == 'LAST',
      );

      if (
        (body?.emiId == -1 || isLastEmi) &&
        loanData?.registeredUsers?.isBlacklist != '1' &&
        body?.subSource != kAutoDebit
      ) {
        if (!body?.userId) body.userId = loanData?.registeredUsers?.id ?? null;
        this.checkPostLoanClosureOffer(body).catch((error) => {});
      }
      return result;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region check admin access to update
  async checkAdminAccessToUpdatePayments(acceessType, adminId) {
    try {
      if (acceessType) {
        const result = await this.adminRepo.checkHasAccess(
          adminId,
          acceessType,
        );
        if (result === true) return true;
        else return result;
      } else return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region prepare transaction
  private async prePareTransaction(body: any, prePareAmount: any, loanData) {
    try {
      // Params preparation
      const userId = loanData?.registeredUsers?.id;
      const remainingCredit = loanData?.registeredUsers?.remainingCredit ?? 0;
      const loanId = body?.loanId;
      const mode = body?.mode;
      const phone = body?.phone ?? loanData?.registeredUsers?.phone;
      const email = body?.email ?? loanData?.registeredUsers?.email;

      let amount = prePareAmount?.dueAmount;
      body.dueAmount = amount;
      const name = loanData?.registeredUsers?.fullName;
      let subSource = body?.subSource ?? 'WEB';
      const source = body?.source ?? kCashfree;
      const bySDK = body?.bySDK === true ? true : false;
      const payerVa = body?.upiId;
      let subStatus = body.subStatus;
      let needNotify = true;

      // Credit Payment Params.
      const creditAmount = prePareAmount?.creditAmount ?? 0;
      if (
        creditAmount > 0 &&
        subStatus != kLoanClosureStr &&
        subStatus != kLoanSettled
      )
        subStatus = kCreditTransaction;
      if (remainingCredit > 0 && creditAmount == 0) {
        let attributes: any = [
          [
            fn(
              'SUM',
              literal(
                `COALESCE("waiver", 0) + COALESCE("paid_waiver", 0) + COALESCE("unpaid_waiver", 0)`,
              ),
            ),
            'totalWaiver',
          ],
        ];
        const waiverData = await this.emiRepo.getTableWhereData(attributes, {
          where: {
            loanId,
          },
        });
        if (waiverData === k500Error)
          throw new Error('Error in Getting Waiver Data from EMI Table');
        let emiId = +body?.emiId;
        let emiNumber;
        if (emiId && emiId != -1) {
          let emiNo = await this.emiRepo.getRowWhereData(['partOfemi'], {
            where: { id: emiId },
          });
          if (emiNo === k500Error)
            throw new Error('Error in Getting EMI Data from EMI Table');
          emiNumber = emiNo;
        }
        let waiverAmount = waiverData[0]?.totalWaiver ?? 0;
        let adminOfferCondition =
          body?.adminId &&
          (body?.subStatus === 'LOAN_CLOSURE' ||
            body?.subStatus === 'LOAN_SETTLEMENT');
        if (
          waiverAmount > 0 &&
          (emiNumber == 'LAST' || body?.emiId == -1) &&
          !adminOfferCondition
        )
          subStatus = kCreditTransaction;
      }
      if (body.isLoanClosure && prePareAmount.type == 'FULLPAY')
        subStatus = 'LOAN_CLOSURE';
      else if (body.isLoanSettled && prePareAmount.type == 'FULLPAY')
        subStatus = 'LOAN_SETTLEMENT';

      const note = `${EnvConfig.nbfc.nbfcCodeName}-${loanId}`;
      if (source === 'AUTOPAY') subSource = kAutoDebit;
      // To Calculate Max DPD for Current Transaction
      let maxDPD = 0;
      const delayedStatus = loanData?.registeredUsers?.loanStatus;
      if (delayedStatus == 2 || delayedStatus == 3) {
        const emiData = await this.emiRepo.getTableWhereData(['penalty_days'], {
          where: { loanId, payment_due_status: '1' },
        });
        if (emiData === k500Error) return kInternalError;
        emiData.forEach((ele) => {
          if (ele.penalty_days > 0 && ele.penalty_days > maxDPD)
            maxDPD = +ele.penalty_days;
        });
      }
      const preparedData: any = {
        amount,
        email,
        phone,
        name,
        loanId,
        subSource,
      };
      body.p_type = prePareAmount.type;
      // Order preparation
      const creationData: any = { loanId: body.loanId, userId };
      creationData.paidAmount = amount;
      creationData.roundOff = prePareAmount?.roundOff ?? 0;
      creationData.source = source;
      creationData.status = kInitiated;
      creationData.subSource = subSource;
      creationData.subStatus = subStatus;
      creationData.principalAmount = prePareAmount?.principalAmount;
      creationData.interestAmount = prePareAmount?.interestAmount;
      creationData.penaltyAmount = prePareAmount?.penaltyAmount;
      creationData.forClosureAmount = prePareAmount?.forClosureAmount ?? 0;
      creationData.sgstForClosureCharge =
        prePareAmount?.sgstForClosureCharge ?? 0;
      creationData.cgstForClosureCharge =
        prePareAmount?.cgstForClosureCharge ?? 0;
      creationData.igstForClosureCharge =
        prePareAmount?.igstForClosureCharge ?? 0;
      creationData.forClosureDays = prePareAmount?.forClosureDays ?? 0;
      creationData.regInterestAmount = prePareAmount?.regInterestAmount;
      creationData.bounceCharge = prePareAmount?.bounceCharge;
      creationData.penalCharge = prePareAmount?.penalCharge;
      creationData.legalCharge = prePareAmount?.legalCharge;
      creationData.cgstOnBounceCharge = prePareAmount?.cgstOnBounceCharge;
      creationData.igstOnBounceCharge = prePareAmount?.igstOnBounceCharge;
      creationData.sgstOnBounceCharge = prePareAmount?.sgstOnBounceCharge;
      creationData.cgstOnPenalCharge = prePareAmount?.cgstOnPenalCharge;
      creationData.igstOnPenalCharge = prePareAmount?.igstOnPenalCharge;
      creationData.sgstOnPenalCharge = prePareAmount?.sgstOnPenalCharge;
      creationData.cgstOnLegalCharge = prePareAmount?.cgstOnLegalCharge;
      creationData.igstOnLegalCharge = prePareAmount?.igstOnLegalCharge;
      creationData.sgstOnLegalCharge = prePareAmount?.sgstOnLegalCharge;
      creationData.accStatus = kCalBySystem;
      creationData.type = prePareAmount.type;
      creationData.adminId = body?.adminId ?? SYSTEM_ADMIN_ID;
      creationData.followerId = loanData?.followerId;
      creationData.remarks = body?.remarks ?? '';
      creationData.maxDPD = maxDPD;
      const approvedAmount = +loanData?.netApprovedAmount;
      const totalFees = loanData?.feesIncome ?? 0;
      const chargePr = ((totalFees ?? 0) * 100) / approvedAmount;
      const principal = prePareAmount?.principalAmount ?? 0;
      const income = Math.round((principal * chargePr) / 100);
      creationData.feesIncome = income;
      creationData.mode = mode;

      if (creditAmount > 0) creationData.usedCreditAmount = creditAmount;

      let merchantTranId;
      if (source === KICICIUPI || source === KICICIUPI2 || source === KYESUPI) {
        const randomCode = this.typeService.generateRandomCode(12);
        merchantTranId = EnvConfig.nbfc.nbfcCodeName + randomCode;
        creationData.transactionId = merchantTranId;
        if (body?.mode) body.merchantTranId = merchantTranId;
        const isCheck = await this.checkTransactionId(creationData);
        if (isCheck) return k422ErrorMessage(kTransactionIdExists);
      }
      if (source == KICICIUPI2) amount = amount.toFixed(2);
      if (prePareAmount?.settled_type)
        creationData.settled_type = prePareAmount?.settled_type ?? '';
      if (body.emiId != -1) creationData.emiId = body.emiId;
      if (source === kCashfree && subSource !== kAutoDebit) {
        const ExistLink = await this.findSametransactionIsExist(creationData);
        if (ExistLink) return { paymentLink: ExistLink };
      }

      const result = await this.transactionRepo.createRowData(creationData);
      if (result === k500Error) return kInternalError;
      if (
        creationData.type == 'PARTPAY' &&
        creationData.subSource == kAutoDebit
      )
        needNotify = false;
      // DPD UPDATE ASSET QUERY IF
      this.storeAssetDpd(result, loanId).catch((error) => {});

      const isAutoDebit = source === 'AUTOPAY' || subSource == kAutoDebit;
      const createdData = isAutoDebit
        ? await this.prePareAutoDebit(body, loanData)
        : bySDK
        ? await this.razorpayService.createOrder({ amount }, kSDK)
        : payerVa
        ? await this.iciciService.CollectPay({
            amount,
            payerVa,
            note,
            merchantTranId,
          })
        : await this.createLink(body, preparedData);
      if (createdData == k500Error) return kInternalError;
      else if (createdData?.statusCode) return createdData;
      const updatedData: any = {};
      updatedData.transactionId = bySDK
        ? createdData?.id
        : createdData?.order_id;
      updatedData.response = createdData?.response;
      const subscriptionDate = createdData?.subscriptionDate;
      if (createdData?.subscriptionDate) {
        updatedData.subscriptionDate = subscriptionDate;
        if (isAutoDebit) {
          /// check how many autodabit place subscriptionDate
          const tran_unique_id: any = await this.checkHowManyAutodebitPlease(
            loanId,
            subscriptionDate,
          );
          if (tran_unique_id?.message) return tran_unique_id;
          updatedData.tran_unique_id = tran_unique_id;
        }
      }

      if (isAutoDebit) {
        updatedData.source = body?.source;
        updatedData.subSource = subSource;
      } else if (body?.source == kCashfree && body?.subSource == kAutoDebit) {
        updatedData.source = body?.source;
        updatedData.subSource = body?.subSource;
      } else if (body?.source == 'UPI' || body?.source == 'UPI2') {
        updatedData.paidAmount = body?.amount ?? creationData.paidAmount;
        updatedData.subSource = body?.subSource ?? creationData.subSource;
      }

      // Order creation in database
      if (
        source !== KICICIUPI &&
        source !== KICICIUPI2 &&
        source !== KYESUPI &&
        source !== 'PAYMENT_LINK'
      ) {
        const isCheck = await this.checkTransactionId(updatedData);
        if (isCheck) return k422ErrorMessage(kTransactionIdExists);
      }
      //update transaction data
      if (source !== 'PAYMENT_LINK') {
        const transUpdated = await this.transactionRepo.updateRowData(
          updatedData,
          result?.id,
        );
        if (transUpdated === k500Error) return kInternalError;
      }

      //for showing UPI app name to user for which he entering upi id
      let upiMessage;
      if (payerVa) {
        let upiHandleName = payerVa.split('@')[1];
        upiHandleName = upiHandleName.toLowerCase();
        let upiApp;
        if (
          upiHandleName == 'apl' ||
          upiHandleName == 'yapl' ||
          upiHandleName == 'rapl'
        )
          upiApp = 'Amazon Pay';
        else if (upiHandleName == 'axisb') upiApp = 'CRED';
        else if (
          upiHandleName == 'okaxis' ||
          upiHandleName == 'okhdfcbank' ||
          upiHandleName == 'okicici' ||
          upiHandleName == 'oksbi'
        )
          upiApp = 'Google Pay';
        else if (upiHandleName == 'yesg') upiApp = 'Groww';
        else if (upiHandleName == 'ikwik') upiApp = 'MobiKwik';
        else if (
          upiHandleName == 'ybl' ||
          upiHandleName == 'ibl' ||
          upiHandleName == 'axl'
        )
          upiApp = 'Phonepe';
        else if (
          upiHandleName == 'waaxis' ||
          upiHandleName == 'waicici' ||
          upiHandleName == 'wahdfcbank' ||
          upiHandleName == 'wasbi'
        )
          upiApp = 'WhatsApp';
        else if (upiHandleName == 'upi') upiApp = 'BHIM';
        else if (upiHandleName == 'paytm') upiApp = 'Paytm';
        else upiApp = 'UPI App';
        upiMessage = `We have sent you payment request on ##${upiApp}##`;
      }
      const link = createdData.payLink;
      body.appType = loanData?.appType;
      body.fcmToken = loanData?.registeredUsers?.fcmToken;
      body.needNotify = needNotify;
      // Auto debit
      if (subSource === kAutoDebit || body?.subSource == kAutoDebit) {
        createdData.id = result.id;
        const data: any = await this.placeManualAutoDebit(
          body,
          createdData,
          preparedData,
        );
        if (data === k500Error) return kInternalError;
        const option = { where: { id: createdData.id } };
        const att = ['status', 'type'];
        const find = await this.transactionRepo.getRowWhereData(att, option);
        if (!find || find === k500Error) return kInternalError;
        // Preparing object for the mail data....
        if (find.status == kInitiated && find.type == kEMIPay) {
          const prepareMailData = {
            name,
            email,
            loanId,
            paidAmount: amount,
            scheduledDate: subscriptionDate,
            status: kInitiated,
            appType: loanData.appType,
            userId,
          };
          if (needNotify) await this.sendAutoDebitMail(prepareMailData);
        }
      }
      if (bySDK && createdData?.id)
        return { order_id: createdData?.id, transactionId: result.id };
      // Intimation to user
      if (needNotify) await this.sendNotification(link, body, loanData);

      //WhatsApp Trigger for the Payment
      const paymentKey = loanId * 484848;
      const template = [kPartPay, kEMIPay, kFullPay];

      if (
        template.includes(creationData?.type) &&
        needNotify &&
        paymentKey &&
        source == 'PAYMENT_LINK' &&
        body?.adminId
      ) {
        const Phone = body?.phone ?? loanData?.registeredUsers?.phone;
        const allPhones = loanData?.registeredUsers?.allPhone ?? [];

        const phonesToNotify = [
          ...new Set(
            allPhones
              .map((item) => this.cryptService.decryptPhone(item))
              .filter((phone) => phone && phone !== '500'),
          ),
        ];
        if (Phone && !phonesToNotify.includes(Phone))
          phonesToNotify.push(Phone);

        for (let i = 0; i < phonesToNotify.length; i++) {
          const phone = phonesToNotify[i];
          this.whatsappService.sendWhatsAppMessageMicroService({
            title: kPaymentLinkTitle,
            appType: loanData?.appType,
            userId,
            loanId,
            paidAmount: body?.dueAmount,
            customerName: name ?? 'User',
            email,
            contactNumber: kHelpContact,
            number: phone,
            paymentLink: paymentKey,
          });
        }
      }
      return {
        paymentLink: link,
        transactionId: result.id,
        upiMessage,
      };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  async sendAutoDebitMail(emailData: any) {
    try {
      const isFailed = emailData.status === 'FAILED';

      const templateType = isFailed ? kAutoDebitFailed : kAutoDebitInitiated;

      const subject = isFailed
        ? kAutoDebitFailedSubject
        : kAutoDebitInitiatedSubject;

      const autoDebitTemplate =
        await this.commonSharedService.getEmailTemplatePath(
          templateType,
          emailData.appType,
        );

      const formattedAmount = this.typeService.amountNumberWithCommas(
        emailData.paidAmount,
      );

      const formattedDate = this.typeService.getDateFormatted(
        emailData.scheduledDate,
      );

      let htmlData = fs.readFileSync(autoDebitTemplate).toString();
      htmlData = this.replaceAll(htmlData, '##AMOUNT##', formattedAmount);
      htmlData = htmlData
        .replace('##NAME##', emailData.name)
        .replace('##LOANID##', emailData.loanId)
        .replace('##DATE##', formattedDate);

      if (isFailed) {
        const key = emailData.loanId * 484848;
        const paymentLink = nPaymentRedirect + key;
        htmlData = htmlData
          .replace('##REASON##', emailData.failedResponse)
          .replace('##PAYMENT_LINK##', paymentLink);
      }

      // Sending Mail to the user....
      await this.sharedNotification.sendMailFromSendinBlue(
        emailData.email,
        subject,
        htmlData,
        emailData?.userId,
        null,
        null,
        null,
        kNoReplyMail,
      );
      return { success: true };
    } catch (error) {
      console.error('Error in sending mail for Auto Debit:', error);
      return { 'Failed to send email': kInternalError };
    }
  }

  //#region find same transaction is exit or not
  private async findSametransactionIsExist(data) {
    try {
      const options = { where: data, order: [['id', 'desc']] };
      const att = ['id', 'response'];
      const result = await this.transactionRepo.getRowWhereData(att, options);
      if (!result || result === k500Error) return '';
      else if (result?.response) {
        const response = JSON.parse(result.response);
        const expiryTime = new Date(response?.order_expiry_time).getTime();
        const nowTime = new Date().getTime();
        if (expiryTime > nowTime + 60 * 1000 && response?.order_token)
          return CF_RETURN_URL + response?.order_token;
      }
    } catch (error) {}
  }
  //#endregion

  //#region update payments if admin want to
  async checkAndUpdatePaymenst(body) {
    try {
      let completionDate: any = body?.payment_date
        ? new Date(body.payment_date)
        : new Date();
      const year = completionDate.getFullYear();
      if (isNaN(year) || year == 1970) completionDate = new Date();
      completionDate = this.typeService.getGlobalDate(completionDate);
      completionDate = completionDate.toJSON();
      const otption = { where: { transactionId: body?.transactionId } };
      const att = ['id', 'loanId', 'userId', 'emiId', 'type', 'subSource'];
      const find = await this.transactionRepo.getRowWhereData(att, otption);

      if (!find || find == k500Error) return kInternalError;

      const paymentData: any = { id: find.id, status: 'COMPLETED' };
      paymentData.utr = body?.utr;
      paymentData.completionDate = completionDate;
      paymentData.paymentTime = this.typeService.getIstTime().toJSON();
      paymentData.loanId = find.loanId;
      paymentData.userId = find.userId;
      paymentData.type = find.type;
      paymentData.subSource = find.subSource;
      if (find.emiId) paymentData.emiId = find.emiId;
      await this.markTransactionAsComplete(paymentData);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region prepare auto debit
  private async prePareAutoDebit(body, loanData) {
    try {
      const toDay = this.typeService.getGlobalDate(new Date());
      toDay.setDate(toDay.getDate() + 1);
      let submissionDate = body?.submissionDate ?? toDay.toJSON();
      submissionDate = this.typeService.getGlobalDate(new Date(submissionDate));

      let filter = [];
      if (body.emiId == -1)
        filter = loanData.emiData.filter((e) => e.payment_status === '0');
      else filter = loanData.emiData.filter((e) => body.emiId === e.id);
      let dateIsGre = false;
      filter.forEach((e) => {
        if (new Date(e.emi_date).getTime() > submissionDate.getTime())
          dateIsGre = true;
      });
      if (dateIsGre) return k422ErrorMessage(kPleaceEnterValidSubmissionDate);

      // prepare order id or update payments source
      body.subSource = kAutoDebit;
      body.source = body.source == kRazorpay ? kRazorpay : kCashfree;
      let order_id;
      const type = body.p_type;
      const targetData = submissionDate.toJSON().substring(0, 10);
      const subscriptionDate: any = submissionDate.toJSON();
      if (type === 'FULLPAY') order_id = 'FP-' + body.loanId + '-' + targetData;
      else if (type === 'EMIPAY') order_id = body.emiId + '-' + targetData;
      else if (type === 'PARTPAY')
        order_id = new Date().getTime() + '-' + body.emiId + '-' + targetData;

      const subscriptionData = loanData?.subscriptionData;
      const mandateData = loanData?.mandateData;
      let referenceId;
      if (subscriptionData) {
        /* For auto debit -> Cashfree & Signdesk requires referenceId 
          while Razorpay works based on token which we stored as umrn */
        body.source = subscriptionData.mode;
        referenceId =
          body.source == kRazorpay
            ? subscriptionData.umrn
            : subscriptionData.referenceId;
      } else if (mandateData || mandateData.length > 0) {
        body.source = 'SIGNDESK';
        referenceId = mandateData[0].emandate_id;
      }
      if (!referenceId) return kInternalError;
      order_id += '-id-' + referenceId;
      return { order_id, referenceId, targetData, subscriptionDate };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region Payment link creation
  private async createLink(body, preparedData: any) {
    try {
      if (body?.source === 'RAZORPAY') {
        return await this.razorpaySer.createRazorpay(
          preparedData,
          body.sendSMS,
        );
      } else if (
        body?.mode == 'GOOGLEPAY' ||
        body?.mode == 'PAYTM' ||
        body?.mode == 'PHONEPE' ||
        body?.mode == 'AMAZONPAY' ||
        body?.mode == 'OTHER'
      ) {
        if (body?.subSource && UPI_SERVICE && !body?.transactionId) {
          return await this.getUPILink(body);
        }
        return { order_id: body.transactionId, payLink: '' };
      } else if (body?.source === 'UPI' || body?.source === 'UPI2') {
        return { order_id: body.transactionId, payLink: '' };
      } else if (body?.source === 'PAYMENT_LINK') {
        const key = body?.loanId * 484848;
        const payLink = nPaymentRedirect + key;
        return { order_id: null, payLink };
      } else {
        preparedData.returnURL = CF_RETURN_URL;
        return await this.cashFreeService.createPaymentOrder(preparedData);
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region check how many autodebit please on subscriptionDate
  private async checkHowManyAutodebitPlease(loanId, subscriptionDate) {
    try {
      const where = { loanId, subscriptionDate };
      const count = await this.transactionRepo.getCountsWhere({
        useMaster: false,
        where,
      });
      if (count === k500Error) return kInternalError;
      if (count > MAX_AUTO_DEBIT_COUNT)
        return k422ErrorMessage(kYouReachedAutoDebitLimit);
      return loanId + '_' + subscriptionDate.substring(0, 10) + '_' + count;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region place auto debit
  private async placeManualAutoDebit(
    body,
    createdData,
    preparedData: any = {},
  ) {
    try {
      let isADNotPlace = false;
      const referenceId = createdData.referenceId;
      const date = createdData.targetData;
      const amount = body.dueAmount;
      const needNotify = body?.needNotify;
      const id = createdData.id;
      if (body.subSource == kAutoDebit && referenceId && amount && date && id) {
        // Cashfree autodebit
        if (body.source === kCashfree) {
          const subscriptionData =
            await this.cashFreeService.getSubscriptionStatus(referenceId);
          if (subscriptionData != k500Error) {
            const status = subscriptionData.status;
            const isOnHold = status == 'ON_HOLD';
            if (isOnHold)
              await this.cashFreeService.reActivateSubscription(referenceId);
          }
          await this.delay(50);
          const data = { referenceId, amount, date };
          const result = await this.cashFreeService.placeAutoDebit(data);
          // Error msg -> Autodebit was not placed successfully
          if (result?.adNotPlaced === true && id) {
            const updatedData = {
              status: kFailed,
              response: JSON.stringify(result),
            };
            await this.repoManager.updateRowData(
              TransactionEntity,
              updatedData,
              id,
            );
            isADNotPlace = true;
          }
          // Success
          else await this.transactionRepo.updateRowData(result, id);
        }
        // Razorpay autodebit
        else if (body.source == kRazorpay) {
          const orderData: any = {
            email: preparedData.email,
            token: referenceId,
            contact: preparedData.phone,
            name: preparedData.name,
            amount: body.amount ?? amount,
          };
          const response = await this.razorpayService.placeAutoDebit(orderData);
          if (response.message || !response.utr) {
            // Error msg -> Autodebit was not placed successfully
            if (response?.adNotPlaced === true && id) {
              const updatedData = {
                status: kFailed,
                response: JSON.stringify(response),
              };
              await this.repoManager.updateRowData(
                TransactionEntity,
                updatedData,
                id,
              );
            }
            isADNotPlace = true;
          }
          const updateResponse = await this.transactionRepo.updateRowData(
            response,
            id,
          );
          if (updateResponse == k500Error) return kInternalError;
        }
        // Signdesk autodebit
        else {
          const finalData = {
            paidAmount: amount,
            mandateId: referenceId,
            targetDate: date,
            id,
          };
          const result = await this.placeAutoDebit([finalData]);
          if (result === k500Error) return kInternalError;
        }

        if (needNotify)
          await this.sendADNotificationSMS({
            isADNotPlace,
            body,
            createdData,
            preparedData,
          });
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region send mail
  private async sendNotification(
    link: string,
    body,
    loanData: loanTransaction,
  ) {
    try {
      const user = loanData?.registeredUsers;
      const userId = body?.userId ?? user?.id;
      const name = user?.fullName;
      const allPhone = user?.allPhone ?? [];
      const email = body?.email ?? user?.email;
      const loanId = loanData.id;
      const appType = loanData?.appType ?? user?.appType;
      const adminId = body?.adminId;
      const loanAmount = (+(loanData?.netApprovedAmount ?? 0)).toFixed(2);
      const toDay = new Date().toJSON().substring(0, 10);
      const isSettled = body.settledId ? true : false;
      let dueAmount = body?.dueAmount;
      const emiId = body?.emiId ?? -1;
      const sendSMS = body.sendSMS?.toString() == 'true' ? true : false;
      if (emiId != -1) {
        const emi = loanData.emiData.find((e) => e.id == emiId);
        dueAmount = +emi.emi_amount + (emi?.penalty ?? 0);
      }
      dueAmount = (+dueAmount).toFixed(2);
      if (
        body.subSource != 'AUTODEBIT' &&
        link &&
        adminId &&
        adminId != SYSTEM_ADMIN_ID &&
        sendSMS
      ) {
        const amount = this.strService.readableAmount(
          body?.amount ?? dueAmount,
          true,
        );
        const content = `Dear ${name}, A payment of Rs. ${amount} requested from ${EnvConfig.nbfc.nbfcName} against your Loan id - ${loanId}. Please make the payment via App or Link. For any query or assistance reach us at ${kHelpContact}.`;
        const smsOptions = {
          NAME: name,
          AMOUNT: amount,
          LOANID: loanId,
          PAYMENTLINK: link,
          NUMBER: kHelpContact,
          CONTACT: kHelpContact,
          NBFC: EnvConfig.nbfc.nbfcName,
          short_url: '1',
          appType,
        };
        const data = {
          userList: [userId],
          content,
          title: 'Payment Request',
          sendToallPhones: allPhone,
          adminId,
          smsOptions,
          isMsgSent: true,
          smsId:
            appType == 0
              ? kLspMsg91Templates.PAYMENT_REQUEST
              : kMsg91Templates.PAYMENT_REQUEST,
        };

        await this.sharedNotification.sendNotificationToUser(data);
      }
      if (
        body.source == 'CASHFREE' &&
        sendSMS &&
        body.subSource != 'AUTODEBIT'
      ) {
        try {
          // send cashfree email
          await this.sharedNotification.sendEmail('CASHFREE_PAYLINK', email, {
            name,
            link,
          });
        } catch (error) {}
      }
      try {
        if (body.dueDate && isSettled) {
          const dueDate = body.dueDate.substring(0, 10);
          const data = { name, loanId, loanAmount, toDay, dueAmount, dueDate };
          // send Settled email
          // await this.notificationService.sendEmail('SETTLED_LOAN', email, data);
        }
      } catch (error) {}
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region check manual transaction id is exit
  async checkTransactionId(data) {
    try {
      const options = { where: { transactionId: data.transactionId } };
      const find = await this.transactionRepo.getRowWhereData(['id'], options);
      if (find) return true;
      else return false;
    } catch (error) {
      return true;
    }
  }
  //#endregion

  async getUPILink(data) {
    try {
      let targetAmount = data.amount ?? data.dueAmount;
      targetAmount = Math.ceil(targetAmount);
      let paData = '';
      const upi_mode = await this.commonSharedService.getServiceName(
        'UPI_MODE',
      );
      // Fetching Active UPI Service for Active VPA
      let activeService =
        await this.commonSharedService.getActivePaymentServices();
      if (activeService?.message) return kInternalError;

      let origin;
      const mode = data?.mode.toUpperCase();
      switch (mode) {
        case 'GOOGLEPAY':
          origin = 'gPay://upi/pay/';
          break;
        case 'com.google.android.apps.nbu.paisa.user'.toUpperCase():
          data.mode = 'GOOGLEPAY';
          origin = 'gPay://upi/pay/';
          break;
        case 'PAYTM':
          origin = 'paytm://upi/pay';
          break;
        case 'net.one97.paytm'.toUpperCase():
          origin = 'paytm://upi/pay/';
          data.mode = 'PAYTM';
          break;
        case 'AMAZONPAY':
          origin = 'amazonpay://upi/pay';
          break;
        case 'PHONEPE':
          origin = 'phonepe://pay';
          break;
        case 'com.phonepe.app'.toUpperCase():
          origin = 'PhonePe://upi/pay/';
          data.mode = 'PHONEPE';
          break;
        case 'com.whatsapp'.toUpperCase():
          origin = 'upi://pay';
          break;
        default:
          origin = 'upi://pay';
          break;
      }

      let payLink = '';
      let transId = '';
      const merchantTranId = data?.merchantTranId;
      // Primary NBFC UPI Service
      if (data?.source == KICICIUPI || data?.source == KICICIUPI2) {
        // Selecting Current Active ICICI Service(1 or 2).
        let upiService = 0;
        if (activeService.includes('UPI_SERVICE')) upiService = 1;
        else if (activeService.includes('UPI_SERVICE2')) upiService = 2;
        if (upiService == 0) return kInternalError;

        // Service Wise Credentials
        let ICICIVPA = ICICI_CREDS[upiService].ICICIVPA;
        let ICICI_TERMINAL_ID = ICICI_CREDS[upiService].ICICI_TERMINAL_ID;
        if (upi_mode == KICICIUPI || upi_mode == KICICIUPI2)
          paData = `?pa=${ICICIVPA}&pn=ICICI%20Merchant`;
        const merchantData = paData;
        let amountData = `&am=${targetAmount}`;
        // Req. to ICICI
        const iciciQRRes = await this.iciciService.QR({
          targetAmount,
          merchantTranId,
        });
        const refId = iciciQRRes?.refId;
        transId = iciciQRRes?.merchantTranId;
        payLink = `${origin}${merchantData}&tr=${refId}${amountData}&cu=INR&mc=${ICICI_TERMINAL_ID}`;
      }
      // Other NBFCs UPI Service
      else if (data?.source == KYESUPI) {
        // Registering
        const intent = await this.yesUpiService.intentRegister({
          orderId: merchantTranId,
          amount: targetAmount,
        });
        if (intent.message) return intent;
        payLink = origin + intent.paymentLink;
        transId = intent.orderId;
      }

      return { order_id: transId, payLink };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  delay = (ms) => new Promise((res) => setTimeout(res, ms));

  //#region place auto debit
  async placeAutoDebit(rawList: any[]) {
    try {
      if (rawList.length === 0) return;
      const batchData = await this.signDeskService.placeAutoDebits(rawList);
      let batchId;
      let results = [];
      if (batchData != k500Error && batchData.status == 'success') {
        batchId = batchData.batch_id;
        results = batchData.result;
      }

      for (let index = 0; index < rawList.length; index++) {
        try {
          const rawData = rawList[index];
          const id = rawData.id;
          const updatedData: any = {};
          if (batchData == k500Error) {
            updatedData.paidAmount = 0;
            updatedData.status = 'FAILED';
            updatedData.subStatus = 'AD_NOT_PLACED';
            updatedData.response =
              '{"reason":"INTERNAL_SERVER_ERROR","response":"500"}';
          } else if (batchData.status == 'success') {
            const successData = results.find(
              (el) => el.emandate_id == rawData.mandateId,
            );
            if (successData?.status === 'success') {
              updatedData.utr = `${rawData.mandateId}-id-${batchId}`;
              updatedData.response = JSON.stringify(successData);
            } else if (successData?.status === 'failed') {
              updatedData.status = 'FAILED';
              updatedData.subStatus = 'FROM_BANK';
              updatedData.response = JSON.stringify(successData);
            } else {
              updatedData.status = 'FAILED';
              updatedData.subStatus = 'AD_NOT_PLACED';
              updatedData.response = successData
                ? JSON.stringify(successData)
                : '{"reason":"INTERNAL_SERVER_ERROR","response":"500"}';
            }
          } else {
            updatedData.status = 'FAILED';
            updatedData.subStatus = 'FROM_BANK';
            updatedData.response = JSON.stringify({
              reason: batchData ?? 'SIGNDESK_API_REJECTION',
            });
          }
          await this.transactionRepo.updateRowData(updatedData, id);
        } catch (error) {}
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }

  async placeTargetEMIs(reqData) {
    const loanIds = reqData.loanIds;
    if (!loanIds) return kParamMissing('loanIds');
    const emiDates = reqData.emiDates;
    if (!emiDates) return kParamMissing('emiDates');

    const attributes = ['emi_amount', 'id', 'loanId', 'penalty'];
    const options = {
      where: {
        emi_date: { [Op.in]: emiDates },
        loanId: { [Op.in]: loanIds },
        payment_status: '0',
      },
    };
    const emiList = await this.emiRepo.getTableWhereData(attributes, options);
    if (emiList == k500Error) return kInternalError;

    const today = new Date();
    today.setDate(today.getDate() + 1);
    for (let index = 0; index < emiList.length; index++) {
      try {
        const emiData = emiList[index];
        const dueAmount = +(emiData.emi_amount ?? 0) + (emiData.penalty ?? 0);
        const body = {
          adminId: SYSTEM_ADMIN_ID,
          emiId: emiData.id,
          loanId: emiData.loanId,
          sendSMS: false,
          source: 'AUTOPAY',
          amount: dueAmount,
          submissionDate: today.toJSON(),
          remarks: 'MISSED_EMI_AD_PLACED',
        };
        const response = await this.funCreatePaymentOrder(body);
      } catch (error) {}
    }
  }

  //#region create payment
  async createWaiverPaymentOrder(body) {
    try {
      body.sendSMS = body?.sendSMS ?? false;
      if (!body.loanId) return kParamMissing('loanId');
      if (body.transactionId && (!body.adminId || !body.utr))
        return kParamsMissing;
      const source = body?.source ?? kRazorpay;
      const subSource = body?.subSource ?? 'WEB';
      body.source = source;
      body.subSource = subSource;
      if (body.source) body.source = body.source.toUpperCase();
      if (body.subSource) body.subSource = body.subSource.toUpperCase();
      if (
        source != kCashfree &&
        source != kRazorpay &&
        source != 'UPI' &&
        source != 'UPI2'
      )
        return k422ErrorMessage(kWrongSourceType);
      const keys = Object.keys(body);
      for (let index = 0; index < keys.length; index++) {
        const key = keys[index];
        const value = body[key];
        if (value == null) delete body[key];
      }
      // if payments go update then check rights
      if (body?.adminId && body?.transactionId && body?.utr) {
        const checkAccess = await this.checkAdminAccessToUpdatePayments(
          'payment update',
          body?.adminId,
        );
        if (checkAccess !== true) return checkAccess;
      }

      // find loan Data
      const loanData: any = await this.getLoanDataWaiver(body);
      if (loanData?.message) return loanData;
      const prePareData: any = await this.prePareAmountForWaiver(
        loanData,
        body,
      );
      if (prePareData?.message) return prePareData;
      const prePareAmount = prePareData?.finalData;
      const result = await this.prePareWaiverTransaction(
        body,
        prePareAmount,
        loanData,
      );
      if (result?.message) return result;
      if (
        body?.adminId &&
        body?.transactionId &&
        body?.utr &&
        (body?.source == 'UPI' || body?.source == 'UPI2')
      )
        await this.checkAndUpdatePaymenst(body);
      return result;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  // prepare and create transaction
  private async prePareWaiverTransaction(
    body: any,
    prePareAmount: any,
    loanData,
  ) {
    try {
      // Params preparation
      const userId = loanData?.registeredUsers?.id;
      const loanId = body.loanId;
      const phone = body?.phone ?? loanData?.registeredUsers?.phone;
      const email = body?.email ?? loanData?.registeredUsers?.email;
      const amount = prePareAmount?.paidAmount;
      body.dueAmount = amount;
      const name = loanData?.registeredUsers?.fullName;
      const subSource = body?.subSource ?? 'WEB';
      const source = body?.source ?? kCashfree;
      const maxDPD = prePareAmount?.maxDPD;
      const preparedData: any = {
        amount,
        email,
        phone,
        name,
        loanId,
        subSource,
      };
      body.p_type = prePareAmount.type;

      // Order preparation
      const creationData: any = { loanId, userId };
      creationData.paidAmount = amount;
      creationData.source = source;
      creationData.status = 'INITIALIZED';
      creationData.subSource = subSource;
      creationData.principalAmount = prePareAmount?.principalAmount;

      creationData.interestAmount = prePareAmount?.interestAmount;

      creationData.regInterestAmount = prePareAmount?.regInterestAmount;

      creationData.bounceCharge = prePareAmount?.bounceCharge;
      creationData.cgstOnBounceCharge = prePareAmount?.cgstOnBounceCharge;
      creationData.sgstOnBounceCharge = prePareAmount?.sgstOnBounceCharge;
      creationData.igstOnBounceCharge = prePareAmount?.igstOnBounceCharge;

      creationData.penaltyAmount = prePareAmount?.penaltyAmount;

      creationData.penalCharge = prePareAmount?.penalCharge;
      creationData.cgstOnPenalCharge = prePareAmount?.cgstOnPenalCharge;
      creationData.sgstOnPenalCharge = prePareAmount?.sgstOnPenalCharge;
      creationData.igstOnPenalCharge = prePareAmount?.igstOnPenalCharge;

      creationData.legalCharge = prePareAmount?.legalCharge;
      creationData.cgstOnLegalCharge = prePareAmount?.cgstOnLegalCharge;
      creationData.sgstOnLegalCharge = prePareAmount?.sgstOnLegalCharge;
      creationData.igstOnLegalCharge = prePareAmount?.igstOnLegalCharge;

      creationData.forClosureAmount = prePareAmount?.forClosureAmount;
      creationData.sgstForClosureCharge = prePareAmount?.sgstForClosureCharge;
      creationData.cgstForClosureCharge = prePareAmount?.cgstForClosureCharge;
      creationData.igstForClosureCharge = prePareAmount?.igstForClosureCharge;

      creationData.accStatus = kCalBySystem;
      creationData.type = prePareAmount.type;
      creationData.adminId = body?.adminId ?? SYSTEM_ADMIN_ID;
      creationData.followerId = loanData?.followerId;
      creationData.maxDPD = maxDPD;
      if (prePareAmount?.settled_type)
        creationData.settled_type = prePareAmount?.settled_type;
      if (prePareAmount.emiId != -1) creationData.emiId = prePareAmount.emiId;
      const createdData = await this.createLink(body, preparedData);
      if (createdData === k500Error) return kInternalError;
      else if (createdData?.statusCode) return createdData;
      creationData.transactionId = createdData.order_id;
      creationData.response = createdData?.response;

      // Order creation in database
      const isCheck = await this.checkTransactionId(creationData);
      if (isCheck) return k422ErrorMessage(kTransactionIdExists);

      const result = await this.transactionRepo.createRowData(creationData);
      if (!result || result === k500Error) return kInternalError;
      const link = createdData.payLink;
      const dynamicPart = link.split('https://rzp.io/')[1];

      await this.sendNotification(link, body, loanData);
      //WhatsApp Trigger for the Payment
      //comment because of msg91 issue
      const template = [kPartPay, kEMIPay, kFullPay];
      if (
        template.includes(creationData?.type) &&
        dynamicPart &&
        source == 'RAZORPAY' &&
        body?.adminId
      ) {
        const Phone = body?.phone ?? loanData?.registeredUsers?.phone;

        const allPhones = loanData?.registeredUsers?.allPhone ?? [];

        const phonesToNotify = [
          ...new Set(
            allPhones
              .map((item) => this.cryptService.decryptPhone(item))
              .filter((phone) => phone && phone !== '500'),
          ),
        ];
        if (Phone && !phonesToNotify.includes(Phone)) {
          phonesToNotify.push(Phone);
        }
        // for (let i = 0; i < phonesToNotify.length; i++) {
        //   const phone = phonesToNotify[i];
        //   this.whatsappService.sendWhatsAppMessageMicroService({
        //     title: kPaymentWaiverTitle,
        //     appType: loanData?.appType,
        //     userId,
        //     loanId,
        //     paidAmount: body?.dueAmount,
        //     customerName: name ?? 'User',
        //     email,
        //     contactNumber: kHelpContact,
        //     number: phone,
        //     paymentLink: dynamicPart,
        //   });
        // }
      }
      return { paymentLink: link };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region promocode close loan create link
  async closeLoan(data) {
    try {
      if (!data?.loanId) return kParamMissing('loanId');
      let rePayAmount = 0;
      let loanId = data?.loanId / 565656;
      let getEmiAmount = await this.promoCodeService.getEmiAmount(loanId, []);
      if (getEmiAmount === kInternalError) return kInternalError;
      rePayAmount = getEmiAmount?.rePayAmount;
      const body = {
        adminId: SYSTEM_ADMIN_ID,
        emiId: -1,
        loanId: loanId,
        amount: Number(rePayAmount).toFixed(2),
        source: 'RAZORPAY',
        subSource: 'WEB',
        isGreaterEMI: false,
        isCloseLoan: true,
        isPromoCode: data?.isPromoCode ? data?.isPromoCode : true,
      };
      const link = await this.funCreatePaymentOrder(body);
      if (!link || link === kInternalError) return kInternalError;
      return link;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  async checkPaymentOrder(data) {
    try {
      const loanId = data.loanId;
      if (!loanId) return kParamMissing('loanId');

      const yesterDay = new Date();
      yesterDay.setDate(yesterDay.getDate() - 2);
      const attributes = [
        'status',
        'createdAt',
        'emiId',
        'id',
        'loanId',
        'paidAmount',
        'source',
        'subSource',
        'transactionId',
        'type',
        'userId',
        'utr',
        'completionDate',
        'updatedAt',
      ];
      const options: any = {
        order: [['id', 'DESC']],
        where: { loanId },
      };

      if (data?.transactionId) {
        options.where.id = data?.transactionId;
        // options.where.status = kCompleted;
      } else {
        options.where.status = kInitiated;
        options.where.transactionId = { [Op.ne]: null };
      }

      if (data.source) options.where.source = data.source;
      const transList = await this.transactionRepo.getTableWhereData(
        attributes,
        options,
      );
      if (transList == k500Error) return kInternalError;

      const find = transList.find((f) => f.status === kCompleted);
      if (find) {
        if (
          find.source === KICICIUPI ||
          find.source === KICICIUPI2 ||
          find.source === KYESUPI
        )
          find.source = 'UPI';
        const paymentDate = new Date(find?.updatedAt);
        const day = paymentDate.getDate().toString().padStart(2, '0');
        const month = (paymentDate.getMonth() + 1).toString().padStart(2, '0'); // Months are zero-indexed
        const year = paymentDate.getFullYear();
        const hour = paymentDate.getHours() % 12 || 12;
        const minute = paymentDate.getMinutes().toString().padStart(2, '0');
        const period = paymentDate.getHours() < 12 ? 'AM' : 'PM';
        const formattedDate = `${day}/${month}/${year} ${hour}:${minute} ${period}`;
        return {
          amount: find.paidAmount,
          status: find.status,
          paymentGateway: find.source,
          transactionId: find.utr ?? find.transactionId.replace('CFORDER', ''),
          date: formattedDate,
        };
      }

      // CF Checking
      const cfList = transList.filter(
        (el) => el.source == 'CASHFREE' && el.subSource == 'APP',
      );
      let checkPaymentStatus: any = await this.checkCFTransactions(cfList);
      if (checkPaymentStatus?.message) return checkPaymentStatus;

      // Razorpay Checking
      if (
        checkPaymentStatus?.status != 'COMPLETED' &&
        checkPaymentStatus?.status != 'FAILED'
      ) {
        const rzrPayList = transList.filter(
          (el) =>
            el.source == 'RAZORPAY' &&
            (el.subSource == 'APP' || el.subSource == 'WEB'),
        );
        checkPaymentStatus = await this.checkRzrPayTransactions(rzrPayList);
        if (checkPaymentStatus?.message) return checkPaymentStatus;

        // ICICI UPI Checking
        if (
          checkPaymentStatus?.status != 'COMPLETED' &&
          checkPaymentStatus?.status != 'FAILED'
        ) {
          const iciciUPIList = transList.filter(
            (el) => el?.source == KICICIUPI || el?.source == KICICIUPI2,
          );
          checkPaymentStatus = await this.checkICICIUPITransactions(
            iciciUPIList,
          );
        }
        // YES UPI Checking
        if (
          checkPaymentStatus?.status != 'COMPLETED' &&
          checkPaymentStatus?.status != 'FAILED'
        ) {
          const yesUpiList = transList.filter((el) => el?.source == KYESUPI);
          checkPaymentStatus = await this.checkYesUpiTransactions(yesUpiList);
        }
      }

      // Checking for Credit Transactions
      if (
        checkPaymentStatus?.status != 'COMPLETED' &&
        checkPaymentStatus?.status != 'FAILED'
      ) {
        const creditTransactions = transList.filter(
          (el) => el?.source == kCredit,
        );

        checkPaymentStatus =
          await this.creditTransService.checkCreditPaymentOrder(
            creditTransactions,
          );
      }
      if (
        checkPaymentStatus?.status == 'COMPLETED' ||
        checkPaymentStatus?.status == 'FAILED'
      ) {
        const nextInterestRate =
          await this.commonSharedService.getEligibleInterestRate(data);
        return { ...checkPaymentStatus, nextInterestRate };
      } else return { status: 'INITIALIZED' };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async checkCFTransactions(cfList) {
    try {
      for (let index = 0; index < cfList.length; index++) {
        try {
          const transData = cfList[index];
          const response = await this.cashFreeService.checkPayment(
            transData.transactionId,
          );
          if (response.status == 'COMPLETED') {
            const paymentData: any = {
              id: transData.id,
              status: response.status,
            };
            paymentData.response = response.response;
            paymentData.utr = response.utr;
            paymentData.completionDate = response.paymentDate.toJSON();
            paymentData.paymentTime = response?.paymentTime;
            paymentData.type = transData.type;
            paymentData.loanId = transData.loanId;
            paymentData.userId = transData.userId;
            paymentData.subSource = transData.subSource;
            if (transData.emiId) paymentData.emiId = transData.emiId;
            await this.markTransactionAsComplete(paymentData);

            return {
              amount: transData.paidAmount,
              status: paymentData.status,
              transactionId: transData.transactionId.replace('CFORDER', ''),
            };
          }
        } catch (error) {}
      }
      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async checkRzrPayTransactions(rzrPayList) {
    try {
      for (let index = 0; index < rzrPayList.length; index++) {
        try {
          const transData = rzrPayList[index];
          const response = await this.razorpaySer.checkPayment(
            transData.transactionId,
          );
          if (response.status == 'COMPLETED') {
            const paymentData: any = {
              id: transData.id,
              status: response.status,
            };
            paymentData.response = response.response;
            paymentData.utr = response.utr;
            paymentData.completionDate = response.paymentDate.toJSON();
            paymentData.paymentTime = response?.paymentTime;
            paymentData.type = transData.type;
            paymentData.loanId = transData.loanId;
            paymentData.userId = transData.userId;
            paymentData.subSource = transData.subSource;

            const currentDate = new Date();
            const day = currentDate.getDate().toString().padStart(2, '0');
            const month = (currentDate.getMonth() + 1)
              .toString()
              .padStart(2, '0'); // Months are zero-indexed
            const year = currentDate.getFullYear();
            const hour = currentDate.getHours() % 12 || 12;
            const minute = currentDate.getMinutes().toString().padStart(2, '0');
            const period = currentDate.getHours() < 12 ? 'AM' : 'PM';
            const formattedDate = `${day}/${month}/${year} ${hour}:${minute} ${period}`;
            if (transData.emiId) paymentData.emiId = transData.emiId;
            await this.markTransactionAsComplete(paymentData);

            return {
              amount: transData.paidAmount,
              status: paymentData.status,
              paymentGateway: kRazorpay,
              transactionId:
                response.utr ?? transData.transactionId.replace('CFORDER', ''),
              date: formattedDate,
            };
          }
        } catch (error) {}
      }
      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async checkCFAutoDebitWebhook(reqData) {
    if (!reqData) return kInternalError;
    const subReferenceId =
      reqData?.form?.cf_subReferenceId ?? reqData?.cf_subReferenceId;
    if (!subReferenceId) return kParamMissing('subReferenceId');

    const attributes = ['id', 'loanId'];
    const where: any = {
      status: kInitiated,
      source: { [Op.or]: ['CASHFREE', 'AUTOPAY'] },
      subSource: { [Op.or]: [kAutoDebit, kDirectBankPay] },
      transactionId: { [Op.like]: `%${subReferenceId}%` },
    };
    const options: any = { where };
    const transData = await this.transactionRepo.getRowWhereData(
      attributes,
      options,
    );
    if (transData == k500Error) return kInternalError;
    if (!transData) return k422ErrorMessage(kNoDataFound);
    if (!transData?.loanId) return kParamMissing('loanId');

    await this.checkTransactionStatus(
      ['CASHFREE-AUTODEBIT'],
      transData?.loanId,
    );
  }

  async checkTransactionStatus(types: string[], loanId?: number) {
    try {
      if (types.includes('CASHFREE-AUTODEBIT'))
        await this.checkCFAutoDebit(loanId);
      if (types.includes('RAZORPAY-AUTODEBIT'))
        await this.checkRazorpayAutoDebits(loanId);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }

  private async checkCFAutoDebit(loanId?: number) {
    try {
      const pendingAutoDebits: any = await this.getPendingAutoDebits(
        'CASHFREE',
        loanId,
      );
      if (pendingAutoDebits == k500Error) return k500Error;
      for (let index = 0; index < pendingAutoDebits.length; index++) {
        try {
          const pendingData = pendingAutoDebits[index];
          const chargeId = pendingData.utr;
          let transactionId = pendingData.transactionId;
          const startIndex = transactionId.indexOf('id-') + 3;
          transactionId = transactionId.substring(
            startIndex,
            transactionId.length,
          );
          if (transactionId.includes('-') || transactionId.includes('_')) {
            let lastIdx = transactionId.indexOf('_');
            if (lastIdx == -1) lastIdx = transactionId.indexOf('-');
            transactionId = transactionId.substring(0, lastIdx);
          }

          if (chargeId && !isNaN(chargeId) && !isNaN(transactionId)) {
            await this.delay(250);
            const referenceId = transactionId + '/';
            const url =
              CF_SUBSCRIPTION + '/' + referenceId + 'payments/' + chargeId;
            const headers = CASHFREE_HEADERS;
            const response = await this.apiService.get(url, null, headers);
            if (response && response != k500Error && response.status == 'OK') {
              const status = response.payment?.status ?? 'UNKNOWN';
              if (status == 'SUCCESS' || status == 'FAILED') {
                const paymentData: any = { ...pendingData };
                paymentData.status =
                  status == 'SUCCESS' ? 'COMPLETED' : 'FAILED';
                paymentData.completionDate =
                  response.payment.scheduledOn + kGlobalTrail;
                paymentData.paymentTime = this.typeService
                  .getIstTime()
                  .toJSON();
                paymentData.response = JSON.stringify(response);
                paymentData.id = pendingData.id;
                paymentData.subSource = pendingData.subSource;
                if (response?.adNotPlaced)
                  paymentData.subStatus = 'AD_NOT_PLACED';
                await this.markTransactionAsComplete(paymentData);
              }
            }
          }
        } catch (error) {}
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }

  private async getPendingAutoDebits(source: string, loanId?: number) {
    try {
      const attributes = [
        'emiId',
        'id',
        'loanId',
        'transactionId',
        'paidAmount',
        'type',
        'userId',
        'subSource',
        'utr',
      ];
      const where: any = {
        status: 'INITIALIZED',
        subSource: 'AUTODEBIT',
        transactionId: { [Op.ne]: null },
      };
      if (loanId) where.loanId = loanId;
      if (source != 'all') {
        where.subSource = [kAutoDebit, kDirectBankPay];
        if (source == 'CASHFREE')
          where.source = { [Op.or]: ['CASHFREE', 'AUTOPAY'] };
        else where.source = source;
      }
      const options: any = { where };
      if (loanId) options.limit = 1;
      return await this.transactionRepo.getTableWhereData(attributes, options);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }

  private async checkRazorpayAutoDebits(loanId?: number) {
    try {
      const transList = await this.getDataForCheckRazorpayAutoDebits(loanId);
      if (transList.message) return transList;
      for (let index = 0; index < transList.length; index++) {
        try {
          const transData = transList[index];
          const utr = transData.utr;
          if (!utr?.includes('pay_')) continue;

          const paymentResponse = await this.razorpayService.checkPaymentStatus(
            utr,
          );
          const finalStatuses = [kCompleted, kFailed];
          const status = paymentResponse.status;
          if (!status || !finalStatuses.includes(status)) continue;
          if (paymentResponse.message) continue;
          const response = paymentResponse.response;
          if (!response) continue;
          const paymentData: any = { status: paymentResponse.status };
          paymentData.id = transData.id;
          paymentData.response = paymentResponse.response;
          paymentData.type = transData.type;
          paymentData.loanId = transData.loanId;
          paymentData.userId = transData.userId;
          paymentData.subSource = transData.subSource;
          if (transData.emiId) paymentData.emiId = transData.emiId;
          if (!transData?.subscriptionDate) continue;
          paymentData.completionDate = transData?.subscriptionDate;
          paymentData.paymentTime = this.typeService.getIstTime().toJSON();
          const res = JSON.parse(response);
          if (res?.adNotPlaced) paymentData.subStatus = 'AD_NOT_PLACED';
          await this.markTransactionAsComplete(paymentData);
        } catch (error) {}
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async getDataForCheckRazorpayAutoDebits(loanId?: number) {
    try {
      const attributes = [
        'emiId',
        'id',
        'loanId',
        'type',
        'userId',
        'utr',
        'subscriptionDate',
        'subSource',
      ];
      const where: any = {
        status: 'INITIALIZED',
        source: { [Op.or]: [kRazorpay, 'AUTOPAY'] },
        subSource: { [Op.or]: [kAutoDebit, kDirectBankPay, 'WEB'] },
        utr: { [Op.ne]: null },
        transactionId: { [Op.ne]: null },
      };
      if (loanId && loanId != -1) where.loanId = loanId;
      const order = [['id', 'DESC']];
      const options = { order, where };

      const transList = await this.transactionRepo.getTableWhereData(
        attributes,
        options,
      );
      if (transList == k500Error) return kInternalError;
      return transList;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async checkRazorpayAutoDebitsWebhook(reqData) {
    const paymentEntity = reqData?.payload?.payment?.entity;
    if (!paymentEntity) throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);

    const { id: utr, status, method, order_id, amount } = paymentEntity;
    if (!utr?.includes('pay_')) return;

    if (method == 'emandate' && amount == 0) {
      const attributes = [
        'id',
        'userId',
        'mode',
        'umrn',
        'status',
        'referenceId',
      ];
      const options = {
        where: {
          status: kInitiated,
          referenceId: order_id,
          mode: kRazorpay,
        },
      };
      const subscriptionData = await this.repoManager.getRowWhereData(
        SubScriptionEntity,
        attributes,
        options,
      );
      if (subscriptionData === k500Error)
        throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
      if (!subscriptionData) return {};

      const finalStatuses = ['ACTIVE', kFailed];
      const paymentStatus = status === 'captured' ? 'ACTIVE' : kFailed;
      if (!finalStatuses.includes(paymentStatus)) return;

      const updatedData = {
        status: paymentStatus,
        umrn: paymentEntity.token_id,
        response: JSON.stringify(paymentEntity),
      };

      const update = await this.repoManager.updateRowData(
        SubScriptionEntity,
        updatedData,
        subscriptionData.id,
      );
      if (update === k500Error)
        throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);

      const userId = subscriptionData.userId;
      const masterAttrs = ['id', 'status', 'dates'];
      const masterOpts = {
        where: { userId },
        order: [['id', 'DESC']],
      };
      const masterData = await this.masterRepository.getRowWhereData(
        masterAttrs,
        masterOpts,
      );
      if (masterData == k500Error) return kInternalError;

      const statusData = masterData?.status ?? {};
      const dates = masterData?.dates ?? {};
      dates.eMandate = new Date().getTime();

      let updateData: any = {};
      if (paymentStatus === 'ACTIVE') {
        statusData.eMandate = 1;
        updateData = { dates, status: statusData };
      } else {
        statusData.eMandate = 2;
        updateData = { status: statusData, dates };
      }
      const updateResult = await this.masterRepository.updateRowData(
        updateData,
        masterData.id,
      );
      if (updateResult == k500Error) return kInternalError;
      await this.userServiceV4.routeDetails({ id: userId });
      return true;
    }

    const attributes = [
      'emiId',
      'id',
      'loanId',
      'type',
      'userId',
      'utr',
      'subscriptionDate',
      'subSource',
      'remarks',
    ];

    const where = {
      status: kInitiated,
      source: kRazorpay,
      subSource: kAutoDebit,
      utr,
      transactionId: { [Op.ne]: null },
    };
    const order = [['id', 'DESC']];
    const options = { order, where };
    const transData = await this.transactionRepo.getRowWhereData(
      attributes,
      options,
    );
    if (transData === k500Error)
      throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
    if (transData?.message) throw new Error(transData.message);
    if (!transData) return {};

    const finalStatuses = [kCompleted, kFailed];
    const paymentStatus = status === 'captured' ? kCompleted : kFailed;
    if (!finalStatuses.includes(paymentStatus)) return;

    const paymentData: any = { status: paymentStatus };
    paymentData.id = transData.id;
    paymentData.response = JSON.stringify(paymentEntity);
    paymentData.type = transData.type;
    paymentData.loanId = transData.loanId;
    paymentData.userId = transData.userId;
    if (transData.emiId) paymentData.emiId = transData.emiId;
    if (!transData?.subscriptionDate) return;
    paymentData.completionDate = transData?.subscriptionDate;
    paymentData.paymentTime = this.typeService.getIstTime().toJSON();
    paymentData.subSource = transData.subSource;
    if (paymentEntity?.adNotPlaced) paymentData.subStatus = 'AD_NOT_PLACED';
    await this.markTransactionAsComplete(paymentData);
    return true;
  }

  async checkRazorpayManualWebhook(reqData) {
    try {
      const paymentEntity = reqData?.payload?.payment?.entity;
      if (!paymentEntity) return kInternalError;
      const { order_id: ordId, status, id: utr } = paymentEntity;
      const attributes = [
        'emiId',
        'id',
        'loanId',
        'type',
        'userId',
        'transactionId',
        'completionDate',
        'subSource',
      ];
      const where: any = {
        status: { [Op.ne]: kCompleted },
        source: kRazorpay,
        subSource: { [Op.ne]: kAutoDebit },
        transactionId: ordId,
      };

      const order = [['id', 'DESC']];
      const options = { order, where };
      const transData = await this.transactionRepo.getRowWhereData(
        attributes,
        options,
      );

      if (transData === k500Error) return kInternalError;
      if (!transData) return k422ErrorMessage(kNoDataFound);

      const finalStatuses = [kCompleted, kFailed];
      const paymentStatus = status === 'captured' ? kCompleted : kFailed;
      if (!paymentStatus || !finalStatuses.includes(paymentStatus)) return;
      const paymentData: any = { status: paymentStatus };

      paymentData.id = transData.id;
      paymentData.utr = utr;
      paymentData.response = JSON.stringify(paymentEntity);
      paymentData.type = transData.type;
      paymentData.loanId = transData.loanId;
      paymentData.userId = transData.userId;
      if (transData.emiId) paymentData.emiId = transData.emiId;
      paymentData.completionDate = this.typeService
        .getGlobalDate(new Date())
        .toJSON();
      paymentData.paymentTime = this.typeService.getIstTime().toJSON();
      paymentData.subSource = transData.subSource;
      await this.markTransactionAsComplete(paymentData);
      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async checkSDAutoDebits(responseData: any) {
    try {
      const mandateList = responseData.mandate_list;
      const batchId = responseData.batch_id;
      const responseList: any[] = [];

      for (let index = 0; index < mandateList.length; index++) {
        try {
          const data = mandateList[index];
          const utr = `${data.emandate_id}-id-${batchId}`;
          const options = { where: { utr } };
          const existingData = await this.transactionRepo.getRowWhereData(
            [
              'createdAt',
              'utr',
              'transactionId',
              'emiId',
              'loanId',
              'userId',
              'id',
              'type',
              'paidAmount',
              'subSource',
              'subscriptionDate',
            ],
            options,
          );
          if (!existingData || existingData == k500Error) continue;
          const comDate = this.typeService.getGlobalDate(
            existingData?.createdAt,
          );
          const id = existingData.id;
          const loanId = existingData.loanId;
          const userId = existingData.userId;
          const subSource = existingData.subSource;
          const response = JSON.stringify(data);
          const updatedData: any = { id, loanId, userId, response, subSource };
          if (data.status == 'RES_Rejected') updatedData.status = 'FAILED';
          else if (data.status == 'RES_Accepted')
            updatedData.status = 'COMPLETED';
          updatedData.completionDate =
            existingData?.subscriptionDate ?? comDate;
          updatedData.paymentTime = this.typeService.getIstTime().toJSON();
          updatedData.type = existingData.type;
          updatedData.utr = existingData.utr;
          updatedData.paidAmount = existingData.paidAmount;
          if (updatedData.type == 'EMIPAY' || updatedData.type == 'PARTPAY')
            updatedData.emiId = existingData.emiId;

          responseList.push(updatedData);
        } catch (error) {}
      }

      return responseList;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // start region checking icici upi transactions status
  async checkICICIUPITransactions(iciciUPIList) {
    try {
      for (let i = 0; i < iciciUPIList.length; i++) {
        try {
          const transData = iciciUPIList[i];
          const transactionId = transData?.transactionId;
          const transactionType = 'C';
          const response = await this.iciciService.CallbackStatus({
            transactionId,
            transactionType,
          });
          if (response?.status == 'COMPLETED' || response?.status == 'FAILED') {
            let paymentData: any = {};
            paymentData.id = transData?.id;
            paymentData.status = response?.status;
            paymentData.response = response?.response;
            paymentData.utr = response?.OriginalBankRRN;
            paymentData.completionDate = response?.paymentDate.toJSON();
            paymentData.paymentTime = response?.paymentTime;
            paymentData.type = transData?.type;
            paymentData.loanId = transData?.loanId;
            paymentData.userId = transData?.userId;
            paymentData.subSource = transData?.subSource;
            if (transData?.emiId) paymentData.emiId = transData?.emiId;

            await this.markTransactionAsComplete(paymentData);

            return {
              amount: transData?.paidAmount,
              status: paymentData?.status,
              paymentGateway: 'UPI',
              transactionId:
                response?.OriginalBankRRN ?? transData?.transactionId,
              date: response?.formattedDateForUI,
            };
          }
        } catch (error) {}
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region checkYesUpiTransactions
  async checkYesUpiTransactions(yesUpiTransList) {
    try {
      for (let i = 0; i < yesUpiTransList.length; i++) {
        const transData = yesUpiTransList[i];
        const transactionId = transData?.transactionId;
        const response: any = await this.yesUpiService.intentStatus({
          transactionId,
        });
        if (
          response?.txnStatus == 'SUCCESS' ||
          response?.txnStatus == 'FAILED'
        ) {
          let paymentData: any = {};
          const transactionStatus =
            response?.txnStatus == 'SUCCESS' ? kCompleted : kFailed;
          paymentData.id = transData?.id;
          paymentData.status = transactionStatus;
          paymentData.response = JSON.stringify(response);
          paymentData.utr = response?.npciTxnId;
          paymentData.completionDate = this.typeService
            .getGlobalDate(new Date(response?.transactionTimestamp))
            .toJSON();
          paymentData.paymentTime = response?.transactionTimestamp + '.000Z';
          paymentData.type = transData?.type;
          paymentData.loanId = transData?.loanId;
          paymentData.userId = transData?.userId;
          paymentData.subSource = transData?.subSource;
          if (transData?.emiId) paymentData.emiId = transData?.emiId;
          let transId = response?.npciTxnId ?? transData?.transactionId;

          await this.markTransactionAsComplete(paymentData);

          return {
            amount: transData?.paidAmount,
            status: paymentData?.status,
            paymentGateway: 'UPI',
            transactionId: transId,
            date: response?.transactionTimestamp,
          };
        }
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async syncPaymentWebData(body) {
    const userId = body?.userId;
    const loanId = body?.loanId;
    const parts = body.internalResponse.split('_');
    const transactionId = parts[parts.length - 1];
    const triggersToUpdate = {
      'https://razorpay.com/payment-link/': {
        allowContains: true,
        onLoadStart: {
          state: { isProcessing: true },
        },
        onLoadStop: {
          state: { isProcessing: false },
        },
      },
      [HOST_URL + `${Latest_Version}/transaction/checkCallBack`]: {
        allowContains: true,
        allowAnyConsole: true,
        onLoadStart: {
          state: { paymentLoader: true },
          triggers: ["console.log('PAYMENT_CHECK')"],
        },
        onLoanStop: {
          triggers: ["console.log('PAYMENT_CHECK')"],
        },
        consoles: [
          {
            combinations: ['PAYMENT_CHECK'],
            state: { paymentLoader: true },
            apiTriggers: [
              {
                url:
                  HOST_URL + `${Latest_Version}/transaction/checkPaymentOrder`,
                method: 'POST',
                body: { loanId, userId, transactionId, isAnimation: true },
              },
            ],
          },
        ],
      },
      [`${process.env.FRONTEND_BASE_URL}cashfree-payment`]: {
        allowContains: true,
        allowAnyConsole: true,
        onLoadStop: {
          state: {
            isProcessing: true,
            paymentLoader: true,
          },
          triggers: ['console.log("PAYMENT_CHECK")'],
        },
        onLoadStart: {
          state: {
            isProcessing: true,
            paymentLoader: true,
          },
          triggers: ['console.log("PAYMENT_CHECK")'],
        },
        consoles: [
          {
            combinations: ['PAYMENT_CHECK'],
            state: {
              isProcessing: true,
              isLoader: true,
            },
            apiTriggers: [
              {
                url:
                  HOST_URL + `${Latest_Version}/transaction/checkPaymentOrder`,
                method: 'POST',
                body: { loanId, userId, transactionId, isAnimation: true },
              },
            ],
          },
        ],
      },
    };
    return { triggersToUpdate };
  }
  //#endregion

  // Re-calculation of paid bifurcation (paid Principal, paid Interest, paid Penalty)
  async reCalculatePaidAmounts(body) {
    const loanId = body?.loanId;
    if (!loanId) return kParamMissing('loanId');
    const isCreditTrans = body?.isCreditTrans;

    // Loan Data with EMI data and Transaction data
    const traAttrs = [
      'id',
      'emiId',
      'status',
      'type',
      'paidAmount',
      'principalAmount',
      'interestAmount',
      'penaltyAmount',
      'transactionId',
      'legalCharge',
      'cgstOnLegalCharge',
      'sgstOnLegalCharge',
      'igstOnLegalCharge',
      'penalCharge',
      'cgstOnPenalCharge',
      'sgstOnPenalCharge',
      'igstOnPenalCharge',
      'regInterestAmount',
      'bounceCharge',
      'cgstOnBounceCharge',
      'sgstOnBounceCharge',
      'igstOnBounceCharge',
      'forClosureAmount',
      'cgstForClosureCharge',
      'sgstForClosureCharge',
      'igstForClosureCharge',
    ];
    const emiAttr = [
      'id',
      'fullPayPrincipal',
      'fullPayPenalty',
      'fullPayInterest',
      'fullPayLegalCharge',
      'fullPayPenal',
      'fullPayRegInterest',
      'fullPayBounce',
      'forClosureAmount',
      'sgstForClosureCharge',
      'cgstForClosureCharge',
      'igstForClosureCharge',
      'interestCalculate',
      'pay_type',
      'dpdAmount',
    ];
    const transInc = {
      model: TransactionEntity,
      attributes: traAttrs,
      where: { status: kCompleted },
    };
    const emiInc = { model: EmiEntity, attributes: emiAttr };
    const options = { where: { id: loanId }, include: [emiInc, transInc] };
    const loanData = await this.loanRepo.getRowWhereData(
      ['id', 'penaltyCharges'],
      options,
    );
    if (loanData === k500Error) throw new Error();
    if (!loanData) return {};
    const originalTrans = await this.commonSharedService.filterTransActionData(
      loanData?.transactionData ?? [],
    );
    loanData.transactionData = originalTrans;
    await this.updatePaidEMIBifurcation(loanData, isCreditTrans);
    return loanData;
  }

  // update paid bifurcation in EMI (paid Principal, paid Interest, paid Penalty)
  private async updatePaidEMIBifurcation(loanData, isCreditTrans) {
    const transData = loanData?.transactionData ?? [];
    if (transData.length == 0) return {};
    const emiData = loanData?.emiData;
    let fullPayTrans =
      transData.find(
        (f) => f?.emiId == null && f?.settled_type != 'WAIVER_SETTLED',
      ) ?? {};

    // Calculate paid foreclosure charge
    let paidForeclosureCharge =
      (fullPayTrans?.forClosureAmount ?? 0) +
      (fullPayTrans?.cgstForClosureCharge ?? 0) +
      (fullPayTrans?.sgstForClosureCharge ?? 0) +
      (fullPayTrans?.igstForClosureCharge ?? 0);
    transData.forEach((tra) => {
      if (tra?.type != kFullPay) {
        paidForeclosureCharge += tra?.forClosureAmount ?? 0;
        paidForeclosureCharge += tra?.cgstForClosureCharge ?? 0;
        paidForeclosureCharge += tra?.sgstForClosureCharge ?? 0;
        paidForeclosureCharge += tra?.igstForClosureCharge ?? 0;
      }
    });
    let fullPaidInt = fullPayTrans?.interestAmount ?? 0;
    // prepare full pay type EMI ids
    const fullpayTypeEmiIds = emiData
      .filter(
        (f) =>
          f?.pay_type === kFullPay &&
          (f.dpdAmount === 0 || f.dpdAmount == null),
      )
      .map((f) => f.id);
    // Check if I_GST is applicable or CGST/SGST
    const iGst = loanData?.penaltyCharges?.I_GST ?? false;

    for (let i = 0; i < emiData.length; i++) {
      const emi = emiData[i];
      const emiId = emi.id;
      let paid_principal = emi?.fullPayPrincipal ?? 0;
      let paid_interest = emi?.fullPayInterest ?? 0;
      let paid_penalty = emi?.fullPayPenalty ?? 0;
      let paidLegalCharge = emi?.fullPayLegalCharge ?? 0;
      let paidPenalCharge = emi?.fullPayPenal ?? 0;
      let paidRegInterestAmount = emi?.fullPayRegInterest ?? 0;
      let paidBounceCharge = emi?.fullPayBounce ?? 0;
      let interest = emi?.interestCalculate ?? 0;

      const trans = transData.filter((f) => f?.emiId == emiId);
      trans.forEach((tra) => {
        if (tra?.type != kFullPay) {
          const bounceCharge =
            (tra?.bounceCharge ?? 0) +
            (tra?.sgstOnBounceCharge ?? 0) +
            (tra?.cgstOnBounceCharge ?? 0) +
            (tra?.igstOnBounceCharge ?? 0);
          const penalCharge =
            (tra?.penalCharge ?? 0) +
            (tra?.sgstOnPenalCharge ?? 0) +
            (tra?.cgstOnPenalCharge ?? 0) +
            (tra?.igstOnPenalCharge ?? 0);
          const legalCharge =
            (tra?.legalCharge ?? 0) +
            (tra?.sgstOnLegalCharge ?? 0) +
            (tra?.cgstOnLegalCharge ?? 0) +
            (tra?.igstOnLegalCharge ?? 0);

          paid_principal += tra?.principalAmount ?? 0;
          paid_interest += tra?.interestAmount ?? 0;
          paid_penalty += tra?.penaltyAmount ?? 0;
          paidRegInterestAmount += tra?.regInterestAmount ?? 0;
          paidBounceCharge += bounceCharge;
          paidPenalCharge += penalCharge;
          paidLegalCharge += legalCharge;
        }
      });
      paid_penalty = +paid_penalty.toFixed(2);
      paidRegInterestAmount = +paidRegInterestAmount.toFixed(2);
      paidBounceCharge = +paidBounceCharge.toFixed(2);
      paidPenalCharge = +paidPenalCharge.toFixed(2);
      paidLegalCharge = +paidLegalCharge.toFixed(2);
      const updatedEMI: any = {
        paid_principal,
        paid_interest,
        paid_penalty,
        paidRegInterestAmount,
        paidBounceCharge,
        paidPenalCharge,
        paidLegalCharge,
      };
      // divide paid foreclosure charge into full pay type EMIs
      if (
        isCreditTrans &&
        paidForeclosureCharge > 0 &&
        fullpayTypeEmiIds.includes(emiId)
      ) {
        let forClosureAmount = paidForeclosureCharge / fullpayTypeEmiIds.length;
        if (forClosureAmount) forClosureAmount = +forClosureAmount.toFixed(2);
        let gst = forClosureAmount - forClosureAmount / 1.18;
        if (gst < 0) gst = 0;
        if (gst) gst = +gst.toFixed(2);
        forClosureAmount -= gst;
        forClosureAmount = +forClosureAmount.toFixed(2);
        updatedEMI.forClosureAmount = forClosureAmount;
        if (iGst) updatedEMI.igstForClosureCharge = gst;
        else {
          updatedEMI.cgstForClosureCharge = gst / 2;
          updatedEMI.sgstForClosureCharge = gst / 2;
        }
      }
      if (fullPaidInt > 0 && isCreditTrans && emi?.pay_type == kFullPay) {
        let emiWiseFullPayInt = 0;
        emiWiseFullPayInt = fullPaidInt;
        if (emiWiseFullPayInt > interest) emiWiseFullPayInt = interest;
        updatedEMI.fullPayInterest = emiWiseFullPayInt;
        fullPaidInt -= emiWiseFullPayInt;
      }
      await this.emiRepo.updateRowData(updatedEMI, emiId);
    }
    return {};
  }

  async getAdvanceFullPay(query) {
    const isDownload = query?.download ?? 'false';
    let loanIds =
      query?.loanIds && typeof query?.loanIds == 'string'
        ? [query?.loanIds]
        : query?.loanIds;

    const emiInc = {
      model: EmiEntity,
      attributes: ['id', 'payment_done_date'],
      where: {
        emi_date: { [Op.gt]: Sequelize.col('emiData.payment_done_date') },
      },
    };
    if (!loanIds) {
      const trInc = {
        model: TransactionEntity,
        attributes: ['id'],
        where: { status: kCompleted },
      };
      const opt = {
        where: { loanStatus: 'Complete' },
        include: [emiInc, trInc],
        limit: 100,
      };
      const loanList = await this.loanRepo.getTableWhereData(['id'], opt);
      if (loanList === k500Error) throw new Error();
      loanIds = loanList.map((e) => e?.id);
    }
    console.log('loanIds', loanIds.length);
    const emisInc = {
      model: EmiEntity,
      attributes: [
        'id',
        'emi_date',
        'pay_type',
        'partOfemi',
        'fullPayPrincipal',
        'fullPayInterest',
        'interestCalculate',
        'payment_done_date',
      ],
    };
    const traAttrs = [
      'id',
      'emiId',
      'status',
      'type',
      'paidAmount',
      'principalAmount',
      'interestAmount',
      'transactionId',
      'completionDate',
    ];
    const transInc = {
      model: TransactionEntity,
      attributes: traAttrs,
      where: { status: kCompleted },
    };
    const options = {
      where: { id: loanIds },
      include: [transInc, emisInc],
      order: [['id', 'DESC']],
    };
    const loanData = await this.loanRepo.getTableWhereData(
      [
        'id',
        'loan_disbursement_date',
        'approvedDuration',
        'loanCompletionDate',
      ],
      options,
    );
    if (loanData === k500Error) throw new Error();
    // return loanData;
    const finalData = [];
    for (let i = 0; i < loanData.length; i++) {
      const loan = loanData[i];
      const loanDisbursementDate = this.typeService.getGlobalDate(
        loan?.loan_disbursement_date,
      );
      const loanCompletionDate = this.typeService.getGlobalDate(
        loan?.loanCompletionDate,
      );
      const payDiff = this.typeService.differenceInDays(
        loanCompletionDate,
        loanDisbursementDate,
      );
      const emiData = loan.emiData;
      emiData.sort((a, b) => a.id - b.id);
      let lastEMIdate;
      let affectedInterest = 0;
      let tempEmidate;
      emiData.forEach((emi) => {
        const emiDate = this.typeService.getGlobalDate(emi.emi_date);
        if (
          loanCompletionDate >= emiDate ||
          (emi.partOfemi == 'FIRST' && payDiff > 3) ||
          (loanCompletionDate > tempEmidate && loanCompletionDate < emiDate)
        )
          affectedInterest += emi.interestCalculate;
        if (emi.partOfemi == 'LAST') lastEMIdate = emiDate;
        tempEmidate = emiDate;
      });
      if (!affectedInterest) continue;
      const tranData = loan?.transactionData ?? [];
      const orgTrans = await this.commonSharedService.filterTransActionData(
        tranData,
      );
      let paidInterest = 0;
      let fullPayDT;
      orgTrans.forEach((tra) => {
        const check = emiData.find((f) => tra?.emiId == f.id);
        if (tra.type == kFullPay || check)
          paidInterest += tra?.interestAmount ?? 0;
        if (tra.type == kFullPay) fullPayDT = tra?.completionDate;
      });
      const differenceInterest = Math.round(paidInterest - affectedInterest);
      if (!differenceInterest) continue;
      const fullPayDate = this.typeService.getGlobalDate(fullPayDT);
      let prePayDay = this.typeService.differenceInDays(
        lastEMIdate,
        loanCompletionDate,
      );
      if (loanDisbursementDate.getTime() == loanCompletionDate.getTime())
        prePayDay = +loan?.approvedDuration;
      const obj = {
        loanId: loan.id,
        loanDisbursementDate:
          this.typeService.getDateFormatted(loanDisbursementDate),
        lastEMIdate: this.typeService.getDateFormatted(lastEMIdate),
        fullPayDate: fullPayDT
          ? this.typeService.getDateFormatted(fullPayDate)
          : '-',
        loanCompletionDate:
          this.typeService.getDateFormatted(loanCompletionDate),
        approvedDuration: +loan?.approvedDuration,
        prePayDay,
        affectedInterest,
        paidInterest,
        differenceInterest,
      };
      finalData.push(obj);
    }
    if (isDownload === 'true') {
      const rawExcelData = {
        sheets: ['AdvanceFullPay'],
        data: [finalData],
        sheetName: 'AdvanceFullPay.xlsx',
      };
      const fileURL = await this.fileService.objectToExcelURL(rawExcelData);
      if (fileURL?.message) throw new Error();
      return { fileURL };
    }
    return finalData;
  }

  async checkTransactionIds(reqData) {
    try {
      const file = reqData.file;
      if (!file) return kParamMissing('file');
      const paymentMethod = reqData.paymentMethod;
      if (!paymentMethod) return kParamMissing('paymentMethod');
      const startDate = reqData.startDate;
      if (!startDate) return kParamMissing('startDate');
      const endDate = reqData.endDate;
      if (!endDate) return kParamMissing('endDate');
      const filePath = file.filename;
      if (!filePath) return kParamMissing('file');
      if (!filePath.endsWith('csv') && !filePath.endsWith('xlsx')) {
        return k422ErrorMessage('Kindly provide valid excel file');
      }

      const result = await this.getFileData(filePath);

      let missingIds = [];

      const attr = ['utr', 'status', 'userId', 'loanId', 'transactionId'];
      const options: any = {
        where: {
          source: kPaymentMode[paymentMethod],
          createdAt: {
            [Op.gte]: startDate,
            [Op.lte]: endDate,
          },
        },
      };
      const transactionData = await this.transactionRepo.getTableWhereData(
        attr,
        options,
      );

      let sheetField;
      let transactionField;
      switch (paymentMethod) {
        case '1': // RAZORPAY
          sheetField = 'order_id';
          transactionField = 'transactionId';
          break;
        case '2': // ICICI_UPI
          sheetField = 'merchantTranId';
          transactionField = 'transactionId';
          break;
        case '3': // CASHFREE
          sheetField = 'Subscription Payment ID';
          transactionField = 'utr';
          break;
      }

      missingIds = result.reduce((acc, data) => {
        const found = transactionData.find(
          (transaction) => transaction[transactionField] == data[sheetField],
        );
        if (found && found.status != 'COMPLETED') {
          const finalizedData = {
            ...data,
            ...found,
          };
          acc.push(finalizedData);
        } else {
          acc.push(data);
        }
        return acc;
      }, []);
      const finalData = await this.prepareData(missingIds);
      if (finalData.length == 0) return true;
      const path = 'MissingTransactions.xlsx';
      const rawExcelData = {
        sheets: ['MissingTransactions'],
        data: [finalData],
        sheetName: path,
        needFindTuneKey: false,
      };
      const excelResponse: any = await this.fileService.objectToExcel(
        rawExcelData,
      );
      if (excelResponse?.message) return excelResponse;

      const fileURL = await this.fileService.uploadFile(
        excelResponse?.filePath,
        CLOUD_FOLDER_PATH.transactions,
        'xlsx',
      );
      if (fileURL.message) return fileURL;
      return { fileURL };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async prepareData(transData) {
    const finalizedData = [];
    for (let i = 0; i < transData.length; i++) {
      const data = transData[i];
      const tempData: any = {
        UserId: data.userId ?? '-',
        LoanId: data.loanId ?? '-',
        Amount: data.amount ?? '-',
        Mobile: data['Customer Phone'] ?? data.contact ?? '-',
        Email: data['Customer Email'] ?? data.email ?? '-',
        Name: data['Customer Name'] ?? '-',
        Status: data.status ?? data['Subscription Payment Status'] ?? '-',
        Date:
          data.created_at ??
          data.txnCompletationDate ??
          data['Subscription Payment Added On'] ??
          '-',
      };

      finalizedData.push(tempData);
    }

    return finalizedData;
  }

  async getFileData(filePath) {
    const extractedList = [];
    let fileListData;
    fileListData = await this.fileService.excelToArray(filePath, {}, true);
    if (fileListData.message) return fileListData;
    else if (!fileListData) return kBadRequest;
    fileListData.finalData.forEach((transaction) => {
      const amount = transaction.amount || transaction.Amount || 0;
      const status =
        transaction.status || transaction['Subscription Payment Status'];
      if (amount > 0 && ['captured', 'SUCCESS'].includes(status)) {
        extractedList.push(transaction);
      }
    });

    return extractedList;
  }

  async getTrans(loanId) {
    const attributes = [
      'id',
      'paidAmount',
      'maxDPD',
      'status',
      'principalAmount',
      'principalDifference',
      'interestAmount',
      'completionDate',
      'transactionId',
      'utr',
      'source',
      'type',
      'userId',
      'loanId',
      'emiId',
      'interestDifference',
      'penaltyAmount',
      'penaltyDifference',
      'subSource',
      'subStatus',
      'accStatus',
      'adminId',
      'subscriptionDate',
      'settled_type',
      'remarks',
      'followerId',
      'roundOff',
      'tran_unique_id',
      'feesIncome',
      'mode',
      'forClosureAmount',
      'regInterestAmount',
      'sgstForClosureCharge',
      'cgstForClosureCharge',
      'igstForClosureCharge',
      'forClosureDays',
      'legalCharge',
      'bounceCharge',
      'penalCharge',
      'sgstOnBounceCharge',
      'cgstOnBounceCharge',
      'igstOnBounceCharge',
      'sgstOnPenalCharge',
      'cgstOnPenalCharge',
      'igstOnPenalCharge',
      'sgstOnLegalCharge',
      'cgstOnLegalCharge',
      'igstOnLegalCharge',
      'createdAt',
      'updatedAt',
      'maxDPD',
    ];
    const where: any = {
      loanId,
      status: 'COMPLETED',
    };
    const options: any = { where };
    options.order = [['id', 'DESC']];

    const transactionList = await this.transactionRepo.getTableWhereData(
      attributes,
      options,
    );
    if (transactionList == k500Error) return kInternalError;
    return transactionList;
  }

  async addToArchiveInitializedTransactions(limit: number) {
    const pastDate = new Date();

    pastDate.setDate(pastDate.getDate() - 30);
    const query = `SELECT * FROM public."TransactionEntities"
        WHERE "status" = 'INITIALIZED' AND "createdAt" < '${pastDate.toJSON()}'
        ORDER BY ID DESC
        LIMIT ${limit}`;

    const intializedData = await this.repoManager.injectRawQuery(
      TransactionEntity,
      query,
    );
    if (intializedData == k500Error) return kInternalError;

    if (intializedData.length > 0) {
      const createData = await this.repoManager.bulkCreate(
        TransactionInitializedArchiveEntity,
        intializedData,
      );
      if (createData == k500Error) throw new Error();

      if (createData.length > 0) {
        const ids = intializedData.map((el) => el.id);
        const deleteData = await this.repoManager.deleteWhereData(
          TransactionEntity,
          { where: { id: { [Op.in]: ids } } },
          false,
        );
        if (deleteData == k500Error) throw new Error();
      }
      await this.addToArchiveInitializedTransactions(limit);
    }
    return true;
  }

  // Funciton for Reversing the Calculation In Transactions
  async reverseSettlement(reqData) {
    const { adminId, loanId } = reqData;
    if (!adminId) return kParamMissing('adminId');
    if (!loanId) return kParamMissing('loanId');

    // Check For Admin Access
    const checkAccess = await this.checkAdminAccessToUpdatePayments(
      'reverse settlement',
      adminId,
    );
    if (checkAccess !== true) return checkAccess;

    const tranInclude: any = { model: TransactionEntity };
    tranInclude.attributes = [
      'id',
      'transactionId',
      'utr',
      'source',
      'type',
      'paidAmount',
      'status',
      'loanId',
      'emiId',
      'userId',
      'principalAmount',
      'interestAmount',
      'penaltyAmount',
      'regInterestAmount',
      'bounceCharge',
      'penalCharge',
      'legalCharge',
      'subStatus',
    ];
    tranInclude.where = { status: 'COMPLETED' };
    tranInclude.required = false;
    // Emi
    const attributes = [
      'id',
      'emi_amount',
      'emi_date',
      'payment_status',
      'penalty',
      'totalPenalty',
      'penalty_days',
      'payment_due_status',
      'principalCovered',
      'interestCalculate',
      'regInterestAmount',
      'bounceCharge',
      'dpdAmount',
      'legalCharge',
      'gstOnBounceCharge',
      'penaltyChargesGST',
      'legalChargeGST',
      'partPaymentPenaltyAmount',
      'pay_type',
      'paid_principal',
      'paid_interest',
      'paidRegInterestAmount',
      'paidBounceCharge',
      'paidPenalCharge',
      'paidLegalCharge',
    ];

    const emiInclude: any = { model: EmiEntity, attributes };
    // Loan
    const where: any = { id: loanId, loanStatus: 'Active' };
    const include = [emiInclude, tranInclude];
    const options = { include, where };
    const loanData = await this.loanRepo.getRowWhereData(
      ['penaltyCharges', 'userId', 'followerId'],
      options,
    );
    if (loanData === k500Error) return kInternalError;
    if (!loanData) return k422ErrorMessage(kNoDataFound);

    const userTypeNew =
      loanData?.penaltyCharges?.MODIFICATION_CALCULATION ?? false;

    let paidPrincipal = 0;
    let paidInterest = 0;
    let paidDeferredInt = 0;
    let paidBounceCharge = 0;
    let paidPenalCharge = 0;
    let paidLegalCharge = 0;

    const transData = loanData?.transactionData;
    const emiData = loanData?.emiData;
    let principal = 0;
    let emiArray = [];
    emiData.forEach((ele) => {
      principal += ele?.principalCovered ?? 0;
      if (!userTypeNew) ele.bounceCharge = 0;
      if (ele?.payment_status == '0')
        emiArray.push({
          emiId: ele?.id,
          paid_principal: ele?.paid_principal ?? 0,
          remainingPrincipal:
            (ele?.principalCovered ?? 0) - (ele?.paid_principal ?? 0),
          // Principal To Update In EMI
          toUpdatePrincipal:
            (ele?.paid_principal ?? 0) +
            ((ele?.principalCovered ?? 0) - (ele?.paid_principal ?? 0)),
          // Expected
          interest: this.typeService.manageAmount(
            (ele?.interestCalculate ?? 0) - (ele?.paid_interest ?? 0),
          ),
          deferredInt: this.typeService.manageAmount(
            ele?.regInterestAmount ?? 0,
          ),
          bounceCharge: this.typeService.manageAmount(
            (ele?.bounceCharge ?? 0) + (ele?.gstOnBounceCharge ?? 0),
          ),
          penalCharge: this.typeService.manageAmount(
            (ele?.dpdAmount ?? 0) + (ele?.penaltyChargesGST ?? 0),
          ),
          penalty: this.typeService.manageAmount(ele?.totalPenalty ?? 0),
          legalCharge: this.typeService.manageAmount(
            (ele?.legalCharge ?? 0) + (ele?.legalChargeGST ?? 0),
          ),
          // Paid Charges
          paidDeferredInt: ele?.paidRegInterestAmount ?? 0,
          paidBounceCharge: ele?.paidBounceCharge ?? 0,
          paidPenalCharge: ele?.paidPenalCharge ?? 0,
          paidPenalty: (ele?.totalPenalty ?? 0) - (ele?.penalty ?? 0),
          paidLegalCharge: ele?.paidLegalCharge ?? 0,
          // Waived Charges
          waivedDeferred: ele?.waived_regInterest ?? 0,
          waivedBounce: ele?.waived_bounce ?? 0,
          waivedPenal: ele?.waived_penal ?? 0,
          waivedPenalty: ele?.waived_penalty ?? 0,
          waivedLegal: ele?.waived_legal ?? 0,
        });
    });
    for (let index = 0; index < transData.length; index++) {
      const ele = transData[index];
      // Paid Amounts
      paidPrincipal += ele?.principalAmount ?? 0;
      paidInterest += ele?.interestAmount ?? 0;
      paidDeferredInt += ele?.regInterestAmount ?? 0;
      paidBounceCharge += ele?.bounceCharge ?? 0;

      paidPenalCharge += (ele?.penaltyAmount ?? 0) + (ele?.penalCharge ?? 0);
      paidLegalCharge += ele?.legalCharge ?? 0;
    }

    let amountCanReversed =
      paidInterest +
      paidDeferredInt +
      paidBounceCharge +
      paidPenalCharge +
      paidLegalCharge;

    let remainingPrincipal = principal - paidPrincipal;
    if (remainingPrincipal > amountCanReversed)
      return k422ErrorMessage(
        `Not Eligible For Reverse Settlement. Remaining Principal:${remainingPrincipal},Amount Available:${amountCanReversed}`,
      );

    const body = {
      transData,
      amountCanReversed,
      remainingPrincipal,
      userTypeNew,
      emiArray,
      loanId,
      userId: loanData?.userId,
      followerId: loanData?.followerId,
      adminId,
    };
    const preparedAmounts: any = await this.reverseDataAndSettleTheLoan(body);
    if (preparedAmounts.message) return preparedAmounts;
    return true;
  }

  async reverseDataAndSettleTheLoan(body) {
    let {
      transData,
      amountCanReversed, // Amount Available for Reverse Settlement for Principal
      remainingPrincipal,
      userTypeNew, // To Check User is New or Old - Because Old User Bounce is Inc. In Penalty
      emiArray, // EMIs Where Principal Is Remaining
      loanId,
      userId,
      adminId,
      followerId,
    } = body;
    let tempPrincipal = remainingPrincipal;
    let utrForPrincipal = '';
    let transIdForPrincipal = '';
    transData = transData.sort((a, b) => b.id - a.id);
    for (let index = 0; index < transData.length; index++) {
      if (remainingPrincipal > 0) {
        // Obj to Update in Trans
        let dataToUpdate: any = {
          paidAmount: 0,
          status: 'COMPLETED',
          loanId: body?.loanId,
          userId: body?.userId,
          subStatus: 'REVERSE_SETTLEMENT',
          adminId: +adminId,
        };
        const ele = transData[index];
        let thisTransPaidLegal = ele?.legalCharge ?? 0;
        let thisTransPaidPenalty =
          (ele?.penaltyAmount ?? 0) + (ele?.penalCharge ?? 0);
        let thisTransBounce = ele?.bounceCharge ?? 0;
        let thisTransDeferredInt = ele?.regInterestAmount ?? 0;

        if (
          thisTransPaidLegal +
            thisTransPaidPenalty +
            thisTransBounce +
            thisTransDeferredInt <=
          0
        )
          continue;
        let tempLegal = 0;
        let tempPenal = 0;
        let tempBounce = 0;
        // Charges to Update in EMI
        let emiLegal = 0;
        let emiPenal = 0;
        let emiBounce = 0;
        let emiDeferred = 0;

        // Legal
        if (thisTransPaidLegal > 0) {
          if (remainingPrincipal >= thisTransPaidLegal) {
            remainingPrincipal -= thisTransPaidLegal;
            emiLegal = thisTransPaidLegal;
            dataToUpdate.legalCharge = ele?.legalCharge ?? 0;
          } else if (remainingPrincipal > 0) {
            tempLegal = Math.round(remainingPrincipal);
            dataToUpdate.legalCharge = tempLegal;
            emiLegal = remainingPrincipal;
            remainingPrincipal = 0;
          }
        }
        // Penal
        if (thisTransPaidPenalty > 0) {
          if (remainingPrincipal >= thisTransPaidPenalty) {
            remainingPrincipal -= thisTransPaidPenalty;
            emiPenal = thisTransPaidPenalty;
            if (ele?.penalCharge > 0) {
              dataToUpdate.penalCharge = ele?.penalCharge ?? 0;
            } else if (ele?.penaltyAmount > 0) {
              dataToUpdate.penaltyAmount = ele?.penaltyAmount ?? 0;
            }
          } else if (remainingPrincipal > 0) {
            tempPenal = Math.round(remainingPrincipal);

            if (userTypeNew) {
              dataToUpdate.penalCharge = tempPenal;
            } else {
              dataToUpdate.penaltyAmount = tempPenal;
            }

            emiPenal = remainingPrincipal;
            remainingPrincipal = 0;
          }
        }
        // Bounce
        if (thisTransBounce > 0) {
          if (remainingPrincipal >= thisTransBounce) {
            remainingPrincipal -= thisTransBounce;
            emiBounce = thisTransBounce;
            if (ele?.bounceCharge > 0) {
              dataToUpdate.bounceCharge = ele?.bounceCharge ?? 0;
            }
          } else if (remainingPrincipal > 0) {
            tempBounce = Math.round(remainingPrincipal);
            dataToUpdate.bounceCharge = tempBounce;
            emiBounce = remainingPrincipal;
            remainingPrincipal = 0;
          }
        }
        // Deferred
        if (thisTransDeferredInt > 0) {
          if (remainingPrincipal >= thisTransDeferredInt) {
            remainingPrincipal -= thisTransDeferredInt;
            emiDeferred = thisTransDeferredInt;
            dataToUpdate.regInterestAmount = ele?.regInterestAmount ?? 0;
          } else if (remainingPrincipal > 0) {
            dataToUpdate.regInterestAmount = remainingPrincipal;
            emiDeferred = remainingPrincipal;
            remainingPrincipal = 0;
          }
        }
        // Loop(To Keep Amount Negative)
        for (let key in dataToUpdate) {
          if (key == 'id') continue;
          if (key == 'loanId') continue;
          if (key == 'adminId') continue;
          if (typeof dataToUpdate[key] === 'number') {
            dataToUpdate[key] *= -1;
            dataToUpdate.paidAmount += dataToUpdate[key];
          }
        }
        // Preparing UTR and Transaction Id for Principal Entry in Transacitons
        utrForPrincipal = ele?.utr;
        transIdForPrincipal = ele?.transactionId;
        // Data Appending Transaction Wise
        dataToUpdate.source = ele?.source;
        let emiId = ele?.emiId;
        dataToUpdate.emiId = emiId;
        dataToUpdate.type = ele?.type;
        dataToUpdate.utr = ele?.utr + '__REVERSE_SETTLEMENT';
        dataToUpdate.transactionId =
          ele?.transactionId + '__REVERSE_SETTLEMENT';
        dataToUpdate.completionDate = this.dateService
          .getGlobalDate(new Date())
          .toJSON();

        const updatedData = await this.transactionRepo.createRowData(
          dataToUpdate,
        );
        if (updatedData == k500Error) return kInternalError;
        let emiAttr = [
          'payment_status',
          'paidRegInterestAmount',
          'paidBounceCharge',
          'paidPenalCharge',
          'paidLegalCharge',
          'paid_penalty',
          'waived_regInterest',
          'waived_bounce',
          'waived_penalty',
          'waived_penal',
          'waived_legal',
          'waiver',
          'paid_waiver',
          'unpaid_waiver',
          'regInterestAmount',
          'bounceCharge',
          'gstOnBounceCharge',
          'penalty',
          'dpdAmount',
          'penaltyChargesGST',
          'legalCharge',
          'legalChargeGST',
          'principalCovered',
          'interestCalculate',
          'paid_principal',
          'paid_interest',
        ];
        const emiData = await this.emiRepo.getRowWhereData(emiAttr, {
          where: { id: emiId },
        });
        if (emiData == k500Error) return kInternalError;
        // Data to Update in EMI as We Made Changes in Transaction
        if (!userTypeNew) emiData.bounceCharge = 0;
        let emiDataToUpdate: any = {
          paidLegalCharge: (emiData?.paidLegalCharge ?? 0) - emiLegal,
          paidBounceCharge: (emiData?.paidBounceCharge ?? 0) - emiBounce,
          paidRegInterestAmount:
            (emiData?.paidRegInterestAmount ?? 0) - emiDeferred,
          waived_legal: (emiData?.waived_legal ?? 0) + emiLegal,
          waived_bounce: (emiData?.waived_bounce ?? 0) + emiBounce,
          waived_regInterest: (emiData?.waived_regInterest ?? 0) + emiDeferred,
        };
        // Bounce Charge Fix
        if (emiDataToUpdate.waived_bounce > ECS_BOUNCE_CHARGE)
          emiDataToUpdate.waived_bounce = ECS_BOUNCE_CHARGE;

        // Legal Charge Fix
        if (emiDataToUpdate.waived_legal > 5900)
          emiDataToUpdate.waived_legal = 5900;
        // Penal Cal (Diff. for New and Old User(Penal Charges vs Penalty))
        if (userTypeNew) {
          emiDataToUpdate.paidPenalCharge =
            (emiData?.paidPenalCharge ?? 0) - emiPenal;
          emiDataToUpdate.waived_penal =
            (emiData?.waived_penal ?? 0) + emiPenal;
        } else {
          emiDataToUpdate.paid_penalty =
            (emiData?.paid_penalty ?? 0) - emiPenal;
          emiDataToUpdate.waived_penalty =
            (emiData?.waived_penalty ?? 0) + emiPenal;
        }
        // Reducing Deferred Int. After Waiver
        emiDataToUpdate.regInterestAmount = +(
          (emiData?.regInterestAmount ?? 0) - emiDeferred
        ).toFixed(2);
        if (emiDataToUpdate.regInterestAmount < 1)
          emiDataToUpdate.regInterestAmount = 0;

        // Reducing Bounce After Waiver
        let newBounce = emiBounce;
        let newBounceGst = newBounce - newBounce / 1.18;
        newBounce = newBounce - newBounceGst;
        emiDataToUpdate.bounceCharge = +(
          (emiData?.bounceCharge ?? 0) - newBounce
        ).toFixed(2);
        if (emiDataToUpdate.bounceCharge < 1) emiDataToUpdate.bounceCharge = 0;
        emiDataToUpdate.gstOnBounceCharge = +(
          (emiData?.gstOnBounceCharge ?? 0) - newBounceGst
        ).toFixed(2);
        if (emiDataToUpdate.gstOnBounceCharge < 1)
          emiDataToUpdate.gstOnBounceCharge = 0;

        // Reducing Penal After Waiver
        let newPenal = emiPenal;
        if (userTypeNew) {
          let newPenalGst = newPenal - newPenal / 1.18;
          newPenal = newPenal - newPenalGst;
          emiDataToUpdate.dpdAmount = +(
            (emiData?.dpdAmount ?? 0) - newPenal
          ).toFixed(2);
          if (emiDataToUpdate.dpdAmount < 1) emiDataToUpdate.dpdAmount = 0;
          emiDataToUpdate.penaltyChargesGST = +(
            (emiData?.penaltyChargesGST ?? 0) - newPenalGst
          ).toFixed(2);
          if (emiDataToUpdate.penaltyChargesGST < 1)
            emiDataToUpdate.penaltyChargesGST = 0;
        } else {
          emiDataToUpdate.penalty = +(
            (emiData?.penalty ?? 0) - emiPenal
          ).toFixed(2);
          if (emiDataToUpdate.penalty < 1) emiDataToUpdate.penalty = 0;
        }

        // Reducing Legal After Waiver
        let newLegal = emiLegal;
        let newLegalGst = newLegal - newLegal / 1.18;
        newLegal = newLegal - newLegalGst;
        emiDataToUpdate.legalCharge = +(
          (emiData?.legalCharge ?? 0) - newLegal
        ).toFixed(2);
        if (emiDataToUpdate.legalCharge < 1) emiDataToUpdate.legalCharge = 0;
        emiDataToUpdate.legalChargeGST = +(
          (emiData?.legalChargeGST ?? 0) - newLegalGst
        ).toFixed(2);
        if (emiDataToUpdate.legalChargeGST < 1)
          emiDataToUpdate.legalChargeGST = 0;

        let waived =
          (emiDataToUpdate.waived_legal ?? 0) -
          (emiData?.waived_legal ?? 0) +
          (emiDataToUpdate.waived_penal ?? 0) -
          (emiData?.waived_penal ?? 0) +
          (emiDataToUpdate.waived_penalty ?? 0) -
          (emiData?.waived_penalty ?? 0) +
          (emiDataToUpdate.waived_bounce ?? 0) -
          (emiData?.waived_bounce ?? 0) +
          (emiDataToUpdate.waived_regInterest ?? 0) -
          (emiData?.waived_regInterest ?? 0);
        emiDataToUpdate.waiver = (emiData?.waiver ?? 0) + waived;
        // Update Payment Status and Date to Close The EMI
        if (emiData?.payment_status != '1') {
          emiDataToUpdate.payment_status = '1';
          emiDataToUpdate.payment_done_date = this.dateService
            .getGlobalDate(new Date())
            .toJSON();
        }
        emiDataToUpdate.waiver = this.typeService.manageAmount(
          emiDataToUpdate.waiver,
        );
        const isEmiUpdated = await this.emiRepo.updateRowData(
          emiDataToUpdate,
          emiId,
        );
        if (isEmiUpdated == k500Error) return kInternalError;
        const userActivity = {
          emiId,
          waiver_emiAmount: 0,
          waiver_penalty: userTypeNew
            ? 0
            : emiDataToUpdate.waived_penalty - (emiData?.waived_penalty ?? 0),
          waiver_regIntAmount: emiDeferred,
          waiver_bounce:
            emiDataToUpdate.waived_bounce - (emiData?.waived_bounce ?? 0),
          waiver_penal: userTypeNew
            ? emiDataToUpdate.waived_penal - (emiData?.waived_penal ?? 0)
            : 0,
          waiver_legal:
            emiDataToUpdate.waived_legal - (emiData?.waived_legal ?? 0),
          type: 'REVERSE_SETTLEMENT',
        };
        let preparedData = this.prepareWaiverObj(emiData, userActivity);
        let data = {
          oldBifurcation: preparedData.oldBifurcation,
          newBifurcation: preparedData.newBifurcation,
          waiverBifurcation: preparedData.waiverBifurcation,
          waiverAmount: -dataToUpdate.paidAmount,
          loanId,
          userId,
          emiId,
          adminId,
          type: 'EMIPAY_REVERSE_SETTLEMENT',
          followerId,
        };
        let updateWaiverEntry = await this.repoManager.createRowData(
          WaiverEntity,
          data,
        );
        if (updateWaiverEntry == k500Error) return kInternalError;

        const createActivity = {
          loanId,
          userId,
          type: 'WAIVER_PAID',
          date: this.typeService.getGlobalDate(new Date()).toJSON(),
          respons: JSON.stringify(userActivity),
        };
        const addEntryForWaiver = await this.userActivityRepo.createRowData(
          createActivity,
        );
        if (addEntryForWaiver == k500Error) return kInternalError;
      }
      if (remainingPrincipal == 0) break;
    }
    // Interest (If Reverse Settlement Not Possible With Extra Charges Then We Will Go For Regular Interest)
    if (remainingPrincipal > 0) {
      for (let index = 0; index < transData.length; index++) {
        if (remainingPrincipal > 0) {
          const ele = transData[index];
          utrForPrincipal = ele?.utr;
          transIdForPrincipal = ele?.transactionId;
          const emiId = ele?.emiId;
          let emiInterest = 0;
          let thisTransInterest = ele?.interestAmount ?? 0;
          if (thisTransInterest <= 0) continue;
          // Data to Update in Transaction
          let dataToUpdate: any = {
            paidAmount: 0,
            status: 'COMPLETED',
            loanId: body?.loanId,
            userId: body?.userId,
            subStatus: 'REVERSE_SETTLEMENT',
            adminId: +adminId,
          };
          if (thisTransInterest > 0) {
            if (remainingPrincipal >= thisTransInterest) {
              remainingPrincipal -= thisTransInterest;
              emiInterest = thisTransInterest;
              dataToUpdate.interestAmount = -(ele?.interestAmount ?? 0);
              dataToUpdate.paidAmount += -(ele?.interestAmount ?? 0);
            } else if (remainingPrincipal > 0) {
              dataToUpdate.interestAmount = -remainingPrincipal;
              dataToUpdate.paidAmount += -remainingPrincipal;
              emiInterest = remainingPrincipal;
              remainingPrincipal = 0;
            }
          }
          dataToUpdate.source = ele?.source;
          dataToUpdate.emiId = ele?.emiId;
          dataToUpdate.type = ele?.type;
          dataToUpdate.utr = ele?.utr + 'I__REVERSE_SETTLEMENT';
          dataToUpdate.transactionId =
            ele?.transactionId + 'I__REVERSE_SETTLEMENT';
          dataToUpdate.completionDate = this.dateService
            .getGlobalDate(new Date())
            .toJSON();

          const updatedData = await this.transactionRepo.createRowData(
            dataToUpdate,
          );
          if (updatedData == k500Error) return kInternalError;
          let emiAttr = [
            'paid_interest',
            'waived_interest',
            'waiver',
            'paid_waiver',
            'unpaid_waiver',
            'payment_status',
            'penalty',
            'regInterestAmount',
            'bounceCharge',
            'gstOnBounceCharge',
            'dpdAmount',
            'penaltyChargesGST',
            'legalCharge',
            'legalChargeGST',
            'waived_regInterest',
            'waived_penalty',
            'waived_bounce',
            'waived_penal',
            'waived_legal',
            'principalCovered',
            'interestCalculate',
            'paid_principal',
            'paidRegInterestAmount',
            'paidBounceCharge',
            'paidPenalCharge',
            'paidLegalCharge',
          ];
          const emiData = await this.emiRepo.getRowWhereData(emiAttr, {
            where: { id: emiId },
          });
          if (!userTypeNew) emiData.bounceCharge = 0;
          if (emiData == k500Error) kInternalError;
          // Data to Update in EMI (As We Made Changes In Transaction)
          let emiDataToUpdate: any = {
            paid_interest: (emiData?.paid_interest ?? 0) - emiInterest,
            waived_interest: (emiData?.waived_interest ?? 0) + emiInterest,

            penalty: 0,
            waived_penalty: +(
              (emiData?.penalty ?? 0) + (emiData?.waived_penalty ?? 0)
            ).toFixed(2),
            regInterestAmount: 0,
            waived_regInterest: +(
              (emiData?.regInterestAmount ?? 0) +
              (emiData?.waived_regInterest ?? 0)
            ).toFixed(2),
            bounceCharge: 0,
            gstOnBounceCharge: 0,
            waived_bounce: +(
              (emiData?.bounceCharge ?? 0) +
              (emiData?.gstOnBounceCharge ?? 0) +
              (emiData?.waived_bounce ?? 0)
            ).toFixed(2),
            dpdAmount: 0,
            penaltyChargesGST: 0,
            waived_penal: +(
              (emiData?.dpdAmount ?? 0) +
              (emiData?.penaltyChargesGST ?? 0) +
              (emiData?.waived_penal ?? 0)
            ).toFixed(2),
            legalCharge: 0,
            legalChargeGST: 0,
            waived_legal: +(
              (emiData?.legalCharge ?? 0) +
              (emiData?.legalChargeGST ?? 0) +
              (emiData?.waived_legal ?? 0)
            ).toFixed(2),
          };
          emiDataToUpdate.waiver =
            emiDataToUpdate.waived_interest +
            emiDataToUpdate.waived_regInterest +
            emiDataToUpdate.waived_bounce +
            emiDataToUpdate.waived_penalty +
            emiDataToUpdate.waived_penal +
            emiDataToUpdate.waived_legal;
          emiDataToUpdate.paid_waiver = 0;
          emiDataToUpdate.unpaid_waiver = 0;
          if (emiData?.payment_status != '1') {
            emiDataToUpdate.payment_status = '1';
            emiDataToUpdate.payment_done_date = this.dateService
              .getGlobalDate(new Date())
              .toJSON();
          }
          emiDataToUpdate.waiver = this.typeService.manageAmount(
            emiDataToUpdate.waiver,
          );
          const isEmiUpdated = await this.emiRepo.updateRowData(
            emiDataToUpdate,
            emiId,
          );
          if (isEmiUpdated == k500Error) return kInternalError;
          const userActivity = {
            emiId,
            waiver_emiAmount:
              emiDataToUpdate.waived_interest - (emiData?.waived_interest ?? 0),
            waiver_penalty: 0,
            waiver_regIntAmount: 0,
            waiver_bounce: 0,
            waiver_penal: 0,
            waiver_legal: 0,
            type: 'REVERSE_SETTLEMENT',
          };
          let preparedData = this.prepareWaiverObj(emiData, userActivity);
          let data = {
            oldBifurcation: preparedData.oldBifurcation,
            newBifurcation: preparedData.newBifurcation,
            waiverBifurcation: preparedData.waiverBifurcation,
            waiverAmount: -dataToUpdate.paidAmount,
            loanId,
            userId,
            emiId,
            adminId,
            type: 'EMIPAY_REVERSE_SETTLEMENT',
            followerId,
          };
          let updateWaiverEntry = await this.repoManager.createRowData(
            WaiverEntity,
            data,
          );
          if (updateWaiverEntry == k500Error) return kInternalError;
          const createActivity = {
            loanId,
            userId,
            type: 'WAIVER_PAID',
            date: this.typeService.getGlobalDate(new Date()).toJSON(),
            respons: JSON.stringify(userActivity),
          };
          const addEntryForWaiver = await this.userActivityRepo.createRowData(
            createActivity,
          );
          if (addEntryForWaiver == k500Error) return kInternalError;
        }
        if (remainingPrincipal == 0) break;
      }
    }
    for (let index = 0; index < emiArray.length; index++) {
      const ele = emiArray[index];
      // After Reversing Charges in Transaction Adding Entry For PRINCIPAL we Covered
      let dataToUpdate: any = {
        paidAmount: 0,
        status: 'COMPLETED',
        loanId: body?.loanId,
        userId: body?.userId,
        emiId: ele?.emiId,
        subStatus: 'REVERSE_SETTLEMENT',
        adminId: +adminId,
        utr: utrForPrincipal + '__REVERSED_PRINCIPAL',
        transactionId: transIdForPrincipal + '__REVERSED_PRINCIPAL',
      };
      dataToUpdate.paidAmount = ele?.remainingPrincipal;
      dataToUpdate.source = 'WEB';
      dataToUpdate.type = 'EMIPAY';
      dataToUpdate.completionDate = this.dateService
        .getGlobalDate(new Date())
        .toJSON();
      dataToUpdate.principalAmount = ele?.remainingPrincipal;
      if (ele?.remainingPrincipal > 0) {
        const isPrincipalUpdated = await this.transactionRepo.createRowData(
          dataToUpdate,
        );
        if (isPrincipalUpdated == k500Error) return kInternalError;
      }
      // Now Updating EMIs(Only Where Principal was Remaining) with Covered Principal and Waived The Remaining/Unpaid Amts (Except Principal)
      let emiAttr = [
        'interestCalculate',
        'regInterestAmount',
        'paidRegInterestAmount',
        'bounceCharge',
        'paidBounceCharge',
        'gstOnBounceCharge',
        'penalty',
        'dpdAmount',
        'penaltyChargesGST',
        'paidPenalCharge',
        'totalPenalty',
        'legalCharge',
        'legalChargeGST',
        'paidLegalCharge',
        'paid_interest',
        'waiver',
        'waived_interest',
        'waived_legal',
        'waived_penal',
        'waived_penalty',
        'waived_bounce',
        'waived_regInterest',
        'principalCovered',
        'interestCalculate',
        'paid_principal',
      ];
      const emis = await this.emiRepo.getRowWhereData(emiAttr, {
        where: { id: ele?.emiId },
      });
      if (!userTypeNew) emis.bounceCharge = 0;
      // Current Expected
      let interest = this.typeService.manageAmount(
        (emis?.interestCalculate ?? 0) - (emis?.paid_interest ?? 0),
      );
      let deferredInt = this.typeService.manageAmount(
        (emis?.regInterestAmount ?? 0) - (emis?.paidRegInterestAmount ?? 0),
      );
      let bounceCharge = this.typeService.manageAmount(
        (emis?.bounceCharge ?? 0) +
          (emis?.gstOnBounceCharge ?? 0) -
          (emis?.paidBounceCharge ?? 0),
      );
      let penalCharge = this.typeService.manageAmount(
        (emis?.dpdAmount ?? 0) +
          (emis?.penaltyChargesGST ?? 0) -
          (emis?.paidPenalCharge ?? 0),
      );
      let penalty = this.typeService.manageAmount(emis?.penalty ?? 0);
      let legalCharge = this.typeService.manageAmount(
        (emis?.legalCharge ?? 0) +
          (emis?.legalChargeGST ?? 0) -
          (emis?.paidLegalCharge ?? 0),
      );
      const updatedData: any = {};
      updatedData.paid_principal = ele?.toUpdatePrincipal;
      updatedData.waived_interest = interest;
      updatedData.waived_legal = (emis?.waived_legal ?? 0) + legalCharge;
      updatedData.waived_penal = (emis?.waived_penal ?? 0) + penalCharge;
      updatedData.waived_penalty = (emis?.waived_penalty ?? 0) + penalty;
      updatedData.waived_bounce = (emis?.waived_bounce ?? 0) + bounceCharge;
      updatedData.waived_regInterest =
        (emis?.waived_regInterest ?? 0) + deferredInt;
      updatedData.regInterestAmount = 0;
      updatedData.bounceCharge = 0;
      updatedData.gstOnBounceCharge = 0;
      updatedData.penalty = 0;
      updatedData.dpdAmount = 0;
      updatedData.penaltyChargesGST = 0;
      updatedData.legalCharge = 0;
      updatedData.legalChargeGST = 0;
      updatedData.paid_waiver = 0;
      const waived =
        legalCharge +
        penalCharge +
        penalty +
        bounceCharge +
        deferredInt +
        interest;
      if (emis.waiver > 0 && emis.waived_interest > 0)
        emis.waiver = (emis.waiver ?? 0) - (emis?.waived_interest ?? 0);
      if (emis?.waiver < 0) emis.waiver = 0;
      updatedData.waiver = (emis?.waiver ?? 0) + waived;
      updatedData.waiver = this.typeService.manageAmount(updatedData.waiver);
      updatedData.payment_done_date = this.dateService
        .getGlobalDate(new Date())
        .toJSON();
      updatedData.payment_status = '1';
      updatedData.pay_type = 'EMIPAY';
      const emiId = ele?.emiId;
      let emiUpdate = await this.emiRepo.updateRowData(updatedData, emiId);
      if (emiUpdate == k500Error) return kInternalError;
      const userActivity = {
        emiId,
        waiver_emiAmount: 0,
        waiver_penalty: penalty,
        waiver_regIntAmount: deferredInt,
        waiver_bounce: bounceCharge,
        waiver_penal: penalCharge,
        waiver_legal: legalCharge,
        type: 'REVERSE_SETTLEMENT',
      };
      let preparedData = this.prepareWaiverObj(emis, userActivity);
      let data = {
        oldBifurcation: preparedData.oldBifurcation,
        newBifurcation: preparedData.newBifurcation,
        waiverBifurcation: preparedData.waiverBifurcation,
        waiverAmount: waived,
        loanId,
        userId,
        emiId,
        adminId,
        type: 'EMIPAY_REVERSE_SETTLEMENT',
        followerId,
      };
      let updateWaiverEntry = await this.repoManager.createRowData(
        WaiverEntity,
        data,
      );
      if (updateWaiverEntry == k500Error) return kInternalError;
      const createActivity = {
        loanId,
        userId,
        type: 'WAIVER_PAID',
        date: this.typeService.getGlobalDate(new Date()).toJSON(),
        respons: JSON.stringify(userActivity),
      };
      const addEntryForWaiver = await this.userActivityRepo.createRowData(
        createActivity,
      );
      if (addEntryForWaiver == k500Error) return kInternalError;
    }
    // Checking For Eligiblity for Loan Close
    const isEligibleForLoanClose =
      await this.isEligibleForLoanCloseByReverseSettlement(loanId);
    if (isEligibleForLoanClose)
      return await this.closeTheLoan({ loanId, userId });
  }

  async isEligibleForLoanCloseByReverseSettlement(loanId) {
    if (!loanId) return false;
    let options: any = { where: { loanId } };
    let attributes = [
      'payment_done_date',
      'payment_status',
      'principalCovered',
      'pay_type',
    ];
    const emiList = await this.emiRepo.getTableWhereData(attributes, options);
    let totalExpectedAmount = 0;

    for (let index = 0; index < emiList.length; index++) {
      try {
        const emi = emiList[index];
        const principalAmount = +(emi?.principalCovered ?? 0);
        if (emi?.pay_type == 'FULLPAY') totalExpectedAmount += principalAmount;
        else totalExpectedAmount += principalAmount;
      } catch (error) {}
    }
    totalExpectedAmount = Math.ceil(totalExpectedAmount);

    if (emiList == k500Error || emiList.length === 0) return false;

    const paidEMIs = emiList.filter(
      (el) => el.payment_status == '1' && el.payment_done_date != null,
    );
    const isAllEMIPaid = paidEMIs.length === emiList.length;
    // Calculate paid amount
    attributes = ['paidAmount'];
    options = { where: { loanId, status: kCompleted } };
    const transList = await this.transactionRepo.getTableWhereData(
      attributes,
      options,
    );
    if (transList == k500Error) return false;
    const paidAmount = transList.reduce(
      (prev, curr) => prev + curr.paidAmount,
      0,
    );
    // Calculate raw EMI amount
    const isPrincipalPaid = paidAmount >= totalExpectedAmount;

    return isAllEMIPaid && isPrincipalPaid;
  }

  // Function for Reversing the Waiver in EMI
  async reverseWaiver(data) {
    let { adminId, loanId, amount, emiId } = data;
    if (!adminId || !loanId || !amount || !emiId) return kParamsMissing;
    let emi = await this.emiRepo.getRowWhereData(
      [
        'paid_waiver',
        'waiver',
        'penalty',
        'unpaid_waiver',
        'regInterestAmount',
        'bounceCharge',
        'gstOnBounceCharge',
        'dpdAmount',
        'penaltyChargesGST',
        'legalCharge',
        'legalChargeGST',
        'paidRegInterestAmount',
        'paidBounceCharge',
        'paidPenalCharge',
        'paidLegalCharge',
        'waived_regInterest',
        'waived_bounce',
        'waived_penal',
        'waived_penalty',
        'waived_legal',
      ],
      { where: { id: emiId } },
    );
    if (emi == k500Error) return kInternalError;

    // Object to Update in EMI
    let obj: any = {};
    let currentWaiverGiven = emi?.unpaid_waiver ?? 0;

    // Covering Deferred Int. By Reversing Deferred Int. Waiver Off
    if (emi?.waived_regInterest > 0 && amount > 0) {
      let currentDeferred = emi?.regInterestAmount ?? 0;
      let deferredCovered =
        amount >= (emi?.waived_regInterest ?? 0)
          ? emi?.waived_regInterest
          : amount;
      amount -= deferredCovered;
      currentWaiverGiven -= deferredCovered;
      let regInterestAmount = currentDeferred + deferredCovered;
      if (regInterestAmount > 0)
        obj.regInterestAmount = +regInterestAmount.toFixed(2);
      let waived_regInterest = (emi?.waived_regInterest ?? 0) - deferredCovered;
      if (waived_regInterest < 0) waived_regInterest = 0;
      obj.waived_regInterest = waived_regInterest;
    }
    // Covering Bounce By Reversing Bounce Waiver Off
    if (emi?.waived_bounce > 0 && amount > 0) {
      let currentBounce =
        (emi?.bounceCharge ?? 0) + (emi?.gstOnBounceCharge ?? 0);
      let bounceCovered =
        amount >= (emi?.waived_bounce ?? 0) ? emi?.waived_bounce : amount;
      amount -= bounceCovered;
      currentWaiverGiven -= bounceCovered;
      let bounceCharge = currentBounce + bounceCovered;
      if (bounceCharge > ECS_BOUNCE_CHARGE) bounceCharge = ECS_BOUNCE_CHARGE;
      if (bounceCharge > 0) obj.bounceCharge = +bounceCharge.toFixed(2);
      let waived_bounce = (emi?.waived_bounce ?? 0) - bounceCovered;
      if (waived_bounce < 0) waived_bounce = 0;
      obj.waived_bounce = waived_bounce;
    }
    // Covering Penalty By Reversing Penalty Waiver Off
    if (emi?.waived_penalty > 0 && amount > 0) {
      let currentPenalty = emi?.penalty ?? 0;
      let penaltyCovered =
        amount >= (emi?.waived_penalty ?? 0) ? emi?.waived_penalty : amount;
      amount -= penaltyCovered;
      currentWaiverGiven -= penaltyCovered;
      let penalty = currentPenalty + penaltyCovered;
      if (penalty > 0) obj.penalty = +penalty.toFixed(2);
      let waived_penalty = (emi?.waived_penalty ?? 0) - penaltyCovered;
      if (waived_penalty < 0) waived_penalty = 0;
      obj.waived_penalty = waived_penalty;
    }
    // Covering Penal By Reversing Penal Waiver Off
    if (emi?.waived_penal > 0 && amount > 0) {
      let currentPenal = (emi?.dpdAmount ?? 0) + (emi?.penaltyChargesGST ?? 0);
      let penalCovered =
        amount >= (emi?.waived_penal ?? 0) ? emi?.waived_penal : amount;
      amount -= penalCovered;
      currentWaiverGiven -= penalCovered;
      let dpdAmount = currentPenal + penalCovered;
      let penaltyChargesGST = 0;
      dpdAmount = dpdAmount - penaltyChargesGST;
      if (dpdAmount > 0) obj.dpdAmount = +dpdAmount.toFixed(2);
      if (penaltyChargesGST > 0)
        obj.penaltyChargesGST = +penaltyChargesGST.toFixed(2);
      let waived_penal = (emi?.waived_penal ?? 0) - penalCovered;
      if (waived_penal < 0) waived_penal = 0;
      obj.waived_penal = waived_penal;
    }
    // Covering Legal By Reversing Legal Waiver Off
    if (emi?.waived_legal > 0 && amount > 0) {
      let currentLegal = (emi?.legalCharge ?? 0) + (emi?.legalChargeGST ?? 0);
      let legalCovered =
        amount >= (emi?.waived_legal ?? 0) ? emi?.waived_legal : amount;
      amount -= legalCovered;
      currentWaiverGiven -= legalCovered;
      let legalCharge = currentLegal + legalCovered;
      let legalChargeGST = legalCharge - legalCharge / 1.18;
      legalCharge = legalCharge - legalChargeGST;
      if (legalCharge > 0) obj.legalCharge = +legalCharge.toFixed(2);
      if (legalChargeGST > 0) obj.legalChargeGST = +legalChargeGST.toFixed(2);
      let waived_legal = (emi?.waived_legal ?? 0) - legalCovered;
      if (waived_legal < 0) waived_legal = 0;
      obj.waived_legal = waived_legal;
    }

    if (currentWaiverGiven < 1) currentWaiverGiven = 0;
    obj.unpaid_waiver = +currentWaiverGiven.toFixed(2);

    if (
      obj.legalCharge + obj.legalChargeGST > 5902 ||
      obj.bounceCharge > ECS_BOUNCE_CHARGE
    ) {
      const text = '*Updating Wrong Charges in EMI While Waiver Rev. By Admin*';
      const bodyDetails = {
        emi,
        data,
      };
      const threads = [
        `Body details -> ${JSON.stringify(bodyDetails)}`,
        `Prepared Data -> ${JSON.stringify(obj)}`,
      ];
      this.slackService.sendMsg({ text, threads });
    }
    let emiUpdate = await this.emiRepo.updateRowData(obj, emiId);
    if (emiUpdate == k500Error) return kInternalError;

    // Updating Waiver Entry for Reports Convenince
    let updateWaiverEntity = await this.repoManager.updateData(
      WaiverEntity,
      { type: 'WAIVER_REVERSED' },
      { emiId },
    );
    if (updateWaiverEntity == k500Error) return kInternalError;
    return true;
  }

  async sendADNotificationSMS(data) {
    if (!data) return;

    let { appType, amount, fcmToken } = data?.body;
    const { name, phone } = data?.preparedData;
    const isADNotPlaced = data?.isADNotPlace;

    // Define SMS templates based on appType
    const successSMSId =
      appType === 1
        ? kMsg91Templates.AutoDebitInitiated
        : kLspMsg91Templates.AutoDebitInitiated;

    const failedSMSId =
      appType === 1
        ? kMsg91Templates.AutoDebitNotPlaced
        : kLspMsg91Templates.AutoDebitNotPlaced;

    const smsId = isADNotPlaced ? failedSMSId : successSMSId;

    // Prepare SMS data based on isADNotPlaced status
    const sendData = isADNotPlaced
      ? {
          NAME: name,
          AMOUNT: amount,
          CONTACT: EnvConfig.number.collectionNumber,
        }
      : { var1: name, var2: amount };

    // Send SMS
    const smsOptions = {
      appType,
      smsId,
      ...sendData,
    };
    await this.allsmsService.sendSMS(phone, MSG91, smsOptions);

    // Prepare notification data
    const notifyData = isADNotPlaced
      ? {
          title: 'Auto Debit Not Placed',
          body: "Looks like you'll have to make a manual payment this time! Your Auto Debit placement has been missed, which might impact your credit history.",
        }
      : {
          title: 'Auto Debit Initiated',
          body: "We've successfully processed Auto debit of your EMI, which will be automatically deducted tomorrow. To ensure continuous service, please maintain an adequate balance.",
        };

    // Send push notification
    if (fcmToken)
      await this.sharedNotification.sendPushNotification(
        fcmToken,
        notifyData.title,
        notifyData.body,
        {},
        true,
      );
  }

  async checkPostLoanClosureOffer(body) {
    const loanId = body?.loanId;
    const userId = body?.userId;

    const bankattr = [
      'consentMode',
      'consentId',
      'consentHandleId',
      'consentPhone',
    ];
    const bankOptions: any = {
      where: {
        loanId,
        consentStatus: { [Op.or]: ['ACCEPTED', 'ACTIVE'] },
        consentId: { [Op.ne]: null },
        consentResponse: { [Op.ne]: null },
      },
      order: [['id', 'DESC']],
    };
    const bankData = await this.repoManager.getRowWhereData(
      BankingEntity,
      bankattr,
      bankOptions,
    );
    if (bankData == k500Error) return kInternalError;

    const userAttributes = ['masterId', 'typeOfDevice'];
    const userOptions: any = {
      where: {
        id: userId,
      },
    };
    let userData = await this.repoManager.getRowWhereData(
      registeredUsers,
      userAttributes,
      userOptions,
    );
    if (userData === k500Error) throw new Error();

    const masterAttributes = ['rejection', 'status', 'dates'];
    const masterOptions: any = {
      where: { id: userData?.masterId },
    };
    const masterData = await this.repoManager.getRowWhereData(
      MasterEntity,
      masterAttributes,
      masterOptions,
    );
    if (masterData === k500Error) throw new Error();

    const kycCompleteDate = masterData?.dates?.aadhaar;
    if (kycCompleteDate) {
      let reKycDate = new Date(kycCompleteDate);
      reKycDate.setDate(reKycDate.getDate() + REKYCDAYS);
      reKycDate = this.dateService.getGlobalDate(reKycDate);
      const currDate = this.dateService.getGlobalDate(new Date());

      if (currDate >= reKycDate) return {};
    }

    userData = { ...userData, masterData };

    const consentId = bankData?.consentId;
    const consentHandleId = bankData?.consentHandleId;
    const custId = await this.cryptService.decryptPhone(bankData?.consentPhone);

    const createdAt = new Date();
    createdAt.setHours(createdAt.getHours() - 24);

    // Finvu consent check and fetch data request
    if (bankData && bankData.consentMode && bankData.consentMode === kfinvu) {
      // Check finvu consent status
      const consentData: any = await this.finvuService.checkConsentStatus(
        consentHandleId,
        `${custId}@finvu`,
      );

      if (
        bankData?.user?.typeOfDevice == '2' &&
        consentData?.consentId == null &&
        consentData?.consentStatus != 'REJECTED'
      )
        return {};

      if (consentData.consentStatus === 'ACCEPTED') {
        // Check consent expired or not
        const consentExpiry =
          await this.bankingSharedService.isFinvuConsentExpired(consentId);
        if (consentExpiry) return {};
        // Check if 3 attempts have been made in a month or not
        const consentLimit =
          await this.bankingSharedService.isFinvuConsentLimitExceeded(
            consentId,
          );
        if (consentLimit) return {};

        // Check if session request have been made in last 24 hrs
        const sessionData = await this.repoManager.getRowWhereData(
          PeriodicEntity,
          null,
          {
            where: {
              userId,
              createdAt: { [Op.gte]: createdAt },
              type: 2,
              source: 1,
            },
          },
        );
        if (sessionData == k500Error) throw new Error();
        if (sessionData) return {};

        // Create new session
        const sessionId = await this.finvuService.fetchDataRequest(
          custId,
          consentId,
          consentHandleId,
        );
        if (!sessionId) throw new Error();
        await this.repoManager.createRowData(PeriodicEntity, {
          sessionId,
          consentId,
          source: 1,
          type: 2,
          userId,
        });
      }
    } // Cams consent check and fetch data
    else if (
      bankData &&
      bankData.consentMode &&
      bankData.consentMode === kCAMS
    ) {
      const consentResponse = await this.camsService.checkConsentStatus(
        bankData.consentHandleId,
        bankData.consentId,
      );
      if (consentResponse?.message) return consentResponse;
      if (consentResponse.consentExpired == true) return {};
      const consentStatus = consentResponse?.consentDetails?.consentStatus;

      if (
        bankData?.user?.typeOfDevice == '2' &&
        consentResponse?.consentDetails?.consentId == null &&
        consentStatus != 'REJECTED'
      )
        return {};

      if (consentStatus == 'ACTIVE') {
        // Check if session request have been made in last 24 hrs
        const sessionData = await this.repoManager.getRowWhereData(
          PeriodicEntity,
          null,
          {
            where: {
              userId,
              createdAt: { [Op.gte]: createdAt },
              type: 2,
              source: 2,
            },
          },
        );
        if (sessionData == k500Error) throw new Error();
        if (sessionData) return {};

        const sessionId = await this.camsService.fetchData(bankData.consentId);
        if (!sessionId) throw new Error();
        await this.repoManager.createRowData(PeriodicEntity, {
          sessionId,
          consentId,
          source: 2,
          type: 2,
          userId,
        });
      }
    }
  }

  async storeAssetDpd(body, loanId) {
    const assetbody = {
      download: false,
      loanIds: [loanId],
    };
    if (!body?.id) return kParamMissing('id');

    const response = await this.reportService.assetMaxDPD(assetbody);
    await this.repoManager.updateRowData(
      TransactionEntity,
      { asset_dpd: response[0]?.maxDPD },
      body?.id,
      true,
    );
  }

  //#region fetchAmountWithGst
  fetchGstFromAmount(x: number, isTwoGst = false) {
    const original = x;
    let gstRaw = x - x / 1.18;
    let pureRaw = x - gstRaw;

    let pure = Math.round(pureRaw);
    let remaining = original - pure;
    if (remaining % 2 !== 0) {
      pure -= 1;
      remaining = original - pure;
    }

    if (isTwoGst) {
      let cgst = remaining / 2;
      let sgst = remaining / 2;
      return {
        pure,
        cgst,
        sgst,
      };
    }

    const gst = remaining;
    return {
      pure,
      gst,
    };
  }

  async inActiveDelayCreditAmountBifurcation() {
    let user = JSON.parse(
      fs.readFileSync(CRYPT_PATH.inActiveDefaulters, 'utf8'),
    );

    const wiverRemoved = [];
    const misMatchWaiverSet = [];
    const noWaiverData = [];
    const reminingRefundUsers = [];
    const transactions = [];
    for (let index = 0; index < user.length; index++) {
      const userValue = user[index];
      let remainingRefund = Math.round(userValue['Gross Refund Amount']) || 0;
      let hasWaiver = false;
      let isMismatch = false;
      if (userValue['User Id'] == 'b2954646-b7c8-472f-bede-584c6e765145')
        continue;

      const emiAttr = [
        'waiver',
        'userId',
        'emiNumber',
        'id',
        'paid_waiver',
        'unpaid_waiver',
        'loanId',
        'waived_principal',
        'waived_interest',
        'waived_penalty',
        'waived_bounce',
        'waived_reginterest',
        'waived_penal',
        'waived_legal',
        'waived_regInterest',
        'paid_penalty',
        'penalty',
        'paid_interest',
        'paid_principal',
        'paidLegalCharge',
        'paidLegalCharge',
        'paidBounceCharge',
        'paidRegInterestAmount',
        'paidPenalCharge',
        'penalty_days',
        'bounceCharge',
        'dpdAmount',
        'regInterestAmount',
        'legalCharge',
        'legalChargeGST',
      ];

      const emiOptions = {
        where: {
          userId: userValue['User Id'],
        },
      };

      const emis = await this.emiRepo.getTableWhereData(emiAttr, emiOptions);
      if (emis.length < 1) continue;

      let loanAttr = [
        'id',
        'userId',
        'penaltyCharges',
        'interestRate',
        'followerId',
        'netApprovedAmount',
        'feesIncome',
      ];

      let loan = await this.loanRepo.getRowWhereData(loanAttr, {
        where: {
          loanStatus: { [Op.ne]: 'Rejected' },
          userId: userValue['User Id'],
        },
        order: [['id', 'DESC']],
        limit: 1,
      });
      let isIgst = loan?.penaltyCharges?.I_GST ?? false;
      let isNewUser = loan?.penaltyCharges?.MODIFICATION_CALCULATION ?? false;

      const sortedEmis = emis.sort((a, b) => {
        const loanIdA = a.loanId ? a.loanId.toString() : '';
        const loanIdB = b.loanId ? b.loanId.toString() : '';
        if (loanIdA !== loanIdB) {
          return loanIdA.localeCompare(loanIdB);
        }
        return a.emiNumber - b.emiNumber;
      });

      for (const emi of sortedEmis) {
        const emiData: any = {};

        let emiPaidAmount = 0;
        const overallWaiverSum =
          Math.floor(emi?.waiver || 0) +
          Math.floor(emi?.paid_waiver || 0) +
          Math.floor(emi?.unpaid_waiver || 0);

        const waiverCharges = {
          waived_penal: Math.floor(emi.waived_penal || 0),
          waived_penalty: Math.floor(emi.waived_penalty || 0),
          waived_bounce: Math.floor(emi.waived_bounce || 0),
          waived_regInterest: Math.floor(emi.waived_regInterest || 0),
          waived_legal: Math.floor(emi.waived_legal || 0),
          waived_interest: Math.floor(emi.waived_interest || 0),
          waived_principal: Math.floor(emi.waived_principal || 0),
        };

        const totalWaiverCharges = Object.values(waiverCharges).reduce(
          (acc, value) => acc + value,
          0,
        );

        const difference = Math.abs(totalWaiverCharges - overallWaiverSum);
        if (difference > 2) {
          isMismatch = true;
          misMatchWaiverSet.push({ emi });
          continue;
        }

        let newCharges: any = {};

        if (totalWaiverCharges > 0) {
          hasWaiver = true;
          if (remainingRefund > 0) {
            const deductionOrder = [
              'waived_penal',
              'waived_penalty',
              'waived_bounce',
              'waived_regInterest',
              'waived_legal',
              'waived_principal',
              'waived_interest',
            ];

            // Round off the waiver charges
            for (const chargeType in waiverCharges) {
              waiverCharges[chargeType] = waiverCharges[chargeType];
            }

            for (const chargeType of deductionOrder) {
              const chargeAmount = waiverCharges[chargeType];
              if (chargeAmount > 0 && remainingRefund > 0) {
                const deduction = Math.min(chargeAmount, remainingRefund);
                waiverCharges[chargeType] = deduction;
                remainingRefund -= deduction;
                emiPaidAmount += deduction;
              } else {
                waiverCharges[chargeType] = 0;
              }
            }
            newCharges = { ...waiverCharges };

            this.processWaiverCharge(
              'waived_penal',
              'paidPenalCharge',
              'dpdAmount',
              waiverCharges,
              emi,
              emiData,
              isIgst,
              isNewUser,
            );
            this.processWaiverCharge(
              'waived_penalty',
              'paid_penalty',
              'penalty',
              waiverCharges,
              emi,
              emiData,
              isIgst,
              isNewUser,
            );
            this.processWaiverCharge(
              'waived_bounce',
              'paidBounceCharge',
              'bounceCharge',
              waiverCharges,
              emi,
              emiData,
              isIgst,
              isNewUser,
            );
            this.processWaiverCharge(
              'waived_regInterest',
              'paidRegInterestAmount',
              'regInterestAmount',
              waiverCharges,
              emi,
              emiData,
              isIgst,
              isNewUser,
            );
            this.processWaiverCharge(
              'waived_legal',
              'paidLegalCharge',
              'legalCharge',
              waiverCharges,
              emi,
              emiData,
              isIgst,
              isNewUser,
            );

            this.processWaiverCharge(
              'waived_principal',
              'paid_principal',
              'principalCovered',
              waiverCharges,
              emi,
              emiData,
              isIgst,
              isNewUser,
            );
            this.processWaiverCharge(
              'waived_interest',
              'paid_interest',
              'interestCalculate',
              waiverCharges,
              emi,
              emiData,
              isIgst,
              isNewUser,
            );
          }
        }

        if (Object.keys(emiData).length > 0) {
          emiData.userId = loan.userId;

          let loanAttr = [
            'id',
            'userId',
            'penaltyCharges',
            'interestRate',
            'followerId',
            'netApprovedAmount',
            'feesIncome',
          ];

          let loanData = await this.loanRepo.getRowWhereData(loanAttr, {
            where: {
              loanStatus: { [Op.ne]: 'Rejected' },
              id: emi.loanId,
            },
            order: [['id', 'DESC']],
            limit: 1,
          });

          let data = {};
          let amt = (loanData?.penaltyCharges?.creditUsed || 0) + emiPaidAmount;
          if (amt < 0) amt = 0;
          if (loanData?.penaltyCharges) {
            data = { ...loanData?.penaltyCharges, creditUsed: amt };
          } else {
            data = {
              creditUsed: amt,
            };
          }
          let loanUpdate = await this.loanRepo.updateRowData(
            { penaltyCharges: data },
            loanData.id,
            true,
          );
          if (loanUpdate === k500Error)
            console.log('Error In Loan Update', loanData.id);

          let emiEntry = await this.emiRepo.updateRowData(
            emiData,
            emi.id,
            true,
          );
          if (emiEntry == k500Error)
            throw new Error(`Error In Emi Update ${emi.id}`);

          wiverRemoved.push({ emiData });

          const randomCode = this.typeService.generateRandomCode(10);
          let transactionId =
            EnvConfig.nbfc.nbfcCodeName + randomCode + `${emi?.emiNumber}_CN`;
          let maxDPD = 0;
          sortedEmis.forEach((ele) => {
            if ((ele.penalty_days ?? 0) > 0 && ele.penalty_days > maxDPD)
              maxDPD = +ele.penalty_days;
          });
          const creationData: any = {
            loanId: loan.id,
            paidAmount: emiPaidAmount,
            userId: loan.userId,
            transactionId: transactionId,
            utr: transactionId,
            completionDate: this.typeService
              .getGlobalDate(new Date('2025-03-31T10:00:00.000Z'))
              .toJSON(),
            emiId: emi.id,
            roundOff: 0,
            source: kCreditPay,
            status: kCompleted,
            subSource: kCreditPay,
            subStatus: 'CREDIT_SETTLEMENT',
            principalAmount: newCharges?.waived_principal ?? 0,
            interestAmount: newCharges?.waived_principal ?? 0,
            penaltyAmount: newCharges?.waived_penalty ?? 0,
            regInterestAmount: newCharges?.waived_regInterest ?? 0,
            bounceCharge: newCharges?.waived_bounce ?? 0,
            cgstOnBounceCharge: 0,
            sgstOnBounceCharge: 0,
            igstOnBounceCharge: 0,
            penalCharge: newCharges?.waived_penal ?? 0,
            cgstOnPenalCharge: 0,
            sgstOnPenalCharge: 0,
            igstOnPenalCharge: 0,
            legalCharge: this.fetchGstFromAmount(
              newCharges?.waived_legal ?? 0,
              !isIgst,
            )?.pure,
            cgstOnLegalCharge:
              this.fetchGstFromAmount(newCharges?.waived_legal ?? 0, !isIgst)
                ?.cgst ?? 0,
            sgstOnLegalCharge:
              this.fetchGstFromAmount(newCharges?.waived_legal ?? 0, !isIgst)
                ?.sgst ?? 0,
            igstOnLegalCharge:
              this.fetchGstFromAmount(newCharges?.waived_legal ?? 0, !isIgst)
                ?.gst ?? 0,
            forClosureAmount: 0,
            sgstForClosureCharge: 0,
            cgstForClosureCharge: 0,
            igstForClosureCharge: 0,
            forClosureDays: 0,
            accStatus: kCalBySystem,
            type: kPartPay,
            adminId: SYSTEM_ADMIN_ID,
            followerId: loan?.followerId,
            remarks: '',
            maxDPD: maxDPD,
            feesIncome: Math.round(
              ((+loan?.feesIncome || 0) * 100) /
                (+loan?.netApprovedAmount || 1),
            ),
            updatedAt: new Date('2025-03-31 15:49:14.673+00'),
            createdAt: new Date('2025-03-31 15:49:14.673+00'),
          };

          let transEntry = await this.transactionRepo.createRowData(
            creationData,
          );
          if (transEntry == k500Error)
            throw new Error(`Error In Trans Update ${loan.userId}`);
          // Add to transactions
          transactions.push(creationData);
        }
      }
      const userUpdate = await this.userRepo.updateRowData(
        {
          totalCredit: Math.round(userValue['Gross Refund Amount']),
          remainingCredit: +remainingRefund,
        },
        loan.userId,
        true,
      );
      if (userUpdate == k500Error)
        throw new Error(`Error In User Update ${loan.userId}`);

      if (remainingRefund > 0 && hasWaiver)
        reminingRefundUsers.push({ userId: userValue['User Id'] });
      else if (remainingRefund > 0 && !isMismatch)
        noWaiverData.push({ userId: userValue['User Id'] });
    }

    return {
      misMatchWaiverSet,
      wiverRemoved,
      reminingRefundUsers,
      noWaiverData,
      transactions,
    };
  }

  processWaiverCharge(
    chargeType,
    paidField,
    expectedField,
    waiverCharges,
    emi,
    emiData,
    isIgst,
    isNewUser,
  ) {
    if (waiverCharges[chargeType] > 0) {
      let chargeWithGst = waiverCharges[chargeType];
      let gstValue = 0;

      if (chargeType == 'waived_legal') {
        gstValue = +(chargeWithGst - chargeWithGst / 1.18).toFixed(2);
        chargeWithGst = +(chargeWithGst - gstValue).toFixed(2);

        gstValue = Math.round(gstValue);
        chargeWithGst = Math.round(chargeWithGst);

        emiData['legalChargeGST'] = +emi['legalChargeGST'] + gstValue;
      }
      emiData[paidField] = +emi[paidField] + waiverCharges[chargeType];
      emiData[expectedField] = +emi[expectedField] + chargeWithGst;
      emiData[chargeType] = Math.floor(
        +emi[chargeType] - +waiverCharges[chargeType],
      );
      if (emi?.waiver) {
        const deductible = Math.floor(
          Math.min(emi.waiver, waiverCharges[chargeType]),
        );
        emiData.waiver = Math.floor(Math.max(+emi.waiver - deductible, 0));
        waiverCharges[chargeType] -= deductible;
        emi.waiver = Math.floor(Math.max(+emi.waiver - deductible, 0));
      }

      if (waiverCharges[chargeType] > 0 && emi?.paid_waiver) {
        const deductible = Math.floor(
          Math.min(emi.paid_waiver, waiverCharges[chargeType]),
        );
        emiData.paid_waiver = Math.floor(
          Math.max(+emi.paid_waiver - deductible, 0),
        );
        waiverCharges[chargeType] -= deductible;
        emi.paid_waiver = Math.floor(
          Math.max(+emi.paid_waiver - deductible, 0),
        );
      }

      if (waiverCharges[chargeType] > 0 && emi?.unpaid_waiver) {
        const deductible = Math.floor(
          Math.min(emi.unpaid_waiver, waiverCharges[chargeType]),
        );
        emiData.unpaid_waiver = Math.floor(
          Math.max(+emi.unpaid_waiver - deductible, 0),
        );
        waiverCharges[chargeType] -= deductible;
        emi.unpaid_waiver = Math.floor(
          Math.max(+emi.unpaid_waiver - deductible, 0),
        );
      }
    }
  }

  async reduceAmountsForCreditPay(
    principalAmount,
    interestAmount,
    penaltyAmount,
    regInterestAmount,
    bounceCharge,
    penalCharge,
    forecloseAmount,
    legalCharge,

    fullPay,
    data,
  ) {
    const creditAmount = data?.creditAmount ?? 0;
    const loanId = data?.loanId ?? 0;
    const iGst = data?.iGst ?? 0;

    const creditBifurcation =
      await this.creditTransService.splitTransForCreditPay(
        creditAmount,
        loanId,
        iGst,
        fullPay,
      );

    for (let index = 0; index < creditBifurcation.length; index++) {
      const ele = creditBifurcation[index];

      principalAmount -= ele.principalAmount ?? 0;
      interestAmount -= ele.interestAmount ?? 0;
      penaltyAmount -= ele.penaltyAmount ?? 0;
      regInterestAmount -= ele.regInterestAmount ?? 0;

      bounceCharge -= ele.bounceCharge ?? 0;
      bounceCharge -= ele.cgstOnBounceCharge ?? 0;
      bounceCharge -= ele.sgstOnBounceCharge ?? 0;
      bounceCharge -= ele.igstOnBounceCharge ?? 0;

      penalCharge -= ele.penalCharge ?? 0;
      penalCharge -= ele.cgstOnPenalCharge ?? 0;
      penalCharge -= ele.sgstOnPenalCharge ?? 0;
      penalCharge -= ele.igstOnPenalCharge ?? 0;

      forecloseAmount -= ele.forClosureAmount ?? 0;
      forecloseAmount -= ele.cgstForClosureCharge ?? 0;
      forecloseAmount -= ele.sgstForClosureCharge ?? 0;
      forecloseAmount -= ele.igstForClosureCharge ?? 0;

      legalCharge -= ele.legalCharge ?? 0;
      legalCharge -= ele.cgstOnLegalCharge ?? 0;
      legalCharge -= ele.sgstOnLegalCharge ?? 0;
      legalCharge -= ele.igstOnLegalCharge ?? 0;
    }
    let updateData = {
      principalAmount,
      interestAmount,
      penaltyAmount,
      regInterestAmount,
      bounceCharge,
      penalCharge,
      forecloseAmount,
      legalCharge,
    };

    for (let key in updateData) {
      if (updateData[key] < 0) updateData[key] = 0;
    }

    return updateData;
  }

  //#region isUserFromActiveOntimeBucket
  async isUserFromActiveOntimeBucket(userId, needUsers = false) {
    try {
      const mode = process.env.MODE;
      if (mode == 'UAT') return true;
      let data = await this.redisService.get('ACTIVE_ONTIME');
      if (data) {
        try {
          data = JSON.parse(data) ?? {};
        } catch (error) {
          // Sending Slack Message for Credit Pay Users
          const text = `*CREDIT USER - CHECKING FOR USER*`;
          const body = {
            error,
            userId,
            needUsers,
          };
          const threads = [`Body details -> ${JSON.stringify(body)}`];
          this.slackService.sendMsg({ text, threads });
          return true;
        }
        let users = data?.users ?? [];
        if (needUsers) return users;
        if (users.indexOf(userId) == -1) return false;
        else return true;
      } else {
        const usersFile = await JSON.parse(
          fs.readFileSync(CRYPT_PATH.activeOntimeUsers, 'utf8'),
        );
        let users = usersFile?.users ?? [];
        await this.redisService.set('ACTIVE_ONTIME', JSON.stringify({ users }));
        if (needUsers) return users;
        if (users.indexOf(userId) == -1) return false;
        else return true;
      }
    } catch (error) {
      // Sending Slack Message for Credit Pay Users
      const text = `*CREDIT USER - CHECKING FOR USER*`;
      const body = {
        error,
        userId,
        needUsers,
      };
      const threads = [`Body details -> ${JSON.stringify(body)}`];
      this.slackService.sendMsg({ text, threads });
      return true;
    }
  }
  //#endregion

  //#region update Transaction for Waiver
  async splitWaiverTransaction(body) {
    const {
      emiData,
      index,
      id,
      iGst,
      utr,
      completionDate,
      coverAmount,
      paymentTime,
    } = body;

    // #1 Prepare Data for Waiver Payment
    const principal = emiData?.coverPrincipal ?? 0;
    const interest = emiData?.coverInterest ?? 0;
    const penalty = emiData?.coverPenalty ?? 0;
    const deferred = emiData?.coverRegInterest ?? 0;
    const bounce = emiData?.coverBounce ?? 0;
    const penal = emiData?.coverPenal ?? 0;
    let legal = emiData?.coverLegal ?? 0;
    let foreclose = emiData?.coverForeclose ?? 0;

    // #2 Get Transaction Data
    const trans = await this.transactionRepo.getRowWhereData(null, {
      where: { id },
    });
    if (!trans || trans == k500Error)
      throw new Error('Error In Transaction Get for Waiver Payment');

    // #3 Prepare Transaction Data
    const creationData: any = { ...trans };
    creationData.paidAmount = coverAmount;
    creationData.type = kPartPay;
    creationData.emiId = emiData?.id;
    creationData.principalAmount = principal;
    creationData.interestAmount = interest;
    creationData.penaltyAmount = penalty;
    creationData.regInterestAmount = deferred;
    creationData.bounceCharge = bounce;
    creationData.cgstOnBounceCharge = 0;
    creationData.sgstOnBounceCharge = 0;
    creationData.igstOnBounceCharge = 0;
    creationData.penalCharge = penal;
    creationData.cgstOnPenalCharge = 0;
    creationData.sgstOnPenalCharge = 0;
    creationData.igstOnPenalCharge = 0;
    legal = this.fetchGstFromAmount(legal, !iGst);
    creationData.legalCharge = legal?.pure;
    creationData.cgstOnLegalCharge = legal?.cgst ?? 0;
    creationData.sgstOnLegalCharge = legal?.sgst ?? 0;
    creationData.igstOnLegalCharge = legal?.gst ?? 0;
    foreclose = this.fetchGstFromAmount(foreclose, !iGst);
    creationData.forClosureAmount = foreclose?.pure;
    creationData.sgstForClosureCharge = foreclose?.sgst ?? 0;
    creationData.cgstForClosureCharge = foreclose?.cgst ?? 0;
    creationData.igstForClosureCharge = foreclose?.gst ?? 0;

    // #4 Update Transaction Data
    if (index == 0) {
      const isUpdated = await this.transactionRepo.updateRowData(
        creationData,
        id,
      );
      if (isUpdated == k500Error)
        throw new Error('Error In Transaction Update for Waiver Payment');
    } else {
      // #5 Create Transaction Data for Waiver Payment - SPLITTING Transaction
      const tail = `DMY${index + 1}`;
      creationData.transactionId = trans.transactionId + tail;
      creationData.utr = utr + tail;
      creationData.status = kCompleted;
      creationData.completionDate = completionDate;
      creationData.paymentTime = paymentTime;
      delete creationData.id;
      const isCreated = await this.transactionRepo.createRowData(creationData);
      if (isCreated == k500Error)
        throw new Error('Error In Transaction Create for Waiver Payment');
    }
    return true;
  }
  //#endregion
}
