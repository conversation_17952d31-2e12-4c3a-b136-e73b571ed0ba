// Imports
import { Body, Controller, Get, Post, Query, Res } from '@nestjs/common';
import { ProteanService } from './protean.servics';
import { kInternalError, kSuccessData } from 'src/constants/responses';

@Controller('thirdParty/protean')
export class ProteanController {
  constructor(private readonly service: ProteanService) {}

  @Get('generateAccessToken')
  async funGenerateAccessToken(@Body() body, @Res() res) {
    try {
      const data = await this.service.generateAccessToken(body);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Post('uan-lookup')
  async funUANLookup(@Body() body, @Res() res) {
    try {
      const data = await this.service.uanLookup(body);
      if (data.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Post('uan-epf-auth')
  async funUANValidation(@Body() body, @Res() res) {
    try {
      const data = await this.service.uanValidation(body);
      if (data.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Post('fetch-employment-history')
  async funUANValidation_S(@Body() body, @Res() res) {
    try {
      const data = await this.service.uanValidationS(body);
      if (data.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Post('uan-details')
  async funUANDetails(@Body() body, @Res() res) {
    try {
      const data = await this.service.uanDetails(body);
      if (data.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Get('cin-lookup')
  async funGetCompanyListByLookup(@Query() query, @Res() res) {
    try {
      const data = await this.service.getCompanyListByLookup(query);
      if (data.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Get('cin-lite')
  async funGetCompanyListByLite(@Query() query, @Res() res) {
    try {
      const data = await this.service.getCompanyListByLite(query);
      if (data.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Get('mca')
  async funCompanyDetailsLookUp(@Query() query, @Res() res) {
    try {
      const data = await this.service.companyDetailsLookUp(query);
      if (data.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Get('searchCompany')
  async funSearchCompany(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.searchCompany(query);
      if (data.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }
}
