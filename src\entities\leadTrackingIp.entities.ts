import {
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
  HasMany,
  BelongsTo,
} from 'sequelize-typescript';

@Table({})
export class LeadTrackingIp extends Model<LeadTrackingIp> {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    allowNull: false,
  })
  id: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  ip: string;

  @Column({
    type: DataType.SMALLINT,
    allowNull: true,
    defaultValue: 0,
  })
  isBlackListed: number;

  @Column({
    type: DataType.SMALLINT,
    // values: ['Finance Buddha', 'FintiFi', 'SwitchMyLoan'],
    comment:
      '0 = Finance Buddha, 1 = FintiFi, 2 = SwitchMyLoan, 3 = SwitchMyLoan, 4 = Scaller Boat Finnovation LLPs, 5 = GoCredit',
  })
  ipSource: number;
}
