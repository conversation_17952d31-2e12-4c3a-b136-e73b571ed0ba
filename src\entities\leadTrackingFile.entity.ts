import {
  Column,
  DataType,
  Model,
  Table,
  HasMany,
  BelongsTo,
} from 'sequelize-typescript';
import { LeadTrackingEntity } from './leadTracking.entity';

@Table({})
export class LeadTrackingFileEntity extends Model<LeadTrackingFileEntity> {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    allowNull: false,
  })
  id: number;

  @Column({
    type: DataType.SMALLINT,
  })
  uploadedBy: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  fileUrl: string;

  @HasMany(() => LeadTrackingEntity)
  leadsData: LeadTrackingEntity;
}
