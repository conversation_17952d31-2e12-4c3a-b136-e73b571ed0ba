// Imports
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { k422ErrorMessage, kParamMissing } from 'src/constants/responses';
import { CRYPT_PATH } from 'src/constants/paths';
import { k500Error } from 'src/constants/misc';
import {
  isUAT,
  yesUpiPartnerKey,
  yesUpiSellerIdentifier,
} from 'src/constants/globals';
import { SharedTransactionService } from 'src/shared/transaction.service';
import { TransactionRepository } from 'src/repositories/transaction.repository';
import { Op } from 'sequelize';
import { kCompleted, kFailed, kInitiated } from 'src/constants/strings';
import { TypeService } from 'src/utils/type.service';
import { APIService } from 'src/utils/api.service';
import { EnvConfig } from 'src/configs/env.config';
const fs = require('fs');
const crypto = require('crypto');
const forge = require('node-forge');

@Injectable()
export class YESService {
  constructor(
    @Inject(forwardRef(() => SharedTransactionService))
    private readonly sharedTransactionService: SharedTransactionService,
    private readonly transRepo: TransactionRepository,
    private readonly typeService: TypeService,
    private readonly apiService: APIService,
  ) {}

  //#region callback
  async callback(body) {
    const transactionId = body?.merchantRequestId;
    if (!transactionId) return kParamMissing('transactionId');

    const status =
      body?.gatewayResponseStatus == 'SUCCESS'
        ? kCompleted
        : body?.gatewayResponseStatus == 'FAILED' ||
          body?.gatewayResponseStatus == 'REJECT' ||
          body?.gatewayResponseStatus == 'FAIL'
        ? kFailed
        : kInitiated;
    if (status == kCompleted || status == kFailed) {
      const options = {
        where: {
          status: {
            [Op.ne]: kCompleted,
          },
          transactionId,
        },
      };
      const transData = await this.transRepo.getRowWhereData(
        ['emiId', 'id', 'loanId', 'type', 'userId'],
        options,
      );
      if (transData == k500Error)
        throw new Error('Error in Fetching Transaction');
      if (!transData) return true;

      // Preparing Payment Data to Complete the Transaction
      let transactionTimestamp = body?.transactionTimestamp;
      if (transactionTimestamp?.length == 16) transactionTimestamp + ':00.000Z';
      else if (transactionTimestamp?.length == 19)
        transactionTimestamp + '.000Z';
      let paymentData: any = {
        id: transData?.id,
        status: status,
        response: JSON.stringify(body),
        utr: body.gatewayTransactionId,
        completionDate: this.typeService
          .getGlobalDate(new Date(body.transactionTimestamp))
          .toJSON(),
        paymentTime: transactionTimestamp,
        type: transData?.type,
        loanId: transData?.loanId,
        userId: transData?.userId,
      };
      if (transData?.emiId) paymentData.emiId = transData?.emiId;
      await this.sharedTransactionService.markTransactionAsComplete(
        paymentData,
      );
      return true;
    } else
      return k422ErrorMessage(
        'Unknown Transaction Status Received From YES UPI',
      );
  }
  //#endregion

  //#region decryptCallbackResponse
  async decryptCallbackResponse(reqData) {
    const { headers, body } = reqData;
    const decryption = this.decryptResponse(
      headers.hash,
      headers.iv,
      headers.key,
      body.body,
      headers.callbacktype,
    );
    if (decryption.result) {
      if (
        decryption?.decCallbacktype == 'SELLER_SETTLEMENT' ||
        decryption?.decCallbacktype == 'SELLER_SETTLEMENT_CALLBACK'
      )
        return true;
      return JSON.parse(decryption.decryptedBody);
    }
    throw new Error('Decryption Failed for YES UPI Callback');
  }
  //#endregion

  //#region Register Intent
  async intentRegister(reqData) {
    const amount = reqData?.amount;
    const orderId = reqData?.orderId;
    if (!amount || !orderId) return k422ErrorMessage('amount or orderId');

    let plainBody: any = JSON.stringify({
      orderId,
      partnerKey: yesUpiPartnerKey,
      actionName: 'REGISTER_UPI_INTENT',
      p1: yesUpiSellerIdentifier,
      p2: amount,
      p3: 45, // Expiry Time in Minutes
    });

    // Preparing Encrypted Body and Headers
    const encryption = this.encryptRequest(plainBody);

    // Body and Headers
    const headers: any = {
      iv: encryption.plainRequestIv,
      token: encryption.requestToken,
      partner: encryption.encryptedPartnerKey,
      key: encryption.encryptedSecretKey,
    };
    const body = encryption.encryptRequestBody;

    const response = await this.apiService.yesPostApi(body, headers);

    let decryption: any = this.decryptResponse(
      response?.headers?.hash,
      response?.headers?.iv,
      response?.headers?.key,
      response?.data?.body,
    );
    // Decryption Success
    if (decryption?.result == true) {
      let intentResponse: any = {};
      decryption = JSON.parse(decryption.decryptedBody);

      // When Transaction is Success
      if (decryption?.status == 'SUCCESS' && decryption.responseCode == '00') {
        let paymentLink = `?mc=${decryption?.sellerInfo?.mcc}&pa=${decryption?.sellerInfo?.vpa}&pn=${decryption?.sellerInfo?.payeeName}&am=${decryption?.amount}&tr=${decryption?.orderId}&cu=INR`;
        // if (reqData.isDirect) paymentLink = 'upi://pay' + paymentLink;
        intentResponse.paymentLink = paymentLink;
        intentResponse.expiryTime = decryption.expiryDate;
        intentResponse.orderId = decryption.orderId;
        intentResponse.amount = decryption.amount;
        return intentResponse;
      }
      // When Any Error Occured
      else if (decryption.status == 'ERROR')
        return k422ErrorMessage(decryption.responseMessage);
    }
    // Decryption Failure
    else if (!decryption?.result)
      throw new Error('Decryption Failed in Intent Registration');

    return false;
  }
  //#endregion

  //#region Intent Status
  async intentStatus(reqData) {
    const transactionId = reqData?.transactionId;
    if (!transactionId) return kParamMissing('transactionId');

    let plainBody: any = JSON.stringify({
      partnerKey: yesUpiPartnerKey,
      actionName: 'UPI_INTENT_STATUS',
      p1: reqData?.transactionId,
    });

    // Preparing Encrypted Body and Headers
    const encryption = this.encryptRequest(plainBody);

    // Headers and Body
    const headers: any = {
      iv: encryption.plainRequestIv,
      token: encryption.requestToken,
      partner: encryption.encryptedPartnerKey,
      key: encryption.encryptedSecretKey,
    };
    const encBody = encryption.encryptRequestBody;

    // Calling Yes Bank's Post API
    const apiResponse = await this.apiService.yesPostApi(encBody, headers);

    // Decrypting Response
    let decryption: any = this.decryptResponse(
      apiResponse.headers.hash,
      apiResponse.headers.iv,
      apiResponse.headers.key,
      apiResponse.data.body,
    );
    // Decryption Success
    if (decryption?.result == true) return JSON.parse(decryption.decryptedBody);
    // Decryption Failure
    else if (!decryption?.result)
      throw new Error('Decryption Failed in Intent Status');
    return false;
  }
  //#endregion

  //#region refund
  async refund(reqData) {
    const { amount, originalNpciTxnId, transactionId, remark } = reqData;
    if (!amount) return kParamMissing('amount');
    if (!originalNpciTxnId) return kParamMissing('originalNpciTxnId');
    if (!transactionId) return kParamMissing('transactionId');
    if (!remark) return kParamMissing('remark');
    const missingField = [
      'amount',
      'originalNpciTxnId',
      'transactionId',
      'remark',
    ].find((field) => !reqData[field]);
    if (missingField) return kParamMissing(missingField);

    let plainbody = JSON.stringify({
      requestReferenceNumber: transactionId,
      actionName: 'PS_COLLECTION_REFUND',
      partnerKey: yesUpiPartnerKey,
      p1: originalNpciTxnId,
      p2: amount,
      p3: remark,
      p4: 'ONLINE',
    });

    // Encrypting Body and Preparing Headers
    const encryption = this.encryptRequest(plainbody);

    // Header and Body
    const headers: any = {
      iv: encryption.plainRequestIv,
      token: encryption.requestToken,
      partner: encryption.encryptedPartnerKey,
      key: encryption.encryptedSecretKey,
    };
    const encBody = encryption.encryptRequestBody;

    // Calling Yes Bank's Post API
    const apiResponse = await this.apiService.yesPostApi(encBody, headers);

    // Decrypting Response
    let decryption: any = this.decryptResponse(
      apiResponse.headers.hash,
      apiResponse.headers.iv,
      apiResponse.headers.key,
      apiResponse.data.body,
    );

    // Decryption Success
    if (decryption?.result == true) {
      decryption = JSON.parse(decryption.decryptedBody);
      const status =
        decryption?.txnStatus == 'SUCCESS'
          ? kCompleted
          : decryption?.txnStatus == 'FAILED'
          ? kFailed
          : kInitiated;

      let data: any = {
        status,
        response: JSON.stringify(decryption),
      };
      if (decryption?.status == 'SUCCESS') {
        let paymentTime = decryption?.refundDetails?.refundInitiationDate;
        if (paymentTime) {
          if (paymentTime?.length == 16) paymentTime + ':00.000Z';
          else if (paymentTime?.length == 19) paymentTime + '.000Z';
          data.paymentTime = paymentTime;
        }
      }
      if (status != kInitiated)
        if (decryption?.yppReferenceNumber)
          data.utr = decryption?.yppReferenceNumber;
      return data;
    }
    // Decryption Failure
    else if (!decryption?.result)
      throw new Error('Decryption Failed in Intent Status');

    return false;
  }
  //#endregion

  //#region refundStatus
  async refundStatus(reqData) {
    // Plain Body to Check Refund Status
    let plainBody = JSON.stringify({
      actionName: 'PS_COLLECTION_REFUND_STATUS',
      partnerKey: yesUpiPartnerKey,
      p1: reqData?.transactionId,
    });

    // Preparing Encrypted Body and Headers
    const encryption = this.encryptRequest(plainBody);

    // Headers and Body
    const headers: any = {
      iv: encryption.plainRequestIv,
      token: encryption.requestToken,
      partner: encryption.encryptedPartnerKey,
      key: encryption.encryptedSecretKey,
    };
    const encBody = encryption.encryptRequestBody;

    // Calling Yes Bank's Post API
    const response = await this.apiService.yesPostApi(encBody, headers);

    // Decrypting Response
    let decryption: any = this.decryptResponse(
      response.headers.hash,
      response.headers.iv,
      response.headers.key,
      response.data.body,
    );

    if (decryption?.result == true) {
      decryption = JSON.parse(decryption.decryptedBody);
      const status =
        decryption?.txnStatus == 'SUCCESS'
          ? kCompleted
          : decryption?.txnStatus == 'FAILED'
          ? kFailed
          : kInitiated;
      let paymentTime = decryption?.refundDetails?.refundInitiationDate;
      if (paymentTime) {
        paymentTime = paymentTime.replace(' ', 'T');
        if (paymentTime?.length == 16) paymentTime += ':00.000Z';
        else if (paymentTime?.length == 19) paymentTime += '.000Z';
      }

      let data: any = {
        status,
        response: JSON.stringify(decryption),
        paymentTime,
      };
      if (status != kInitiated)
        if (decryption?.yppReferenceNumber)
          data.utr = decryption?.yppReferenceNumber;

      return data;
    } else if (!decryption?.result)
      throw new Error('Decryption Failed in Intent Status');

    return response.data.body;
  }
  //#endregion

  //#region settlement
  // Function to Settle the Amount into Our Account.
  // This Will Be Called By CRON on Fixed Times(4AM | 10AM | 1PM | 5PM | 10PM).
  async settlement() {
    if (isUAT || EnvConfig?.nbfc?.nbfcType == '0') return true;

    const date = new Date();
    const month = (date.getMonth() + 1).toString().padStart(2, '0'); // 1 -> 01
    const settlementHour = date.getHours(); // 4AM // 10AM // 1PM // 5PM // 10PM
    const settlementId = `${date.getFullYear()}${month}${date.getDate()}${settlementHour}`;

    const plainBody: any = JSON.stringify({
      partnerReferenceNo: settlementId,
      actionName: 'SELLER_SETTLEMENT',
      partnerKey: yesUpiPartnerKey,
      p1: yesUpiSellerIdentifier,
      p2: 'NEFT',
    });

    // Preparing Encrypted Body and Headers
    const encryption = this.encryptRequest(plainBody);

    // Headers and Body
    const headers: any = {
      iv: encryption.plainRequestIv,
      token: encryption.requestToken,
      partner: encryption.encryptedPartnerKey,
      key: encryption.encryptedSecretKey,
    };
    const encbody = encryption.encryptRequestBody;

    // Calling Yes Bank's Post API
    const apiResponse = await this.apiService.yesPostApi(encbody, headers);

    // Decrypting Response
    let decryption: any = this.decryptResponse(
      apiResponse?.headers?.hash,
      apiResponse?.headers?.iv,
      apiResponse?.headers?.key,
      apiResponse?.data?.body,
    );
    if (decryption.result == true) return JSON.parse(decryption.decryptedBody);
    else return k422ErrorMessage('Decryption Failed Yes UPI Settlement');
  }
  //#endregion

  //#region settlementStatus
  async settlementStatus(reqData) {
    let plainBody: any = JSON.stringify({
      requestId: reqData?.requestId ?? '1',
      actionName: 'SELLER_SETTLEMENT_STATUS',
      partnerKey: yesUpiPartnerKey,
      p1: reqData?.settlementId,
      p2: 'NEFT',
    });

    // Preparing Encrypted Body and Headers
    const encryption = this.encryptRequest(plainBody);

    // Headers and Body
    const headers: any = {
      iv: encryption.plainRequestIv,
      token: encryption.requestToken,
      partner: encryption.encryptedPartnerKey,
      key: encryption.encryptedSecretKey,
    };
    const encbody = encryption.encryptRequestBody;

    // Calling Yes Bank's Post API
    const apiResponse = await this.apiService.yesPostApi(encbody, headers);

    // Decrypting Response
    let decryption: any = this.decryptResponse(
      apiResponse.headers.hash,
      apiResponse.headers.iv,
      apiResponse.headers.key,
      apiResponse.data.body,
    );
    if (decryption.result == true)
      return (decryption = JSON.parse(decryption.decryptedBody));
    else return k422ErrorMessage('Decryption Failed For Yes UPI Settlement');
  }
  //#endregion

  //#region encryptRequest
  encryptRequest(plainRequestBody) {
    const securityKey = this.generateKey(32);
    const plainRequestIv = this.generateIv(16);

    // Encrypting Unique Symmetric Key By Yes Bank's Public Key
    const encryptKey = this.rsaEncrypt(
      securityKey,
      CRYPT_PATH.yesBankPublicKey,
    );

    // Encrypting Our Yes UPI Partner Key By Yes Bank's Public Key
    const encryptPartner = this.rsaEncrypt(
      yesUpiPartnerKey,
      CRYPT_PATH.yesBankPublicKey,
    );

    // Encrypting Plain Body With Unique Symmetric Key and Initialization Vectors(IV)
    const encryptBody = this.gcmEncrypt(
      plainRequestBody,
      securityKey,
      plainRequestIv,
    );

    // Signing Data with Our Private Key
    const requestToken = this.signData(
      plainRequestBody,
      fs.readFileSync(CRYPT_PATH.authPrivateKey, 'utf8'),
    );
    return {
      encryptedSecretKey: encryptKey,
      plainRequestIv,
      encryptedPartnerKey: encryptPartner,
      encryptRequestBody: encryptBody,
      requestToken: requestToken,
    };
  }
  //#endregion

  //create random secret key
  private generateKey(keySize) {
    return crypto
      .createHash('sha512')
      .update('fd85b494-aaaa')
      .digest('base64')
      .substr(0, keySize);
  }

  //create random init vector
  private generateIv(ivSize) {
    return crypto
      .createHash('sha512')
      .update('smslt')
      .digest('base64')
      .substr(0, ivSize);
  }

  private gcmEncrypt(plainText, secretKey, secretIv) {
    try {
      const cipher = forge.cipher.createCipher('AES-GCM', secretKey);
      cipher.start({
        iv: secretIv,
        tagLength: 128,
      });
      cipher.update(forge.util.createBuffer(plainText, 'utf8'));
      const result = cipher.finish();
      if (!result) {
        throw new Error("Couldn't Encrypt Body");
      }
      const data = cipher.output.data;
      const tag = cipher.mode.tag.data;
      return forge.util.encode64(data + tag);
    } catch (error) {
      throw new Error('GCM Body Encryption Failed');
    }
  }

  private rsaEncrypt(plainText, publicKeyFile) {
    try {
      const publicObj = forge.pki.publicKeyFromPem(
        fs.readFileSync(publicKeyFile, 'utf8'),
      );
      const bytes = publicObj.encrypt(plainText);
      return forge.util.encode64(bytes);
    } catch (error) {
      throw new Error('RSA Encryption Failed');
    }
  }

  private signData(data, privateKey) {
    try {
      const sign = crypto.createSign('RSA-SHA1');
      sign.update(Buffer.from(data));
      sign.end();
      return sign.sign(privateKey, 'base64');
    } catch (error) {
      throw new Error('Failed to Sign Data While Encryption');
    }
  }

  private verifyData(data, signature, publicKey) {
    try {
      const verify = crypto.createVerify('RSA-SHA1');
      verify.update(data);
      verify.end();
      return verify.verify(publicKey, signature, 'base64');
    } catch (error) {
      throw new Error('Unable to Verify Data While Decryption.');
    }
  }

  decryptResponse(hash, iv, secretKey, body, callbacktype?) {
    const ourPrivateKey = CRYPT_PATH.authPrivateKey;
    // Decrypting Unique Symmetric Key By Our Public Key
    let decryptKey = this.rsaDecrypt(secretKey, ourPrivateKey);

    // Decrypting Plain Body With Unique Symmetric Key and Initialization Vectors(IV)
    let encryptBody = this.gcmDecrypt(body, decryptKey, iv);

    // Decrypting Callback Type For Callback Responses
    let decCallbacktype;
    if (callbacktype)
      decCallbacktype = this.rsaDecrypt(callbacktype, ourPrivateKey);

    // Verifying Data Using Yes Bank's Public Key
    let result = this.verifyData(
      encryptBody,
      hash,
      fs.readFileSync(CRYPT_PATH.yesBankPublicKey, 'utf8'),
    );

    return {
      result: result,
      decryptedBody: encryptBody,
      decCallbacktype,
    };
  }

  private gcmDecrypt(cipherText, secretKey, secretIv) {
    try {
      const data = forge.util.decode64(cipherText);
      const encryptData = data.slice(0, data.length - 16);
      const tag = data.slice(data.length - 16, data.length);
      const decipher = forge.cipher.createDecipher('AES-GCM', secretKey);
      decipher.start({
        iv: secretIv,
        tag: tag, // Authentication Tag From Encryption
      });
      decipher.update(forge.util.createBuffer(encryptData));
      const result = decipher.finish();
      if (!result) {
        throw new Error("Couldn't Decrypt Body");
      }
      const bytes = decipher.output.getBytes();
      return forge.util.decodeUtf8(bytes);
    } catch (error) {
      throw new Error('GCM Body Decryption Failed');
    }
  }

  private rsaDecrypt(cipherText, privateKeyFile) {
    try {
      const privateObj = forge.pki.privateKeyFromPem(
        fs.readFileSync(privateKeyFile, 'utf8'),
      );
      const bytes = forge.util.decode64(cipherText);
      return privateObj.decrypt(bytes);
    } catch (error) {
      throw new Error('RSA Decryption Failed');
    }
  }
}
