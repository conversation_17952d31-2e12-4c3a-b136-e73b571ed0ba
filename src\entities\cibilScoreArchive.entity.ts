// Imports
import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
} from 'sequelize-typescript';
import { loanTransaction } from './loan.entity';
import { registeredUsers } from './user.entity';

@Table({})
export class CibilScoreArchive extends Model<CibilScoreArchive> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ForeignKey(() => loanTransaction)
  @Column({ type: DataType.INTEGER,  allowNull: true })
  loanId: number;

  @ForeignKey(() => registeredUsers)
  @Column({ type: DataType.UUID, allowNull: false })
  userId: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  requestdata: string;
}