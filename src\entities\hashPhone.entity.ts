// Imports
import {
  Table,
  Model,
  Column,
  DataType,
  ForeignKey,
  HasOne,
} from 'sequelize-typescript';
import { registeredUsers } from './user.entity';

@Table({})
export class HashPhoneEntity extends Model<HashPhoneEntity> {
  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
    primaryKey: true,
  })
  hashPhone: string;

  @ForeignKey(() => registeredUsers)
  @Column({
    type: DataType.UUID,
    allowNull: false,
    unique: true,
  })
  userId: string;

  @HasOne(() => registeredUsers, {
    sourceKey: 'userId',
    foreignKey: 'id',
  })
  userData: registeredUsers;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
  })
  isWhatsApp: boolean;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  lastWhatsAppCheckDate: Date;
}
