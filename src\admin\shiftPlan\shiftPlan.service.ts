import { Injectable } from '@nestjs/common';
import { Op } from 'sequelize';
import {
  CREDIT_ANALYST_ROLE,
  CSE_ROLE_ID,
  PAGE_LIMIT,
} from 'src/constants/globals';
import { k500Error } from 'src/constants/misc';
import { k422ErrorMessage, kParamMissing } from 'src/constants/responses';
import { admin } from 'src/entities/admin.entity';
import { ChangeLogsEntity } from 'src/entities/change.logs.entity';
import { shiftPlanEntity } from 'src/entities/shiftPlan.entity';
import { RedisService } from 'src/redis/redis.service';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { CAAssignmentService } from 'src/shared/assignment/caAssignCase.service';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { DateService } from 'src/utils/date.service';

@Injectable()
export class shiftPlanService {
  constructor(
    private readonly repoManager: RepositoryManager,
    private readonly commonSharedService: CommonSharedService,
    private readonly redisService: RedisService,
    private readonly dateService: DateService,
    private readonly caAssignmentService: CAAssignmentService,
  ) {}

  async createShift(reqData) {
    const roleId = reqData?.roleId;
    const startTime = reqData?.startTime;
    const endTime = reqData?.endTime;
    const employees = reqData?.employees;
    const updatedBy = reqData?.updatedBy;
    const isActive = reqData?.isActive;

    if (!roleId) return kParamMissing('roleId');
    if (!startTime) return kParamMissing('startTime');
    if (!endTime) return kParamMissing('endTime');
    if (!employees) return kParamMissing('employees');
    if (!updatedBy) return kParamMissing('updatedBy');
    if (!isActive) return kParamMissing('isActive');

    const data = {
      roleId,
      startTime,
      endTime,
      employees,
      updatedBy,
      isActive,
    };

    const result = await this.repoManager.createRowData(shiftPlanEntity, data);
    if (result == k500Error) throw new Error();

    const logsData = {
      type: 'Shift plan',
      subType: 'Create shift plan',
      oldData: '',
      newData: JSON.stringify(result),
      adminId: updatedBy,
    };
    const createdData = await this.repoManager.createRowData(
      ChangeLogsEntity,
      logsData,
    );
    if (createdData === k500Error) throw new Error();
    return true;
  }

  async getShiftList(reqData) {
    const page = +reqData?.page || 1;
    const filter = +reqData?.roleId;
    let searchWhere: any = {};

    if (filter) searchWhere.roleId = filter;

    const attributes = [
      'roleId',
      'startTime',
      'endTime',
      'employees',
      'updatedBy',
      'updatedAt',
      'id',
      'isActive',
    ];
    const options: any = {
      where: searchWhere,
      order: [['id', 'DESC']],
    };

    const offset = page * PAGE_LIMIT - PAGE_LIMIT;
    options.offset = offset;
    options.limit = PAGE_LIMIT;

    const shiftDetails = await this.repoManager.getTableCountWhereData(
      shiftPlanEntity,
      attributes,
      options,
    );
    if (!shiftDetails || shiftDetails === k500Error) throw new Error();

    const caseCountDetailsForCSE = [];
    let cseRedisData = await this.redisService.get(`SHIFTPLAN_${CSE_ROLE_ID}`);
    if (cseRedisData) cseRedisData = JSON.parse(cseRedisData);
    else cseRedisData = [];
    cseRedisData.forEach((shiftData) => {
      const shiftPlan = {
        id: shiftData.id,
        employees: shiftData.employees.map((emp) => ({
          id: emp.id,
          caseCount: emp.caseCount,
        })),
      };
      caseCountDetailsForCSE.push(shiftPlan);
    });

    //caseCount and allCaActiveShiftData data
    const caseCountDetailsForCreditAnalysis =
      await this.caAssignmentService.getCAAdminWiseCaseCountData();

    const allCaActiveShiftData =
      await this.caAssignmentService.getActiveShiftOfCA();

    const finalData = [];
    for (let index = 0; index < shiftDetails.rows.length; index++) {
      try {
        let shiftPlan = shiftDetails.rows[index];

        //get shiftPlan.employees from redis if not then use real data
        //for cse
        if (shiftPlan.roleId == CSE_ROLE_ID) {
          shiftPlan.employees =
            caseCountDetailsForCSE
              ?.find((ele) => ele.id == shiftPlan.id)
              ?.employees?.map((ele) => ele.id) ?? shiftPlan.employees;
        }
        //for credit analyst
        else if (shiftPlan.roleId == CREDIT_ANALYST_ROLE) {
          shiftPlan.employees =
            allCaActiveShiftData?.find((ele) => ele.id == shiftPlan.id)
              ?.employees ?? shiftPlan.employees;
        }
        const employees = [];
        for (const employee of shiftPlan.employees) {
          const adminData = await this.commonSharedService.getAdminData(
            employee,
          );

          let caseCount: any = { total: 0 };
          if (shiftPlan.roleId == CSE_ROLE_ID && shiftPlan.isActive == 1) {
            const shiftCaseCount = caseCountDetailsForCSE.find(
              (shift) => shift.id === shiftPlan.id,
            );
            caseCount.total =
              shiftCaseCount?.employees.find((emp) => emp.id === employee)
                ?.caseCount ?? 0;
          } else if (
            shiftPlan.roleId == CREDIT_ANALYST_ROLE &&
            shiftPlan.isActive == 1
          ) {
            const caCaseCountFromRedis =
              caseCountDetailsForCreditAnalysis?.find(
                (emp) => emp.adminId === employee,
              )?.caseCount;
            caseCount = {
              total: caCaseCountFromRedis?.total ?? 0,
              employment: caCaseCountFromRedis?.employment ?? 0,
              bank: caCaseCountFromRedis?.bank ?? 0,
              selfie: caCaseCountFromRedis?.selfie ?? 0,
              kyc: caCaseCountFromRedis?.kyc ?? 0,
              final: caCaseCountFromRedis?.final ?? 0,
            };
          }

          if (shiftPlan.isActive == 0) {
            if (shiftPlan.roleId == CSE_ROLE_ID) caseCount = { total: 0 };
            else if (shiftPlan.roleId == CREDIT_ANALYST_ROLE)
              caseCount = {
                total: 0,
                employment: 0,
                bank: 0,
                selfie: 0,
                kyc: 0,
                final: 0,
              };
          }
          employees.push({
            id: adminData?.id ?? '-',
            fullName: adminData?.fullName ?? '-',
            caseCount,
          });
        }
        const obj = {
          Id: shiftPlan?.id,
          roleId: shiftPlan?.roleId,
          Role: shiftPlan?.roleId == CSE_ROLE_ID ? 'CSE' : 'CREDIT ANALYSTS ',
          Timing:
            (await this.dateService.formatTime(shiftPlan?.startTime)) +
            ' to ' +
            (await this.dateService.formatTime(shiftPlan?.endTime)),
          Employees: employees,
          'Last updated by':
            (await this.commonSharedService.getAdminData(shiftPlan?.updatedBy))
              ?.fullName ?? '-',
          'Last updated':
            (await this.dateService.formatDate(shiftPlan?.updatedAt)) ?? '-',
          isActive: shiftPlan?.isActive == '1' ? true : false,
        };
        finalData.push(obj);
      } catch (error) {}
    }
    shiftDetails.rows = finalData;
    return shiftDetails;
  }

  async updateShift(reqData) {
    const roleId = reqData?.roleId;
    const startTime = reqData?.startTime;
    const endTime = reqData?.endTime;
    const employees = reqData?.employees;
    const shiftId = reqData?.shiftId;
    const updatedBy = reqData?.updatedBy;
    const isActive = reqData?.isActive;

    if (!shiftId) return kParamMissing('shiftId');
    if (!updatedBy) return kParamMissing('updatedBy');
    if (!roleId) return kParamMissing('roleId');

    const shiftData = await this.repoManager.getRowWhereData(
      shiftPlanEntity,
      ['id', 'employees'],
      { where: { id: shiftId } },
    );
    if (!shiftData || shiftData === k500Error) throw new Error();

    if (employees || isActive == 1) {
      const isEmployeeExistInOtherShift =
        await this.repoManager.getRowWhereData(shiftPlanEntity, ['id'], {
          where: {
            employees: { [Op.overlap]: employees ?? shiftData.employees },
            isActive: '1',
            roleId,
            id: { [Op.ne]: shiftId },
          },
        });

      if (isEmployeeExistInOtherShift == k500Error) throw new Error();
      if (isEmployeeExistInOtherShift)
        return k422ErrorMessage(
          'any of the selected employees already exist in another shift, remove them first',
        );
    }

    const updateData = {
      roleId,
      startTime,
      endTime,
      employees,
      updatedBy,
      isActive,
    };

    const updatedDetails = await this.repoManager.updateRowData(
      shiftPlanEntity,
      updateData,
      shiftId,
    );

    if (updatedDetails === k500Error) throw new Error();

    const logsData = {
      type: 'Shift plan',
      subType: 'Update shift plan',
      oldData: JSON.stringify(shiftData),
      newData: JSON.stringify(updateData),
      adminId: updatedBy,
    };
    const createdData = await this.repoManager.createRowData(
      ChangeLogsEntity,
      logsData,
    );
    if (createdData === k500Error) throw new Error();

    return true;
  }

  // async deleteShift(reqData) {
  //   const shiftId = reqData?.shiftId;
  //   if (!shiftId) return kParamMissing('shiftId');
  //   const updatedBy = reqData?.updatedBy;

  //   const shiftPlan = await this.repoManager.getRowWhereData(
  //     shiftPlanEntity,
  //     ['id'],
  //     {
  //       where: {
  //         id: shiftId,
  //       },
  //     },
  //   );

  //   if (!shiftPlan || shiftPlan === k500Error) throw new Error();

  //   const deleteShift = await this.repoManager.deleteWhereData(
  //     shiftPlanEntity,
  //     {
  //       where: {
  //         id: shiftPlan?.id,
  //       },
  //     },
  //   );

  //   if (deleteShift == k500Error) throw new Error();
  //   const logsData = {
  //     userId: '',
  //     type: 'Delete shift plan',
  //     loanId: '',
  //     oldData: JSON.stringify(shiftPlan),
  //     newData: '',
  //     adminId: updatedBy
  //   };
  //   const createdData = await this.repoManager.createRowData(
  //     ChangeLogsEntity,
  //     logsData,
  //   );
  //   if (createdData === k500Error) throw new Error();
  //   return true;
  // }

  async employeesByRoleId(reqData) {
    const roleId = reqData?.roleId;
    if (!roleId) return kParamMissing('roleId');

    const employeesData = await this.repoManager.getTableWhereData(
      admin,
      ['id', 'fullName'],
      {
        where: {
          roleId,
          isActive: '1',
        },
      },
    );

    if (!employeesData || employeesData === k500Error) throw new Error();
    if (employeesData.length == 0)
      return k422ErrorMessage('No employees found.');

    const attr = ['employees'];
    const options = {
      where: {
        roleId,
        isActive: '1',
      },
    };
    const shiftData = await this.repoManager.getTableWhereData(
      shiftPlanEntity,
      attr,
      options,
    );
    const shiftEmployeeIds = shiftData.flatMap((shift) => shift.employees);
    const employeesList = employeesData.map((emp) => ({
      ...emp,
      isAssigned: shiftEmployeeIds.includes(emp.id),
    }));

    return employeesList;
  }
}
