// Imports
import { Table, Column, Model, DataType } from 'sequelize-typescript';

@Table({})
export class userSmsEntity extends Model<userSmsEntity> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({ type: DataType.STRING })
  sender: string;

  @Column({ type: DataType.TEXT })
  deviceId: string;

  @Column({ type: DataType.UUID })
  userId: string;

  @Column({ type: DataType.TEXT })
  message: string;

  @Column({ allowNull: true, type: DataType.STRING(32) })
  tag: string;

  @Column({ allowNull: true, type: DataType.STRING(64) })
  key_word: string;

  @Column({ allowNull: true, type: DataType.STRING(64) })
  supporting_key_word: string;

  @Column({ type: DataType.STRING })
  date: string;

  @Column({ type: DataType.STRING })
  messageType: string;
}
