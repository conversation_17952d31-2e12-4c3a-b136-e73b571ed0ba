// Imports
import {
  Body,
  Controller,
  Post,
  Res,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { DevOpsGuard } from 'src/authentication/auth.guard';
import { kUploadFileObj } from 'src/constants/objects';
import {
  k422ErrorMessage,
  kInternalError,
  kSuccessData,
} from 'src/constants/responses';
import { ExperianSharedService } from 'src/shared/experian.service';
import { FileService } from 'src/utils/file.service';
import { ExperianThirdParty } from './experian.service';

@Controller('experian')
export class ExperianController {
  constructor(
    private readonly fileService: FileService,
    private readonly sharedService: ExperianSharedService,
    private readonly service: ExperianThirdParty,
  ) {}

  @Post('getExperianDetails')
  async funGetExperianDetails(@Body() body, @Res() res) {
    try {
      const data: any = await this.sharedService.getExperianDetails(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @UseInterceptors(FileInterceptor('file', kUploadFileObj()))
  @Post('bulkExperianData')
  async funBulkExperianData(@UploadedFile() file, @Res() res) {
    try {
      if (!file) return res.json(k422ErrorMessage('Invalid Details'));
      const path = file.filename;
      const excelToArray: any = await this.fileService.excelToArray(path);
      console.log({ excelToArray });

      if (excelToArray?.message) throw new Error();
      await this.fileService.removeFile(path);
      const results: any = [];
      if (excelToArray) {
        for (let index = 0; index < excelToArray.length; index++) {
          const element = excelToArray[index];
          if (element['Member Reference Number'] || element.userId) {
            const data: any = await this.sharedService.getExperianDetails(
              element,
            );
            results.push(data);
          }
        }
        return res.json({ ...kSuccessData, results });
      }
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @UseGuards(DevOpsGuard)
  @Post('updateExperianMockResponse')
  async funUpdateExperianMockResponse(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.updateExperianMockResponse(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }
}
