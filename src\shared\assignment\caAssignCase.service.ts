import { Injectable } from '@nestjs/common';
import { Op } from 'sequelize';
import { CREDIT_ANALYST_ROLE, SYSTEM_ADMIN_ID } from 'src/constants/globals';
import { k500Error } from 'src/constants/misc';
import {
  kCARedisKeys,
  kVerificationAccessStatus,
  UserStage,
} from 'src/constants/objects';
import { kInternalError } from 'src/constants/responses';
import { kCAAssigneeSwap } from 'src/constants/strings';
import { shiftPlanEntity } from 'src/entities/shiftPlan.entity';
import { RedisService } from 'src/redis/redis.service';
import { AdminRepository } from 'src/repositories/admin.repository';
import { BankingRepository } from 'src/repositories/banking.repository';
import { LoanRepository } from 'src/repositories/loan.repository';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { UserRepository } from 'src/repositories/user.repository';

@Injectable()
export class CAAssignmentService {
  constructor(
    private readonly redisService: RedisService,

    ////Repository
    private readonly adminRepo: AdminRepository,
    private readonly loanRepo: LoanRepository,
    private readonly bankingRepo: BankingRepository,
    private readonly userRepo: UserRepository,
    private readonly repoManager: RepositoryManager,
  ) {}

  async getCAData(reqData: any = {}) {
    const isRefresh = reqData?.isRefresh?.toString() == 'true' ? true : false;
    const redisKey = kCARedisKeys.adminList;
    if (!isRefresh) {
      const redisData = await this.redisService.get(redisKey);
      if (redisData) return JSON.parse(redisData);
      else return [];
    }
    const adminData = await this.adminRepo.getTableWhereData(
      ['id', 'fullName', 'isLogin', 'verificationAccessStatus'],
      {
        where: {
          roleId: CREDIT_ANALYST_ROLE,
          isActive: '1',
        },
        order: [['id', 'ASC']],
      },
    );
    if (adminData == k500Error) return kInternalError;
    await this.redisService.set(redisKey, JSON.stringify(adminData));
    return adminData;
  }

  async getActiveShiftOfCA(reqData: any = {}) {
    const isRefresh = reqData?.isRefresh?.toString() == 'true' ? true : false;
    const redisKey = kCARedisKeys.activeShiftList;
    if (!isRefresh) {
      const redisData = await this.redisService.get(redisKey);
      if (redisData) return JSON.parse(redisData);
      else return [];
    }
    const activeShiftData = await this.repoManager.getTableWhereData(
      shiftPlanEntity,
      ['id', 'startTime', 'endTime', 'employees'],
      {
        where: {
          isActive: '1',
          roleId: CREDIT_ANALYST_ROLE,
        },
      },
    );
    if (activeShiftData == k500Error) return kInternalError;
    await this.redisService.set(redisKey, JSON.stringify(activeShiftData));
    return activeShiftData;
  }

  async getActiveRunningShiftOfCA(reqData: any = {}) {
    const isRefresh = reqData?.isRefresh?.toString() == 'true' ? true : false;
    const redisKey = kCARedisKeys.activeRunningShiftList;
    let redisData;
    if (!isRefresh) {
      redisData = await this.redisService.get(redisKey);
      if (redisData) return JSON.parse(redisData);
      else return [];
    }
    const activeShiftData = await this.getActiveShiftOfCA();
    if (activeShiftData?.message) return activeShiftData;
    const activeRunningShift = [];

    const rightNowTime = new Date().toTimeString().split(' ')[0];
    for (let i = 0; i < activeShiftData.length; i++) {
      const shift = activeShiftData[i];
      const shiftStartTime = shift?.startTime;
      const shiftEndTime = shift?.endTime;

      if (shiftEndTime > shiftStartTime) {
        if (rightNowTime >= shiftStartTime && rightNowTime <= shiftEndTime) {
          activeRunningShift.push(shift);
        }
      } else {
        if (rightNowTime >= shiftStartTime || rightNowTime <= shiftEndTime) {
          activeRunningShift.push(shift);
        }
      }
    }
    await this.redisService.set(redisKey, JSON.stringify(activeRunningShift));
    return activeRunningShift;
  }

  async getRunningShiftCAVerificationAccessWiseData(reqData: any = {}) {
    const isRefresh = reqData?.isRefresh?.toString() == 'true' ? true : false;
    const redisKey = kCARedisKeys.verificationAccessWiseAdminData;
    let redisData;
    if (!isRefresh) {
      redisData = await this.redisService.get(redisKey);
      if (redisData) return JSON.parse(redisData);
      else return {};
    }
    const allActiveRunningShiftData = await this.getActiveRunningShiftOfCA();
    if (allActiveRunningShiftData?.message) return allActiveRunningShiftData;
    const activeRunningShiftAdminIds = [];
    allActiveRunningShiftData.forEach((ele) => {
      activeRunningShiftAdminIds.push(...ele.employees);
    });
    activeRunningShiftAdminIds.sort((a, b) => a - b);

    const allAdminData: any = await this.getCAData();
    if (allAdminData?.message) return allAdminData;

    const stepWiseAdminData = {
      [kVerificationAccessStatus.employment]: [],
      [kVerificationAccessStatus.bank]: [],
      [kVerificationAccessStatus.selfie]: [],
      [kVerificationAccessStatus.kyc]: [],
      [kVerificationAccessStatus.final]: [],
    };

    activeRunningShiftAdminIds.forEach((adminId) => {
      const adminData = allAdminData.find((el) => el.id == adminId);
      const status = adminData?.verificationAccessStatus;
      if (status) {
        if (status?.employment) stepWiseAdminData.employment.push(adminId);
        if (status?.bank) stepWiseAdminData.bank.push(adminId);
        if (status?.selfie) stepWiseAdminData.selfie.push(adminId);
        if (status?.kyc) stepWiseAdminData.kyc.push(adminId);
        if (status?.final) stepWiseAdminData.final.push(adminId);
      }
    });
    await this.redisService.set(redisKey, JSON.stringify(stepWiseAdminData));
    return stepWiseAdminData;
  }

  async getCAAdminWiseCaseCountData(reqData: any = {}) {
    const isRefresh = reqData?.isRefresh?.toString() == 'true' ? true : false;
    const redisKey = kCARedisKeys.adminWiseCaseCountData;
    let redisData: any = await this.redisService.get(redisKey);
    if (redisData) redisData = JSON.parse(redisData);
    else redisData = [];
    if (!isRefresh) return redisData;

    const allAdminData: any = await this.getCAData();
    if (allAdminData?.message) return allAdminData;
    const caseCountData = [];
    for (let i = 0; i < allAdminData.length; i++) {
      const adminId = allAdminData[i]?.id;
      const oldCountData = redisData?.find((ele) => ele.adminId == adminId);
      if (oldCountData) caseCountData.push(oldCountData);
      else {
        caseCountData.push({
          adminId,
          caseCount: {
            total: 0,
            [kVerificationAccessStatus.employment]: 0,
            [kVerificationAccessStatus.bank]: 0,
            [kVerificationAccessStatus.selfie]: 0,
            [kVerificationAccessStatus.kyc]: 0,
            [kVerificationAccessStatus.final]: 0,
          },
        });
      }
    }
    await this.redisService.set(redisKey, JSON.stringify(caseCountData));
    return caseCountData;
  }

  async getAssignee(reqData) {
    const { step, oldAssignee, bankApprovedBy, assigneeIds } = reqData;
    let assignAdminId = SYSTEM_ADMIN_ID;
    const caseCountData: any = await this.getCAAdminWiseCaseCountData();
    if (caseCountData?.message) return assignAdminId;

    const stepWiseAdminData =
      await this.getRunningShiftCAVerificationAccessWiseData();
    if (stepWiseAdminData?.message) return assignAdminId;

    assignAdminId = this.getMinCaseCountAdmin({
      step,
      caseCountData,
      stepWiseAdminData,
      bankApprovedBy,
      oldAssignee,
      assigneeIds,
    });
    if (assignAdminId == SYSTEM_ADMIN_ID) return assignAdminId;
    else {
      const assignAdminCaseData = caseCountData.find(
        (ele) => ele.adminId == assignAdminId,
      );
      if (!assignAdminCaseData) return SYSTEM_ADMIN_ID;
      assignAdminCaseData.caseCount[step]++;
      assignAdminCaseData.caseCount['total']++;
      const redisKey = kCARedisKeys.adminWiseCaseCountData;
      await this.redisService.set(redisKey, JSON.stringify(caseCountData));
    }
    return assignAdminId;
  }

  getMinCaseCountAdmin(reqData) {
    const {
      step,
      caseCountData,
      stepWiseAdminData,
      bankApprovedBy,
      oldAssignee,
    } = reqData;
    // assigneeIds length more than 1 is more manual reshuffle and otherwise length is 0
    let { assigneeIds } = reqData;
    let minCount = Infinity;
    let secondMinCount = Infinity;
    let minCountAdminId = SYSTEM_ADMIN_ID;
    let secondMinCountAdminId = SYSTEM_ADMIN_ID;

    let adminIds = stepWiseAdminData[step] ?? [];
    //not any admin in shift at given step and case is not for manualAssignee
    if (adminIds?.length == 0 && assigneeIds?.length == 0)
      return SYSTEM_ADMIN_ID;

    // logic for remain same assignee
    if (
      oldAssignee &&
      oldAssignee != SYSTEM_ADMIN_ID &&
      adminIds?.includes(+oldAssignee) &&
      assigneeIds?.length == 0
    ) {
      if (step != kVerificationAccessStatus.final) return oldAssignee;
      else if (
        step == kVerificationAccessStatus.final &&
        bankApprovedBy != oldAssignee
      )
        return oldAssignee;
    }

    //for manualAssignee logic
    if (assigneeIds?.length != 0) {
      assigneeIds = assigneeIds.map((el) => +el);
      adminIds = assigneeIds;
    }

    const adminCountData = caseCountData.filter((el) =>
      adminIds.includes(el.adminId),
    );
    for (const ele of adminCountData) {
      const count = ele?.caseCount?.[step];
      const adminId = ele?.adminId;
      if (count < minCount) {
        secondMinCount = minCount;
        minCount = count;
        secondMinCountAdminId = minCountAdminId;
        minCountAdminId = adminId;
      } else if (count < secondMinCount && count > minCount) {
        secondMinCount = count;
        secondMinCountAdminId = adminId;
      }
    }

    let assignAdminId = SYSTEM_ADMIN_ID;
    if (minCountAdminId != SYSTEM_ADMIN_ID) {
      if (step == kVerificationAccessStatus.final)
        assignAdminId =
          minCountAdminId != bankApprovedBy
            ? minCountAdminId
            : secondMinCountAdminId;
      else assignAdminId = minCountAdminId;
    }

    return assignAdminId;
  }

  async resetAllCACaseCountData() {
    const redisKey = kCARedisKeys.adminWiseCaseCountData;
    const allAdminData: any = await this.getCAData();
    if (allAdminData?.message) return allAdminData;
    const resetCountObj = {
      total: 0,
      [kVerificationAccessStatus.employment]: 0,
      [kVerificationAccessStatus.bank]: 0,
      [kVerificationAccessStatus.selfie]: 0,
      [kVerificationAccessStatus.kyc]: 0,
      [kVerificationAccessStatus.final]: 0,
    };
    const caseCountData = allAdminData.map((ele) => ({
      adminId: ele?.id,
      caseCount: resetCountObj,
    }));
    await this.redisService.set(redisKey, JSON.stringify(caseCountData));
    return true;
  }

  async resetAdminWiseCACaseCountData(reqData) {
    const adminIds = reqData?.adminIds ?? [];
    if (!adminIds?.length) return true;
    const caseCountData: any = await this.getCAAdminWiseCaseCountData();
    if (caseCountData?.message) return caseCountData;
    const resetCountObj = {
      total: 0,
      [kVerificationAccessStatus.employment]: 0,
      [kVerificationAccessStatus.bank]: 0,
      [kVerificationAccessStatus.selfie]: 0,
      [kVerificationAccessStatus.kyc]: 0,
      [kVerificationAccessStatus.final]: 0,
    };
    caseCountData.forEach((ele) => {
      if (adminIds.includes(ele.adminId)) {
        ele.caseCount = resetCountObj;
      }
    });
    const redisKey = kCARedisKeys.adminWiseCaseCountData;
    await this.redisService.set(redisKey, JSON.stringify(caseCountData));
    return true;
  }

  async syncCAShiftDataWhenShiftChange() {
    let redisActiveRunningShiftListData = await this.redisService.get(
      kCARedisKeys.activeRunningShiftList,
    );
    if (redisActiveRunningShiftListData)
      redisActiveRunningShiftListData = JSON.parse(
        redisActiveRunningShiftListData,
      );
    else redisActiveRunningShiftListData = [];
    let redisAllActiveShiftListData = await this.redisService.get(
      kCARedisKeys.activeShiftList,
    );
    if (redisAllActiveShiftListData)
      redisAllActiveShiftListData = JSON.parse(redisAllActiveShiftListData);
    else redisAllActiveShiftListData = [];
    const activeRunningShiftAdminIds = [];
    const activeRunningShiftIds = [];

    const rightNowTime = new Date().toTimeString().split(' ')[0];

    for (let i = 0; i < redisAllActiveShiftListData.length; i++) {
      const shift = redisAllActiveShiftListData[i];
      const shiftStartTime = shift?.startTime;
      const shiftEndTime = shift?.endTime;

      if (shiftEndTime > shiftStartTime) {
        if (rightNowTime >= shiftStartTime && rightNowTime <= shiftEndTime) {
          activeRunningShiftIds.push(shift.id);
          activeRunningShiftAdminIds.push(...shift.employees);
        }
      } else {
        if (rightNowTime >= shiftStartTime || rightNowTime <= shiftEndTime) {
          activeRunningShiftIds.push(shift.id);
          activeRunningShiftAdminIds.push(...shift.employees);
        }
      }
    }

    const redisActiveRunningShiftIds = redisActiveRunningShiftListData?.map(
      (ele) => ele.id,
    );
    const completedShiftData: any = redisActiveRunningShiftListData?.filter(
      (ele) => !activeRunningShiftIds.includes(ele.id),
    );
    const completedShiftAdminIds = completedShiftData
      .map((ele) => ele.employees)
      .flat();
    let needResetData = false;
    //for shift completedShift
    if (completedShiftData.length != 0) {
      needResetData = true;
      if (kCAAssigneeSwap)
        await this.assignUncheckedCaseToSystem(completedShiftAdminIds);
    }
    //for new shift start
    else if (redisActiveRunningShiftIds.length != activeRunningShiftIds.length)
      needResetData = true;

    if (needResetData) {
      const reqData = { isRefresh: true };
      await this.getActiveRunningShiftOfCA(reqData);
      await this.getRunningShiftCAVerificationAccessWiseData(reqData);
      await this.assignCaseFromSystemToAssignee();
    }
    return true;
  }

  async syncCAShiftData() {
    if (!kCAAssigneeSwap) {
      const activeShiftData = await this.getActiveShiftOfCA();
      if (activeShiftData?.message) return activeShiftData;

      const adminIds = activeShiftData?.map((ele) => ele?.employees)?.flat();
      await this.assignUncheckedCaseToSystem(adminIds);
    }
    const reqData = { isRefresh: true };
    await this.getCAData(reqData);
    await this.getActiveShiftOfCA(reqData);
    await this.resetAllCACaseCountData();
    await this.getActiveRunningShiftOfCA(reqData);
    await this.getRunningShiftCAVerificationAccessWiseData(reqData);
  }

  async assignCA(step, loanId, assigneeIds = []) {
    if (!step || !loanId) return;

    let assignTo: any;
    const stepWiseAdminData =
      await this.getRunningShiftCAVerificationAccessWiseData();
    if (stepWiseAdminData?.message) assignTo = SYSTEM_ADMIN_ID;

    let adminIds = stepWiseAdminData?.[step] ?? [];
    //not any admin in shift at given step and case is not for manualAssignee
    if (adminIds?.length == 0 && assigneeIds?.length == 0)
      assignTo = SYSTEM_ADMIN_ID;
    else {
      const loanData = await this.loanRepo.getRowWhereData(
        ['id', 'loanStatus', 'assignTo'],
        {
          where: {
            id: loanId,
            loanStatus: 'InProcess',
          },
        },
      );
      if (loanData == k500Error || !loanData) return;

      let bankData: any;
      if (step == kVerificationAccessStatus.final) {
        bankData = await this.bankingRepo.getRowWhereData(['id', 'adminId'], {
          where: {
            loanId,
          },
          order: [['id', 'DESC']],
        });
        if (bankData == k500Error || !bankData) return;
      }

      try {
        assignTo = await this.getAssignee({
          step,
          oldAssignee: loanData?.assignTo,
          bankApprovedBy: bankData?.adminId,
          assigneeIds,
        });
        if (assignTo?.message || !assignTo) assignTo = SYSTEM_ADMIN_ID;
      } catch (error) {
        assignTo = SYSTEM_ADMIN_ID;
      }
    }

    await this.loanRepo.updateRowData({ assignTo }, loanId);
  }

  async assignUncheckedCaseToSystem(adminIds?) {
    //for when shift completed at that time remain case assign to system
    const loanData: any = await this.getInProcessLoanData(adminIds);
    if (loanData?.message) return loanData;
    if (loanData?.length == 0) return true;

    const remainLoanIds = loanData.map((ele) => ele.id);
    await this.loanRepo.updateRowWhereData(
      { assignTo: SYSTEM_ADMIN_ID },
      { where: { id: remainLoanIds } },
    );
  }

  async assignCaseFromSystemToAssignee() {
    const allLoanData: any = await this.getInProcessLoanData([]);
    if (allLoanData?.message) return allLoanData;
    if (allLoanData?.length == 0) return true;

    for (let i = 0; i < allLoanData.length; i++) {
      const loanData = allLoanData[i];
      await this.assignCA(loanData?.verificationStep, loanData?.id);
    }
    return true;
  }

  async getInProcessLoanData(assigneeIds?) {
    const stepStatus = {
      [UserStage.EMPLOYMENT]: kVerificationAccessStatus.employment,
      [UserStage.BANKING]: kVerificationAccessStatus.bank,
      [UserStage.SELFIE]: kVerificationAccessStatus.selfie,
      [UserStage.PAN]: kVerificationAccessStatus.kyc,
      [UserStage.FINAL_VERIFICATION]: kVerificationAccessStatus.final,
    };

    const allUserData = await this.userRepo.getTableWhereData(['id', 'stage'], {
      where: {
        stage: Object.keys(stepStatus),
        stageStatus: 0,
      },
    });
    if (allUserData == k500Error) return kInternalError;
    if (allUserData?.length == 0) return [];

    const userIds = allUserData.map((el) => el.id);

    let loanOptionsWhere: any = {};
    if (!assigneeIds || assigneeIds?.length == 0) {
      loanOptionsWhere = {
        [Op.or]: [
          {
            assignTo: {
              [Op.is]: null,
            },
          },
          {
            assignTo: SYSTEM_ADMIN_ID,
          },
        ],
      };
    } else loanOptionsWhere = { assignTo: assigneeIds };

    loanOptionsWhere.loanStatus = 'InProcess';
    loanOptionsWhere.userId = userIds;
    const allLoanData = await this.loanRepo.getTableWhereData(
      ['id', 'userId'],
      {
        where: loanOptionsWhere,
      },
    );
    if (allLoanData == k500Error) return kInternalError;

    const finalLoanData = [];
    for (let i = 0; i < allLoanData.length; i++) {
      const loanData = allLoanData[i];
      const userData = allUserData.find((ele) => ele.id == loanData.userId);
      finalLoanData.push({
        id: loanData.id,
        verificationStep: stepStatus[userData.stage],
      });
    }
    return finalLoanData;
  }
}
