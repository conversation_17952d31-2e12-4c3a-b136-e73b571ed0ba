import { <PERSON>ongsT<PERSON>, Column, DataType, ForeignKey, Model, Table } from "sequelize-typescript";
import { registeredUsers } from "./user.entity";


@Table({})
export class fcmTokenEntity extends Model<fcmTokenEntity>{
    @Column({
        type: DataType.TEXT,
        allowNull: false,
        unique: true,
        primaryKey: true
    })
    fcmToken:string;

    @ForeignKey(() => registeredUsers)
    @Column({
      type: DataType.TEXT
    })
    userId: string;

}