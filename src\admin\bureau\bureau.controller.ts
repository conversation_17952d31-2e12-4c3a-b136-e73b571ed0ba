import { Controller, Get, Query, Res } from '@nestjs/common';
import { kInternalError, kSuccessData } from 'src/constants/responses';
import { BureauService } from './bureau.service';
import { ErrorContextService } from 'src/utils/error.context.service';

@Controller('admin/bureau')
export class BureauController {
  constructor(
    private readonly service: BureauService,
    private readonly errorContextService: ErrorContextService,
  ) {}

  @Get('getAllBureauDataUserWise')
  async getAllBureauDataUserWise(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.getAllBureauDataUserWise(query);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Get('getBureauDataIdWise')
  async getBureauDataIdWise(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.getBureauDataIdWise(query);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  //#region get bureau score data by loanId
  @Get('getBureauScoreData')
  async getBureauScoreData(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.getBureauScoreData(query);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }
}
