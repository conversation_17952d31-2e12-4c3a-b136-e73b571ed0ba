<html>
<header>
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <link rel="preconnect" href="https://fonts.googleapis.com" />
   <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
   <link href="https://fonts.googleapis.com/css2?family=Lexend&family=Lexend+Deca:wght@200&display=swap"
      rel="stylesheet" />
   <style>
      .body {
         font-family: "Lexend";
      }

      .selfieImg {
         float: right;
         margin-right: 30px;
         height: 113px;
         width: 113px;
      }
   </style>
</header>

<body style="padding: 0; margin: 0; ">
   <table style="width: 100%; overflow-y: scroll;;border-collapse: collapse;">
      <tbody>
         <tr>
            <td>
               <table style="max-width: 725px;width: 100% ;margin: auto;border-collapse: collapse;font-family: Lexend;">
                  <tbody>
                     <tr>
                        <td style="height: 80px;">
                           <div style="margin-bottom: 8px;margin-top: 18px;">
                              <img style="margin-bottom: 8px;" width="150px" height="40px" src={{nbfcLogo}} />
                        </td>
                        <td style="padding: 0 0px 0 0; width: 400px;">
                           {{#cIf isStamp}}
                           <div style="color: #fff; font-size: 14px; text-align: end;">Certificate No :
                              {{stampNumber}}
                           </div>
                           {{/cIf}}
                           <div style="font-size: 12px;text-align: end; margin: 5px 0 0 0 ;">{{nbfcAddress}}
                           </div>
                        </td>
                     </tr>
                  </tbody>
               </table>
            </td>
         </tr>
         <tr>
            <td style='background-color: #e02d42; height: 2px; margin-top: -2px'></td>
         </tr>
         <tr>
            <td>
               <div style="
                     padding: 10px 30px;
                     font-size: 10px;
                     display: flex;
                     justify-content: space-between;
                     align-items: center;
                   ">
                  <div>
                     {{#cIf showLendingPartner}}
                     <div style="
                         background-color: #ffe9ec;
                         padding: 4px 12px;
                         border-radius: 20px;
                         font-size: 12px;
                         font-weight: 500;
                         font-family: Lexend;
                         display: inline-block;
                       ">
                        Lending partner: {{appName}}
                     </div>
                     {{/cIf}}
                  </div>
                  <div>
                     <b>पेज की निरंतरता में 1</b>
                  </div>
               </div>
            </td>
         </tr>
         <tr>
            <td>
               <table
                  style="border: 1px solid #fff;border-collapse: collapse;border-spacing: 0; margin: auto;;font-family: Lexend;height: 100%;">
                  <tbody>
                     <tr>
                        <td style="padding: 2px 30px; font-size:12px">
                           <div style="font-weight: 600; padding: 10px 0 0 0">
                              <ol start="4">
                                 <li style="padding: 0 0 5px 0; letter-spacing: 0.2px; list-style:none;">
                                    <ol>
                                       <li style="padding: 0 0 2px 0; list-style:none">
                                          {{#cIf collateralCharges}}7.{{else}}6.{{/cIf}} ऋण चुकौती अनुसूची:
                                          <table style='
                                             border: 1px solid #000;
                                             border-collapse: collapse;
                                             margin: 10px 0px;
                                             width: 100%; 
                                             '>
                                             <tbody>
                                                <tr style="background-color: #D9D9D9">
                                                   <td style='
                                                      border: 1px solid #000;
                                                      border-collapse: collapse;
                                                      height: 25px;
                                                      text-align: center;
                                                      font-size: 12px;
                                                      '>
                                                      क्रमांक
                                                   </td>
                                                   <td style='
                                                      border: 1px solid #000;
                                                      border-collapse: collapse;
                                                      text-align: center;
                                                      font-size: 12px;
                                                      min-width: 75px;
                                                      '>
                                                      नियत तिथि
                                                   </td>
                                                   <td style='
                                                      border: 1px solid #000;
                                                      border-collapse: collapse;
                                                      text-align: center;
                                                      font-size: 12px;
                                                      '>
                                                      मूलधन राशि
                                                   </td>
                                                   <td style='
                                                      border: 1px solid #000;
                                                      border-collapse: collapse;
                                                      text-align: center;
                                                      font-size: 12px;
                                                      '>
                                                      ब्याज दर की राशि
                                                   </td>
                                                   <td style='
                                                      border: 1px solid #000;
                                                      border-collapse: collapse;
                                                      text-align: center;
                                                      font-size: 12px;
                                                      '>
                                                      कुल पुनर्भुगतान राशि
                                                   </td>
                                                </tr>
                                                {{#each emiData}}
                                                <tr>
                                                   <td style='
                                                      border: 1px solid #000;
                                                      border-collapse: collapse;
                                                      height: 25px;
                                                      text-align: center;
                                                      font-size: 12px;
                                                      '>
                                                      {{this.id}}
                                                   </td>
                                                   <td style='
                                                      border: 1px solid #000;
                                                      border-collapse: collapse;
                                                      text-align: center;
                                                      font-size: 12px;
                                                      '>
                                                      {{this.dueDate}}
                                                   </td>
                                                   <td style='
                                                      border: 1px solid #000;
                                                      border-collapse: collapse;
                                                      text-align: center;
                                                      font-size: 12px;
                                                      '>
                                                      {{this.principal}}
                                                   </td>
                                                   <td style='
                                                      border: 1px solid #000;
                                                      border-collapse: collapse;
                                                      text-align: center;
                                                      font-size: 12px;
                                                      '>
                                                      {{this.interest}}
                                                   </td>
                                                   <td style='
                                                      border: 1px solid #000;
                                                      border-collapse: collapse;
                                                      text-align: center;
                                                      font-size: 12px;
                                                      '>
                                                      {{this.dueAmount}}
                                                   </td>
                                                </tr>
                                                {{/each}}
                                                <tr>
                                                   <td style='
                                                      border: 1px solid #000;
                                                      border-collapse: collapse;
                                                      height: 25px;
                                                      text-align: center;
                                                      font-size: 12px;
                                                      ' colspan="2">
                                                      कुल
                                                   </td>
                                                   <td style='
                                                      border: 1px solid #000;
                                                      border-collapse: collapse;
                                                      text-align: center;
                                                      font-size: 12px;
                                                      '>
                                                      {{emiTotalPrincipal}}
                                                   </td>
                                                   <td style='
                                                      border: 1px solid #000;
                                                      border-collapse: collapse;
                                                      text-align: center;
                                                      font-size: 12px;
                                                      '>
                                                      {{emiTotalInterest}}
                                                   </td>
                                                   <td style='
                                                      border: 1px solid #000;
                                                      border-collapse: collapse;
                                                      text-align: center;
                                                      font-size: 12px;
                                                      '>
                                                      {{emiTotalAmount}}
                                                   </td>
                                                </tr>
                                             </tbody>
                                          </table>
                                          <span
                                             style="padding: 0px 30px 30px 0px;letter-spacing: 0.2px;font-size:12px;line-height: 20px; margin-top: 10px; ">
                                             *ब्याज दर (नियत दर) : <b>{{interestRateFixed}}%</b> (वार्षिक)
                                          </span>
                                       </li>
                                    </ol>
                                 </li>
                                 <li style="padding: 0 0 0 0; letter-spacing: 0.2px; font-weight: bold;">
                                    <p style="font-weight: normal;">
                                       उधारकर्ता यह पुष्टि करता/करती है और आश्वासन देता/देती है कि वह बिना पूर्व सूचना
                                       दिए अपना बैंक खाता बंद नहीं करेगा/करेगी। यदि ऐसा खाता बंद किया जाता है, तो
                                       उधारकर्ता तुरंत इसकी सूचना ऋणदाता को देगा/देगी। उपरोक्त शर्त का कोई भी उल्लंघन
                                       उधारकर्ता द्वारा एक गंभीर उल्लंघन (Material Breach) माना जाएगा।
                                    </p>
                                 </li>
                              </ol>
                           </div>
                           <div style="font-weight: 600; padding: 5px 0 0 0">
                              <div style="
                          font-weight: 700;
                          padding-left: 25px;
                          margin-bottom: 5px;
                        ">
                                 <span style="margin-right: 4px">6.</span>
                                 ऋणदाता के अधिकार, शक्तियाँ और उपचार:
                              </div>
                              <div style="font-weight: 400; padding: 0 0 5px 40px">
                                 <div style="float: left; width: 10px; margin-left: 20px">
                                    1.
                                 </div>
                                 <div style="padding-inline-start: 40px">
                                    ऋणदाता को उधारकर्ता की किसी भी देनदारी के संबंध में लियन (Lien) या समायोजन (Set-off)
                                    का अधिकार प्राप्त होगा, ठीक उसी प्रकार जैसे कि वह देनदारी बिना किसी सुरक्षा के हो।
                                    ऋणदाता को उधारकर्ता के खाते में उपलब्ध किसी भी संपत्ति/क्रेडिट बैलेंस या सुरक्षाओं
                                    (Securities) पर लियन रखने का अधिकार होगा, चाहे वे ऋणदाता के पास सुरक्षा हेतु या किसी
                                    अन्य उद्देश्य से रखी गई हों।
                                 </div>
                              </div>
                              <div style="font-weight: 400; padding: 0 0 5px 40px">
                                 <div style="float: left; width: 10px; margin-left: 20px">
                                    2.
                                 </div>
                                 <div style="padding-inline-start: 40px">
                                    यदि ऋणदाता द्वारा मांग किए जाने पर निर्धारित समय सीमा के भीतर बकाया ऋण राशि का
                                    भुगतान नहीं किया जाता है, तो उधारकर्ता के किसी भी खाते में उपलब्ध क्रेडिट बैलेंस को
                                    ऋण खाते के बकाया देय राशि के समायोजन हेतु उपयोग किया जा सकता है।
                                 </div>
                              </div>
                              <div style="font-weight: 400; padding: 0 0 5px 40px">
                                 <div style="float: left; width: 10px; margin-left: 20px">
                                    3.
                                 </div>
                                 <div style="padding-inline-start: 40px">
                                    किसी भी घाटे (Deficit) की स्थिति में, ऋणदाता द्वारा वह घाटे की राशि उधारकर्ता से
                                    वसूल की जा सकती है।
                                 </div>
                              </div>
                              <div style="font-weight: 400; padding: 0 0 5px 40px">
                                 <div style="float: left; width: 10px; margin-left: 20px">
                                    4.
                                 </div>
                                 <div style="padding-inline-start: 40px">
                                    ऋणदाता द्वारा किसी अधिकार, शक्ति या उपचार का प्रयोग करने में किसी भी प्रकार की रोक,
                                    विफलता या देरी को उस अधिकार, शक्ति या उपचार का त्याग नहीं माना जाएगा और किसी भी एकल
                                    या आंशिक अधिकार, शक्ति या उपचार का प्रयोग आगे के प्रयोग को रोक नहीं सकेगा। ऋणदाता का
                                    प्रत्येक अधिकार और उपचार तब तक पूर्ण रूप से प्रभावी रहेगा जब तक कि उस अधिकार, शक्ति
                                    या उपचार को ऋणदाता द्वारा लिखित रूप में विशेष रूप से त्याग नहीं किया जाता।
                                 </div>
                              </div>
                              <div style="font-weight: 400; padding: 0 0 5px 40px">
                                 <div style="float: left; width: 10px; margin-left: 20px">
                                    5.
                                 </div>
                                 <div style="padding-inline-start: 40px">
                                    ऋणदाता के अधिकार और उपचार संयुक्त होंगे और उन्हें ऋणदाता की एकल विवेकाधिकार पर
                                    अलग-अलग, लगातार या एक साथ किया जा सकता है।
                                 </div>
                              </div>
                              <div style="font-weight: 400; padding: 0 0 5px 40px">
                                 <div style="float: left; width: 10px; margin-left: 20px">
                                    6.
                                 </div>
                                 <div style="padding-inline-start: 40px">
                                    यदि आप निर्धारित समय में ऋण किस्तों का भुगतान नहीं करते हैं, तो ऋणदाता आपको आपकी
                                    पंजीकृत मोबाइल संख्या पर किस्त भुगतान के लिए सूचित करेगा। हालांकि, यदि आपके नंबर से
                                    कोई प्रतिक्रिया नहीं मिलती है, तो ऋणदाता केवल समय पर भुगतान के लिए आपसे संपर्क
                                    करेगा।
                                 </div>
                              </div>
                           </div>
                        </td>
                     </tr>
                  </tbody>
               </table>
            </td>
         </tr>
      </tbody>
   </table>
   <div style="font-size: 10px; bottom: 5px; position: fixed; right: 0; padding: 10px 30px 5px 0px; text-align: right;">
      <b>पृष्ठ 2</b>
   </div>
</body>