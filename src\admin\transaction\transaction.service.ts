// Imports
import { Injectable } from '@nestjs/common';
import { Op, Sequelize } from 'sequelize';
import { PAGE_LIMIT, SYSTEM_ADMIN_ID } from 'src/constants/globals';
import { k500Error } from 'src/constants/misc';
import * as fs from 'fs';
import {
  k422ErrorMessage,
  kInternalError,
  kInvalidParamValue,
  kParamMissing,
  kParamsMissing,
} from 'src/constants/responses';
import {
  kAutoDebit,
  kCC,
  kCashfree,
  kCompleted,
  kDirectBankPay,
  kFailed,
  kRazorpay,
  kSigndesk,
  kSplitRefundable,
  kTechSupportMail,
} from 'src/constants/strings';
import { employmentDesignation } from 'src/entities/designation.entity';
import { disbursementEntity } from 'src/entities/disbursement.entity';
import { EmiEntity } from 'src/entities/emi.entity';
import { employmentDetails } from 'src/entities/employment.entity';
import { loanTransaction } from 'src/entities/loan.entity';
import { loanPurpose } from 'src/entities/loan.purpose.entity';
import { mandateEntity } from 'src/entities/mandate.entity';
import { SubScriptionEntity } from 'src/entities/subscription.entity';
import { registeredUsers } from 'src/entities/user.entity';
import { SequelOptions } from 'src/interfaces/include.options';
import { LoanRepository } from 'src/repositories/loan.repository';
import { TransactionRepository } from 'src/repositories/transaction.repository';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { CalculationSharedService } from 'src/shared/calculation.service';
import { CryptService } from 'src/utils/crypt.service';
import { TypeService } from 'src/utils/type.service';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { TransactionEntity } from 'src/entities/transaction.entity';
import { DateService } from 'src/utils/date.service';
import { UserRepository } from 'src/repositories/user.repository';
import { SharedNotificationService } from 'src/shared/services/notification.service';
import {
  kGetCashfreePayment,
  kGetRazorpayPayment,
  kGetSigndeskPayment,
  nObjectToExcel,
} from 'src/constants/network';
import { EMIRepository } from 'src/repositories/emi.repository';
import { kUnclosedLoanEmi } from 'src/constants/directories';
import { EnvConfig } from 'src/configs/env.config';
import { SharedTransactionService } from 'src/shared/transaction.service';
import { KfiscalYearMonths, kMonths } from 'src/constants/objects';
import { RedisService } from 'src/redis/redis.service';
import { SlackService } from 'src/thirdParty/slack/slack.service';
import { FileService } from 'src/utils/file.service';
import { ReportHistoryRepository } from 'src/repositories/reportHistory.repository';
import { ErrorContextService } from 'src/utils/error.context.service';
import { APIService } from 'src/utils/api.service';
@Injectable()
export class TransactionService {
  constructor(
    private readonly calculation: CalculationSharedService,
    private readonly repository: TransactionRepository,
    private readonly commonShared: CommonSharedService,
    private readonly typeService: TypeService,
    private readonly cryptService: CryptService,
    // Repositories
    private readonly loanRepo: LoanRepository,
    private readonly userRepo: UserRepository,
    private readonly emiRepo: EMIRepository,
    private readonly reportHistoryRepo: ReportHistoryRepository,
    private readonly repoManager: RepositoryManager,
    // Utils
    private readonly dateService: DateService,
    private readonly sharedNotification: SharedNotificationService,
    private readonly sharedTransaction: SharedTransactionService,
    private readonly fileService: FileService,
    private readonly slackService: SlackService,
    private readonly redisService: RedisService,
    private readonly errorContextService: ErrorContextService,
    private readonly apiService: APIService,
  ) {}

  //#region get repaid data
  async getAllRepaidLoans(query, downloadBy) {
    query.start_date = this.typeService.getGlobalDate(query.start_date);
    query.end_date = this.typeService.getGlobalDate(query.end_date);
    const maskOptions = await this.commonShared.findMaskRole(downloadBy);
    const options = await this.prePareRepaidOptions(query);
    if (options?.message) return options;
    const result = await this.findRepaidLoanData(options);
    if (result?.message) return result;
    const rows: any = await this.prePareAllRepaidLoans(
      result.rows,
      maskOptions,
    );
    if (rows?.message) return result;
    const total: any = await this.getAllRepaidAmount(query);
    if (total?.message) return result;
    if (query?.download == 'true' && query?.allData == 'true')
      return { count: result.count, rows, total };
    // Download -> Report
    if (query?.download == 'true') {
      const rawExcelData = {
        sheets: ['Dashboard Repayment Report'],
        data: [rows],
        sheetName: 'Dashboard Repayment Report.xlsx',
      };
      const url: any = await this.fileService.objectToExcelURL(
        rawExcelData,
        true,
      );
      if (url.message) return url;
      const reportData: any = {
        adminId: downloadBy,
        status: '1',
        fromDate: query.start_date,
        toDate: query.end_date,
        apiUrl: 'admin/transaction/allRepaidLoans',
        reportName: 'Dashboard repayment',
        downloadUrl: url,
      };
      if (query.type == 'delay' || query.type == 'onTime') {
        reportData.extraparms = { type: query.type };
      }
      await this.reportHistoryRepo.create(reportData);
      return { fileUrl: url };
    }

    return { count: result.count, rows, total };
  }
  //#endregion

  //#region pre pare all rePaid loan transaction option
  private async prePareRepaidOptions(query) {
    try {
      const tranList = await this.getDelayEmiPaymenst(query);
      /// where condition
      const startDate = query.start_date;
      const endDate = query.end_date;
      const page = query?.page ?? 1;
      const type = query?.type ?? '';
      let adminId = query?.adminId;
      let userSearch: any = {};
      let loanId;
      let utr;
      let search = (query?.searchText ?? '').toLowerCase();
      if (search) {
        if (search.startsWith('l-')) loanId = search.replace('l-', '');
        else if (search.startsWith('u-')) utr = search.replace('u-', '');
        else if (!isNaN(search)) {
          search = this.cryptService.encryptPhone(search);
          if (search == k500Error) return k500Error;
          search = search.split('===')[1];
          userSearch.phone = { [Op.like]: '%' + search + '%' };
        } else userSearch.fullName = { [Op.iRegexp]: query?.searchText };
      }
      //#region inclued data

      /// emi model
      const att = [
        'id',
        'emi_date',
        'penalty',
        'penalty_days',
        'pay_type',
        'principalCovered',
        'interestCalculate',
        'waiver',
        'paid_waiver',
        'unpaid_waiver',
        'loanId',
      ];
      const emiInclude = { model: EmiEntity, attributes: att };
      /// loan model
      const purposeModel = { model: loanPurpose, attributes: ['purposeName'] };
      const disbModel = { model: disbursementEntity, attributes: ['amount'] };
      const loanInclude = {
        model: loanTransaction,
        attributes: [
          'id',
          'stampFees',
          'processingFees',
          'loan_disbursement_date',
          'netApprovedAmount',
          'interestRate',
          'completedLoan',
          'manualVerificationAcceptId',
          'followerId',
          'loanStatus',
        ],
        include: [emiInclude, purposeModel, disbModel],
      };
      /// employmenst model
      const attDes = ['id', 'designationName'];
      const desigModel = { model: employmentDesignation, attributes: attDes };
      const empInclude = {
        model: employmentDetails,
        attributes: ['id', 'companyName'],
        include: [desigModel],
      };
      /// user details
      const userInclude = {
        model: registeredUsers,
        attributes: [
          'fullName',
          'gender',
          'phone',
          'city',
          'state',
          'kycId',
          'lastCrm',
        ],
        where: userSearch,
        include: [empInclude],
      };
      //#endregion
      const options: any = {
        where: {
          status: 'COMPLETED',
          completionDate: {
            [Op.gte]: startDate.toJSON(),
            [Op.lte]: endDate.toJSON(),
          },
          type: { [Op.ne]: 'REFUND' },
          subStatus: {
            [Op.or]: [
              {
                [Op.ne]: 'REVERSE_SETTLEMENT',
              },
              { [Op.eq]: null },
            ],
          },
        },
        order: [['createdAt', 'DESC']],
        include: [loanInclude, userInclude],
      };

      if (adminId != -1) options.where.followerId = adminId;

      if (loanId) options.where.loanId = loanId;
      if (utr) options.where.utr = { [Op.iRegexp]: utr };
      if (type && type != 'delay' && type != 'onTime')
        options.where.type = type;
      if (tranList.length > 0) {
        if (query?.type == 'delay') options.where.id = tranList;
        else if (query?.type == 'onTime') {
          const idArray = [];
          tranList.forEach((id) => {
            idArray.push({ id: { [Op.ne]: id } });
          });
          options.where = { ...options.where, [Op.and]: idArray };
        }
      }
      if (query?.download != 'true') {
        options.offset = page * PAGE_LIMIT - PAGE_LIMIT;
        options.limit = PAGE_LIMIT;
      }
      return options;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region find repaid loan data
  private async findRepaidLoanData(options) {
    const att = [
      'id',
      'emiId',
      'userId',
      'loanId',
      'updatedAt',
      'completionDate',
      'penaltyAmount',
      'principalAmount',
      'interestAmount',
      'utr',
      'paidAmount',
      'source',
      'type',
      'subSource',
      'penaltyAmount',
      'createdAt',

      'forClosureAmount',
      'sgstForClosureCharge',
      'cgstForClosureCharge',
      'igstForClosureCharge',

      'penalCharge',
      'sgstOnPenalCharge',
      'cgstOnPenalCharge',
      'igstOnPenalCharge',

      'bounceCharge',
      'sgstOnBounceCharge',
      'cgstOnBounceCharge',
      'igstOnBounceCharge',

      'legalCharge',
      'sgstOnLegalCharge',
      'cgstOnLegalCharge',
      'igstOnLegalCharge',

      'regInterestAmount',
      'response',
      'transactionId',
      'followerId',
    ];
    const result = await this.repository.getTableWhereDataWithCounts(
      att,
      options,
    );
    if (result === k500Error) throw new Error();
    return result;
  }
  //#endregion

  //#region prepare all repaod loan
  private async prePareAllRepaidLoans(tranList, maskingFlags) {
    const loanIds = [...new Set(tranList.map((el) => el?.loanId))];
    const attr = [
      'loanId',
      [Sequelize.fn('SUM', Sequelize.col('paidAmount')), 'totalPaid'],
    ];
    const opts = {
      where: {
        loanId: loanIds,
        status: kCompleted,
      },
      group: ['loanId'],
    };

    const attributes = ['emiId', 'type', 'paidAmount'];
    const options = {
      where: {
        loanId: loanIds,
        status: kCompleted,
        type: 'REFUND',
      },
    };
    const allTransData = await this.repository.getTableWhereData(
      attributes,
      options,
    );

    const refunObject = {};
    allTransData.forEach((trans) => {
      if (trans.type === 'REFUND') {
        if (!refunObject[trans.emiId]) {
          refunObject[trans.emiId] = {
            amount: trans.paidAmount,
          };
        }
      }
    });

    const totalPaid = await this.repository.getTableWhereData(attr, opts);
    if (totalPaid === k500Error) throw new Error();

    const finalList = [];
    for (let index = 0; index < tranList.length; index++) {
      const ele = tranList[index];
      const loan = ele.loanData;
      const loanStatus = loan.loanStatus;
      const loanId = ele.loanId;
      const userData = ele?.userData;
      const crmData = userData?.lastCrm ?? {};
      let crmDate = '-';
      if (crmData?.createdAt) {
        const dateInfo = this.dateService.dateToReadableFormat(
          crmData?.createdAt,
        );
        crmDate = dateInfo.readableStr;
      }
      const createdAt = this.typeService.getDateFormatted(ele?.createdAt);
      const disbursAmount = (loan?.disbursementData[0]?.amount ?? 0) / 100;
      const disDate = this.typeService.getDateFormatted(
        loan?.loan_disbursement_date,
      );
      const emp = userData?.employmentData;
      const phone = this.cryptService.decryptPhone(userData?.phone);

      const mobileNumber = maskingFlags?.isMaskPhone
        ? this.cryptService.dataMasking('phone', phone)
        : phone;
      const tPaid = totalPaid.find((f) => f.loanId == loanId)?.totalPaid;
      const foreclosure = ele?.forClosureAmount ?? 0;
      let emiData = loan?.emiData ?? [];
      const completionDate = new Date(ele?.completionDate ?? ele?.updatedAt);
      const repaidDate = this.typeService.getDateFormatted(completionDate);
      const data = this.funRepaidDealyDayFlag(ele, emiData);

      const transDate = this.typeService
        .getGlobalDate(completionDate)
        .getTime();
      let interestRate = +loan?.interestRate;
      let prepaidPrincipal = 0;
      let prepaidInterest = 0;
      let emi;
      // Full Pay
      if (ele?.type == 'FULLPAY') {
        let upcomingPrincipal = 0;
        emiData.forEach((e) => {
          if (new Date(e.emi_date).getTime() > transDate) {
            prepaidPrincipal += e.principalCovered ?? 0;
            upcomingPrincipal += e.principalCovered ?? 0;
          }
        });
        let pastEmis =
          emiData.filter((e) => new Date(e.emi_date).getTime() <= transDate) ??
          [];
        pastEmis = pastEmis.sort((a, b) => a.id - b.id);
        let pastEmiDate = pastEmis[pastEmis.length - 1]?.emi_date;
        emiData = emiData.sort((a, b) => a.id - b.id);
        // Calculating Int. from Loan Disbursment Date
        if (
          pastEmis.length == 0 &&
          new Date(emiData[0].emi_date) > new Date(ele?.completionDate)
        )
          pastEmiDate = new Date(loan?.loan_disbursement_date);
        let daysdiff = this.typeService.dateDifference(
          new Date(ele.completionDate),
          pastEmiDate,
        );
        prepaidInterest = this.typeService.manageAmount(
          (upcomingPrincipal * interestRate * daysdiff) / 100,
        );
        if (
          pastEmis.length == 0 &&
          new Date(emiData[0].emi_date) > new Date(ele?.completionDate)
        )
          prepaidInterest = ele?.interestAmount;
        if (prepaidPrincipal > ele?.principalAmount)
          prepaidPrincipal = ele?.principalAmount;
        if (prepaidInterest > ele?.interestAmount)
          prepaidInterest = ele?.interestAmount;
      }
      // Emi Pay
      else if (ele?.type == 'EMIPAY') {
        emi = emiData.filter(
          (el) =>
            new Date(el.emi_date).getTime() > transDate && el.id == ele?.emiId,
        );
        prepaidPrincipal = emi.length == 0 ? 0 : emi[0].principalCovered;
        prepaidInterest = emi.length == 0 ? 0 : emi[0].interestCalculate;
      }
      // Part Pay
      else if (ele?.type == 'PARTPAY') {
        emi = emiData.filter(
          (el) =>
            new Date(el.emi_date).getTime() > transDate && el.id == ele?.emiId,
        );
        prepaidPrincipal = emi.length == 0 ? 0 : ele?.principalAmount;
        prepaidInterest = emi.length == 0 ? 0 : ele?.interestAmount;
      }
      let isRefund = 'No';
      if (
        refunObject[ele.emiId] &&
        ele.paidAmount == Math.abs(refunObject[ele.emiId].amount)
      ) {
        isRefund = 'Yes';
        delete refunObject[ele.emiId];
      }

      const tempData: any = {
        'Loan id': loanId,
        userId: ele.userId,
        Name: userData?.fullName ?? '-',
        'Mobile number': mobileNumber ?? '-',
        'Follower name':
          (await this.commonShared.getAdminData(ele?.followerId))?.fullName ??
          '-',
        'Completed loans': loan?.completedLoan ?? 0,
        'Approved amount': loan?.netApprovedAmount ?? 0,
        'Processing fees (%)': loan?.processingFees ?? '-',
        'Stamp duty fees': loan?.stampFees ?? 0,
        'Amount disbursed': disbursAmount,
        'Repaid amount': ele?.paidAmount ?? 0,
        'Penalty Amt.': this.typeService.manageAmount(
          (ele?.penaltyAmount ?? 0) +
            (ele?.penalCharge ?? 0) +
            (ele?.sgstOnPenalCharge ?? 0) +
            (ele?.cgstOnPenalCharge ?? 0) +
            (ele?.igstOnPenalCharge ?? 0),
        ),
        'ECS Charge': this.typeService.manageAmount(
          (ele?.bounceCharge ?? 0) +
            (ele?.sgstOnBounceCharge ?? 0) +
            (ele?.cgstOnBounceCharge ?? 0) +
            (ele?.igstOnBounceCharge ?? 0),
        ),
        'Deferred Interest': this.typeService.manageAmount(
          ele?.regInterestAmount ?? 0,
        ),
        'Legal Charge': this.typeService.manageAmount(
          (ele?.legalCharge ?? 0) +
            (ele?.sgstOnLegalCharge ?? 0) +
            (ele?.cgstOnLegalCharge ?? 0) +
            (ele?.igstOnLegalCharge ?? 0),
        ),
        'Due date': data?.dueDate ?? '-',
        'Delay days (as on today)': data?.delayDay ?? 0,
        'Repaid Date': repaidDate,
        'Repaid flag': data?.repaidFlag ?? '-',
        'Payment ID': ele?.utr ?? '-',
        'Payment mode': ele?.source ?? '-',
        'EMI Types': data?.emiNo ?? '-',
        Interest: data?.emiInterest ?? '-',
        Principal: data?.emiPrincipal ?? '-',
        'Repayment via': ele?.subSource ?? '-',
        'Company name': emp?.companyName ?? '-',
        Designation: emp?.designation?.designationName ?? '-',
        Purpose: loan?.purpose?.purposeName ?? '-',
        Gender: userData?.gender ?? '-',
        City: userData?.city ?? '-',
        State: userData?.state ?? '-',
        'Loan Interest': loan?.interestRate,
        'Disbursement date': disDate ?? '-',
        'Total paid Amt': tPaid ?? 0,
        'Foreclosure Charge': foreclosure ?? 0,
        'Created at': createdAt ?? '-',
        'Loan approved by':
          (
            await this.commonShared.getAdminData(
              loan?.manualVerificationAcceptId,
            )
          )?.fullName ?? '-',
        'Last crm by': crmData?.adminName ?? '-',
        'Last crm date': crmDate,
        TransactionId: ele.transactionId ?? '-',
        'Total WaiveOff': data?.totalWaiveOff ?? '-',
        'WaiveOff Given By': data?.totalWaiveOff
          ? (await this.commonShared.getAdminData(loan?.followerId))
              ?.fullName ?? '-'
          : '-',
        'Paid principal': ele?.principalAmount ?? 0,
        'Paid interest': ele?.interestAmount ?? 0,
        'Prepaid Principal': prepaidPrincipal,
        'Prepaid Interest': prepaidInterest,
        'Refund Status': isRefund,
        'Loan Status': loanStatus,

        // GST charges -> Penal charge
        'Penal charge (IGST)': ele?.igstOnPenalCharge ?? 0,
        'Penal charge (CGST)': ele?.cgstOnPenalCharge ?? 0,
        'Penal charge (SGST)': ele?.sgstOnPenalCharge ?? 0,

        // GST charges -> Legal charge
        'Legal charge (IGST)': ele?.igstOnLegalCharge ?? 0,
        'Legal charge (CGST)': ele?.cgstOnLegalCharge ?? 0,
        'Legal charge (SGST)': ele?.sgstOnLegalCharge ?? 0,
        // GST charges -> Bounce charge
        'Bounce charge (IGST)': ele?.igstOnBounceCharge ?? 0,
        'Bounce charge (CGST)': ele?.cgstOnBounceCharge ?? 0,
        'Bounce charge (SGST)': ele?.sgstOnBounceCharge ?? 0,
        // GST charges -> Foreclosure charge
        'Foreclosure charge (IGST)': ele?.igstForClosureCharge ?? 0,
        'Foreclosure charge (CGST)': ele?.cgstForClosureCharge ?? 0,
        'Foreclosure charge (SGST)': ele?.sgstForClosureCharge ?? 0,
      };

      // Adhoc -> As per requirement given by Accounts team need below id for Cashfree (22/06/2024)
      if (ele.source == kCashfree && ele.response) {
        try {
          const response = JSON.parse(ele.response ?? null);
          if (response?.payment?.orderId)
            tempData.TransctionId = response?.payment?.orderId;
        } catch (error) {}
      }

      finalList.push(tempData);
    }
    return finalList;
  }
  //#endregion

  //#region  this function call for repaid flag or dueDate or dealy day
  private funRepaidDealyDayFlag(ele, emiData) {
    let repaidFlag = '-';
    let dueDate = '-';
    let delayDay = 0;
    let emiNo = '-';
    let emiPrincipal = 0;
    let emiInterest = 0;
    let totalWaiveOff = 0;
    try {
      const paidDate = new Date(ele?.completionDate ?? ele?.updatedAt);
      emiData.forEach(
        (emi) =>
          (totalWaiveOff +=
            (emi?.waiver || 0) +
            (emi?.unpaid_waiver || 0) +
            (emi?.paid_waiver || 0)),
      );
      let emi;
      emiData.sort((a, b) => a.id - b.id);
      if (ele?.emiId) {
        emi = emiData.find((f) => f.id === ele?.emiId);
        emiPrincipal = emi?.principalCovered ?? 0;
        emiInterest = emi?.interestCalculate ?? 0;
        const index = emiData.findIndex((el) => el.id == ele?.emiId);
        emiNo = (index + 1).toString();
      } else {
        const filter = emiData.filter((f) => f.pay_type === 'FULLPAY');
        filter.sort((a, b) => a.id - b.id);
        emi = filter[0];
        emiNo = '';
        emiData.forEach((el, index) => {
          if (el.pay_type == 'FULLPAY') {
            if (!emiNo) emiNo = (index + 1).toString();
            else emiNo += ', ' + (index + 1).toString();
            emiPrincipal += el?.principalCovered ?? 0;
            emiInterest += el?.interestCalculate ?? 0;
          }
        });
      }
      if (emi) {
        dueDate = this.typeService.dateToFormatStr(emi?.emi_date);
        delayDay = emi?.penalty_days ?? 0;
        const emiDate = new Date(emi?.emi_date).getTime();
        if (paidDate.getTime() < emiDate) repaidFlag = 'Pre-Paid';
        else if (paidDate.getTime() > emiDate) repaidFlag = 'Delayed';
        else repaidFlag = 'On-Time';
      }
      emiNo = ele?.type + ' ' + emiNo;
    } catch (error) {}
    return {
      repaidFlag,
      dueDate,
      delayDay,
      emiNo,
      emiPrincipal,
      emiInterest,
      totalWaiveOff,
    };
  }
  //#endregion

  //#region get all repaid amount total
  private async getAllRepaidAmount(query) {
    try {
      const data = {
        totalPaidAmount: 0,
        onTimePaidAmount: 0,
        dealyPaidAmount: 0,
        count: 0,
        onTimePaidCounts: 0,
        delayPaidCounts: 0,
      };
      if (query?.getTotal == 'true' && query?.download != 'true') {
        const startDate = query.start_date.toJSON();
        const endDate = query.end_date.toJSON();
        /// find total of repay transaction
        const options: any = {
          where: {
            status: 'COMPLETED',
            completionDate: { [Op.gte]: startDate, [Op.lte]: endDate },
            type: { [Op.ne]: 'REFUND' },
          },
          group: ['status'],
        };

        let att: any = [
          [Sequelize.fn('SUM', Sequelize.col('paidAmount')), 'amount'],
          [Sequelize.fn('COUNT', Sequelize.col('status')), 'COUNT'],
        ];
        const total = await this.repository.getRowWhereData(att, options);
        if (!total || total === k500Error) return kInternalError;
        /// find emi delay emi from transaction
        const emiModel: any = {
          model: EmiEntity,
          attributes: [],
          where: {
            penalty_days: { [Op.gte]: 1 },
          },
        };
        options.include = [emiModel];
        const emi = await this.repository.getRowWhereData(att, options);
        if (emi === k500Error) return kInternalError;

        /// find emi delay emi from transaction for fullpay
        att = ['id', 'paidAmount'];
        options.where.type = 'FULLPAY';
        delete options.group;
        emiModel.where.pay_type = 'FULLPAY';
        const loanModel = {
          model: loanTransaction,
          attributes: [],
          include: [emiModel],
          required: true,
        };
        options.include = [loanModel];
        const loan = await this.repository.getTableWhereData(att, options);
        if (!loan || loan === k500Error) return kInternalError;
        let delyaAmount = 0;
        let dealyCount = 0;
        for (let index = 0; index < loan.length; index++) {
          try {
            const ele = loan[index];
            delyaAmount += ele.paidAmount;
            dealyCount += 1;
          } catch (error) {}
        }
        delyaAmount += emi?.amount ?? 0;
        dealyCount += +(emi?.COUNT ?? 0);
        data.totalPaidAmount = Math.floor(total?.amount ?? 0);
        data.onTimePaidAmount = Math.floor((total?.amount ?? 0) - delyaAmount);
        data.dealyPaidAmount = Math.floor(delyaAmount);
        data.count = +(total?.COUNT ?? 0);
        data.onTimePaidCounts = (total?.COUNT ?? 0) - dealyCount;
        data.delayPaidCounts = dealyCount;
      }
      return data;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region get delay transaction id
  private async getDelayEmiPaymenst(query) {
    const tranList = [];
    try {
      if (query?.type == 'delay' || query?.type == 'onTime') {
        const startDate = query.start_date.toJSON();
        const endDate = query.end_date.toJSON();
        const options: any = {
          where: {
            status: 'COMPLETED',
            completionDate: { [Op.gte]: startDate, [Op.lte]: endDate },
            type: { [Op.ne]: 'REFUND' },
          },
        };
        /// get find delay emi
        const emiModel: any = {
          model: EmiEntity,
          attributes: [],
          where: { penalty_days: { [Op.gte]: 1 } },
        };
        options.include = [emiModel];
        const att = ['id'];
        const emiTran = await this.repository.getTableWhereData(att, options);
        /// find full pay delay emi
        options.where.type = 'FULLPAY';
        delete options.group;
        emiModel.where.pay_type = 'FULLPAY';
        const loanModel = {
          model: loanTransaction,
          attributes: [],
          include: [emiModel],
          required: true,
        };
        options.include = [loanModel];
        const fullTran = await this.repository.getTableWhereData(att, options);

        if (emiTran && emiTran != k500Error)
          emiTran.forEach((ele) => {
            tranList.push(ele.id);
          });

        if (fullTran && fullTran != k500Error)
          fullTran.forEach((ele) => {
            tranList.push(ele.id);
          });
      }
    } catch (error) {}
    return tranList;
  }
  //#endregion

  async fixAllIssues() {
    try {
      await this.fixUTRIssueIfPersists();
    } catch (error) {}
  }

  private async fixDateIssueIfPersists() {
    try {
      const attributes = ['completionDate', 'createdAt', 'id'];
      const options = {
        where: {
          completionDate: { [Op.iLike]: '+0%' },
          status: 'FAILED',
        },
      };

      const transList = await this.repository.getTableWhereData(
        attributes,
        options,
      );
      if (transList == k500Error) return kInternalError;

      for (let index = 0; index < transList.length; index++) {
        try {
          const transData = transList[index];
          const targetDate = transData.completionDate;
          if (targetDate.length != 27) return;

          const id = transData.id;
          const completionDate = this.typeService
            .getGlobalDate(new Date(transData.createdAt))
            .toJSON();
          const updatedData = { completionDate };
          const updateResponse = await this.repository.updateRowData(
            updatedData,
            id,
          );
          if (updateResponse == k500Error) return kInternalError;
        } catch (error) {}
      }

      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  private async fixUTRIssueIfPersists() {
    try {
      const attributes = ['id', 'response', 'source', 'transactionId'];
      const options = {
        where: { status: 'COMPLETED', utr: { [Op.eq]: null } },
      };

      const transList = await this.repository.getTableWhereData(
        attributes,
        options,
      );
      if (transList == k500Error) return kInternalError;

      for (let index = 0; index < transList.length; index++) {
        try {
          const transData = transList[index];
          const source = transData.source;
          if (!source) continue;
          const transId = transData.transactionId;
          const id = transData.id;

          if (source == kRazorpay) {
            const utr = 'pay_' + transId;
            const updatedData = { utr };
            await this.repository.updateRowData(updatedData, id);
          } else if (source == kCashfree) {
            const rawResponse = transData.response;
            if (!rawResponse) continue;
            const response = JSON.parse(rawResponse);
            const data = response.find((el) => el.payment_status == 'SUCCESS');
            const utr = data.cf_payment_id;
            if (!utr) continue;
            const updatedData = { utr };
            await this.repository.updateRowData(updatedData, id);
          }
        } catch (error) {}
      }
    } catch (error) {}
  }

  async fetchCfAutoDebitList(status: string, loanId: number) {
    try {
      const userSearchWhere = {
        loanId,
        [Op.or]: [{ source: 'AUTOPAY' }, { subSource: 'AUTODEBIT' }],
      };
      if (status != 'all') userSearchWhere['status'] = status;
      const transAttr = [
        'id',
        'paidAmount',
        'status',
        'completionDate',
        'transactionId',
        'utr',
        'source',
        'userId',
        'loanId',
        'emiId',
        'subSource',
        'createdAt',
      ];
      const transOptions = {
        where: userSearchWhere,
      };
      const transData = await this.repository.getTableWhereData(
        transAttr,
        transOptions,
      );
      const finalData: any = await this.prepareFinalCFAutoDebitData(transData);
      if (finalData?.message) return finalData;
      return finalData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private prepareFinalCFAutoDebitData(list) {
    try {
      const finalData = [];
      list.forEach((ele) => {
        try {
          const tempData: any = {};
          const createdAt = this.typeService.getDateFormatted(ele?.createdAt);
          const completionDate = this.typeService.getDateFormatted(
            ele?.completionDate,
          );
          tempData['Entry type'] = ele?.source;
          tempData['Batch id'] = ele?.utr;
          tempData['Amount'] = ele?.paidAmount;
          tempData['Loan id'] = ele?.loanId;
          tempData['Emi id'] = ele?.emiId;
          tempData['Mandate id'] = ele?.transactionId;
          tempData['Status'] = ele?.status;
          tempData['Submission date'] = createdAt;
          tempData['Payment done date'] = completionDate;
          tempData['Admin id'] = null;
          tempData['Auto pay create reason'] = null;
          tempData['Created at'] = createdAt;
          finalData.push(tempData);
        } catch (error) {}
      });
      return finalData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  async prepareSDAutodebits(reqData) {
    const loanIds = reqData.loanIds;
    if (!loanIds) return kParamMissing('loanIds');

    const eMandateInclude: SequelOptions = { model: mandateEntity };
    eMandateInclude.attributes = ['emandate_id'];
    const subscriptionInclude: SequelOptions = { model: SubScriptionEntity };
    subscriptionInclude.attributes = ['referenceId'];
    const include = [eMandateInclude, subscriptionInclude];
    const attributes = ['id'];
    const options = {
      include,
      order: [['id']],
      where: { id: loanIds, loanStatus: 'Active' },
    };

    const loanList = await this.loanRepo.getTableWhereData(attributes, options);
    if (loanList == k500Error) return kInternalError;

    const preparedList = [];
    for (let index = 0; index < loanList.length; index++) {
      const el = loanList[index];
      const eMandateList = el.mandateData ?? [];
      let refId = null;
      // For old users
      if (eMandateList?.length > 0) {
        refId = eMandateList[0].emandate_id;
      }
      // For new users
      else if (el.subscriptionData) {
        refId = el.subscriptionData.referenceId;
      }
      if (!refId) continue;

      const fullpayData = await this.calculation.getFullPaymentData({
        loanId: el.id,
      });
      if (fullpayData?.message) continue;

      const fullPayAmount = fullpayData.totalAmount;
      const preparedData = {
        emandate_id: refId,
        loanId: el.id,
        amount: Math.floor(fullPayAmount > 99999 ? 99999 : fullPayAmount),
      };
      preparedList.push(preparedData);
    }

    const body = {
      submission_date: new Date().toJSON().substring(0, 10),
      mandate_list: preparedList,
    };
    return body;
  }

  async fixAutodebitMismatchDate() {
    try {
      const today = new Date();
      today.setDate(today.getDate() - 1);
      const minDate = this.typeService.getGlobalDate(today);
      const rawQuery = `SELECT "TransactionEntities"."id", "emiId", "TransactionEntities"."loanId", 
      "completionDate", "subscriptionDate", "EmiEntities"."payment_done_date" FROM public."TransactionEntities" 
      INNER JOIN "EmiEntities" ON "EmiEntities"."id" = "TransactionEntities"."emiId"
      WHERE "status" = 'COMPLETED' AND "type" = 'EMIPAY' 
      AND "completionDate" >= '${minDate.toJSON()}'
      AND "emiId" IS NOT NULL AND "subSource" = 'AUTODEBIT'
      AND "completionDate" != "subscriptionDate" 
      AND "completionDate" IS NOT NULL
      AND "subscriptionDate" IS NOT NULL;`;
      const targetList: any = await this.repoManager.injectRawQuery(
        TransactionEntity,
        rawQuery,
      );
      if (targetList == k500Error) return kInternalError;

      // for (let index = 0; index < targetList.length; index++) {
      //   try {
      //     const targetData = targetList[index];
      //     const emiId = targetData.emiId;
      //     if (!emiId) continue;
      //     const transId = targetData.id;
      //     if (!transId) continue;
      //     if (
      //       targetData.completionDate &&
      //       targetData.completionDate != targetData.payment_done_date
      //     ) {
      //       continue;
      //     }

      //     const subscriptionDate = targetData.subscriptionDate;
      //     // Update in transaction table
      //     let updatedData: any = { completionDate: subscriptionDate };
      //     let updatedResponse: any = await this.repository.updateRowData(
      //       updatedData,
      //       transId,
      //       true,
      //     );
      //     if (updatedResponse == k500Error) continue;
      //     // Update in emi table
      //     updatedData = { payment_done_date: subscriptionDate };
      //     updatedResponse = await this.emiRepo.updateRowData(
      //       updatedData,
      //       emiId,
      //     );
      //   } catch (error) {}
      // }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //startregion get transaction

  async getTransactionsData(query) {
    try {
      const fromDate = this.typeService.getGlobalDate(query?.fromDate).toJSON();
      const toDate = this.typeService.getGlobalDate(query?.toDate).toJSON();
      const types = query?.types;
      if (!fromDate || !toDate || !types) return kParamsMissing;

      const options: any = {
        where: {
          type: types,
          completionDate: { [Op.gte]: fromDate, [Op.lte]: toDate },
          status: 'COMPLETED',
          subStatus: {
            [Op.or]: [
              {
                [Op.ne]: 'REVERSE_SETTLEMENT',
              },
              { [Op.eq]: null },
            ],
          },
        },
      };
      // For only total counts and total amount paid
      const countAttr: any = [
        [Sequelize.fn('SUM', Sequelize.col('paidAmount')), 'totalAmount'],
        [Sequelize.fn('COUNT', Sequelize.col('paidAmount')), 'count'],
      ];
      const transactionList = await this.repository.getRowWhereData(
        countAttr,
        options,
      );

      if (transactionList === k500Error) return kInternalError;
      transactionList.count = +(transactionList?.count ?? 0);
      transactionList.totalAmount = Math.round(
        transactionList?.totalAmount ?? 0,
      );
      return transactionList;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region Get Last Autodebit Response
  async getLastAutodebitResponse(query) {
    try {
      const userId = query?.userId;
      if (!userId) return kParamMissing('userId');
      const loanInclude: any = { model: loanTransaction, attributes: ['id'] };
      loanInclude.where = { loanStatus: 'Active' };
      const options = {
        where: {
          userId,
          [Op.or]: [{ subSource: 'AUTODEBIT' }, { source: 'AUTOPAY' }],
        },
        order: [['id', 'desc']],
        include: [loanInclude],
      };
      const att = [
        'id',
        'status',
        'response',
        'paidAmount',
        'subscriptionDate',
      ];
      const find = await this.repository.getRowWhereData(att, options);
      if (!find || find === k500Error) return {};
      if (find.status === 'INITIALIZED') {
        return { amount: find.paidAmount, date: find.subscriptionDate };
      }
      let message = '';
      const data = { status: find?.status, message: message };
      try {
        const response = JSON.parse(find?.response);
        message = response?.payment?.failureReason ?? '';
        if (!message) message = response?.error_description ?? '';
        if (!message) message = response?.error_message ?? '';
        if (!message) message = response?.reason ?? '';
        if (data.status === 'COMPLETED') message = 'SUCCESS';
      } catch (error) {}
      data.message = message;
      return data;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //check Uncompleted Transactions of user
  async checkUncompletedTransactions() {
    try {
      const toDayDate = await this.typeService
        .getGlobalDate(new Date())
        .toJSON();
      const emiPayRawQuery = `SELECT emi."userId",trans."paidAmount", emi."loanId",emi."emi_date" , trans."completionDate", trans."transactionId"
      FROM "EmiEntities" AS emi
      JOIN public."TransactionEntities" AS trans
      ON emi."loanId" = trans."loanId"
      WHERE trans."type" = 'FULLPAY'
      AND trans."status" = 'COMPLETED'
      AND trans."completionDate" = '${toDayDate}'
      AND emi."payment_status" = '0' ORDER BY emi."loanId"`;

      const fullPaRrawQuery = `SELECT emi."userId",trans."paidAmount",emi."loanId",emi."emi_date" ,trans."completionDate", trans."transactionId" FROM "EmiEntities" AS emi  INNER JOIN "TransactionEntities" AS trans ON trans."emiId" = emi.id 
      WHERE trans."paidAmount" = (emi."principalCovered" + emi."interestCalculate") 
      AND trans."status" = 'COMPLETED' AND emi."payment_status" = '0' 
      AND (emi."penalty_days" <= 5 OR emi."penalty_days" is null)  
      AND trans."type" = 'EMIPAY' 
      AND emi."emi_date" >= trans."completionDate" 
      ORDER BY emi."loanId"`;

      //emi pay user data
      const emiPayData: any = await this.repoManager.injectRawQuery(
        EmiEntity,
        emiPayRawQuery,
      );
      if (emiPayData === k500Error) return kInternalError;

      //full pay user data
      const fullPayData: any = await this.repoManager.injectRawQuery(
        EmiEntity,
        fullPaRrawQuery,
      );
      //loan Data
      const options: any = { where: { loanStatus: 'Active' } };
      const emiInclude = {
        model: EmiEntity,
        attributes: ['id'],
        where: {
          payment_status: '1',
          partOfemi: 'LAST',
          payment_done_date: toDayDate,
        },
      };
      options.include = [emiInclude];
      const loanData = await this.loanRepo.getTableWhereData(
        ['id', 'userId'],
        options,
      );
      if (loanData === k500Error) return kInternalError;
      const issueLoan = loanData.map((e) => e.id);
      const emiAttributes = ['userId', 'loanId', 'emi_date', 'payment_status'];
      const emiData = await this.emiRepo.getTableWhereData(emiAttributes, {
        where: {
          loanId: issueLoan,
          payment_status: '0',
        },
      });
      if (emiData === k500Error) return kInternalError;
      const ffData = [];
      loanData.forEach((loan) => {
        try {
          const loanId = loan?.id;
          const issue = emiData.find((f) => f.loanId == loanId);
          if (!issue) ffData.push(loan);
        } catch (error) {}
      });
      if (fullPayData == k500Error) return kInternalError;
      const finallData = [...emiPayData, ...fullPayData, ...loanData];
      const emiUserId = emiPayData.map((id) => id.userId);
      const fullUserId = fullPayData.map((id) => id.userId);
      const loanUserId = loanData.map((id) => id.userId);
      const userIds = [...emiUserId, ...fullUserId, ...loanUserId];
      //get user name from userEntity
      const userData = await this.userRepo.getTableWhereData(
        ['id', 'fullName'],
        { where: { id: userIds } },
      );
      if (userData == k500Error) return kInternalError;
      return await this.prepareData(finallData, userData);
    } catch (error) {}
  }

  // data prepare and send mail
  async prepareData(finallData, userData) {
    try {
      const template = kUnclosedLoanEmi;
      const htmlTemplate = fs.readFileSync(template, 'utf-8');
      const MODE = process.env.MODE;
      for (let index = 0; index < finallData.length; index++) {
        try {
          const ele = finallData[index];
          const userName = userData.find((e) => e?.id == ele?.userId);
          const userId = ele?.userId ?? '-';
          const fullName = userName?.fullName ?? '-';
          const loanId = ele?.loanId ?? '-';
          const paidDate = ele?.completionDate ?? '-';
          const paidAmount = ele?.paidAmount
            ? this.typeService.amountNumberWithCommas(ele?.paidAmount)
            : '-';
          const tranId =
            ele?.transactionId ?? 'Emi is pay but loan is not closed';
          const emiDate = ele?.emi_date ?? '-';
          let html: any = htmlTemplate;
          html = html.replace('##userId##', userId);
          html = html.replace('##fullName##', fullName);
          html = html.replace('##loanId##', loanId);
          html = html.replace('##paidDate##', paidDate);
          html = html.replace('##emiDate##', emiDate);
          html = html.replace('##tranId##', tranId);
          html = html.replace('##paidAmount##', paidAmount);
          html = html.replace('##NBFC##', EnvConfig.nbfc.nbfcName);
          const subject = `${MODE} Alert: Payment successful with unclosed EMI/Loan`;
          const cc = kCC;
          await this.sharedNotification.sendMailFromSendinBlue(
            kTechSupportMail,
            subject,
            html,
            userId,
            cc,
          );
        } catch (error) {}
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region  get all  the transaction details by loanId
  async getTransactionDetails(query) {
    try {
      const loanId = query?.loanId;
      if (!loanId) return kParamMissing('loanId');
      const traStatus = ['ALL', 'INITIALIZED', 'COMPLETED', 'FAILED'];
      const status = query?.status;
      if (status && !traStatus.includes(status))
        return kInvalidParamValue('status');

      const traAttrs = [
        'id',
        'paidAmount',
        'completionDate',
        'subscriptionDate',
        'status',
        'emiId',
        'transactionId',
        'utr',
        'source',
        'subSource',
        'type',
        'subStatus',
        'principalAmount',
        'interestAmount',
        'penaltyAmount',
        'createdAt',
        'updatedAt',
        'adminId',
        'response',
        'bounceCharge',
        'legalCharge',
        'sgstOnLegalCharge',
        'cgstOnLegalCharge',
        'igstOnLegalCharge',
        'cgstOnBounceCharge',
        'sgstOnBounceCharge',
        'igstOnBounceCharge',
        'regInterestAmount',
        'cgstOnPenalCharge',
        'sgstOnPenalCharge',
        'igstOnPenalCharge',
        'penalCharge',
        'forClosureAmount',
        'sgstForClosureCharge',
        'cgstForClosureCharge',
        'igstForClosureCharge',
        'paymentTime',
      ];
      const transInc: any = {
        model: TransactionEntity,
        attributes: traAttrs,
        where: { loanId },
      };
      if (status && status != 'ALL') transInc.where.status = status;
      const emiInc = { model: EmiEntity, attributes: ['id', 'pay_type'] };
      const loanOpts = { where: { id: loanId }, include: [transInc, emiInc] };
      const loanData = await this.loanRepo.getRowWhereData(
        ['id', 'penaltyCharges'],
        loanOpts,
      );
      if (loanData === k500Error) return kInternalError;
      else if (!loanData) return [];
      return await this.prepareTransData(loanData, query);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async prepareTransData(loanData, query) {
    try {
      const emiId = query?.emiId;
      const transData = loanData?.transactionData ?? [];
      const modification =
        loanData?.penaltyCharges?.MODIFICATION_CALCULATION ?? false;
      const length = transData.length;
      if (!transData || length == 0) return [];
      transData.sort((a, b) => b.id - a.id);
      const emiData = loanData?.emiData ?? [];
      emiData.sort((a, b) => a.id - b.id);
      const finalData = [];
      const emiTrans = [];
      for (let i = 0; i < length; i++) {
        try {
          const ele = transData[i];
          const emi = ele?.emiId;
          const type = ele?.type;
          const source = ele?.source;
          const subSource = ele?.subSource;
          const status = ele?.status;
          const utr = ele?.utr;
          let subStatus = ele?.subStatus;
          const penalCharges = Math.round(
            (ele?.penalCharge ?? 0) +
              (ele?.sgstOnPenalCharge ?? 0) +
              (ele?.cgstOnPenalCharge ?? 0) +
              (ele?.igstOnPenalCharge ?? 0),
          );
          let totalLegalCharge =
            (ele?.legalCharge ?? 0) +
            (ele?.sgstOnLegalCharge ?? 0) +
            (ele?.sgstOnLegalCharge ?? 0) +
            (ele?.igstOnLegalCharge ?? 0);
          let totalBounceCharge =
            (ele?.bounceCharge ?? 0) +
            (ele?.cgstOnBounceCharge ?? 0) +
            (ele?.sgstOnBounceCharge ?? 0) +
            (ele?.igstOnBounceCharge ?? 0);

          const forClosureCharge = Math.round(
            (ele?.forClosureAmount ?? 0) +
              (ele?.sgstForClosureCharge ?? 0) +
              (ele?.cgstForClosureCharge ?? 0) +
              (ele?.igstForClosureCharge ?? 0),
          );

          let emiNum;
          let fullPay = false;
          emiData.forEach((el, index) => {
            try {
              if (ele?.emiId == el.id) emiNum = `${index + 1}`;
              if (type == 'FULLPAY' && el.pay_type == 'FULLPAY') {
                if (!emiNum) emiNum = `${index + 1}`;
                else emiNum += ` & ${index + 1}`;
                fullPay = true;
              }
            } catch (error) {}
          });
          const adminData = await this.commonShared.getAdminData(ele?.adminId);

          // Repaid Date
          let repaidDate =
            subSource == kAutoDebit
              ? ele?.subscriptionDate ?? ele.createdAt
              : ele.createdAt;
          let formattedRepaidDate: any =
            this.dateService.readableDate(repaidDate);
          if (subSource == kAutoDebit) {
            formattedRepaidDate = formattedRepaidDate.split(' ');
            formattedRepaidDate =
              formattedRepaidDate[0] +
              ' ' +
              formattedRepaidDate[1] +
              ' ' +
              formattedRepaidDate[2];
          } else if (subSource == kDirectBankPay && status == kCompleted) {
            formattedRepaidDate = this.dateService.readableDate(
              ele?.completionDate,
            );
          }

          // Response Date
          let resDate = ele?.paymentTime
            ? ele?.paymentTime
            : subSource == kAutoDebit && status != 'INITIALIZED'
            ? ele?.updatedAt
            : ele?.completionDate;
          let responseDate: any = this.dateService.readableDate(
            resDate,
            false,
            'ist',
          );
          if (!ele?.paymentTime) {
            responseDate = this.dateService.readableDate(resDate).split(' ');
            responseDate =
              responseDate[0] + ' ' + responseDate[1] + ' ' + responseDate[2];
          }

          let paymentURL = '-';
          let failureReason;
          const response = ele?.response ? JSON.parse(ele?.response) : {};
          if (status != 'INITIALIZED') {
            if (source == kCashfree) {
              const resObj = Array.isArray(response) ? response[0] : response;
              const paymentId =
                resObj?.payment?.referenceId ?? resObj?.cf_payment_id ?? '';
              paymentURL = `${kGetCashfreePayment}?txId=${paymentId}`;
            } else if (source == kRazorpay) {
              const pay = type == 'REFUND' ? 'refunds/' : 'payments/';
              paymentURL = `${kGetRazorpayPayment}${pay}${utr}`;
            } else if (source == kSigndesk) {
              const mandateId = utr.split('-id-')[1];
              paymentURL =
                mandateId != 'NA' ? `${kGetSigndeskPayment}${mandateId}` : '-';
            }
            if (status == kFailed)
              failureReason =
                response?.payment?.failureReason ??
                response?.response?.data?.message ??
                response?.error_description ??
                response?.error_message ??
                response?.data?.error?.description ??
                response?.statusDescription ??
                response?.reason ??
                '-';
          }
          subStatus = subStatus == kSplitRefundable ? '-' : subStatus;
          const adNotPlaced =
            status == kFailed &&
            subSource == kAutoDebit &&
            response?.adNotPlaced == true
              ? 'AD_NOT_PLACED'
              : '-';
          const tra: any = {
            Status: status ?? '-',
            'Repaid date': formattedRepaidDate,
            'Response date': resDate ? responseDate : '-',
            'Repay amount': Math.round(ele?.paidAmount),
            Principal: Math.round(ele?.principalAmount),
            Interest: Math.round(ele?.interestAmount),
            'Deferred interest': Math.round(ele?.regInterestAmount) ?? 0,
            'Penal charge': Math.round(
              penalCharges + (ele?.penaltyAmount ?? 0),
            ),
            'For closure charge': forClosureCharge ?? 0,
            EMI: emiNum ?? '-',
            'Pay type': type ?? '-',
            'Paid via': source ?? '-',
            Source: subSource ?? '-',
            UTR: utr ?? '-',
            'Initiated by': adminData.fullName,
            'Sub status': subStatus ?? adNotPlaced,
            'Legal charge': Math.round(totalLegalCharge) ?? 0,
            'ECS charge': Math.round(totalBounceCharge) ?? 0,
            paymentURL,
          };
          if (failureReason) tra.failureReason = failureReason;
          if (emiId == emi || fullPay) emiTrans.push(tra);
          finalData.push(tra);
        } catch (error) {}
      }
      if (emiId) return emiTrans;
      return finalData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  async getAllPendingCFOrders(body) {
    try {
      const fullDayCheck = body?.fullDayCheck ?? false;
      const source = body?.source ?? kCashfree;
      const minutes = body?.minutes;
      let minCheck = 15;
      if (fullDayCheck) minCheck = 2880;
      else if (minutes) minCheck = +minutes;
      const minDate = new Date();
      minDate.setMinutes(minDate.getMinutes() - minCheck);
      const toDay = new Date().toJSON();
      const dateRange = this.typeService.getUTCDateRange(toDay, toDay);

      const attributes = ['loanId'];
      const options = {
        order: [['id', 'DESC']],
        where: {
          status: 'INITIALIZED',
          source,
          subSource: { [Op.ne]: kAutoDebit },
          transactionId: { [Op.ne]: null },
          createdAt: {
            [Op.gte]: dateRange.fromDate,
            [Op.lte]: dateRange.endDate,
          },
          // Limit removed
          //  updatedAt: { [Op.gte]: minDate },
        },
      };

      const pendingList = await this.repository.getTableWhereData(
        attributes,
        options,
      );
      if (pendingList == k500Error) return kInternalError;

      for (let index = 0; index < pendingList.length; index++) {
        const loanId = pendingList[index].loanId;
        await this.sharedTransaction.checkCFOrder(loanId, true);
      }
      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  async placeAutoDebitForEMIDues(reqData: any) {
    try {
      const today = new Date();
      let totalTransactionId = 0;
      let emi_date = this.typeService.getGlobalDate(today).toJSON();
      try {
        const submissionDate: string = reqData?.submissionDate ?? '';
        if (submissionDate.includes('T') && submissionDate.endsWith('Z'))
          emi_date = submissionDate;
      } catch (error) {}
      const targetEMIs: any = await this.getDataForPlaceAutoDebitForEMIDues(
        reqData,
        emi_date,
      );
      if (targetEMIs?.message) return targetEMIs;
      // const nbfc = EnvConfig.nbfcType;
      const mode = reqData.modes;
      const length = targetEMIs.length;
      const adData = [];
      for (let index = 0; index < length; index++) {
        try {
          const emiData = targetEMIs[index];
          const emiId = emiData.id;
          const amount = +emiData.emi_amount;
          if (!amount) continue;
          const loanData = emiData.loan;
          const subscriptionData = loanData?.subscriptionData;
          if (!subscriptionData) continue;
          const source = subscriptionData?.mode;
          if (!source) continue;
          const loanId = loanData.id;
          if (!loanId) continue;

          adData.push({
            adminId: SYSTEM_ADMIN_ID,
            amount,
            emiId,
            loanId,
            payment_date: null,
            sendSMS: true,
            source,
            submissionDate: emi_date,
            subSource: kAutoDebit,
          });
        } catch (error) {
          console.error(error);
        }
      }
      // if (nbfc == '1' && mode.includes(kCashfree)) reqData.batchSize = 1;
      const BATCH_SIZE = reqData?.batchSize ?? 10;
      const adLength = adData.length;
      for (let i = 0; i <= adLength; i += BATCH_SIZE) {
        const batch = adData.slice(i, i + BATCH_SIZE);
        await Promise.all(
          batch.map(async (data) => {
            try {
              const res = await this.sharedTransaction.funCreatePaymentOrder(
                data,
              );
              if (res?.transactionId) totalTransactionId++;
            } catch (error) {}
          }),
        );
      }
      // slack message for corn-alert
      const slackPayload = {
        url: 'admin/transaction/placeAutoDebitForEMIDues',
        fieldObj: {
          targetEMIs: length,
          totalTransactionId,
        },
      };
      this.slackService.sendSlackCronAlert(slackPayload);
      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async getDataForPlaceAutoDebitForEMIDues(reqData: any, emi_date) {
    try {
      const mode = reqData.modes;
      if (!mode) return kParamMissing('modes');
      if (mode.length == 0)
        return k422ErrorMessage('Invalid value provided for parameter modes');

      const subScriptionInc: any = { model: SubScriptionEntity };
      subScriptionInc.attributes = ['id', 'mode'];
      subScriptionInc.where = { mode };
      const loanInclude: any = { model: loanTransaction };
      loanInclude.attributes = ['id'];
      loanInclude.required = true;
      loanInclude.include = [subScriptionInc];
      const include = [loanInclude];
      const attributes = ['id', 'emi_amount', 'loanId'];
      const options = { where: { emi_date, payment_status: '0' }, include };
      const emiList = await this.emiRepo.getTableWhereData(attributes, options);
      if (emiList == k500Error) return kInternalError;
      return emiList;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  async getFiscalSummaryOfRepaidAmount(query) {
    try {
      const year = query.year;
      const isRawData = query?.isRawData ?? false;
      const isRefresh = query?.isRefresh === 'true';

      // Fetch fiscal data
      return await this.prepareFiscalData(year, isRawData, isRefresh);
    } catch (error) {}
  }

  async prepareFiscalData(years, isRawData, isRefresh) {
    try {
      // Define fiscal year boundaries
      const startMonth = 4; // Fiscal year starts in April
      const endMonth = 3; // Fiscal year ends in March
      const today = new Date();
      const currentMonth = today.getMonth() + 1; // Get current month (1-indexed)
      const currentYear = today.getFullYear();

      // Determine fiscal year range
      const startFiscalYear =
        today.getMonth() >= 3 ? currentYear : currentYear - 1;
      const endFiscalYear = startFiscalYear + 1;
      const startFiscalDate = `${startFiscalYear}-04-01`;
      const endFiscalDate = `${endFiscalYear}-03-31`;
      const fiscalRange = `${startFiscalDate}/${endFiscalDate}`;

      // Calculate starting fiscal year based on input years
      const startYear =
        currentMonth > endMonth ? currentYear - years + 1 : currentYear - years;

      let isDbHit = true;
      let getTransactionData: any = {};
      const result = [];
      const fileData = [];
      const URLS: any = [];

      // Loop through each fiscal year within the range
      for (let year = startYear; year <= currentYear; year++) {
        const financialYear = await this.calculateFinancialYear(
          year,
          startMonth,
        );
        const key = `FISCAL_REPAID_SUMMARY_${financialYear}`;
        const currentFiscalYear = financialYear === fiscalRange;

        let cachedData;
        if (!isRefresh) {
          cachedData = await this.redisService.get(key);
          if (cachedData) {
            cachedData = JSON.parse(cachedData);
            result.push(cachedData);
            continue; // Skip calculation for this year
          }
        }

        // Initialize final data structure
        let finalData: any = {
          fiscalYear: `FY(${financialYear.split('/')[0].split('-')[0]}-${
            financialYear.split('/')[1].split('-')[0]
          })`,
          ...this.initializeFinalData(),
        };

        if (!cachedData) {
          if (isDbHit) {
            getTransactionData = await this.getTransactionData(year);
            isDbHit = false;
          }

          let { emiData, transactionData } = getTransactionData;

          const currentMonth = new Date().getMonth() + 1;
          let currentQuarter = Math.ceil((currentMonth - startMonth + 1) / 3);
          if (currentMonth < startMonth) {
            currentQuarter = Math.ceil(
              (currentMonth + (12 - startMonth + 1)) / 3,
            );
          }
          let quarterData: any = { ...this.initializeFinalData() };

          quarterData.Q1 = this.initializeQuarterData();
          if (currentQuarter >= 2)
            quarterData.Q2 = this.initializeQuarterData();
          if (currentQuarter >= 3)
            quarterData.Q3 = this.initializeQuarterData();
          if (currentQuarter >= 4)
            quarterData.Q4 = this.initializeQuarterData();

          // Convert fiscal start and end dates to JSON format
          const fiscalStDate = this.typeService
            .getGlobalDate(new Date(financialYear.split('/')[0]))
            .toJSON();
          const fiscalEtDate = this.typeService
            .getGlobalDate(new Date(financialYear.split('/')[1]))
            .toJSON();

          // Filter transactions for the fiscal year
          const transData = transactionData.filter(
            (el) =>
              el.completionDate >= fiscalStDate &&
              el.completionDate <= fiscalEtDate,
          );

          let monthData = this.initializeMonthData(currentFiscalYear);
          let loanIdsInMonth = [];
          const length = transData.length;
          // Process transactions
          for (let i = 0; i < length; i++) {
            try {
              const el = transData[i];
              let transactionType = el?.paymentTag;

              if (!transactionType) {
                const emiList = emiData.filter(
                  (emi) => emi.loanId === el.loanId,
                );
                transactionType = await this.updateTransPaymentTag(emiList, el);
              } else {
                if (transactionType == 1) transactionType = 'ontime';
                else if (transactionType == 2) transactionType = 'delay';
                else if (transactionType == 3) transactionType = 'pre';
              }

              if (isRawData) {
                fileData.push({ ...el, transactionType });
              } else {
                this.aggregateTransactionData(
                  'monthData',
                  transactionType,
                  el,
                  finalData,
                  monthData,
                  currentFiscalYear,
                );
                this.aggregateTransactionData(
                  'quarterData',
                  transactionType,
                  el,
                  quarterData,
                );
                loanIdsInMonth.push(el.loanId);
              }
            } catch (error) {}
          }

          // Store unique loan count and data
          finalData.loanCount = [...new Set(loanIdsInMonth)].length;
          finalData.monthData = monthData;

          // Clean up quarter data
          delete quarterData.preAmount;
          delete quarterData.ontimeAmount;
          delete quarterData.delayAmount;
          finalData.quarterData = quarterData;
          emiData = [];
          transactionData = [];

          if (!currentFiscalYear) {
            await this.redisService.set(key, JSON.stringify(finalData));
          }
        }

        result.push(finalData);
        if (isRawData) {
          const rawExcelData = {
            sheets: ['local-reports'],
            data: [fileData],
            sheetName: 'fiscalYearData.xlsx',
            needFindTuneKey: false,
            reportStore: true,
          };
          const url = await this.apiService.requestPost(
            nObjectToExcel,
            rawExcelData,
          );
          URLS.push({ [finalData.fiscalYear]: url });
        }
      }
      // Handle raw data export
      if (isRawData) {
        const text = `*FISCAL_REPAID_SUMMARY_${startYear}-${currentYear}_REPORT*`;
        const body = { years, isRawData, isRefresh };
        const threads = [
          `Body details -> ${JSON.stringify(body)}`,
          `URL -> ${JSON.stringify(URLS)}`,
        ];
        this.slackService.sendMsg({ text, threads });
      }
      return isRawData ? URLS : result;
    } catch (error) {}
  }

  async getTransactionData(year) {
    if (!year) return kParamMissing('year');

    const today = new Date();
    const years: any = `${year}-04-01`;
    const startDate = this.typeService.getGlobalDate(years).toJSON();
    const endDate = this.typeService.getGlobalDate(today).toJSON();

    // Define query options for fetching transactions
    const options: any = {
      where: {
        status: 'COMPLETED',
        completionDate: { [Op.gte]: startDate, [Op.lte]: endDate },
        type: { [Op.ne]: 'REFUND' },
        subStatus: {
          [Op.or]: [{ [Op.ne]: 'REVERSE_SETTLEMENT' }, { [Op.eq]: null }],
        },
      },
      order: [['id', 'ASC']],
    };

    const attributes = [
      'id',
      'emiId',
      'loanId',
      'completionDate',
      'penaltyAmount',
      'principalAmount',
      'interestAmount',
      'paidAmount',
      'forClosureAmount',
      'igstForClosureCharge',
      'cgstForClosureCharge',
      'sgstForClosureCharge',
      'penalCharge',
      'sgstOnPenalCharge',
      'cgstOnPenalCharge',
      'igstOnPenalCharge',
      'bounceCharge',
      'sgstOnBounceCharge',
      'cgstOnBounceCharge',
      'igstOnBounceCharge',
      'regInterestAmount',
      'legalCharge',
      'sgstOnLegalCharge',
      'cgstOnLegalCharge',
      'igstOnLegalCharge',
      'paymentTag',
      'type',
    ];

    // Fetch transaction data
    const transactionData = await this.repository.getTableWhereData(
      attributes,
      options,
    );
    if (transactionData === k500Error) throw new Error();

    // Extract unique loan IDs
    const loanIds = [...new Set(transactionData.map((el) => el.loanId))];

    // Fetch EMI data for the loans
    const emiData = await this.emiRepo.getTableWhereData(
      ['id', 'emi_date', 'payment_due_status', 'payment_done_date', 'loanId'],
      { where: { loanId: loanIds } },
    );
    if (emiData === k500Error) throw new Error();

    return { transactionData, emiData };
  }

  private async calculateFinancialYear(year, startMonth) {
    // Define the start and end of the financial year
    const startDate = new Date(year, startMonth - 1, 2); // Start of the specified month in the given year
    const endDate = new Date(year + 1, startMonth - 1, 1); // The last day of the next year's start month

    // Format the dates as yyyy-mm-dd
    const startDateString = startDate.toISOString().split('T')[0]; // "yyyy-mm-dd"
    const endDateString = endDate.toISOString().split('T')[0]; // "yyyy-mm-dd"

    return `${startDateString}/${endDateString}`;
  }

  initializeMonthData(currentFiscalYear) {
    const currentMonthIndex = new Date().getMonth() + 1; // Current month (1-indexed)
    const fiscalYearStartMonth = 4; // Fiscal Year starts in April

    let currentMonth = 11; // Default: All 12 months (for previous fiscal years)

    if (currentFiscalYear) {
      // If it's the current fiscal year, only include months up to the current month
      currentMonth =
        currentMonthIndex >= fiscalYearStartMonth
          ? currentMonthIndex - fiscalYearStartMonth // Count from April
          : currentMonthIndex + 8; // Adjust for months before April
    }

    const monthData = {};

    for (let month = 0; month <= currentMonth; month++) {
      if (KfiscalYearMonths[month]) {
        monthData[KfiscalYearMonths[month].trim()] = {
          paidAmount: 0,
          principalAmount: 0,
          interestAmount: 0,
          deferredInterest: 0,
          ecsCharges: 0,
          penalCharges: 0,
          legalCharges: 0,
          foreclosureCharges: 0,
          payTypeData: {
            paidAmount: { pre: 0, ontime: 0, delay: 0 },
            principalAmount: { pre: 0, ontime: 0, delay: 0 },
            interestAmount: { pre: 0, ontime: 0, delay: 0 },
            ecsCharges: { ontime: 0, delay: 0 },
            diffPayTypeData: {
              paidAmount: { pre: 0, ontime: 0, delay: 0 },
              principalAmount: { pre: 0, ontime: 0, delay: 0 },
              interestAmount: { pre: 0, ontime: 0, delay: 0 },
              ecsCharges: { ontime: 0, delay: 0 },
            },
          },
        };
      }
    }
    return monthData;
  }

  initializeQuarterData() {
    return {
      paidAmount: 0,
      principalAmount: 0,
      interestAmount: 0,
      deferredInterest: 0,
      ecsCharges: 0,
      penalCharges: 0,
      legalCharges: 0,
      foreclosureCharges: 0,
      payTypeData: {
        paidAmount: {
          pre: 0,
          ontime: 0,
          delay: 0,
        },
        principalAmount: {
          pre: 0,
          ontime: 0,
          delay: 0,
        },
        interestAmount: {
          pre: 0,
          ontime: 0,
          delay: 0,
        },
        ecsCharges: {
          ontime: 0,
          delay: 0,
        },
        foreclosureCharges: 0,
        penalCharges: 0,
        legalCharges: 0,
        deferredInterest: 0,
        diffPayTypeData: {
          paidAmount: {
            pre: 0,
            ontime: 0,
            delay: 0,
          },
          principalAmount: {
            pre: 0,
            ontime: 0,
            delay: 0,
          },
          interestAmount: {
            pre: 0,
            ontime: 0,
            delay: 0,
          },
          ecsCharges: {
            ontime: 0,
            delay: 0,
          },
          foreclosureCharges: 0,
          penalCharges: 0,
          legalCharges: 0,
          deferredInterest: 0,
        },
      },
    };
  }

  initializeFinalData() {
    return {
      paidAmount: 0,
      principalAmount: 0,
      interestAmount: 0,
      deferredInterest: 0,
      ecsCharges: 0,
      penalCharges: 0,
      legalCharges: 0,
      foreclosureCharges: 0,
      preAmount: 0,
      ontimeAmount: 0,
      delayAmount: 0,
      preAmountPercentage: 0,
      ontimeAmountPercentage: 0,
      delayAmountPercentage: 0,
      avegPaidAmount: 0,
      avegPrincipalAmount: 0,
      avegInterestAmount: 0,
      avegDeferredInterest: 0,
      avegEcsCharges: 0,
      avegPenalCharges: 0,
      avegLegalCharges: 0,
      avegForeclosureCharges: 0,
      payTypeDataForAvg: {
        paidAmount: {
          pre: 0,
          ontime: 0,
          delay: 0,
        },
        principalAmount: {
          pre: 0,
          ontime: 0,
          delay: 0,
        },
        interestAmount: {
          pre: 0,
          ontime: 0,
          delay: 0,
        },
        ecsCharges: {
          ontime: 0,
          delay: 0,
        },
        foreclosureCharges: 0,
        penalCharges: 0,
        legalCharges: 0,
        deferredInterest: 0,

        averageAmount: {
          avegPaidAmount: {
            pre: 0,
            ontime: 0,
            delay: 0,
          },
          avegPrincipalAmount: {
            pre: 0,
            ontime: 0,
            delay: 0,
          },
          avegInterestAmount: {
            pre: 0,
            ontime: 0,
            delay: 0,
          },
          avegEcsCharges: {
            ontime: 0,
            delay: 0,
          },
          avegForeclosureCharges: 0,
          avegPenalCharges: 0,
          avegLegalCharges: 0,
          avegDeferredInterest: 0,
        },
      },
    };
  }

  aggregateTransactionData(
    datatype,
    transactionType,
    el,
    finalData,
    monthData?,
    currentFiscalYear?,
  ) {
    try {
      // Get current month and previous month
      const months = kMonths[new Date(el.completionDate).getMonth()].trim();
      const prevMonth =
        kMonths[(new Date(el.completionDate).getMonth() - 1 + 12) % 12]?.trim();
      const fiscalYearStartMonth = 3;
      // Get current quarter and previous quarter
      const completionDate = new Date(el.completionDate);

      const month = completionDate.getMonth() + 1; // 1-12 (January = 1)

      const quarter =
        Math.ceil(((month - fiscalYearStartMonth + 12) % 12) / 3) || 4; // Calculate quarter (1, 2, 3, 4)

      const quarterLabel = `Q${quarter}`; // Quarter label (Q1, Q2, Q3, Q4)

      const prevQuarter = quarter === 1 ? `Q4` : `Q${quarter - 1}`; // Previous quarter

      // Extract various amounts from the transaction data
      let paidAmount = Math.round(el?.paidAmount ?? 0);
      const principalAmount = Math.round(el?.principalAmount ?? 0);
      const interestAmount = Math.round(el?.interestAmount ?? 0);
      const regInterestAmount = Math.round(el?.regInterestAmount ?? 0);

      const forClosureAmount = Math.round(el?.forClosureAmount ?? 0);
      const gstOfforClosureAmount =
        (el?.sgstForClosureCharge ?? 0) +
        (el?.cgstForClosureCharge ?? 0) +
        (el?.igstForClosureCharge ?? 0);

      const bounceCharge = Math.round(el?.bounceCharge ?? 0);
      const gstOfBounceCharge =
        (el?.sgstOnBounceCharge ?? 0) +
        (el?.cgstOnBounceCharge ?? 0) +
        (el?.igstOnBounceCharge ?? 0);

      const penalCharge =
        Math.round(el?.penalCharge ?? 0) + Math.round(el?.penaltyAmount ?? 0);
      const gstOfPenalCharge =
        (el?.cgstOnPenalCharge ?? 0) +
        (el?.sgstOnPenalCharge ?? 0) +
        (el?.igstOnPenalCharge ?? 0);

      const legalCharge = Math.round(el?.legalCharge ?? 0);
      const gstOfLegalCharge =
        (el?.sgstOnLegalCharge ?? 0) +
        (el?.cgstOnLegalCharge ?? 0) +
        (el?.igstOnLegalCharge ?? 0);

      paidAmount =
        paidAmount -
        Math.round(
          gstOfBounceCharge +
            gstOfLegalCharge +
            gstOfPenalCharge +
            gstOfforClosureAmount,
        );

      // Helper function to calculate percentage change
      const calculatePercentageChange = (currentValue, prevValue) => {
        if (prevValue === 0) {
          return currentValue === 0 ? 0 : currentValue;
        }
        return currentValue - prevValue;
      };

      // Set variables for current and previous data based on datatype (month or quarter)
      let calculateAverage = datatype == 'monthData' ? monthData : finalData;
      let currentDataVariable = datatype == 'monthData' ? months : quarterLabel;
      let prevDataVariable = datatype == 'monthData' ? prevMonth : prevQuarter;
      let quarterDataVariable: any = finalData;

      let commonVariable: any =
        datatype == 'monthData' ? monthData : quarterDataVariable;
      let finalDatas = finalData;

      // Update the accumulated data for the current period
      commonVariable[currentDataVariable].paidAmount += paidAmount;
      finalDatas.paidAmount += paidAmount;

      commonVariable[currentDataVariable].principalAmount += principalAmount;
      finalDatas.principalAmount += principalAmount;

      commonVariable[currentDataVariable].interestAmount += interestAmount;
      finalDatas.interestAmount += interestAmount;

      commonVariable[currentDataVariable].deferredInterest += regInterestAmount;
      finalDatas.deferredInterest += regInterestAmount;

      commonVariable[currentDataVariable].foreclosureCharges +=
        forClosureAmount;
      finalDatas.foreclosureCharges += forClosureAmount;

      commonVariable[currentDataVariable].ecsCharges += bounceCharge;
      finalDatas.ecsCharges += bounceCharge;

      commonVariable[currentDataVariable].penalCharges += penalCharge;
      finalDatas.penalCharges += penalCharge;

      commonVariable[currentDataVariable].legalCharges += legalCharge;
      finalDatas.legalCharges += legalCharge;

      // Function to update pay type data for different transaction types (pre, ontime, delay)
      const updatePayTypeData = (payType) => {
        const prefix =
          payType === 'pre' ? 'pre' : payType === 'ontime' ? 'ontime' : 'delay';

        commonVariable[currentDataVariable].payTypeData.paidAmount[prefix] +=
          paidAmount;
        finalDatas.payTypeDataForAvg.paidAmount[prefix] += paidAmount;
        commonVariable[currentDataVariable].payTypeData.principalAmount[
          prefix
        ] += principalAmount;
        finalDatas.payTypeDataForAvg.principalAmount[prefix] += principalAmount;
        commonVariable[currentDataVariable].payTypeData.interestAmount[
          prefix
        ] += interestAmount;

        finalDatas.payTypeDataForAvg.interestAmount[prefix] += interestAmount;
        finalDatas.payTypeDataForAvg.foreclosureCharges += forClosureAmount;
        finalDatas.payTypeDataForAvg.deferredInterest += regInterestAmount;
        finalDatas.payTypeDataForAvg.penalCharges += penalCharge;
        finalDatas.payTypeDataForAvg.legalCharges += legalCharge;

        if (prefix != 'pre') {
          commonVariable[currentDataVariable].payTypeData.ecsCharges[prefix] +=
            bounceCharge ?? 0;
          finalDatas.payTypeDataForAvg.ecsCharges[prefix] += bounceCharge ?? 0;
        }

        let totalAmount = 0;

        // Calculate total amounts for each pay type (pre, ontime, delay)
        if (prefix == 'ontime') {
          totalAmount +=
            (principalAmount ?? 0) +
            (interestAmount ?? 0) +
            (bounceCharge ?? 0) +
            (regInterestAmount ?? 0) +
            (penalCharge ?? 0) +
            (forClosureAmount ?? 0);
        }

        if (prefix == 'pre') {
          totalAmount +=
            (principalAmount ?? 0) +
            (interestAmount ?? 0) +
            (bounceCharge ?? 0) +
            (regInterestAmount ?? 0) +
            (penalCharge ?? 0) +
            (forClosureAmount ?? 0);
        }

        if (prefix == 'delay') {
          totalAmount +=
            principalAmount +
            interestAmount +
            regInterestAmount +
            forClosureAmount +
            bounceCharge +
            penalCharge +
            legalCharge;
        }

        // Calculate averages per pay type without forEach
        const calculateAverage = (field, type, datatype) => {
          let total =
            finalDatas.payTypeDataForAvg[field][type] ??
            finalDatas.payTypeDataForAvg[field];
          const currentMonth = new Date().getMonth() + 1;
          const fiscalYearStartMonth = 4;
          let count = 0;
          if (datatype == 'monthData') {
            if (currentMonth >= fiscalYearStartMonth) {
              count = currentMonth - fiscalYearStartMonth + 1;
            } else if (
              currentMonth < fiscalYearStartMonth &&
              currentFiscalYear
            ) {
              count = 12 - fiscalYearStartMonth + currentMonth + 1;
            } else {
              count = 12; // Full year count if outside current fiscal year
            }
          } else {
            let currentQuarter = 0;
            if (currentMonth >= fiscalYearStartMonth) {
              currentQuarter =
                Math.floor((currentMonth - fiscalYearStartMonth) / 3) + 1;
            } else {
              currentQuarter = 3 + Math.floor(currentMonth / 3) + 1;
            }
            count = currentQuarter > 0 ? currentQuarter : 1; // Avoid division by zero
          }

          return total / count; // Avoid division by zero
        };

        // Update average calculations for each pay type
        finalDatas.payTypeDataForAvg.averageAmount.avegPaidAmount[prefix] =
          calculateAverage('paidAmount', prefix, datatype);

        finalDatas.payTypeDataForAvg.averageAmount.avegPrincipalAmount[prefix] =
          calculateAverage('principalAmount', prefix, datatype);
        finalDatas.payTypeDataForAvg.averageAmount.avegInterestAmount[prefix] =
          calculateAverage('interestAmount', prefix, datatype);
        if (prefix !== 'pre') {
          finalDatas.payTypeDataForAvg.averageAmount.avegEcsCharges[prefix] =
            calculateAverage('ecsCharges', prefix, datatype);
        }

        finalDatas.payTypeDataForAvg.averageAmount.avegDeferredInterest =
          calculateAverage('deferredInterest', prefix, datatype);
        finalDatas.payTypeDataForAvg.averageAmount.avegForeclosureCharges =
          calculateAverage('foreclosureCharges', prefix, datatype);
        finalDatas.payTypeDataForAvg.averageAmount.avegPenalCharges =
          calculateAverage('penalCharges', prefix, datatype);
        finalDatas.payTypeDataForAvg.averageAmount.avegLegalCharges =
          calculateAverage('legalCharges', prefix, datatype);
        finalDatas[`${prefix}Amount`] += totalAmount;
      };

      // Update data for the specified transaction type (pre, ontime, delay)
      if (transactionType === 'pre') {
        updatePayTypeData('pre');
      } else if (transactionType === 'ontime') {
        updatePayTypeData('ontime');
      } else if (transactionType === 'delay') {
        updatePayTypeData('delay');
      }

      // Calculate percentages of preAmount, ontimeAmount, and delayAmount relative to totalPaidAmount
      finalDatas.preAmountPercentage = finalDatas.paidAmount
        ? Math.round((finalDatas.preAmount / finalDatas.paidAmount) * 100)
        : 0;

      finalDatas.ontimeAmountPercentage = finalDatas.paidAmount
        ? Math.round((finalDatas.ontimeAmount / finalDatas.paidAmount) * 100)
        : 0;

      finalDatas.delayAmountPercentage = finalDatas.paidAmount
        ? Math.round((finalDatas.delayAmount / finalDatas.paidAmount) * 100)
        : 0;

      // Calculate percentage differences for previous and current data
      if (commonVariable[prevDataVariable]) {
        try {
          const fieldsToCalculate = [
            'paidAmount',
            'principalAmount',
            'interestAmount',
            'deferredInterest',
            'foreclosureCharges',
            'ecsCharges',
            'penalCharges',
            'legalCharges',
          ];

          fieldsToCalculate.forEach((field) => {
            commonVariable[currentDataVariable][`diff${field}`] =
              calculatePercentageChange(
                commonVariable[currentDataVariable][field] ?? 0,
                commonVariable[prevDataVariable][field] ?? 0,
              );
          });

          // Calculate diffs for 'pre', 'ontime', and 'delay' separately
          const diffPayTypeData =
            commonVariable[currentDataVariable].payTypeData.diffPayTypeData;

          ['paidAmount', 'principalAmount', 'interestAmount'].forEach(
            (field) => {
              ['pre', 'ontime', 'delay'].forEach((type) => {
                diffPayTypeData[field][type] = calculatePercentageChange(
                  commonVariable[currentDataVariable].payTypeData[field][
                    type
                  ] ?? 0,
                  commonVariable[prevDataVariable].payTypeData[field][type] ??
                    0,
                );
              });
            },
          );

          // Calculate diffs for ecsCharges, foreclosureCharges, and others
          ['ecsCharges'].forEach((field) => {
            ['ontime', 'delay'].forEach((type) => {
              diffPayTypeData[field][type] = calculatePercentageChange(
                commonVariable[currentDataVariable].payTypeData[field][type] ??
                  0,
                commonVariable[prevDataVariable].payTypeData[field][type] ?? 0,
              );
            });
          });
        } catch (error) {}
      }

      // Calculate averages for the overall dataset
      finalDatas.avegPaidAmount = this.calculateAverageOfMonthAndQuater(
        'paidAmount',
        datatype,
        calculateAverage,
        currentFiscalYear,
      );
      finalDatas.avegPrincipalAmount = this.calculateAverageOfMonthAndQuater(
        'principalAmount',
        datatype,
        calculateAverage,
        currentFiscalYear,
      );
      finalDatas.avegInterestAmount = this.calculateAverageOfMonthAndQuater(
        'interestAmount',
        datatype,
        calculateAverage,
        currentFiscalYear,
      );
      finalDatas.avegDeferredInterest = this.calculateAverageOfMonthAndQuater(
        'deferredInterest',
        datatype,
        calculateAverage,
        currentFiscalYear,
      );

      finalDatas.avegEcsCharges = this.calculateAverageOfMonthAndQuater(
        'ecsCharges',
        datatype,
        calculateAverage,
        currentFiscalYear,
      );
      finalDatas.avegPenalCharges = this.calculateAverageOfMonthAndQuater(
        'penalCharges',
        datatype,
        calculateAverage,
        currentFiscalYear,
      );
      finalDatas.avegLegalCharges = this.calculateAverageOfMonthAndQuater(
        'legalCharges',
        datatype,
        calculateAverage,
        currentFiscalYear,
      );
      finalDatas.avegForeclosureCharges = this.calculateAverageOfMonthAndQuater(
        'foreclosureCharges',
        datatype,
        calculateAverage,
        currentFiscalYear,
      );
    } catch (error) {}
  }

  calculateAverageOfMonthAndQuater(
    field,
    datatype,
    commonVariable,
    prevousFiscalYear,
  ) {
    let sum = 0;
    let count = 0;
    const currentMonth = new Date().getMonth() + 1;
    const fiscalYearStartMonth = 4;

    try {
      if (datatype == 'monthData') {
        if (currentMonth >= fiscalYearStartMonth && prevousFiscalYear) {
          count = currentMonth - fiscalYearStartMonth + 1;
        } else if (currentMonth < fiscalYearStartMonth && prevousFiscalYear) {
          count = 12 - fiscalYearStartMonth + currentMonth + 1;
        } else {
          count = 12;
        }

        for (const key in commonVariable) {
          try {
            if (commonVariable[key][field] !== undefined) {
              sum += commonVariable[key][field];
            }
          } catch (error) {}
        }
      } else {
        let currentQuarter = 0;
        if (currentMonth >= fiscalYearStartMonth) {
          currentQuarter =
            Math.floor((currentMonth - fiscalYearStartMonth) / 3) + 1;
        } else {
          currentQuarter = 3 + Math.floor(currentMonth / 3) + 1;
        }

        for (const key in commonVariable) {
          try {
            if (
              commonVariable[key][field] !== undefined &&
              key != 'payTypeDataForAvg'
            ) {
              sum += commonVariable[key][field];
            }
          } catch (error) {}
        }
        count = currentQuarter > 0 ? currentQuarter : 1; // Avoid division by zero
      }
      return sum / count;
    } catch (error) {}
  }

  async funUpdateTransactionTag(body) {
    const loanId = body?.loanId;
    if (!loanId) return kParamMissing('loanId');
    const options: any = {
      where: {
        loanId,
        status: 'COMPLETED',
        type: { [Op.ne]: 'REFUND' },
        paymentTag: { [Op.eq]: null },
        subStatus: {
          [Op.or]: [{ [Op.ne]: 'REVERSE_SETTLEMENT' }, { [Op.eq]: null }],
        },
      },
      order: [['id', 'ASC']],
    };
    const att = [
      'id',
      'emiId',
      'loanId',
      'completionDate',
      'penaltyAmount',
      'principalAmount',
      'interestAmount',
      'paidAmount',
      'penaltyAmount',
      'forClosureAmount',
      'igstForClosureCharge',
      'cgstForClosureCharge',
      'sgstForClosureCharge',
      'penalCharge',
      'sgstOnPenalCharge',
      'cgstOnPenalCharge',
      'igstOnPenalCharge',
      'bounceCharge',
      'sgstOnBounceCharge',
      'cgstOnBounceCharge',
      'igstOnBounceCharge',
      'regInterestAmount',
      'legalCharge',
      'sgstOnLegalCharge',
      'cgstOnLegalCharge',
      'igstOnLegalCharge',
      'paymentTag',
      'type',
    ];

    const transactionData = await this.repository.getTableWhereData(
      att,
      options,
    );
    if (transactionData == k500Error) throw new Error();
    const loanIds = [...new Set(transactionData.map((el) => el.loanId))];
    const emiData = await this.emiRepo.getTableWhereData(
      ['id', 'emi_date', 'payment_due_status', 'payment_done_date', 'loanId'],
      { where: { loanId: loanIds } },
    );
    if (emiData == k500Error) throw new Error();
    const length = transactionData.length;
    for (let i = 0; i < length; i++) {
      try {
        const ele = transactionData[i];
        let transactionType = ele?.paymentTag;
        const emiList = emiData.filter((emi) => emi.loanId === ele.loanId);
        transactionType = await this.updateTransPaymentTag(emiList, ele);
      } catch (error) {}
    }
    return {};
  }

  async updateTransPaymentTag(emiData, el) {
    let transactionType;
    let paymentTag;

    for (let i = 0; i < emiData.length; i++) {
      try {
        const ele = emiData[i];
        if (el?.type == 'FULLPAY') {
          if (ele?.emi_date < el?.completionDate) transactionType = 'delay';
          else if (ele?.emi_date == el?.completionDate)
            transactionType = 'ontime';
          else if (ele?.emi_date > el?.completionDate) transactionType = 'pre';
        } else if (ele?.id == el?.emiId) {
          if (ele?.emi_date > el?.completionDate) transactionType = 'pre';
          else if (ele?.emi_date == el?.completionDate)
            transactionType = 'ontime';
          else transactionType = 'delay';
        }
      } catch (error) {}
    }

    if (transactionType == 'ontime') paymentTag = 1;
    else if (transactionType == 'delay') paymentTag = 2;
    else if (transactionType == 'pre') paymentTag = 3;

    if (el?.id)
      await this.repository.updateRowData({ paymentTag }, el.id, true);
    return transactionType;
  }
}
