import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type LeadTrackingDocument = LeadTracking & Document;

@Schema({ timestamps: true, strict: false })
export class LeadTracking {
  @Prop({})
  source: string;

  @Prop({ type: Object })
  body: Record<string, any>;

  @Prop({ type: Object })
  headers: Record<string, any>;

  @Prop({})
  ip: string;

  @Prop({})
  adminId: string;

  @Prop({})
  apiEndpoint: string;
}

export const LeadTrackingSchema = SchemaFactory.createForClass(LeadTracking);
