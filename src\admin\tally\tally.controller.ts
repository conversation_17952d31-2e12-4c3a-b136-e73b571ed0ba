// Imports
import { TallyService } from './tally.service';
import { kInternalError, kSuccessData } from 'src/constants/responses';
import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Res,
  UploadedFile,
  UploadedFiles,
  UseInterceptors,
} from '@nestjs/common';
import { AnyFilesInterceptor, FileInterceptor } from '@nestjs/platform-express';
import { kUploadFileObj } from 'src/constants/objects';
import { ErrorContextService } from 'src/utils/error.context.service';

@Controller('admin/tally')
export class TallyController {
  constructor(
    private readonly service: TallyService,
    private readonly errorContextService: ErrorContextService,
  ) {}

  //Disbursement summary card
  @Get('getAllDisbursementDetails')
  async funAllDisbursementDetails(@Query() query, @Res() res) {
    try {
      const result: any = await this.service.allDisbursementDetails(query);
      if (result?.message) return res.json(result);
      return res.json({ ...kSuccessData, data: result });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  //Repayment summary card
  @Get('getAllRepaymentData')
  async funAllRepaymentData(@Query() query, @Res() res) {
    try {
      const result: any = await this.service.getRepaymentData(query);
      if (result?.message) return res.json(result);
      return res.json({ ...kSuccessData, data: result });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  //create payment settlements from 3rd party(razorpay-1,2,cashfree)
  @Post('syncSettlements')
  async funSyncSettlements(@Body() body, @Res() res) {
    try {
      const result: any = await this.service.syncSettlements(body);
      if (result?.message) return res.json(result);
      return res.json({ ...kSuccessData, data: result });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  //Wallet settlement card
  @Get('getWalletSettlementDetails')
  async funWalletSettlementDetails(@Query() query, @Res() res) {
    try {
      const result: any = await this.service.getWalletSettlementDetails(query);
      if (result?.message) return res.json(result);
      return res.json({ ...kSuccessData, data: result });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  //Account Ledger table
  @Get('getLoanDisbursementDetails')
  async funLoanDisbursementDetails(@Query() query, @Res() res) {
    try {
      const result: any = await this.service.getLoanDisbursementDetails(query);
      if (result?.message) return res.json(result);
      return res.json({ ...kSuccessData, data: result });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  //Get Ledger loan details
  @Get('getLedgerLoanDetails')
  async funLedgerLoanDetails(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.getLedgerLoanDetails(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }

  @Post('generateBulkLedgers')
  async funGenerateBulkLedgers(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.generateBulkLedgers(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }

  // LCR Report Generate
  @Post('lcrReport')
  async funGenerateLCRReport(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.generateLCRReport(body);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  // Get LCR Report
  @Post('getLCRInfo')
  async funGetLCRInfo(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.getLCRInfo(body);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Post('onDemandSettlement')
  async funOnDemandSettlement(@Body() body, @Res() res) {
    try {
      const data = await this.service.onDemandSettlement(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Post('accountSummary')
  async funAccountsSummary(@Body() body, @Res() res) {
    try {
      const data = await this.service.accountSummary(body);
      if (data?.message) return res.send(data);

      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Post('hypothification')
  async funHypothification(@Body() body, @Res() res) {
    try {
      const data = await this.service.hypothification(body);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Post('addLenderDetails')
  @UseInterceptors(AnyFilesInterceptor(kUploadFileObj()))
  async addLenderDetails(@UploadedFiles() files, @Body() body, @Res() res) {
    try {
      const data: any = await this.service.addLenderDetails(body, files);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Post('viewLenderDetails')
  async viewLenderDetails(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.viewLenderDetails(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Post('hypothecationRepaymentDetails')
  async hypothecationRepaymentDetails(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.hypothecationRepaymentDetails(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Post('updatehypothecationRepaymentDetails')
  async updatehypothecationRepaymentDetails(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.updateHypothecationRepaymentDetails(
        body,
      );
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Post('newHypothification')
  async newHypothification(@Body() body, @Res() res) {
    try {
      const data = await this.service.newHypothification(body);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Post('viewHypothecationUserHistory')
  async viewHypothecationUserHistory(@Body() body, @Res() res) {
    try {
      const data = await this.service.viewHypothecationUserHistory(body);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  // Purpose -> Zoho books
  @Post('zohoTallyDisbursementData')
  @UseInterceptors(FileInterceptor('file', kUploadFileObj()))
  async funZohoTallyDisbursementData(
    @Body() body,
    @UploadedFile() file,
    @Res() res,
  ) {
    try {
      body.file = file;
      const data = await this.service.zohoTallyDisbursementData({
        body: body,
        file: file,
      });
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Post('customTallyInterestIncome')
  async funCustomTallyInterestIncome(@Body() body, @Res() res) {
    try {
      const data = await this.service.customTallyInterestIncome(body);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }
  @Post('newAccountSection')
  async funNewAccountSection(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.newAccountSection(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Get('getAccountSummary')
  async funGetAccountSummary(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.getAccountSummary(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }

  @Get('updateExpense')
  async funUpdateExpense(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.updateExpense(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      console.log({ error });
      return res.send(kInternalError);
    }
  }
}
