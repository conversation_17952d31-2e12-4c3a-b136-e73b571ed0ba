// Imports
import { Injectable } from '@nestjs/common';
import { k500Error } from 'src/constants/misc';
import { kInternalError, kParamMissing } from 'src/constants/responses';
import { AddressesRepository } from 'src/repositories/addresses.repository';

@Injectable()
export class ResidenceService {
  constructor(private readonly addressRepo: AddressesRepository) {}

  async addressList(reqData) {
    try {
      const userId = reqData?.userId;
      if (!userId) return kParamMissing('userId');

      const attributes = [
        'address',
        'lat',
        'long',
        'type',
        'probability',
        'subType',
        'status',
      ];
      const data = await this.addressRepo.getTableWhereData(attributes, {
        where: { userId },
      });
      if (data == k500Error) return kInternalError;
      return data;
    } catch (error) {
      console.error('Error in: ', error);
      return kInternalError;
    }
  }
}
