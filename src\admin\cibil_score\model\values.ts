// state List
export const STATE_LIST = [
  'Andaman and Nicobar Islands',
  'Andhra Pradesh',
  'Arunachal Pradesh',
  'Assam',
  'Bihar',
  'Chandigarh',
  'Chhattisgarh',
  'Dadra and Nagar Haveli',
  'Daman and Diu',
  'Goa',
  'Gujarat',
  'Haryana',
  'Himachal Pradesh',
  'Jammu and Kashmir',
  'Jharkhand',
  'Karnataka',
  'Kerala',
  'Lakshadweep',
  'Madhya Pradesh',
  'Maharashtra',
  'Manipur',
  'Meghalaya',
  'Mizoram',
  'Nagaland',
  'New Delhi',
  'Orissa',
  'Puducherry',
  'Punjab',
  'Rajasthan',
  'Sikkim',
  'Tamil Nadu',
  'Tripura',
  'Uttar Pradesh',
  'Uttarakhand',
  'West Bengal',
  'Telangana',
];

// account Status
const accountStatusMap = {
  Open: '01',
  ClosedByPayment: '02',
  SettledClosed: '03',
};

export const STATE_LIST_TUDF = [
  'jammu & Kashmir',
  'Himachal Pradesh',
  'Punjab',
  'Chandigarh',
  'Uttaranchal',
  'Haryana',
  'Delhi',
  'Rajasthan',
  'Uttar Pradesh',
  'Bihar',
  'Sikkim',
  'Arunachal Pradesh',
  'Nagaland',
  'Manipur',
  'Mizoram',
  'Tripura',
  'Meghalaya',
  'Assam ',
  'West Bengal',
  'Jharkhand',
  'Orissa',
  'Chhattisgarh',
  'Madhya Pradesh',
  'Gujarat',
  'Daman & Diu',
  'Dadra & Nagar Haveli',
  'Maharashtra',
  'Andhra Pradesh',
  'Karnataka',
  'Goa',
  'Lakshadweep',
  'Kerala',
  'Tamil Nadu',
  'Pondicherry',
  'Andaman & Nicobar Islands',
  'Telangana',
];

export const STATE_PIN_TUDF = {
  '01': { min: 18, max: 19 },
  '02': { min: 17, max: 17 },
  '03': { min: 14, max: 16 },
  '04': { min: 16, max: 16 },
  '05': { min: 24, max: 26 },
  '06': { min: 12, max: 13 },
  '07': { min: 11, max: 11 },
  '08': { min: 30, max: 34 },
  '09': { min: 20, max: 28 },
  '10': { min: 80, max: 85 },
  '11': { min: 73, max: 73 },
  '12': { min: 78, max: 79 },
  '13': { min: 79, max: 79 },
  '14': { min: 79, max: 79 },
  '15': { min: 79, max: 79 },
  '16': { min: 72, max: 79 },
  '17': { min: 79, max: 79 },
  '18': { min: 78, max: 79 },
  '19': { min: 70, max: 74 },
  '20': { min: 81, max: 83 },
  '21': { min: 75, max: 77 },
  '22': { min: 46, max: 49 },
  '23': { min: 45, max: 48 },
  '24': { min: 36, max: 39 },
  '25': { min: 36, max: 39 },
  '26': { min: 39, max: 39 },
  '27': { min: 40, max: 44 },
  '28': { min: 50, max: 56 },
  '29': { min: 53, max: 59 },
  '30': { min: 40, max: 40 },
  '31': { min: 67, max: 68 },
  '32': { min: 67, max: 69 },
  '33': { min: 53, max: 66 },
  '34': { min: 53, max: 67 },
  '35': { min: 74, max: 74 },
  '36': { min: 50, max: 56 },
};
