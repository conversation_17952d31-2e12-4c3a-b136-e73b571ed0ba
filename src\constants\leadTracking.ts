import {
  AcceptedLead_0,
  AcceptedLead_1,
  AcceptedLead_2,
  AcceptedLead_3,
  AcceptedLead_4,
  AcceptedLead_5,
  AcceptedLead_6,
  AcceptedLead_7,
  AcceptedLead_8,
  AcceptedLead_9,
} from 'src/entities/acceptedLead.partition.entity';
import {
  RejectedLead_0,
  RejectedLead_1,
  RejectedLead_2,
  RejectedLead_3,
  RejectedLead_4,
  RejectedLead_5,
  RejectedLead_6,
  RejectedLead_7,
  RejectedLead_8,
  RejectedLead_9,
} from 'src/entities/rejectedLead.partition.entity';

export const LEAD_SOURCE = {
  0: 'Finance Buddha',
  1: 'FintiFi',
  2: 'SwitchMyLoan',
  3: 'LoanTap',
  4: 'Scaller Boat Finnovation LLPs',
  5: 'GoCredit',
};

export const kSelfEmployed = 'Self-Employed';
export const kAlternateSelfEmployed = 'Self Employed';

export const JOB_TYPE_NUM = {
  salaried: 0,
  [kSelfEmployed.toLowerCase()]: 1,
};

export const MIN_ACCEPTABLE_SALARY = 25000;
export const MAX_ACCEPTABLE_CIBIL = 900;
export const MIN_ACCEPTABLE_CIBIL = 650;

export const REJECTED_STATES = [
  'nagaland',
  'assam',
  'manipur',
  'arunachal pradesh',
  'tripura',
  'mizoram',
  'meghalaya',
];

export const kLeadStatusObj = {
  0: 'Accepted',
  1: 'In Process',
  2: 'Rejected',
  3: 'Disbursed',
  4: 'Existing',
  5: 'Expired',
};

export const kLeadStatus = {
  Accepted: 0,
  InProcess: 1,
  Rejected: 2,
  Disbursed: 3,
  Existing: 4,
  Expired: 5,
};

export const kLeadStatusFilterObj = {
  1: 'All (LP)',
  2: 'Accepted',
  3: 'In Process',
  4: 'Disbursed',
  5: 'All (CL)',
  6: 'Rejected',
  7: 'Existing User',
  8: 'Expired',
};

export const kJobTypeObj = {
  0: 'salaried',
  1: kSelfEmployed.toLowerCase(),
};

export const kGenderObj = {
  0: 'Male',
  1: 'Female',
};

// Reject Reasons
export const kFullNameNotExist = 'Full name does not exist!';
export const kPhoneNotExist = 'Phone does not exist!';
export const kPANNotExist = 'PAN does not exist!';
export const kSalaryNotExist = 'Salary does not exist!';
export const kCibilScoreNotExist = 'CIBIL Score does not exist!';
export const kEmailNotExist = 'Email does not exist!';
export const kDOBNotExist = 'DOB does not exist!';
export const kGenderNotExist = 'Gender does not exist!';
export const kJobTypeNotExist = 'Job Type does not exist!';

export const kFullNameInvalid = 'Full name is Invalid!';
export const kPhoneInvalid = 'Phone is Invalid!';
export const kPANInvalid = 'PAN is Invalid!';
export const kSalaryInvalid = 'Salary is Invalid!';
export const kCibilScoreInvalid = 'CIBIL Score is Invalid!';
export const kEmailInvalid = 'Email is Invalid!';
export const kDOBInvalid = 'DOB is Invalid!';
export const kPincodeInvalid = 'Pincode is Invalid!';
export const kJobTypeInvalid = 'Job Type is Invalid!';

export const kMinAge = 'Age is less than minimum required criteria!';
export const kMaxAge = 'Age is more than maximum required criteria!';

export const kMinCibilScore =
  'CIBIL Score is less than minimum required criteria!';
export const kMaxCibilScore =
  'CIBIL Score is more than maximum required criteria!';

export const kSalaryMinCriteria =
  'Salary is less than minimum required criteria!';

export const kDuplicatePAN = 'Duplicate PAN in the request body!';
export const kDuplicatePhone = 'Duplicate Phone in the request body!';

export const kPincodeNotServiceable =
  'Pincode is not serviceable in seven sister states!';
export const kStatesNotServiceable = 'Seven sister states are not serviceable!';

export const kUserPanExists = 'A user with same PAN already exists.';
export const kUserPhoneExists = 'A user with same phone already exists.';

export const kLeadPanExists = 'A lead with same PAN already exists.';
export const kLeadPhoneExists = 'A lead with same phone already exists.';

export const acceptedLeadModels = [
  AcceptedLead_0,
  AcceptedLead_1,
  AcceptedLead_2,
  AcceptedLead_3,
  AcceptedLead_4,
  AcceptedLead_5,
  AcceptedLead_6,
  AcceptedLead_7,
  AcceptedLead_8,
  AcceptedLead_9,
];

export const rejectedLeadModels = [
  RejectedLead_0,
  RejectedLead_1,
  RejectedLead_2,
  RejectedLead_3,
  RejectedLead_4,
  RejectedLead_5,
  RejectedLead_6,
  RejectedLead_7,
  RejectedLead_8,
  RejectedLead_9,
];

//#region Get Lead Table series (based on Lead's Phone number)
/// Calculate series based on sum of last two digits
/// 9876543210 => 10 => 1 + 0 => 1  => 1 (series)
/// 0123456789 => 89 => 8 + 9 => 17 => 7 (series)
export const funGetLeadTableSeries = (phone) => {
  if (!phone || isNaN(phone)) return 0;
  let numberStr = phone.toString();
  let digitsOnly = numberStr.replace(/\D/g, '');
  if (!digitsOnly?.length) return;
  const lastTwoDigits = digitsOnly.slice(-2);
  let digitSum = lastTwoDigits
    .split('')
    .reduce((sum, digit) => sum + parseInt(digit, 10), 0);
  digitSum = +digitSum.toString().slice(-1);
  return digitSum;
};
//#endregion

//#region Get key by passing search value and object
export const funGetKeyOfLeadSource = (val, targetObj) => {
  return Object.keys(targetObj).find(
    (key) => targetObj[key as keyof typeof targetObj] === val,
  );
};
//#endregion

export const LEAD_EXPIRE_DAYS = 30;
export const LEAD_DATE_RANGE_LIMIT_DAYS = 30;

export const kUserStatus = {
  NotRegistered: 0,
  Registered: 1,
  CoolOff: 2,
  Blacklist: 3,
  Disbursed: 4,
  InProcess: 5,
  Rejected: 6,
  Expired: 7,
};

export const kUserStatusObj = {
  0: 'Not registered',
  1: 'Registered',
  2: 'Cool off',
  3: 'Blacklist',
  4: 'Disbursed',
  5: 'In process',
  6: 'Rejected',
  7: 'Expired',
};

export const kDisplayUserStatus = {
  0: 'Not registered',
  1: 'Registered',
  3: 'Disbursed',
  5: 'Expired',
};
