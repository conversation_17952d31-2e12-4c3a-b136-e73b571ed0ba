export const LOAN_APPS = [
  {
    package: 'co.indiagold.gold.buy.loan',
    name: 'Gold loan at home in 30 mins',
  },
  {
    package: 'org.altruist.BajajExperia',
    name: 'Bajaj Finserv: UPI, Pay, Loans',
  },
  {
    package: 'com.dreamplug.androidapp',
    name: 'CRED: UPI & Credit Cards',
  },
  {
    package: 'com.kreditbee.android',
    name: 'KreditBee: Quick Personal Loan',
  },
  {
    package: 'com.fastbanking',
    name: 'Kissht: Instant Line of Credit',
  },
  { package: 'com.naviapp', name: 'Navi Mutual Fund, Gold & Loans' },
  {
    package: 'com.mobikwik_new.bajajfinserv',
    name: 'Bajaj Finserv Wallet',
  },
  {
    package: 'in.zestmoney.android.zestlife',
    name: 'ZestMoney - Shop on easy EMIs',
  },
  { package: 'com.portal.hcin', name: 'Home Credit: Loan App' },
  {
    package: 'com.balancehero.truebalance',
    name: 'TrueBalance- Personal Loan App',
  },
  {
    package: 'com.stashfin.android',
    name: 'Stashfin - Personal Loan App',
  },
  { package: 'com.mpokket.app', name: 'mPokket: Instant Loan App' },
  {
    package: 'com.branch_international.branch.branch_demo_android',
    name: 'Branch Personal Cash Loan App',
  },
  {
    package: 'com.freecharge.android',
    name: 'Freecharge - UPI, Pay & Loans',
  },
  {
    package: 'in.bajajfinservmarkets.app',
    name: 'Bajaj Markets Instant Loan App',
  },
  {
    package: 'indwin.c3.shareapp',
    name: 'slice: UPI · card · credit',
  },
  {
    package: 'com.whizdm.moneyview.loans',
    name: 'moneyview: Personal Loan App',
  },

  {
    package: 'com.capitalfirst',
    name: 'IDFC FIRST Bank: Instant Loans',
  },
  {
    package: 'com.snapmint.customerapp',
    name: 'Snapmint: Buy Now, Pay in EMIs',
  },
  {
    package: 'com.mycash.moneytap.app',
    name: 'MoneyTap - Credit Line & Loan',
  },
  { package: 'com.buddyloan.vls', name: 'Buddy Loan: Personal Loan' },
  {
    package: 'ng.com.fairmoney.fairmoney',
    name: 'FairMoney - Instant Loan App',
  },
  { package: 'com.flexsalary', name: 'FlexSalary Instant Loan App' },
  {
    package: 'in.india.upi.flexpay',
    name: 'FlexPay: Personal Loan App',
  },
  { package: 'in.rebase.app', name: 'SmartCoin - Personal Loan App' },
  {
    package: 'com.rufilo.user',
    name: 'Small Business Loan: Tradofina',
  },
  {
    package: 'com.innofinsolutions.instamoney',
    name: 'InstaMoney: Personal Loan App',
  },
  {
    package: 'com.tatacapital.moneyfy',
    name: 'MoneyFy-Mutual Fund, SIP, Loan',
  },
  {
    package: 'com.earlysalary.android',
    name: 'Fibe Instant Personal Loan App',
  },
  {
    package: 'com.simpl.android',
    name: 'Simpl: Shop Now. Pay Later.',
  },
  {
    package: 'com.indigo.hdfcloans',
    name: 'Loan Assist - Quick Bank Loans',
  },
  {
    package: 'com.drcash.android',
    name: 'Personal Loan - ClicKredit',
  },
  {
    package: 'com.adityabirlacapital.abconeapp',
    name: 'Aditya Birla Capital',
  },
  { package: 'in.spicemudra', name: 'Spice Money Adhikari' },
  {
    package: 'com.bankbazaar.app',
    name: 'CreditScore, Loans, CreditCard',
  },
  {
    package: 'com.whizdm.moneyview',
    name: 'Money View: Money Manager',
  },
  {
    package: 'co.tslc.cashe.android',
    name: 'CASHe Personal Loan App',
  },
  { package: 'co.afg.rupie', name: 'Personal Loan App - RapidRupee' },
  {
    package: 'com.customer.herofincorp',
    name: 'Hero FinCorp - Customer App',
  },
  {
    package: 'com.nirafinance.customer',
    name: 'NIRA Instant Personal Loan App',
  },
  {
    package: 'com.rupeeredee.app',
    name: 'RupeeRedee - Personal Loan App',
  },
  {
    package: 'com.mykredit.android',
    name: 'Personal Loan APP - My Kredit',
  },
  {
    package: 'com.herofincorp.simplycash',
    name: 'Hero FinCorp Personal Loan App',
  },
  { package: 'com.tvscs.tvscreditapp', name: 'TVS Credit Saathi' },
  {
    package: 'com.citrus.citruspay',
    name: 'LazyPay: Loan App & Pay Later',
  },
  { package: 'com.muthootfinance.imuthoot', name: 'iMuthoot' },
  {
    package: 'aepsapp.paisanikal.com.aepsandroid',
    name: 'Easy Pay - Growth for Business',
  },
  {
    package: 'cards.uni.app.credit',
    name: 'Uni : Next-gen cards & Paychek',
  },
  {
    package: 'in.loanfront.android',
    name: 'LoanFront - Personal Loan App',
  },
  {
    package: 'com.gopaysense.android.boost',
    name: 'PaySense: Personal Loan App',
  },
  { package: 'com.datasignstech.lam', name: 'MyShubhLife' },
  { package: 'com.ideopay.user', name: 'RING' },
  {
    package: 'com.twelve.club',
    name: '12% Club: Invest or Borrow@12%',
  },
];
