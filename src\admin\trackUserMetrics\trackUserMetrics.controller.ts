// Imports
import { TrackUserMetricsService } from './trackUserMetrics.service';
import { kInternalError, kSuccessData } from 'src/constants/responses';
import { Body, Controller, Get, Post, Query, Res } from '@nestjs/common';

@Controller('admin/analysis')
export class ErrorMetricsController {
  constructor(private readonly service: TrackUserMetricsService) {}

  @Post('updateStepMetrics')
  async updateStepMetrics(@Res() res) {
    try {
      const result: any = await this.service.updateStepMetrics();
      if (result?.message) return res.json(result);
      return res.json({ ...kSuccessData, data: result });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Post('getUserMetrics')
  async getUserMetrics(@Body() body, @Res() res) {
    try {
      const result: any = await this.service.getUserMetrics(body);
      if (result?.message) return res.json(result);
      return res.json({ ...kSuccessData, data: result });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Get('getCategoryMetrics')
  async getCategoryMetrics(@Query() query, @Res() res) {
    try {
      const result: any = await this.service.getCategoryMetrics(query);
      if (result?.message) return res.json(result);
      return res.json({ ...kSuccessData, data: result });
    } catch (error) {
      return res.json(kInternalError);
    }
  }
}
