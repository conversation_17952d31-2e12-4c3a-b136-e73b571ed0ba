<html>
  <head>
    <meta http-equiv='Content-Type' content='text/html; charset=UTF-8' />
    <link rel='preconnect' href='https://fonts.googleapis.com' />
    <link rel='preconnect' href='https://fonts.gstatic.com' />
    <link
      href='https://fonts.googleapis.com/css2?family=Lexend&family=Lexend+Deca:wght@200&display=swap'
      rel='stylesheet'
    />
    <style>
      body { font-family: "Lexend"; } .wrap-normal { word-wrap: normal; }
      .wrap-nowrap-cell { white-space: nowrap; } .statement-table { padding: 0
      20px; width: 100%; font-family: Lexend; max-width: 800px; margin: 0 auto;
      border-collapse: collapse; } .statement-table th, .statement-table td {
      border: 1px solid #cccccc; } th { padding: 10px; text-align: left; width:
      max-content; background-color: #f4f4f4; color: #666666; font-size: 12px;
      font-weight: normal; } td { padding: 10px; text-align: left; font-size:
      12px; } td > ul { list-style: none; padding-inline-start: 0px; } td ul ul
      { color: gray; } ul li { padding: 5px; } .custom-list { list-style-type:
      none; /* Remove default list styles */ padding-left: 0; }
      .with-dot::before { content: "•"; /* Add bullet point */ margin-right:
      5px; color: gray; /* Change color to match the text */ font-size: 1.2em;
      /* Adjust size if needed */ }
    </style>
  </head>
  <body
    style='
      margin: 0;
      padding: 0;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      border: 1px;
    '
  >
    <table
      style='
        max-width: 800px;
        width: 100%;
        margin: auto;
        border-collapse: collapse;
        font-family: Lexend;
        font-size: 12px;
        margin-bottom:100px
        position:relative;
        z-index: 10;
        background-color: #FFF;'
    >
      <tbody>
        <tr>
          <td>
            <table
              style='
                max-width: 800px;
                width: 100%;
                margin: auto;
                border-collapse: collapse;
                font-family: Lexend;
              '
            >
              <tbody>
                <tr>
                  <td style='height: 80px; padding-left: 20px'>
                    <div style='margin-top: 18px; margin-bottom: 8px'>
                      <img width='165px' height='40px' src='{{nbfcLogo}}' />
                    </div>
                  </td>
                  <td
                    style='
                      padding: 0 0px 0 0;
                      width: 400px;
                      padding-right: 20px;
                      text-align: right;
                    '
                  >
                    <div
                      style='
                        color: #666666;
                        font-size: 12px;
                        margin-bottom: 5px;
                      '
                    >
                      RBI NBFC Registered No.:
                      <span
                        style='font-weight: 600; color: #000000'
                      >{{rbiRegisterationNo}}</span>
                    </div>
                    <div style='color: #666666; font-size: 12px'>
                      CIN No.:
                      <span
                        style='font-weight: 600; color: #000000'
                      >{{cinNo}}</span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
        <tr>
          <td style='background-color: #d8d8d8; height: 1px; padding: 0px'></td>
        </tr>
        <tr>
          <td
            style='
              background-color: #e02d42;
              width: 400px;
              justify-content: end;
              height: 2px;
              float: right;
              padding: 0px;
              margin-top: -2.5px;
            '
          ></td>
        </tr>
        <tr>
          <td>
            <table
              style='
                padding: 20px;
                padding-left: 0;
                width: 100%;
                max-width: 800px;
                margin: auto;
              '
            >
              <tbody>
                <!-- sub header  -->
                <tr>
                  <td style='padding-left: 0px'>
                    <span
                      style='
                        font-weight: 600;
                        font-size: 16px;
                        letter-spacing: 0.3px;
                        vertical-align: middle;
                      '
                    >Account Statement of Loan ID:
                      <span
                        style='color: #e02d42'
                      >{{basicDetails.id}}</span></span>
                  </td>
                  <td
                    style='
                      color: #666666;
                      font-size: 14px;
                      text-align: end;
                      margin: 5px 0 0 0;
                    '
                  >
                    Generated on :
                    {{generatedDate}}
                  </td>
                </tr>
              </tbody>
            </table>
            <table class='statement-table' style='margin-bottom: 30px'>
              <tr>
                <th>A/C Name</th>
                <td>{{basicDetails.name}}</td>
                <th>Interest rate</th>
                <td>{{basicDetails.interestRate}}</td>
              </tr>
              <tr>
                <th>Contact No.</th>
                <td>+91 {{basicDetails.phone}}</td>
                <th>A/C opening date</th>
                <td>{{basicDetails.accountOpen}}</td>
              </tr>
              <tr>
                <th>Email</th>
                <td>{{basicDetails.email}}</td>
                <th>A/C close date</th>
                <td>{{basicDetails.accountClose}}</td>
              </tr>
              <tr>
                <th>Product Description</th>
                <td>{{basicDetails.productDescription}}</td>
                <th>Loan amount to borrower</th>
                <td>₹{{basicDetails.loanAmount}}</td>
              </tr>
              <tr>
                <th>Sub Product</th>
                <td>{{basicDetails.subProduct}}</td>
                <th>Disbursed amount</th>
                <td>₹{{basicDetails.disbursedAmount}}</td>
              </tr>
              <tr>
                <th>Tenure</th>
                <td>{{basicDetails.tenure}}</td>
                <th>Loan charges</th>
                <td>₹{{basicDetails.loanCharges}}</td>
              </tr>
              <tr>
                <th>City</th>
                <td>{{basicDetails.city}}</td>
                <th rowspan='2'>Address</th>
                <td rowspan='2'>{{basicDetails.address}}</td>
              </tr>
              <tr>
                <th>Pincode</th>
                <td>{{basicDetails.pinCode}}</td>
              </tr>
            </table>
            <table
              style='
                background-color: #ffffff;
                margin: auto;
                width: 100%;
                max-width: 800px;
                font-family: Lexend;
              '
            >
              <tr>
                <td colspan='2' style='text-align: start; padding: 20px 0px'>
                  <div style='font-size: 14px; letter-spacing: 0.3px'>
                    Statement for the period of
                    <span style='font-weight: 600; color: #000000'>
                      {{startDate}}</span>
                    to
                    <span style='font-weight: 600; color: #000000'>
                      {{endDate}}</span>
                  </div>
                </td>
              </tr>
            </table>
            <table class='statement-table' style='padding-bottom: 100px'>
              <thead>
                <tr>
                  {{#each ledgerFields}}
                    <th style='background-color: #2f1518; color: #fff'>
                      {{this}}
                    </th>
                  {{/each}}
                </tr>
              </thead>
              <tbody>
                {{#each disDetails as |disData index|}}
                  {{#each disData.Particulars as |item i|}}
                    {{#if disData.Title}}
                      {{#if (eq i 0)}}
                        <tr>
                          <td style='border-bottom: 0'></td>
                          <td style='border-bottom: 0'>
                            <b>{{disData.Title}}</b>
                          </td>
                          <td style='border-bottom: 0'></td>
                          <td style='border-bottom: 0'></td>
                          <td style='border-bottom: 0'></td>
                        </tr>
                      {{/if}}
                    {{/if}}
                    <tr>
                      {{#if (eq i 0)}}
                        <td
                          rowspan='{{disData.Particulars.length}}'
                          style='border-top: 0; border-bottom: 0'
                        >
                          {{disData.Date}}
                        </td>
                      {{/if}}
                      <td
                        style='border-top: 0; border-bottom: 0'
                        class='with-dot'
                      >
                        {{item}}
                      </td>
                      {{#if (eq i 0)}}
                        <td
                          rowspan='{{disData.Particulars.length}}'
                          style='border-top: 0; border-bottom: 0'
                        >
                          {{disData.UTR}}
                        </td>
                      {{/if}}
                      <td
                        style='border-top: 0; border-bottom: 0; text-align: right'
                      >
                        {{lookup disData.Amount i}}
                      </td>
                      {{#if (eq i 0)}}
                        <td
                          rowspan='{{disData.Particulars.length}}'
                          style='border-top: 0; border-bottom: 0; text-align: right'
                        >
                          {{disData.Balance}}
                        </td>
                      {{/if}}
                    </tr>
                  {{/each}}
                  {{#if (eq disData.Particulars.length 0)}}
                    <tr>
                      <td>{{disData.Date}}</td>
                      <td></td>
                      <td></td>
                      <td></td>
                      <td style='text-align: right'>{{disData.Balance}}</td>
                    </tr>
                  {{/if}}
                {{/each}}
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
    <footer
      style='
      width: 100%;
      text-align: center;
      font-family: Lexend;
      position: fixed;
      bottom: 0;
      z-index: 5;
    '
    >
      <table
        style='max-width: 800px; margin: 0 auto; width: 100%; font-family: Lexend'
      >
        <tr>
          <td
            style='
              color: #2f1518;
              width: 100%;
              padding: 10px 0;
              background-color: #ffffff;
              text-align: center;
              font-size: 12px;
            '
          >
            Contact No:
            {{helpContact}}
            <span style='padding: 0 5px'>/</span>
            Email:
            {{infoEmail}}

          </td>
        </tr>
        <tr>
          <td
            style='
              color: #ffffff;
              width: 100%;
              padding: 10px 0;
              background-color: #2f1518;
              text-align: center;
              font-size: 12px;'
          > {{nbfcAddress}}</td>
        </tr>
      </table>
    </footer>
  </body>
</html>