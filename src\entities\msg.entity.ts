// Imports
import { Table, Column, Model, DataType } from 'sequelize-typescript';

@Table({})
export class MsgEntity extends Model<MsgEntity> {
  @Column({
    primaryKey: true,
    type: DataType.STRING,
  })
  id: string;

  @Column({
    allowNull: false,
    type: DataType.STRING(32),
  })
  from: string;

  @Column({
    allowNull: false,
    comment: '1 -> RECEIVED, 2 -> SEND',
    type: DataType.SMALLINT,
  })
  type: number;

  @Column({
    allowNull: false,
    comment: '1 -> Text, 2 -> Attachment, 3 -> Button, 4 -> Interactive',
    type: DataType.SMALLINT,
  })
  subType: number;

  @Column({
    allowNull: true,
    type: DataType.TEXT,
  })
  textContent: string;

  @Column({
    allowNull: true,
    type: DataType.TEXT,
  })
  fileUrl: string;

  @Column({
    allowNull: true,
    type: DataType.TEXT,
  })
  reply: string;

  @Column({
    allowNull: true,
    type: DataType.STRING,
  })
  processing_steps: string;

  @Column({
    allowNull: true,
    type: DataType.SMALLINT,
  })
  subStep: string;
}
