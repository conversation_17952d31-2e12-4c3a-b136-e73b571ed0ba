// Imports
import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Res,
  UploadedFile,
  UseInterceptors,
  Headers,
} from '@nestjs/common';
import {
  kInternalError,
  kParamsMissing,
  kSuccessData,
} from 'src/constants/responses';
import { MiscService } from 'src/admin/misc/misc.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { kUploadFileObj } from 'src/constants/objects';
import { k500Error } from 'src/constants/misc';
import { ErrorContextService } from 'src/utils/error.context.service';
import { IPConfig } from 'src/utils/custom.decorators';

@Controller('admin/misc/')
export class MiscController {
  constructor(
    private readonly service: MiscService,
    private readonly errorContextService: ErrorContextService,
  ) {}

  @Post('createUserPermission')
  async funCreateUserPermission(@Body() body, @Headers() headers, @Res() res) {
    try {
      body.updatedBy = headers?.adminid;
      const data = await this.service.createUserPermission(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }

  @Post('editUserPermission')
  async funEditUserPermission(@Body() body, @Headers() headers, @Res() res) {
    try {
      body.updatedBy = headers?.adminid;
      const data = await this.service.editUserPermission(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }

  @Post('deleteUserPermission')
  async funDeleteUserPermission(
    @Body() body,
    @Headers() headers,
    @Res() res,
    @IPConfig() ip,
  ) {
    try {
      body.ip = ip;
      body.adminId = headers?.adminid;
      if (!body?.id) return res.json(kParamsMissing);
      const data = await this.service.deleteUserPermission(body);
      if (data == k500Error) return res.json(kInternalError);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Post('uploadFile')
  @UseInterceptors(FileInterceptor('media', kUploadFileObj()))
  async funUploadFile(@Body() body, @UploadedFile() file, @Res() res) {
    try {
      const data = await this.service.uploadFile({ ...body, file });
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }

  @Post('uploadFilePublicBucket')
  @UseInterceptors(FileInterceptor('media', kUploadFileObj()))
  async funUploadFilePublicBucket(
    @Body() body,
    @UploadedFile() file,
    @Res() res,
  ) {
    try {
      const data = await this.service.uploadFilePublicBucket({ ...body, file });
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      console.error('Error in: ', error);
      return res.send(kInternalError);
    }
  }

  @Post('uploadDatabaseBackup')
  @UseInterceptors(FileInterceptor('media', kUploadFileObj()))
  async funDBBackup(@Body() body, @UploadedFile() file, @Res() res) {
    try {
      const data = await this.service.uploadDatabaseBackup({ ...body, file });
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }

  @Get('getConfigs')
  async getConfigs(@Res() res) {
    try {
      const data: any = await this.service.getConfig();
      if (data.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }

  @Post('updateConfigs')
  async funUpdateConfig(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.updateConfig(body);
      if (data.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }
  @Post('addRBIGuidelines')
  @UseInterceptors(FileInterceptor('file'))
  async funaddRBIGuidelines(@UploadedFile() file, @Body() body, @Res() res) {
    const data: any = await this.service.addRBIGuidelines({ file, body });
    if (data.message) return res.send(data);
    return res.send({ ...kSuccessData, data });
  }
  @Get('getAllRBIGuidelines')
  async fungetAllRBIGuidelines(@Query() query, @Res() res) {
    const data: any = await this.service.getAllRBIGuidelines(query);
    if (data.message) return res.send(data);
    return res.send({ ...kSuccessData, data });
  }
  @Get('getLatestRBIGuideline')
  async funGetLatestRBIGuideline(@Query() query, @Res() res) {
    const data: any = await this.service.getLatestRBIGuideline(query);
    if (data.message) return res.send(data);
    return res.send({ ...kSuccessData, data });
  }
}
