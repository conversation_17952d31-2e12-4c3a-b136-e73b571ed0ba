// Imports
import { Inject, Injectable, forwardRef } from '@nestjs/common';
import {
  DUPLICATE_PROFILE,
  ECS_BOUNCE_CHARGE,
  GLOBAL_RANGES,
  SYSTEM_ADMIN_ID,
} from 'src/constants/globals';
import { k204Response, k500Error } from 'src/constants/misc';
import {
  k400ErrorMessage,
  k422ErrorMessage,
  kInternalError,
  kParamMissing,
} from 'src/constants/responses';
import {
  KICICIUPI,
  MIN_SALARY_CRITERIA,
  kCompleted,
  kCryptography,
  kInactiveUser,
  kNoDataFound,
  kUsrCategories,
} from 'src/constants/strings';
import { BankingEntity } from 'src/entities/banking.entity';
import { employmentDetails } from 'src/entities/employment.entity';
import { esignEntity } from 'src/entities/esign.entity';
import { KYCEntity } from 'src/entities/kyc.entity';
import { loanTransaction } from 'src/entities/loan.entity';
import { SalarySlipEntity } from 'src/entities/salarySlip.entity';
import { SubScriptionEntity } from 'src/entities/subscription.entity';
import { UserSelfieEntity } from 'src/entities/user.selfie.entity';
import { WorkMailEntity } from 'src/entities/workMail.entity';
import { EmploymentRepository } from 'src/repositories/employment.repository';
import { LoanRepository } from 'src/repositories/loan.repository';
import { LocationRepository } from 'src/repositories/location.repository';
import { MasterRepository } from 'src/repositories/master.repository';
import { SalarySlipRepository } from 'src/repositories/salarySlip.repository';
import { UserRepository } from 'src/repositories/user.repository';
import { WorkMailRepository } from 'src/repositories/workMail.repository';
import { TypeService } from 'src/utils/type.service';
import { CommonSharedService } from './common.shared.service';
import { TransactionRepository } from 'src/repositories/transaction.repository';
import { BankingSharedService } from './banking.service';
import { Op, Sequelize, where } from 'sequelize';
import { BankingRepository } from 'src/repositories/banking.repository';
import { ChangeLogsRepository } from 'src/repositories/changeLogs.repository';
import { CryptService } from 'src/utils/crypt.service';
import { MasterEntity } from 'src/entities/master.entity';
import { CrmRepository } from 'src/repositories/crm.repository';
import { FileService } from 'src/utils/file.service';
import { SequelOptions } from 'src/interfaces/include.options';
import { PredictionEntity } from 'src/entities/prediction.entity';
import { PredictionService } from 'src/admin/eligibility/prediction.service';
import { EligibilitySharedService } from './eligibility.shared.service';
import { KYCRepository } from 'src/repositories/kyc.repository';
import { UserDeleteRepository } from 'src/repositories/userDetele.repository';
import { EmiEntity } from 'src/entities/emi.entity';
import { TallyService } from 'src/admin/tally/tally.service';
import { BlockUserHistoryRepository } from 'src/repositories/user.blockHistory.repository';
import { DeviceRepository } from 'src/repositories/device.repositoy';
import { DeviceInfoInstallAppRepository } from 'src/repositories/deviceInfoInstallApp.repository';
import { PredictionRepository } from 'src/repositories/prediction.repository';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { registeredUsers } from 'src/entities/user.entity';
import { APIService } from 'src/utils/api.service';
import { nUserMigrate } from 'src/constants/network';
import { EMIRepository } from 'src/repositories/emi.repository';
import { UserServiceV4 } from 'src/v4/user/user.service.v4';
import { EmploymentHistoryRepository } from 'src/repositories/employmentHistory.repository';
import { CompanyRepository } from 'src/repositories/google.company.repository';
import { ActiveLoanAddressesEntity } from 'src/entities/activeLoanAddress.entity';

import { CibilScoreRepository } from 'src/repositories/cibil.score.repository';
import { EmploymentHistoryDetailsEntity } from 'src/entities/employment.history.entity';
import { LocationEntity } from 'src/entities/location.entity';
import { loanPurpose } from 'src/entities/loan.purpose.entity';
import { CibilScoreEntity } from 'src/entities/cibil.score.entity';
import { ProteanEntity } from 'src/entities/protean.entity';
import { HashPhoneEntity } from 'src/entities/hashPhone.entity';
import { fcmTokenEntity } from 'src/entities/fcmTokenEntity.entity';
import { RedisService } from 'src/redis/redis.service';
import { ErrorContextService } from 'src/utils/error.context.service';
import { GoogleFileEntity } from 'src/entities/google_file_entity';
import { GoogleCompanyResultEntity } from 'src/entities/company.entity';
import { EnvConfig } from 'src/configs/env.config';
import { LeadTrackingRepository } from 'src/repositories/leadTracking.repository';
import { LeadTrackingEntity } from 'src/entities/leadTracking.entity';
import { crmDisposition } from 'src/entities/crmDisposition.entity';
import { crmTitle } from 'src/entities/crmTitle.entity';
import { FetchDataEntity } from 'src/entities/fetchData.entity';
import { disbursementEntity } from 'src/entities/disbursement.entity';
import { LeadTrackingServices } from 'src/thirdParty/leadTracking/leadTracking.service';
import {
  acceptedLeadModels,
  funGetLeadTableSeries,
  kLeadStatus,
  LEAD_DATE_RANGE_LIMIT_DAYS,
  LEAD_EXPIRE_DAYS,
  rejectedLeadModels,
} from 'src/constants/leadTracking';
import { CibilScoreArchive } from 'src/entities/cibilScoreArchive.entity';
import { CRYPT_PATH } from 'src/constants/paths';

import * as fs from 'fs';
import { LegalCollectionRepository } from 'src/repositories/legal.collection.repository';
import { LegalCollectionEntity } from 'src/entities/legal.collection.entity';
import { DateService } from 'src/utils/date.service';
@Injectable()
export class MigrationSharedService {
  startTime;
  constructor(
    private readonly empRepo: EmploymentRepository,
    private readonly kycRepo: KYCRepository,
    private readonly loanRepo: LoanRepository,
    private readonly masterRepo: MasterRepository,
    private readonly locationRepo: LocationRepository,
    private readonly salarySlipRepo: SalarySlipRepository,
    private readonly userRepo: UserRepository,
    private readonly workMailRepo: WorkMailRepository,
    private readonly transRepo: TransactionRepository,
    private readonly typeService: TypeService,
    private readonly commonSharedService: CommonSharedService,
    private readonly tallyService: TallyService,
    @Inject(forwardRef(() => BankingSharedService))
    private readonly sharedBanking: BankingSharedService,
    private readonly userDeleteRepo: UserDeleteRepository,
    private readonly cryptService: CryptService,
    private readonly api: APIService,
    // Repositories
    private readonly bankingRepo: BankingRepository,
    private readonly changLogsRepo: ChangeLogsRepository,
    private readonly crmRepo: CrmRepository,
    private readonly deviceRepo: DeviceRepository,
    private readonly deviceAppInfoRepo: DeviceInfoInstallAppRepository,
    private readonly repoManager: RepositoryManager,
    private readonly leadTrackingRepo: LeadTrackingRepository,
    private readonly legalRepo: LegalCollectionRepository,
    // Utils
    private readonly fileService: FileService,
    // Shared services
    private readonly predictionService: PredictionService,
    @Inject(forwardRef(() => EligibilitySharedService))
    private readonly sharedEligibility: EligibilitySharedService,
    private readonly blockUserHistoryRepo: BlockUserHistoryRepository,
    private readonly predictionRepo: PredictionRepository,
    private readonly emiRepo: EMIRepository,
    @Inject(forwardRef(() => UserServiceV4))
    private readonly userService: UserServiceV4,
    private readonly employmentHistoryRepository: EmploymentHistoryRepository,
    private readonly companyRepository: CompanyRepository,
    private readonly cibilScoreRepo: CibilScoreRepository,
    private readonly redisService: RedisService,
    private readonly errorContextService: ErrorContextService,
    private readonly leadTrackingService: LeadTrackingServices,
    private readonly dateService: DateService,
  ) {}

  async migrateTov3(reqData) {
    try {
      return {};
      const userId = reqData.userId;
      if (!userId) return kParamMissing('userId');
      const toDay = this.typeService.getGlobalDate(new Date());
      // Get user data

      // Selfie
      const selfieInclude: any = { model: UserSelfieEntity };
      selfieInclude.attributes = ['status'];
      // KYC
      const kycInclude: any = { model: KYCEntity };
      kycInclude.attributes = [
        'aadhaarRejectReason',
        'aadhaarStatus',
        'kycCompletionDate',
        'panRejectReason',
        'panStatus',
        'updatedAt',
      ];
      // Work mail
      const workMailInclude: any = { model: WorkMailEntity };
      workMailInclude.attributes = [
        'approveById',
        'id',
        'rejectReason',
        'status',
      ];
      // Salary slip
      const salarySlipInclude: any = { model: SalarySlipEntity };
      salarySlipInclude.attributes = [
        'approveById',
        'id',
        'rejectReason',
        'status',
      ];
      // Employment
      const empInclude: any = { model: employmentDetails };
      empInclude.attributes = [
        'companyStatusApproveById',
        'companyVerification',
        'id',
        'rejectReason',
        'verifiedDate',
        'salary',
      ];
      empInclude.include = [salarySlipInclude, workMailInclude];
      // Bank
      const bankInclude: any = { model: BankingEntity };
      bankInclude.attributes = [
        'rejectReason',
        'salaryVerification',
        'salaryVerificationDate',
      ];
      // Subscription
      const subscriptionInclude: any = { model: SubScriptionEntity };
      subscriptionInclude.attributes = ['status', 'updatedAt'];
      // eSign
      const eSignInclude: any = { model: esignEntity };
      eSignInclude.attributes = ['status', 'updatedAt'];
      // Loan
      const loanInclude: any = { model: loanTransaction };
      loanInclude.attributes = [
        'loan_disbursement',
        'loan_disbursement_date',
        'id',
        'loanStatus',
        'predictionId',
        'remark',
        'verifiedDate',
        'manualVerification',
      ];
      loanInclude.include = [bankInclude, eSignInclude, subscriptionInclude];
      const include = [empInclude, kycInclude, loanInclude, selfieInclude];
      const attributes = [
        'createdAt',
        'id',
        'homeStatus',
        'pin',
        'residenceRejectReason',
        'quantity_status',
        'contactRejectReason',
        'NextDateForApply',
      ];
      const options = { include, where: { id: userId } };
      const userData = await this.userRepo.getRowWhereData(attributes, options);
      if (userData == k500Error) return kInternalError;
      if (!userData) return k422ErrorMessage(kNoDataFound);

      const statusData = {
        phone: -1,
        permission: -1,
        selfie: -1,
        email: -1,
        pan: -1,
        basic: -1,
        personal: -1,
        professional: -1,
        pin: -1,
        aadhaar: -1,
        company: -1,
        workMail: -1,
        salarySlip: -1,
        bank: -1,
        loan: -2,
        eMandate: -1,
        eSign: -1,
        disbursement: -1,
      };
      statusData.permission = 1;
      statusData.phone = +userData.phoneStatusVerified;
      statusData.basic = 1;
      statusData.email = +userData.emailStatusVerified;
      statusData.selfie = +(userData.selfieData?.status ?? '-1');
      statusData.personal = -1;

      statusData.pin = userData.pin ? 1 : -1;
      statusData.pan = +(userData.kycData?.panStatus ?? '-1');
      statusData.aadhaar = +(userData.kycData?.aadhaarStatus ?? '-1');
      statusData.company = +(
        userData.employmentData?.companyVerification ?? '-1'
      );
      const workMailData = userData.employmentData?.workMail ?? {};
      statusData.workMail = +(workMailData.status ?? '-1');
      const salarySlipData = userData.employmentData?.salarySlip ?? {};
      statusData.salarySlip = +(salarySlipData.status ?? '-1');

      const registeredDate = new Date(userData.createdAt).getTime();
      const dates = {
        registration: registeredDate,
        basicDetails: registeredDate,
        professionalDetails: registeredDate,
        aadhaar: 0,
        pan: 0,
        employment: 0,
        banking: 0,
        eligibility: 0,
        eMandate: 0,
        eSign: 0,
        disbursement: 0,
      };
      const otherInfo = { salaryInfo: 0 };

      // Employment rejection
      otherInfo.salaryInfo = parseInt(
        (+(userData?.employmentData?.salary ?? '0').toString()).toFixed(),
      );
      if (!otherInfo.salaryInfo) statusData.professional = -1;
      else statusData.professional = 1;
      // Aadhaar and pan verification date
      const kycData = userData.kycData;
      if (kycData) {
        const kycCompletionDate = kycData.kycCompletionDate;
        if (kycCompletionDate) {
          dates.aadhaar = new Date(kycCompletionDate).getTime();
          dates.pan = new Date(kycCompletionDate).getTime();
        } else {
          dates.aadhaar = kycData.updatedAt.getTime();
          dates.pan = kycData.updatedAt.getTime();
        }
      }
      // Company verification date
      const empData = userData.employmentData;
      if (empData) {
        if (empData.verifiedDate)
          dates.employment = empData.verifiedDate.getTime();
      }

      const rejection: any = {};
      // Aadhaar and pan rejections
      if (kycData) {
        rejection.aadhaar = kycData.aadhaarRejectReason ?? '';
        rejection.pan = kycData.panRejectReason ?? '';
      }
      // Employment rejection
      rejection.company = userData.employmentData?.rejectReason ?? '';
      // Work mail rejection
      rejection.workMail = workMailData.rejectReason ?? '';
      // Salary slip rejection
      rejection.salarySlip = workMailData.salarySlip ?? '';

      // Status data before loan
      const registrationData = {
        status: statusData,
        dates,
        rejection,
        otherInfo,
        empId: null,
        companyAdminId: null,
        salarySlipId: null,
        salarySlipAdminId: null,
        workMailId: null,
        workMailAdminId: null,
      };
      // Foreign keys
      if (empData) {
        registrationData.empId = empData.id;
        registrationData.companyAdminId = empData.companyStatusApproveById;
      }
      if (workMailData) {
        registrationData.workMailId = workMailData.id;
        registrationData.workMailAdminId = workMailData.approveById;
      }
      if (salarySlipData) {
        registrationData.salarySlipId = salarySlipData.id;
        registrationData.salarySlipAdminId = workMailData.approveById;
      }

      const loanList = userData.loanData ?? [];
      loanList.sort((a, b) => a.id - b.id);

      if (loanList.length <= 0) {
        let interestRate: any =
          await this.commonSharedService.getEligibleInterestRate({ userId });
        if (interestRate?.message)
          interestRate = GLOBAL_RANGES.MIN_PER_DAY_INTEREST_RATE;
        const creationData = {
          interestRate: interestRate.toString(),
          userId,
        };
        const loanData = await this.loanRepo.createRowData(creationData);
        if (!loanData || loanData == k500Error) return kInternalError;
        const masterData = {
          dates: registrationData.dates,
          loanId: loanData.id,
          rejection,
          status: registrationData.status,
          userId,
          empId: registrationData.empId,
          workMailId: registrationData.workMailId,
          salarySlipId: registrationData.salarySlipId,
          companyAdminId: registrationData.companyAdminId,
          workMailAdminId: registrationData.workMailAdminId,
          salarySlipAdminId: registrationData.salarySlipAdminId,
          otherInfo: registrationData.otherInfo,
        };
        const createMaster = await this.masterRepo.createRowData(masterData);
        if (!createMaster || createMaster === k500Error) return kInternalError;

        // Update user data
        let updatedData = { masterId: createMaster.id };
        let updateResult = await this.userRepo.updateRowData(
          updatedData,
          userId,
        );
        if (updateResult == k500Error) return kInternalError;

        // Update loan data
        updateResult = await this.loanRepo.updateRowData(
          updatedData,
          loanData.id,
        );
        if (updateResult == k500Error) return kInternalError;

        // Update emp data
        if (empData.id) {
          updateResult = await this.empRepo.updateRowData(
            updatedData,
            empData.id,
          );
          if (updateResult == k500Error) return kInternalError;
        }

        // Update work mail data
        if (workMailData.id) {
          updateResult = await this.workMailRepo.updateRowData(
            updatedData,
            workMailData.id,
          );
          if (updateResult == k500Error) return kInternalError;
        }

        // Update salary slip data
        if (salarySlipData.id) {
          updateResult = await this.salarySlipRepo.updateRowData(
            updatedData,
            salarySlipData.id,
          );
          if (updateResult == k500Error) return kInternalError;
        }
      }

      for (let index = 0; index < loanList.length; index++) {
        try {
          const loanData = loanList[index];
          const masterData = {
            dates: registrationData.dates,
            loanId: loanData.id,
            rejection,
            status: registrationData.status,
            userId,
            empId: registrationData.empId,
            workMailId: registrationData.workMailId,
            salarySlipId: registrationData.salarySlipId,
            companyAdminId: registrationData.companyAdminId,
            workMailAdminId: registrationData.workMailAdminId,
            salarySlipAdminId: registrationData.salarySlipAdminId,
          };

          if (index == loanList.length - 1) {
            if (userData.NextDateForApply != null) {
              masterData['coolOffData'] = {
                count: 1,
                coolOffEndsOn: this.typeService
                  .getGlobalDate(userData.NextDateForApply)
                  .toJSON(),
                coolOffStartedOn: toDay.toJSON(),
              };
            }
          }
          masterData.status['eligibility'] = +loanData?.manualVerification;
          // Loan status
          const loanStatus = loanData.loanStatus;
          masterData.status.loan = -1;
          if (loanStatus == 'Rejected') {
            masterData.status.loan = 2;
            masterData.status['eligibility'] = 2;
          } else if (loanStatus == 'Complete') masterData.status.loan = 7;
          else if (loanStatus == 'Active') masterData.status.loan = 6;
          else if (loanStatus == 'Accepted' && loanData?.manualVerification) {
            if (loanData?.manualVerification == '5') {
              masterData.status.loan = 4;
              masterData.status['eligibility'] = 4;
            } else masterData.status.loan = +loanData?.manualVerification;
          }
          // Loan date
          if (loanData.verifiedDate)
            dates.eligibility = new Date(loanData.verifiedDate).getTime();
          // Loan rejection
          masterData.rejection.eligibility = loanData.remark ?? '';

          // Banking status
          const bankingData = loanData.bankingData ?? {};
          const salaryVerification = +(bankingData?.salaryVerification ?? '-1');
          masterData.status.bank = salaryVerification;
          // Banking date
          if (bankingData.salaryVerificationDate)
            dates.banking = new Date(
              bankingData.salaryVerificationDate,
            ).getTime();

          // Reference status
          const isReferenceSubmitted = loanData.predictionId != null;

          // eMandate status
          const subscriptionData = loanData.subscriptionData ?? {};
          const subscriptionStatus = (
            subscriptionData.status ?? 'INITIALIZED'
          ).toUpperCase();
          if (
            ['ON_HOLD', 'ACTIVE', 'BANK_APPROVAL_PENDING'].includes(
              subscriptionStatus,
            )
          )
            masterData.status.eMandate = 1;
          else if (subscriptionStatus == 'FAILED')
            masterData.status.eMandate = 2;
          // eSign date
          if (subscriptionData.updatedAt)
            dates.eMandate = new Date(subscriptionData.updatedAt).getTime();

          // eSign status
          const eSignData = loanData.eSignData ?? { status: '-1' };
          masterData.status.eSign = +eSignData.status;
          // eSign date
          if (eSignData.updatedAt)
            dates.eSign = new Date(eSignData.updatedAt).getTime();

          // Disbursement status
          const isDisbursed = loanData.loan_disbursement != null;
          masterData.status.disbursement = isDisbursed ? 1 : -1;
          // Disbursement date
          if (loanData.loan_disbursement_date) {
            dates.disbursement = new Date(
              loanData.loan_disbursement_date,
            ).getTime();
          }

          // const att = ['id'];
          // const options = { where: { loanId: loanData.id } };
          // const find = await this.masterRepo.getRowWhereData(att, options);
          // if (find === k500Error) continue;
          // const id = find?.id;
          // // Create master data
          // const createdData = id
          //   ? await this.masterRepo.updateRowData(masterData, id)
          //   : await this.masterRepo.createRowData(masterData);
          // if (createdData == k500Error) continue;
          let upsert = await this.masterRepo.upsert(masterData, {
            conflictFields: ['loanId'],
          });
          if (upsert.statusCode == k500Error) continue;
          // Update user data
          let updatedData = { masterId: upsert[0].id };
          let updateResult;
          if (loanList.length - 1 == index) {
            updateResult = await this.userRepo.updateRowData(
              updatedData,
              userId,
            );
            if (updateResult == k500Error) continue;
          }

          // Update loan data
          updateResult = this.loanRepo.updateRowData(updatedData, loanData.id);
          //if (updateResult == k500Error) continue;

          // Update emp data
          if (empData.id) {
            updateResult = this.empRepo.updateRowData(updatedData, empData.id);
            //if (updateResult == k500Error) continue;
          }

          // Update work mail data
          if (workMailData.id) {
            updateResult = this.workMailRepo.updateRowData(
              updatedData,
              workMailData.id,
            );
            //if (updateResult == k500Error) continue;
          }

          // Update salary slip data
          if (salarySlipData.id) {
            updateResult = this.salarySlipRepo.updateRowData(
              updatedData,
              salarySlipData.id,
            );
            if (updateResult == k500Error) continue;
          }

          //await this.userService.routeDetails({ id: userId });
        } catch (error) {}
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async userMigration(query) {
    let count = 0;
    const apiList = ['http://localhost:3006/v3/user/userMigration?page='];
    for (let index = 0; index < 55; index++) {
      try {
        let url = apiList[count];
        url += index;
        console.log(count, url);
        count++;
        if (count >= apiList.length) {
          await this.typeService.delay(120000);
          count = 0;
        }
      } catch (error) {}
    }
  }

  async startPagination(offset, limit, page) {
    try {
      const data = await this.userRepo.getTableWhereData(['id'], {
        offset,
        limit,
        order: [['id']],
      });
      for (let index = 0; index < data.length; index++) {
        if (index % 100 == 0) console.log(index, page);
        const userId = data[index].id;
        // const res = await this.migrateTov3({ userId });
        // // if (res) console.log(res, userId);
        // await this.userService.routeDetails({ id: userId });
      }
      await this.typeService.delay(100000);
      console.log('data.length', data.length);
      return data;
    } catch (error) {
      console.log(error);
    }
  }

  // Adding last location in master data if not exists
  async migrateLastLocation(userId) {
    const rawQuery = `SELECT "createdAt", "lat", "location", "long"
      FROM "LocationEntities" 
      WHERE "userId" = '${userId}' ORDER BY ID DESC LIMIT 1;`;
    const outputList = await this.repoManager.injectRawQuery(
      LocationEntity,
      rawQuery,
      { source: 'REPLICA' },
    );
    if (outputList == k500Error) throw new Error();

    const locationData = outputList[0];
    if (locationData == k500Error) throw new Error();

    if (!locationData) return { lastLocation: '-', lastLocationDateTime: '-' };

    const lastLocation = locationData.location ?? '-';
    const lastLocationDateTime = locationData.createdAt?.getTime();

    // Get master data
    const masterAttr = ['id', 'miscData'];
    const masterOptions = { order: [['id', 'DESC']], where: { userId } };
    let masterData = await this.masterRepo.getRowWhereData(
      masterAttr,
      masterOptions,
    );
    if (masterData == k500Error) throw new Error();

    // In case data not migrated to v3
    if (!masterData) {
      await this.migrateTov3({ userId });
      masterData = await this.masterRepo.getRowWhereData(
        masterAttr,
        masterOptions,
      );
      if (masterData == k500Error) return kInternalError;
      if (!masterData) return k422ErrorMessage(kNoDataFound);
    }

    // Update master data
    const miscData = masterData.miscData ?? {};
    miscData.lastLocation = lastLocation;
    miscData.lastLocationDateTime = lastLocationDateTime;
    miscData.lastLat = +locationData.lat;
    miscData.lastLong = +locationData.long;
    const updatedData = { miscData };
    const updateResponse = await this.masterRepo.updateRowData(
      updatedData,
      masterData.id,
    );
    if (updateResponse == k500Error) return kInternalError;

    return { lastLocation, lastLocationDateTime };
  }

  async migrateMasterLoanRejactByBank(reqData) {
    try {
      const attributes = ['id', 'subscriptionDate'];
      const options = { where: { completionDate: 'nullT10:00:00.000Z' } };

      const transList = await this.transRepo.getTableWhereData(
        attributes,
        options,
      );
      if (transList?.message) return transList;

      for (let index = 0; index < transList.length; index++) {
        try {
          const transData = transList[index];
          if (!transData.subscriptionDate) continue;

          const updatedData = { completionDate: transData.subscriptionDate };
          await this.transRepo.updateRowData(updatedData, transData.id);
        } catch (error) {}
      }
      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async refreshUserStage(reqData) {
    const userIds = reqData.userIds;
    try {
      const options = { where: { id: { [Op.in]: userIds } } };
      let userList = await this.userRepo.getTableWhereData(['id'], options);
      console.log(userList.length);
      if (userList == k500Error) return userList;
      userList = userList.map((el) => el.id);

      return { userIds: userList };
    } catch (error) {}
  }

  async checkNReVerifyBank(body) {
    try {
      const loanIds = body?.loanIds ?? [];
      if (loanIds.length === 0) return [];
      const bankingInclude: any = { model: BankingEntity };
      bankingInclude.attributes = [
        'accountDetails',
        'bankStatement',
        'consentResponse',
      ];
      bankingInclude.where = { loanId: loanIds };
      const include = [bankingInclude];
      const attributes = ['id', 'userId'];
      const options = { include, where: { loanStatus: 'InProcess' } };

      const loanList = await this.loanRepo.getTableWhereData(
        attributes,
        options,
      );
      if (loanList == k500Error) return kInternalError;

      const userIds = [];
      for (let index = 0; index < loanList.length; index++) {
        try {
          const loanData = loanList[index];
          const userId = loanData.userId;
          const bankingData = loanData.bankingData ?? {};

          const preparedData = {
            filePath: bankingData.bankStatement,
            userId,
            additionalURLs: [],
            accountDetails: JSON.parse(bankingData.accountDetails),
          };
          if (!preparedData.filePath && bankingData.consentResponse) {
            const response = JSON.parse(bankingData.consentResponse);
            preparedData.filePath = response.pdfURL;
          }
          console.log(loanData.id, userId, index);
          const response = await this.sharedBanking.validateEligibility(
            preparedData,
          );
          console.log('response', response);
          userIds.push(userId);
        } catch (error) {}
      }
      return { userIds };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // Migrating salary date from banking table and change logs table to loan table
  async migrateVerifiedSalaryDate() {
    try {
      const attributes = ['id'];
      const options = {
        where: {
          loanStatus: { [Op.or]: ['Active', 'Complete'] },
          verifiedSalaryDate: { [Op.eq]: null },
        },
      };
      const loanList = await this.loanRepo.getTableWhereData(
        attributes,
        options,
      );
      if (loanList == k500Error) return kInternalError;

      for (let index = 0; index < loanList.length; index++) {
        try {
          const loanData = loanList[index];
          const loanId = loanData.id;
          const salaryDate = await this.getApprovedSalaryDate(loanId);
          if (salaryDate?.message) continue;
          if (!salaryDate) continue;

          const updatedData = { verifiedSalaryDate: salaryDate };
          await this.loanRepo.updateRowData(updatedData, loanId);
          console.log(index, loanList.length);
        } catch (error) {}
      }

      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async oldCrmToLastCrmInUser() {
    try {
      let attributes: any = ['id'];
      let options: any = {
        order: [['updatedAt', 'DESC']],
        where: { lastCrm: { [Op.eq]: null }, loanStatus: 3 },
      };

      const userList = await this.userRepo.getTableWhereData(
        attributes,
        options,
      );
      if (userList == k500Error) return kInternalError;

      const userIds = userList.map((el) => el.id);
      attributes = [[Sequelize.fn('MAX', Sequelize.col('id')), 'id']];
      options = {
        group: [['id', 'userId']],
        where: { userId: { [Op.in]: userIds } },
      };
      // Get last crm
      let crmList: any = await this.crmRepo.getTableWhereData(
        attributes,
        options,
      );
      if (crmList == k500Error) return kInternalError;
      const crmIds = crmList.map((el) => el.id);
      options = { order: [['id', 'DESC']], where: { id: crmIds } };
      crmList = await this.crmRepo.getTableWhereData(null, options);
      if (crmList == k500Error) return kInternalError;

      for (let index = 0; index < userList.length; index++) {
        const userData = userList[index];
        const userId = userData.id;
        const crmData = crmList.find((el) => el.userId == userId);
        if (!crmData || !userId) continue;
        const relationData = crmData.relationData ?? {};
        const lastCrm = {
          amount: crmData.amount,
          loanId: crmData.loanId,
          reason: crmData.reason,
          remark: crmData.remark,
          status: crmData.status,
          titleId: crmData.titleId,
          crmOrder: crmData.crmOrder,
          due_date: crmData.due_date,
          reasonId: crmData.crmReasonId,
          statusId: relationData?.statusId ?? crmData.crmStatusId,
          adminName: (
            await this.commonSharedService.getAdminData(crmData.adminId)
          ).fullName,
          createdAt: crmData?.createdAt
            ? new Date(crmData.createdAt).toJSON()
            : '',
          titleName: 'Phone Busy',
          categoryId: crmData.categoryId,
          reasonName: 'Medical Issue',
          statusName: relationData?.statusName ?? '',
          dispositionId: relationData?.dispositionId,
          referenceName: crmData.referenceName ?? '',
          settlementData: crmData.settlementData,
          adminDepartment: '',
          dispositionName: relationData?.dispositionName ?? '',
        };
        const updatedData = { lastCrm };
        await this.userRepo.updateRowData(updatedData, userId);
      }
      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async getApprovedSalaryDate(loanId) {
    try {
      const attributes = ['newData'];
      const options = {
        order: [['id', 'DESC']],
        where: { newData: { [Op.ne]: '-' }, loanId, type: 'Salary Date' },
      };
      const changedData = await this.changLogsRepo.getRowWhereData(
        attributes,
        options,
      );
      if (changedData == k500Error) return kInternalError;
      if (changedData && !isNaN(changedData.newData))
        return +changedData.newData;
      const bankAttr = ['salaryDate'];
      const bankOptions = { order: [['id', 'DESC']], where: { loanId } };
      const bankData = await this.bankingRepo.getRowWhereData(
        bankAttr,
        bankOptions,
      );
      if (bankData == k500Error) return kInternalError;
      if (!bankData) return k422ErrorMessage('No bankData found');
      return bankData.salaryDate;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region migrate tansaction who date not in global
  async migrateTansactionCompletionDate() {
    try {
      const options = {
        where: {
          completionDate: { [Op.notLike]: '%T10:00:00.000Z' },
          status: 'COMPLETED',
        },
        order: [['id']],
      };
      const att = ['id', 'completionDate'];
      const result = await this.transRepo.getTableWhereData(att, options);
      if (!result || result === k500Error) return kInternalError;
      for (let index = 0; index < result.length; index++) {
        try {
          const data = result[index];
          if (!data?.completionDate) continue;
          const completionDate = this.typeService
            .getGlobalDate(new Date(data.completionDate))
            .toJSON();
          const update = await this.transRepo.updateRowData(
            { completionDate },
            data.id,
            true,
          );
          console.log(update, data.id);
        } catch (error) {}
      }
    } catch (error) {}
  }
  //#endregion

  // migration api for adminId to FollowerId in Transaction
  async migrateFollowerIdInTransaction(reqData) {
    try {
      const attributes = ['id', 'adminId'];
      const options = { where: { followerId: null, adminId: { [Op.ne]: 37 } } };
      const transList = await this.transRepo.getTableWhereData(
        attributes,
        options,
      );
      if (transList?.message) return transList;
      const finalData = {};
      for (let index = 0; index < transList.length; index++) {
        try {
          const trans = transList[index];
          const followerId = trans?.adminId;
          if (!followerId) continue;
          if (finalData[followerId]) finalData[followerId].push(trans.id);
          else finalData[followerId] = [trans.id];
        } catch (error) {}
      }
      const keys = Object.keys(finalData);
      for (let index = 0; index < keys.length; index++) {
        const key = keys[index];
        try {
          const update = await this.transRepo.updateRowData(
            { followerId: +key },
            finalData[key],
            true,
          );
          console.log(key, update);
        } catch (error) {}
      }
      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // migrate phone and email in allPhone & allEmail
  async migrateAllPhoneEmail() {
    try {
      const options = {
        where: {
          [Op.or]: [
            { allPhone: { [Op.eq]: null } },
            { allEmail: { [Op.eq]: null } },
          ],
        },
        limit: 1000,
      };
      const att = ['id', 'phone', 'email'];
      const result = await this.userRepo.getTableWhereData(att, options);
      if (result === k500Error) return kInternalError;
      const length = result.length;
      for (let index = 0; index <= length; index++) {
        try {
          const ele = result[index];
          const id = ele.id;
          const phone = this.cryptService.decryptPhone(ele?.phone);
          const email = ele?.email;
          const updateData = {
            allPhone: phone ? [phone] : [],
            allEmail: email ? [email] : [],
          };
          await this.userRepo.updateRowData(updateData, id, true);
        } catch (error) {}
      }
      if (length != 0) await this.migrateAllPhoneEmail();
      return result;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // migrate not updated WorkMail Status
  async migrateWorkMailStatus() {
    try {
      const options = {
        where: { status: { workMail: 0, loan: { [Op.or]: [6, 7] } } },
      };
      const masterData = await this.masterRepo.getTableWhereData(
        ['id'],
        options,
      );
      if (masterData === k500Error) return kInternalError;
      const masterIds = masterData.map((f) => f.id);
      const masterInc = { model: MasterEntity, attributes: ['id', 'status'] };
      const attr = ['id', 'status', 'masterId'];
      const ops = {
        where: { masterId: masterIds },
        include: [masterInc],
        order: [['id', 'DESC']],
      };
      const mailData = await this.workMailRepo.getTableWhereData(attr, ops);
      if (mailData === k500Error) return kInternalError;
      const workMailData = [];
      mailData.forEach((work) => {
        try {
          const find = workMailData.find((f) => f.masterId == work.masterId);
          if (!find) workMailData.push(work);
        } catch (error) {}
      });
      const statusPainding: any = [];
      const length = workMailData.length;
      for (let index = 0; index < length; index++) {
        try {
          const workDT = workMailData[index];
          const workMailId = workDT.id;
          const master = workDT.masterData;
          const masterId = master.id;
          const status = master.status;
          const workMailStatus = workDT?.status;
          const updateMaster: any = {};
          console.log('masterId', masterId);
          if (workMailStatus == '0') {
            status.workMail = 3;
            updateMaster.status = status;
            statusPainding.push(workMailId);
          } else {
            status.workMail = +workMailStatus;
            updateMaster.status = status;
          }
          await this.masterRepo.updateRowData(updateMaster, masterId, true);
        } catch (error) {}
      }
      console.log('statusPainding', statusPainding);
      const updateWorkEmail = { status: '3' };
      await this.workMailRepo.updateRowData(
        updateWorkEmail,
        statusPainding,
        true,
      );
      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  async resetAffectedUsers() {
    try {
      const attributes = ['id', 'phone', 'fullName'];

      const options = {
        where: {
          id: [
            'f0e6ab66-9c11-4cee-949b-732e14a0aa08',
            '296887d8-a2d8-44bf-963a-a6085d876991',
          ],
          stage: 19,
        },
      };

      const userList = await this.userRepo.getTableWhereData(
        attributes,
        options,
      );
      if (userList == k500Error) return kInternalError;

      const finalizedList = [];
      for (let index = 0; index < userList.length; index++) {
        const userData = userList[index];
        finalizedList.push({
          userId: userData.id,
          name: userData.fullName ?? '',
          phone: this.cryptService.decryptPhone(userData.phone),
        });
      }

      const rawExcelData = {
        sheets: ['Affected users'],
        data: [finalizedList],
        sheetName: 'Affected users.xlsx',
        needFindTuneKey: true,
      };
      const url: any = await this.fileService.objectToExcelURL(rawExcelData);
      if (url?.message) return url;
      return { fileUrl: url };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async migrateRejectReason() {
    try {
      const loanInc = {
        model: loanTransaction,
        attributes: ['id', 'remark', 'manualVerificationAcceptId'],
        where: { loanStatus: 'Rejected', remark: null, declineId: null },
      };
      const empApprove = ['salaried professional', 'salaried', 'consultant'];
      const ops = {
        where: { otherInfo: { employmentInfo: { [Op.notIn]: empApprove } } },
        include: [loanInc],
        order: [['id', 'DESC']],
      };
      const attr = ['loanId', 'otherInfo'];
      const masterData = await this.masterRepo.getTableWhereData(attr, ops);
      if (masterData === k500Error) return kInternalError;
      for (let index = 0; index < masterData.length; index++) {
        try {
          const master = masterData[index];
          const loan = master?.loanData;
          const loanId = loan?.id;
          const adminId = loan?.manualVerificationAcceptId ?? SYSTEM_ADMIN_ID;
          const empInfo = master?.otherInfo?.employmentInfo;
          let reason;
          if (empInfo == 'student') {
            reason = 'Not eligible employment sector';
          } else if (
            empInfo == 'self-employed' ||
            empInfo == 'retired' ||
            empInfo == 'homemaker'
          )
            reason = 'User is not salaried';
          const obj: any = {
            manualVerificationAcceptId: adminId,
            manualVerification: '2',
          };
          if (reason) obj.remark = reason;
          console.log('UPDATE:', index, loanId);
          await this.loanRepo.updateRowData(obj, loanId, true);
        } catch (error) {}
      }
      return masterData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // migrate old user's categorization due to employement startdate missing
  async migrateUserCategorization() {
    try {
      const predictionIncl = {
        model: PredictionEntity,
        attributes: ['id', 'categorizationTag'],
        where: { categorizationTag: { [Op.eq]: null } },
      };
      const ops = {
        where: { loanStatus: { [Op.in]: ['Active', 'Complete'] } },
        include: [predictionIncl],
        order: [['id', 'DESC']],
      };
      const attr = ['id'];
      const loanData = await this.loanRepo.getTableWhereData(attr, ops);
      if (loanData === k500Error) return kInternalError;
      const length = loanData.length;
      console.log(length);
      // return loanData;
      for (let index = 0; index < loanData.length; index++) {
        try {
          const loan = loanData[index];
          const loanId = loan?.id;
          const data = {
            loanId,
            type: kUsrCategories,
            updateScore: true,
            migrate: true,
          };
          const tagData = await this.sharedEligibility.calculateScore(data);
          if (tagData?.message) continue;
        } catch (error) {}
      }
      return loanData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async migrateCgstAndSgstAmount() {
    try {
      const loanData = await this.loanRepo.getTableWhereData(
        ['id', 'charges'],
        { where: { loanStatus: { [Op.ne]: 'Rejected' } } },
      );
      if (loanData === k500Error) return kInternalError;

      const length = loanData.length;
      console.log('Total loan', length);
      for (let index = 0; index < length; index++) {
        try {
          const loan = loanData[index];
          const charges = loan?.charges;
          const gst_amt = charges?.gst_amt;

          if (
            gst_amt !== undefined &&
            (!charges?.cgst_amt || !charges?.sgst_amt)
          ) {
            const subGST = parseFloat((gst_amt / 2).toFixed(2));
            charges.cgst_amt = subGST;
            charges.sgst_amt = subGST;
            if (loan?.id)
              await this.loanRepo.updateRowData({ charges }, loan.id, true);
          }
        } catch (error) {}
      }
      return loanData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //delete error files
  async migrateDeleteErrorFiles() {
    try {
      const attributes = ['id', 'errorFileCount', 'errorFiles'];
      const options: any = {
        where: {
          errorFileCount: { [Op.ne]: 0 },
        },
      };
      const data = await this.userDeleteRepo.getTableWhereData(
        attributes,
        options,
      );
      if (data === k500Error) return kInternalError;

      for (let index = 0; index < data.length; index++) {
        try {
          const errorFiles: any = [];
          const ele = data[index];
          for (let j = 0; j < ele?.errorFiles.length; j++) {
            try {
              const res = await this.fileService.deleteCloudFile(
                ele?.errorFiles[j],
              );
              if (res != k204Response) errorFiles.push(ele?.errorFiles[j]);
            } catch (error) {}
            const Rupdated2: any = {};
            Rupdated2.errorFileCount = errorFiles.length;
            Rupdated2.errorFiles = errorFiles;
            await this.userDeleteRepo.updateRowData(Rupdated2, ele.id);
          }
        } catch (error) {}
      }
      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //migrate loanCompletionDate
  async migrateLoanCompletionDate() {
    try {
      const emiInc = {
        model: EmiEntity,
        attributes: ['payment_done_date'],
        where: { payment_done_date: { [Op.ne]: null }, partOfemi: 'LAST' },
      };
      const ops = {
        where: {
          loanStatus: 'Complete',
          loanCompletionDate: { [Op.eq]: null },
        },
        include: [emiInc],
      };
      const attr = ['id', 'loanCompletionDate'];
      const loanData = await this.loanRepo.getTableWhereData(attr, ops);
      if (loanData === k500Error) return kInternalError;
      const length = loanData.length;
      if (length == 0) return {};
      console.log(length);
      for (let index = 0; index < length; index++) {
        try {
          const loan = loanData[index];
          const loanId = loan?.id;
          const emi = loan?.emiData ?? [];
          const doneDate = emi[0]?.payment_done_date;
          const loanCompletionDate = doneDate
            ? this.typeService.getGlobalDate(doneDate).toJSON()
            : null;
          await this.loanRepo.updateRowData(
            { loanCompletionDate },
            loanId,
            true,
          );
        } catch (error) {}
      }
      return loanData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async migrateLoanBalancedStatus(body) {
    try {
      let startDate = body?.startDate;
      let endDate = body?.endDate;
      startDate = this.typeService.getGlobalDate(startDate).toJSON();
      endDate = this.typeService.getGlobalDate(endDate).toJSON();

      const attributes = ['id', 'loanStatus'];
      const options: any = {
        where: {
          loan_disbursement_date: {
            [Op.gte]: startDate,
            [Op.lte]: endDate,
          },
          loanStatus: 'Complete',
        },
      };
      const loanData = await this.loanRepo.getTableWhereData(
        attributes,
        options,
      );
      if (loanData === k500Error) return kInternalError;

      const length = loanData.length;
      console.log('l', length);

      for (let i = 0; i < length; i++) {
        const ele = loanData[i];
        const balanceDetails: any =
          await this.tallyService.getLedgerLoanDetails({ loanId: ele?.id });
        if (balanceDetails.balance > 100) {
          const update: any = { isNotBalanced: 1 };
          await this.loanRepo.updateRowData(update, ele.id, true);
        }
      }
      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  // migrate aadharLatlong  in aadharLatlongPoint in KYC
  async migrateKYCLatLongToPoint() {
    try {
      //get user which aadhaarLatLong is not null and empty
      const options = {
        where: {
          aadhaarLatLong: { [Op.or]: { [Op.notIn]: [null, ''] } },
          aadhaarLatLongPoint: { [Op.eq]: null },
        },
      };
      const att = ['id', 'aadhaarLatLong'];
      const kycData = await this.kycRepo.getTableWhereData(att, options);
      if (kycData === k500Error) return kInternalError;

      const update_data = {};

      /// pre pare the data
      for (let i = 0; i < kycData.length; i++) {
        try {
          const element = kycData[i];
          const aadhaarLatLong = element?.aadhaarLatLong;
          if (aadhaarLatLong) {
            const latLngObject = JSON.parse(aadhaarLatLong);
            const lat = latLngObject['lat'];
            const lng = latLngObject['lng'];
            if (lat !== undefined && lng !== undefined) {
              const id = element.id;
              const key = `${lat},${lng}`;
              if (update_data[key]) update_data[key].push(id);
              else update_data[key] = [id];
            }
          }
        } catch (error) {}
      }

      /// update the data

      const keys = Object.keys(update_data);
      for (let index = 0; index < keys.length; index++) {
        try {
          const key = keys[index];
          const id = update_data[key];
          if (index % 100 === 0) console.log(index, new Date());
          const update = { aadhaarLatLongPoint: key };
          await this.kycRepo.updateRowData(update, id, true);
        } catch (error) {}
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //migrate  aadhaarResponse column address related keys to aadhaarAddress column in KYCentities
  async migrateAaddharResponseToAadhaarAddress() {
    try {
      const kycAtr = ['id', 'aadhaarAddress', 'aadhaarResponse', 'kyc_mode'];
      const kycOptions = {
        where: {
          kyc_mode: { [Op.or]: ['ZOOP', 'LENDITT'] },
        },
      };

      const kycData = await this.kycRepo.getTableWhereData(kycAtr, kycOptions);
      if (kycData === k500Error) return kInternalError;
      for (let index = 0; index < kycData.length; index++) {
        try {
          const ele = kycData[index];
          const aadhaarAddressObj = JSON.parse(ele?.aadhaarAddress);
          const aadhaarResponseObj = JSON.parse(ele?.aadhaarResponse);
          const id = ele?.id;
          if (
            aadhaarAddressObj?.dist === '' &&
            aadhaarResponseObj?.districtName !== ''
          ) {
            aadhaarAddressObj.dist = aadhaarResponseObj?.districtName;
          }
          if (
            aadhaarAddressObj?.state === '' &&
            aadhaarResponseObj?.stateName !== ''
          ) {
            aadhaarAddressObj.state = aadhaarResponseObj?.stateName;
          }
          if (
            aadhaarAddressObj?.po === '' &&
            aadhaarResponseObj?.poName !== ''
          ) {
            aadhaarAddressObj.po = aadhaarResponseObj?.poName;
          }
          if (
            aadhaarAddressObj?.loc === '' &&
            aadhaarResponseObj?.locality !== ''
          ) {
            aadhaarAddressObj.loc = aadhaarResponseObj?.locality;
          }
          if (
            aadhaarAddressObj?.vtc === '' &&
            aadhaarResponseObj?.vtcName !== ''
          ) {
            aadhaarAddressObj.vtc = aadhaarResponseObj?.vtcName;
          }
          if (
            aadhaarAddressObj?.subdist === '' &&
            aadhaarResponseObj?.subDistrictName !== ''
          ) {
            aadhaarAddressObj.subdist = aadhaarResponseObj?.subDistrictName;
          }
          if (
            aadhaarAddressObj?.street === '' &&
            aadhaarResponseObj?.street !== ''
          ) {
            aadhaarAddressObj.street = aadhaarResponseObj?.street;
          }
          if (
            aadhaarAddressObj?.house === '' &&
            aadhaarResponseObj?.building !== ''
          ) {
            aadhaarAddressObj.house = aadhaarResponseObj?.building;
          }

          const updatedData = {
            aadhaarAddress: JSON.stringify(aadhaarAddressObj),
          };
          await this.kycRepo.updateRowData(updatedData, id, true);
        } catch (error) {}
      }
      return { Res: 'allDataUpdated' };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // start region  user remove from blacklis and cooloff
  async migrateCoolOffUSer() {
    try {
      //get blackuser and coolOffUser with respect of resonId
      const opt = {
        where: { reasonId: { [Op.or]: [53, 55] }, blockedBy: SYSTEM_ADMIN_ID },
      };
      const getBlacklistUser =
        await this.blockUserHistoryRepo.getTableWhereData(['userId'], opt);
      if (getBlacklistUser === k500Error) return kInternalError;
      let userIds = [...new Set(getBlacklistUser.map((el) => el.userId))];
      if (!userIds.length) return [];
      let userOption: any = { where: { id: userIds } };
      let masterInclude: any = { model: MasterEntity };
      masterInclude.attributes = ['userId', 'coolOffData'];
      userOption.include = [masterInclude];
      //get coolOff User blockuser and coolOffUser
      const userData = await this.userRepo.getTableWhereData(
        ['id', 'isBlacklist', 'phone'],
        userOption,
      );
      if (userData === k500Error) return kInternalError;
      let filteredData = [];
      for (let i = 0; i < userData?.length; i++) {
        try {
          const ele = userData[i];
          let updateData;
          const userId = ele?.id;
          const mobileNumber = this.cryptService.decryptPhone(ele?.phone);
          let isBlacklist = ele?.isBlacklist;
          let coolOffData = ele?.masterData?.coolOffData;
          if (isBlacklist === '1') {
            isBlacklist = '0';
            updateData = await this.userRepo.updateRowWhereData(
              { isBlacklist },
              { where: { id: userId } },
            );
            if (updateData === k500Error) continue;
            filteredData.push({ userId, mobileNumber });
          }
          if (
            !(
              coolOffData.coolOffEndsOn == '' ||
              coolOffData.coolOffStartedOn == ''
            )
          ) {
            coolOffData.count = 0;
            coolOffData.coolOffEndsOn = '';
            coolOffData.coolOffStartedOn = '';
            updateData = await this.masterRepo.updateRowWhereData(
              { coolOffData },
              { where: { userId } },
            );
            if (updateData === k500Error) continue;
            filteredData.push({ userId, mobileNumber });
          }
        } catch (error) {}
      }
      return filteredData;
    } catch (error) {}
  }
  //#endregion

  //migrate loan feesIncome
  async migrateLoanfeesIncome() {
    try {
      const opts = {
        where: {
          feesIncome: 0,
          loanStatus: { [Op.or]: ['Active', 'Complete'] },
        },
      };
      const loanData = await this.loanRepo.getTableWhereData(
        ['id', 'loanFees', 'charges', 'loanCompletionDate'],
        opts,
      );
      if (loanData === k500Error) return kInternalError;
      const length = loanData?.length;
      console.log('Total Loan', length);

      for (let i = 0; i < length; i++) {
        try {
          const loan = loanData[i];
          const loanId = loan?.id;
          console.log('loanId', loanId);
          const loanFees = +(loan?.loanFees ?? 0);
          const charges = loan?.charges ?? {};
          const gstAmt = +(charges?.gst_amt ?? 0);
          if (loanFees && gstAmt) {
            const feesIncome: any = Math.round(loanFees - gstAmt);
            await this.loanRepo.updateRowData({ feesIncome }, loanId, true);
            console.log(loanId, 'feesIncome', feesIncome);
          }
        } catch (error) {}
      }
      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // migrate user data
  async migrateNewUserData(reqData) {
    try {
      const lspId = reqData?.lspId;
      if (!lspId) return kParamMissing('lspId');
      const locationData = reqData?.locationList;
      if (!locationData) return kParamMissing('locationList');
      const deviceData = reqData?.deviceList;
      if (!deviceData) return kParamMissing('deviceList');
      const deviceInfoData = reqData?.deviceInfoList;
      if (!deviceInfoData) return kParamMissing('deviceInfoList');
      const userBasicDetails = reqData?.basicData;
      if (!userBasicDetails) return kParamMissing('basicData');

      const userDetails = await this.userRepo.getRowWhereData(['id'], {
        where: { lspId },
      });
      if (userDetails === k500Error) return kInternalError;
      if (!userDetails) return k422ErrorMessage(kNoDataFound);

      const masterData = await this.masterRepo.getRowWhereData(['otherInfo'], {
        where: {
          userId: userDetails.id,
        },
      });
      if (!masterData) return k422ErrorMessage(kNoDataFound);
      masterData.otherInfo.salaryInfo = userBasicDetails?.salary;

      locationData.forEach((location) => {
        location.userId = userDetails.id;
      });

      deviceData.forEach((device) => {
        device.userId = userDetails.id;
      });

      deviceInfoData.forEach((deviceInfo) => {
        deviceInfo.userId = userDetails.id;
      });
      const [
        locationDetails,
        deviceDetails,
        deviceInfoDetails,
        userData,
        updateMasterData,
      ]: any = await Promise.all([
        // location details
        this.locationRepo.bulkCreate(locationData),

        // device details
        this.deviceRepo.bulkCreate(deviceData),

        // device info details
        this.deviceAppInfoRepo.bulkCreate(deviceInfoData),

        // user basic details
        this.userRepo.updateRowWhereData(
          {
            isSalaried: userBasicDetails?.isSalaried,
            salaryMode: userBasicDetails?.salaryMode,
          },
          {
            where: {
              id: userDetails.id,
            },
          },
        ),

        this.masterRepo.updateRowWhereData(masterData, {
          where: {
            userId: userDetails.id,
          },
        }),
      ]);

      if (
        locationDetails === k500Error ||
        deviceDetails === k500Error ||
        deviceInfoDetails === k500Error ||
        userData === k500Error ||
        updateMasterData == k500Error
      )
        return kInternalError;
      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async migrateExistingUser(reqData) {
    try {
      const rowQ = `SELECT "phone", "createdAt", "updatedAt" FROM public."registeredUsers" where "appType" = '0' AND "lspId" is null AND "uniqueId" <= ${reqData.max} AND "uniqueId" > ${reqData.min} order by "uniqueId" desc limit 1000`;
      console.log(rowQ);
      const userDetails = await this.repoManager.injectRawQuery(
        registeredUsers,
        rowQ,
      );
      if (userDetails.length == 0) return 'all completed';
      if (userDetails == k500Error)
        console.log('userDetails error', userDetails);
      console.log('userDetails', userDetails.length);
      const encryptedPhone = [];
      userDetails.map((user) => {
        encryptedPhone.push(user.phone);
        user.phone = this.cryptService.decryptPhone(user.phone);
      });
      const url = nUserMigrate;
      const lspIds = await this.api.requestPost(url, userDetails);
      console.log('lspIds', lspIds?.data?.length);
      lspIds.data.map((user, index) => {
        user.phone = encryptedPhone[index];
      });
      const query = lspIds.data
        .map(
          (entry) =>
            `UPDATE public."registeredUsers" SET "lspId" = '${entry.id}' WHERE phone = '${entry.phone}';`,
        )
        .join('');
      const queryData = await this.repoManager.injectRawQuery(
        registeredUsers,
        query,
      );
      if (queryData == k500Error) return k500Error;
      await this.migrateExistingUser(reqData);
      return true;
    } catch (error) {
      console.log('error', error);
      return kInternalError;
    }
  }

  async migrateExistingUserV2(reqData) {
    try {
      const rowQ = `SELECT id, "phone", "createdAt", "updatedAt" FROM public."registeredUsers" where "appType" = '0' AND "lspId" is null AND "uniqueId" <= ${reqData.max} AND "uniqueId" > ${reqData.min} order by "uniqueId" desc limit 1000`;
      const userDetails = await this.repoManager.injectRawQuery(
        registeredUsers,
        rowQ,
      );
      if (userDetails.length == 0) return 'all completed';
      if (userDetails == k500Error)
        console.log('userDetails error', userDetails);
      console.log('userDetails', userDetails.length);
      userDetails.map((user) => {
        user.phone = this.cryptService.decryptPhone(user.phone);
      });
      const url = nUserMigrate;
      const lspIds = await this.api.requestPost(url, {
        targetList: userDetails,
      });
      console.log('lspIds', lspIds?.data?.length);
      for (let index = 0; index < lspIds?.data.length; index++) {
        try {
          const element = lspIds.data[index];
          const res = await this.userRepo.updateRowData(
            { lspId: element.lspId },
            element.userId,
            true,
          );
        } catch (error) {
          console.log('errorS', error);
        }
      }
      await this.migrateExistingUserV2(reqData);
      return true;
    } catch (error) {
      console.log('error', error);
      return kInternalError;
    }
  }

  // migrate loan reject reason
  async migrateLoanRejectRemark(body) {
    try {
      const userId = body?.userIds ?? [];
      const loanInc = {
        model: loanTransaction,
        attributes: [
          'id',
          'userId',
          'loanStatus',
          'loanRejectReason',
          'remark',
          'declineId',
        ],
      };
      const userData = await this.userRepo.getTableWhereData(['id'], {
        where: { id: userId },
        include: [loanInc],
      });
      if (userData === k500Error) return kInternalError;
      const length = userData?.length;
      console.log('Total Loan', length);

      for (let i = 0; i < length; i++) {
        try {
          const user = userData[i];
          const loanData = user?.loanData;
          const loanDT = loanData.filter(
            (el) =>
              !el?.declineId &&
              !el?.loanRejectReason &&
              !el?.remark &&
              el?.loanStatus == 'Rejected',
          );
          for (let index = 0; index < loanDT.length; index++) {
            const loan = loanDT[index];
            const loanId = loan?.id;
            const dwId = loanId - 1;
            const upId = loanId + 1;
            const dwLoan = loanData.find((f) => f.id == dwId);
            const upLoan = loanData.find((f) => f.id == upId);
            let remark = kInactiveUser;
            if (dwLoan || upLoan) {
              console.log('loanId', loanId);
              remark = 'Duplicate entry';
            }
            // await this.loanRepo.updateRowData({ remark }, loanId, true);
          }
        } catch (error) {}
      }
      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async migrateKycData(offset = 0, limit = 1000) {
    try {
      const attributes = ['id', 'aadhaarNumber'];
      const options = {
        where: {
          aadhaarNumber: { [Op.ne]: null },
          aadhaarNo: { [Op.eq]: null },
          [Op.and]: {
            id: { [Op.lt]: 200 },
          },
        },
        limit: 1000,
      };

      const rowQ = `SELECT "id", PGP_SYM_DECRYPT(CAST("aadhaarNumber" AS BYTEA), 'LENDITT')
      AS "aadhaarNumber" FROM "KYCEntities" AS "KYCEntity"
      WHERE "KYCEntity"."aadhaarNumber" IS NOT NULL AND "KYCEntity"."aadhaarNo" IS NULL AND id <= ${
        offset + limit
      } AND id > ${offset}`;
      const response = await this.repoManager.injectRawQuery(KYCEntity, rowQ);
      if (response == k500Error) {
        console.log(rowQ, { response });
        await this.migrateKycData(offset + limit, limit);
      }
      console.log(offset, offset + limit, response.length);
      ///code here

      let finalizedData: any = [];
      for (let data of response) {
        let customerRefId: number;
        let aadhaarNo: Text;
        if (!data?.aadhaarNumber.includes('test')) {
          customerRefId = data?.aadhaarNumber
            ? Number(data.aadhaarNumber.substring(0, 8))
            : null;
          aadhaarNo = data?.aadhaarNumber
            ? this.cryptService.getMD5Hash(data.aadhaarNumber)
            : null;
        } else {
          const extractAadhaarNo = data?.aadhaarNumber.slice(9) ?? null;
          customerRefId = extractAadhaarNo
            ? Number(extractAadhaarNo.substring(0, 8))
            : null;
          aadhaarNo = extractAadhaarNo
            ? this.cryptService.getMD5Hash(extractAadhaarNo)
            : null;
        }

        const updatedData = {
          id: data.id,
          customerRefId,
          aadhaarNo,
        };
        if (
          updatedData.customerRefId == null ||
          updatedData.aadhaarNo == null ||
          isNaN(updatedData.customerRefId)
        ) {
          console.log('data', data);
          continue;
        }
        finalizedData.push(updatedData);
      }
      console.log('final', finalizedData.length);
      const updateQueries = finalizedData
        .map(
          (update) => `
            UPDATE public."KYCEntities" 
            SET "customerRefId" = '${update.customerRefId}', "aadhaarNo" = '${update.aadhaarNo}' 
            WHERE id = '${update.id}';
        `,
        )
        .join('');

      const updateAadhar = await this.repoManager.injectRawQuery(
        KYCEntity,
        updateQueries,
      );
      if (updateAadhar == k500Error) return kInternalError;
      // if (length !== 0) await this.migrateKycData();

      if (response.length !== 0)
        await this.migrateKycData(offset + limit, limit);
      console.log('completeddd');
      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // async migratePredictionData() {
  //   try {
  //     const attributes = ['loanId', 'id'];
  //     const options = {
  //       where: {
  //         [Op.or]: [
  //           Sequelize.literal(
  //             `"automationDetails"->'message'->>'message' = 'INTERNAL_SERVER_ERROR'`,
  //           ),
  //           Sequelize.literal(
  //             `"ml_approval"->'message'->>'message' = 'INTERNAL_SERVER_ERROR'`,
  //           ),
  //         ],
  //       },
  //       limit: 100,
  //       order: [['id', 'DESC']],
  //     };
  //     const loanIds: any = await this.predictionRepo.getTableWhereData(
  //       attributes,
  //       options,
  //     );
  //     if (loanIds?.message) return kInternalError;

  //     const updateQueries = promiseData
  //       .map(
  //         (update) => `
  //           UPDATE public."PredictionEntities"
  //       SET "automationDetails" = '${JSON.stringify(
  //         update.data1,
  //       )}',"ml_approval" = '${JSON.stringify(update.data2)}'
  //       WHERE "id" = '${update.id}';
  //       `,
  //       )
  //       .join('');

  //     const updateData = await this.repoManager.injectRawQuery(
  //       PredictionEntity,
  //       updateQueries,
  //     );
  //     if (updateData == k500Error) return kInternalError;
  //     if (loanIds.length !== 0) await this.migratePredictionData();
  //     return true;
  //   } catch (error) {
  //     console.log({ error });
  //     return kInternalError;
  //   }
  // }

  async migratePredictionData(offset = 115279, limit = 100) {
    try {
      const rowQ = `SELECT id, "loanId"
      FROM public."PredictionEntities"
      where ("automationDetails"->'message'->>'message' = 'INTERNAL_SERVER_ERROR' or "ml_approval"->'message'->>'message' = 'INTERNAL_SERVER_ERROR') AND id <= ${
        offset + limit
      } AND id > ${offset}`;
      const loanIds: any = await this.repoManager.injectRawQuery(
        PredictionEntity,
        rowQ,
      );
      if (loanIds?.message) return kInternalError;
      console.log(offset, offset + limit, loanIds.length);

      // let finalData = [];
      // for (let loanIdKey of loanIds) {
      //   const transactionData: any =
      //     await this.predictionService.getTransctionDataByLoanId(
      //       loanIdKey['loanId'],
      //     );
      //   if (!transactionData) return true;

      //   const [data1, data2]: any = await Promise.all([
      //     this.predictionService.predictRepaymentStatusFromPY(
      //       loanIdKey['loanId'],
      //       transactionData,
      //     ),
      //     this.predictionService.predictLoanApprovalStatusFromPY(
      //       loanIdKey['loanId'],
      //       transactionData,
      //     ),
      //   ]);
      //   finalData.push({ data1, data2, id: loanIdKey.id });
      // }
      const promises = loanIds.map(async (loanIdKey) => {
        const transactionData =
          await this.predictionService.getTransctionDataByLoanId(
            loanIdKey['loanId'],
          );
        if (!transactionData)
          return { data1: null, data2: null, id: loanIdKey.id };

        const [data1, data2] = await Promise.all([
          this.predictionService.predictRepaymentStatusFromPY(
            loanIdKey['loanId'],
            transactionData,
          ),
          this.predictionService.predictLoanApprovalStatusFromPY(
            loanIdKey['loanId'],
            transactionData,
          ),
        ]);

        return { data1, data2, id: loanIdKey.id };
      });

      const promiseData = await Promise.all(promises);
      const updateQueries = promiseData
        .map(
          (update) => `
            UPDATE public."PredictionEntities"
        SET "automationDetails" = '${JSON.stringify(
          update.data1,
        )}',"ml_approval" = '${JSON.stringify(update.data2)}'
        WHERE "id" = '${update.id}';
        `,
        )
        .join('');

      const updateData = await this.repoManager.injectRawQuery(
        PredictionEntity,
        updateQueries,
      );
      if (updateData == k500Error) return kInternalError;
      if (loanIds.length !== 0)
        await this.migratePredictionData(offset + limit, limit);
      return true;
    } catch (error) {
      console.log({ error });
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async migrateIntrestRate(query) {
    if (!query?.readOnly) return kParamMissing('readOnly status');
    const tag = query?.readOnly;
    const userInclude = {
      model: registeredUsers,
      attributes: ['id', 'fullName'],
    };
    const masterInclude = {
      model: MasterEntity,
      attributes: ['id'],
      where: { status: { bank: { [Op.in]: ['1', '3'] } } },
      include: [userInclude],
    };
    const option = {
      where: {
        loanStatus: { [Op.in]: ['Accepted', 'InProcess'] },
        esign_id: null,
      },
      include: [masterInclude],
    };
    const loanList = await this.loanRepo.getTableWhereDataWithCounts(
      ['id', 'loanStatus', 'userId'],
      option,
    );
    if (loanList == k500Error) throw new Error();

    if (tag == 'true') return loanList;
    const finalData = [];
    for (let i = 0; i < loanList.rows.length; i++) {
      try {
        const ele = loanList.rows[i];
        const loanId = ele?.id;
        const userId = ele?.userId;

        if (loanId && userId) {
          await this.sharedEligibility.checkLoanEligibility({
            loanId,
            userId,
          });
          finalData.push({ loanId, userId });
        }
      } catch (error) {}
    }
    return finalData;
  }

  //start region migrate upi payments mismatched date data
  async migrateUpiMismatchedDateData(body) {
    try {
      const startDate = body?.startDate;
      const endDate = body?.endDate;
      const transAtr = ['id', 'emiId', 'loanId', 'response', 'type'];
      const transOptions = {
        where: {
          source: KICICIUPI,
          status: kCompleted,
          completionDate: { [Op.gte]: startDate, [Op.lte]: endDate },
          [Op.or]: [{ type: 'EMIPAY' }, { type: 'FULLPAY' }],
        },
        order: [['id', 'ASC']],
      };
      const transactionData = await this.transRepo.getTableWhereData(
        transAtr,
        transOptions,
      );
      if (transactionData === k500Error) return kInternalError;
      for (let i = 0; i < transactionData.length; i++) {
        try {
          const ele = transactionData[i];
          const transId = ele?.id;
          const emiId = ele?.emiId;
          const loanId = ele?.loanId;
          const type = ele?.type;
          const responseObj = JSON.parse(ele?.response);
          const originalTxnCompletionDate = responseObj?.TxnCompletionDate;
          const year = originalTxnCompletionDate.slice(0, 4);
          const month = originalTxnCompletionDate.slice(4, 6);
          const day = originalTxnCompletionDate.slice(6, 8);
          const hour = originalTxnCompletionDate.slice(8, 10);
          const minute = originalTxnCompletionDate.slice(10, 12);
          const second = originalTxnCompletionDate.slice(12, 14);
          let paymentDate = new Date(
            `${year}-${month}-${day}T${hour}:${minute}:${second}`,
          );
          paymentDate.setMinutes(paymentDate.getMinutes());
          paymentDate = this.typeService.getGlobalDate(paymentDate);
          await this.transRepo.updateRowData(
            { completionDate: paymentDate.toJSON() },
            transId,
            true,
          );
          if (type === 'EMIPAY' && emiId) {
            await this.emiRepo.updateRowData(
              { payment_done_date: paymentDate.toJSON() },
              emiId,
              true,
            );
          }
          if (type === 'FULLPAY') {
            const emiAtr = ['id', 'pay_type'];
            const emiOptions = { where: { loanId } };
            const emiData = await this.emiRepo.getTableWhereData(
              emiAtr,
              emiOptions,
            );
            if (emiData === k500Error) return kInternalError;
            emiData.sort((a, b) => a.id - b.id);
            emiData.forEach(async (el) => {
              if (el?.pay_type === 'FULLPAY') {
                await this.emiRepo.updateRowData(
                  { payment_done_date: paymentDate.toJSON() },
                  el?.id,
                  true,
                );
              }
            });
          }
        } catch (error) {}
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  // migrate user to bank under verification for loan offer screen
  async migrateUserFromFVtoBank() {
    const masterInc: any = {
      model: MasterEntity,
      attributes: ['id', 'status'],
      where: {
        status: {
          loan: { [Op.in]: [-1, 0] },
          bank: { [Op.in]: [1, 3] },
        },
      },
    };
    const bankInc: any = {
      model: BankingEntity,
      attributes: ['id', 'salaryVerification'],
    };
    const attr = ['id', 'userId'];
    const options: any = {
      where: { loanStatus: { [Op.or]: ['InProcess', 'Accepted'] } },
      include: [masterInc, bankInc],
    };
    const loanData = await this.loanRepo.getTableWhereData(attr, options);
    if (loanData == k500Error) throw new Error();

    console.log(loanData);

    const length = loanData.length;
    // for (let i = 0; i < length; i++) {
    //   try {
    //     const ele = loanData[i];
    //     const masterData = ele.masterData;
    //     const bankData = ele.bankingData;
    //     const status = masterData.status;
    //     const updatedData = {
    //       manualVerification: -1,
    //       loanStatus: 'InProcess',
    //     };
    //     await this.loanRepo.updateRowData(updatedData, ele.id, true);
    //     status.loan = -1;
    //     status.eligibility = -1;
    //     status.bank = 0;
    //     const masterUpdatedData = { status };
    //     await this.masterRepo.updateRowData(
    //       masterUpdatedData,
    //       masterData.id,
    //       true,
    //     );

    //     await this.bankingRepo.updateRowData(
    //       { salaryVerification: '0' },
    //       bankData.id,
    //     );
    //     await this.userService.routeDetails({ id: ele.userId });
    //   } catch (error) {}
    // }
    return {};
  }

  async migrateUserPhone(offset = 0, limit = 1000, length = 0) {
    try {
      length = length + limit;
      const attr = ['id', 'uniqueId', 'phone', 'createdAt'];
      const option = {
        limit,
        where: {
          phone: { [Op.ne]: null },
          hashPhone: { [Op.eq]: null },
          uniqueId: { [Op.and]: [{ [Op.gt]: offset }, { [Op.lte]: length }] },
        },
        order: [['uniqueId', 'ASC']],
      };
      let userDetails = await this.userRepo.getTableWhereData(attr, option);
      if (!userDetails || userDetails == k500Error) return kInternalError;
      for (let i = 0; i < userDetails.length; i++) {
        const userData = userDetails[i];
        const decryptedPhone = this.cryptService.decryptPhone(userData.phone);
        const hashPhone = this.cryptService.getMD5Hash(decryptedPhone);
        const data = {
          hashPhone,
        };
        const users = await this.userRepo.updateRowData(
          data,
          userData.id,
          true,
        );
        if (users == k500Error) {
          await this.blockDuplicateUsers(userData);
        }
      }

      if (userDetails.length != 0) {
        return await this.migrateUserPhone(offset + limit, limit, length);
      }
      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async blockDuplicateUsers(user) {
    try {
      const blockUsersId = [];
      const blockUsersHistory = [];
      let userProfileResult: any;
      const usersWithEmail = [];
      const withoutEmailUser = [];

      const tail = user.phone.split('===')[1];
      const masterInclude = {
        model: MasterEntity,
        attributes: ['status', 'dates'],
      };
      const attr = ['id', 'phone', 'hashPhone', 'email', 'lastOnlineTime'];
      const option = {
        where: {
          phone: { [Op.like]: '%' + tail },
        },
        include: masterInclude,
        order: [['id']],
      };
      const duplicates = await this.userRepo.getTableWhereData(attr, option);
      if (duplicates == k500Error) return kInternalError;

      const addToBlockHistory = (user) => {
        blockUsersId.push(user.id);
        blockUsersHistory.push({
          isBlackList: 1,
          userId: user.id,
          blockedBy: SYSTEM_ADMIN_ID,
          reasonId: 64,
          reason: DUPLICATE_PROFILE,
        });
      };

      const updateProfile = (user) => {
        if (user.hashPhone) {
          return;
        }
        const decryptedPhone = this.cryptService.decryptPhone(user.phone);
        const hashPhone = this.cryptService.getMD5Hash(decryptedPhone);

        return {
          data: { hashPhone },
          id: user.id,
        };
      };

      await Promise.all(
        duplicates.map(async (user) => {
          const aadhaar = user?.masterData.status.aadhaar;
          if (aadhaar == '1') {
            userProfileResult = await updateProfile(user);
          } else {
            if (user.email) {
              usersWithEmail.push(user);
            } else {
              withoutEmailUser.push(user);
            }
          }
        }),
      );

      if (usersWithEmail.length > 1) {
        const latestUser = usersWithEmail.reduce((maxUser, currentUser) => {
          const maxUserDate = Math.max(
            ...Object.values(maxUser.masterData.dates).map(
              (val: number) => val,
            ),
          );
          const currentUserDate = Math.max(
            ...Object.values(currentUser.masterData.dates).map(
              (val: number) => val,
            ),
          );
          return currentUserDate >= maxUserDate ? currentUser : maxUser;
        });
        await Promise.all(
          usersWithEmail.map((user) => {
            if (user.id !== latestUser.id) {
              addToBlockHistory(user);
            }
          }),
        );

        userProfileResult
          ? addToBlockHistory(latestUser)
          : (userProfileResult = await updateProfile(latestUser));
      } else if (usersWithEmail.length == 1) {
        userProfileResult
          ? addToBlockHistory(usersWithEmail[0])
          : (userProfileResult = await updateProfile(usersWithEmail[0]));
      }

      if (withoutEmailUser.length > 1) {
        const latestUser = withoutEmailUser.reduce((maxUser, currentUser) => {
          const maxUserTime = new Date(maxUser.lastOnlineTime);
          const currentUserTime = new Date(currentUser.lastOnlineTime);
          return currentUserTime >= maxUserTime ? currentUser : maxUser;
        });

        await Promise.all(
          withoutEmailUser.map((user) => {
            if (user.id !== latestUser.id) {
              addToBlockHistory(user);
            }
          }),
        );

        userProfileResult
          ? addToBlockHistory(latestUser)
          : (userProfileResult = await updateProfile(latestUser));
      } else if (withoutEmailUser.length == 1) {
        addToBlockHistory(withoutEmailUser[0]);
      }

      const data = {
        isBlacklist: '1',
        hashPhone: null,
      };
      const options = {
        where: {
          id: { [Op.in]: blockUsersId },
        },
        silent: true,
      };
      const updateDuplicate = await this.userRepo.updateRowWhereData(
        data,
        options,
      );
      if (updateDuplicate == k500Error) return kInternalError;
      if (userProfileResult) {
        const { data, id } = userProfileResult;
        const updateUserProfile = await this.userRepo.updateRowData(
          data,
          id,
          true,
        );
        if (updateUserProfile == k500Error) return kInternalError;
      }
      await this.blockUserHistoryRepo.bulkCreate(blockUsersHistory);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async migrationCltvData(body) {
    console.log('migrationCltvData start');
    let unMatchCompany = [];
    const loanList = body?.loanList;
    const status = body.status ?? [
      'InProcess',
      'Accepted',
      'Active',
      'Complete',
    ];
    const attr = ['id', 'createdAt', 'userId'];
    const opt: any = {
      where: { companyId: { [Op.eq]: null }, loanStatus: status },
      limit: 5000,
    };
    if (loanList) opt.where.id = loanList;
    const data = await this.loanRepo.getTableWhereDataWithCounts(attr, opt);
    if (data == k500Error) throw new Error();
    const userIds: any = data.rows.map((row) => row.userId);
    console.log('userIds', userIds.length);

    const attributes = ['userId', 'companyName', 'createdAt'];
    const options = { where: { userId: userIds } };

    let missingComp: any =
      await this.employmentHistoryRepository.getTableWhereData(
        attributes,
        options,
      );
    if (missingComp == k500Error) throw new Error();

    const value: any = await this.empRepo.getTableWhereData(
      attributes,
      options,
    );
    if (value == k500Error) throw new Error();
    if (value) missingComp = [...missingComp, ...value];

    console.log('missingComp', missingComp.length);

    const companyNames = new Set();
    const closestEntries = [];

    for (let index = 0; index < data.rows.length; index++) {
      const element = data.rows[index];
      const createdAt: any = new Date(element.createdAt);
      let minDifference = Infinity;
      let closestEntry = null;

      const userEmploymentHistory = missingComp.filter(
        (comp) => comp.userId === element.userId,
      );
      if (userEmploymentHistory && userEmploymentHistory.length > 0) {
        for (let j = 0; j < userEmploymentHistory.length; j++) {
          const value = userEmploymentHistory[j];
          const compantDate: any = new Date(value.createdAt);
          const difference = Math.abs(compantDate - createdAt);
          if (difference < minDifference) {
            minDifference = difference;
            closestEntry = value;
          }
        }
      }

      if (closestEntry) {
        closestEntries.push({ loanId: element.id, closestEntry });
        companyNames.add(closestEntry.companyName.toUpperCase());
      }
    }
    const compIdResults = await this.companyRepository.getTableWhereData(
      ['id', 'companyName'],
      {
        where: { companyName: { [Op.in]: Array.from(companyNames) } },
      },
    );
    if (compIdResults == k500Error) throw new Error();
    console.log('compIdResults', compIdResults.length);

    const companyNameToIdMap = {};
    for (let i = 0; i < compIdResults.length; i++) {
      const result = compIdResults[i];
      companyNameToIdMap[result.companyName.toUpperCase()] = result.id;
    }
    const updatePromises = closestEntries.map(
      async ({ loanId, closestEntry }) => {
        const companyName = closestEntry.companyName.toUpperCase();
        let companyId = companyNameToIdMap[companyName] ?? 0;

        if (!companyId) {
          unMatchCompany.push({ companyName: closestEntry.companyName });
        }

        await this.loanRepo.updateRowData({ companyId }, loanId, true);
      },
    );

    await Promise.all(updatePromises);

    if (compIdResults.length > 0) await this.migrationCltvData(body);
    return unMatchCompany;
  }

  async addMissingCompany(body) {
    console.log('addMissingCompany start');
    const companyNames = new Set();
    const status = body.status ?? [
      'InProcess',
      'Accepted',
      'Active',
      'Complete',
    ];
    const attr = ['id', 'createdAt', 'userId'];
    const opt = {
      where: {
        companyId: { [Op.eq]: null },
        loanStatus: { [Op.in]: status },
      },
    };

    const data = await this.loanRepo.getTableWhereDataWithCounts(attr, opt);
    if (data == k500Error) throw new Error();

    const userIds: any = data.rows.map((row) => row.userId);
    console.log('userIds', userIds.length);

    const attributes = ['userId', 'companyName', 'createdAt'];
    const options = {
      where: {
        userId: userIds,
      },
    };

    let missingComp: any =
      await this.employmentHistoryRepository.getTableWhereData(
        attributes,
        options,
      );
    if (missingComp == k500Error) throw new Error();

    const value: any = await this.empRepo.getTableWhereData(
      attributes,
      options,
    );
    if (value == k500Error) throw new Error();
    if (value) missingComp = [...missingComp, ...value];

    console.log('missingComp', missingComp.length);

    for (let index = 0; index < data?.rows?.length; index++) {
      const element = data.rows[index];
      const createdAt: any = new Date(element.createdAt);
      let minDifference = Infinity;
      let closestEntry = null;

      const userEmploymentHistory = missingComp.filter(
        (comp) => comp.userId === element?.userId,
      );

      if (userEmploymentHistory && userEmploymentHistory.length > 0) {
        for (let j = 0; j < userEmploymentHistory.length; j++) {
          const value = userEmploymentHistory[j];
          const compantDate: any = new Date(value.createdAt);
          const difference = Math.abs(compantDate - createdAt);

          if (difference < minDifference) {
            minDifference = difference;
            closestEntry = value;
          }
        }
      }

      if (closestEntry) {
        companyNames.add(closestEntry?.companyName?.toUpperCase());
      }
    }

    const compIdResults = await this.companyRepository.getTableWhereData(
      ['id', 'companyName'],
      {
        where: { companyName: { [Op.in]: Array.from(companyNames) } },
      },
    );
    if (compIdResults == k500Error) throw new Error();
    console.log('compIdResults', compIdResults.length);

    let checkCompany = Array.from(companyNames);
    const compIdResultsSet = new Set(
      compIdResults.map((result) => result?.companyName),
    );

    for (const companyName of checkCompany) {
      if (!compIdResultsSet.has(companyName)) {
        console.log('companyName', companyName);
        const data = {
          companyName,
          forMigration: true,
        };
        await this.companyRepository.createRowData(data);
      }
    }
    return { checkCompany };
  }

  async funCibilFetchDateMigrate(reqData) {
    const cibilOptions: any = { where: { status: '1' } };
    if (reqData?.userId) cibilOptions.where.userId = reqData?.userId;
    const cibilList = await this.cibilScoreRepo.getTableWhereData(
      ['id', 'userId', 'scores'],
      cibilOptions,
    );
    if (cibilList == k500Error) throw new Error();
    for (let i = 0; i < cibilList.length; i++) {
      try {
        const ele = cibilList[i];
        let fetchDate = ele?.scores[0].scoreDate;
        if (!fetchDate) continue;
        fetchDate = this.typeService.strDateToDate(fetchDate);
        fetchDate = this.typeService.getGlobalDate(fetchDate).toJSON();
        const updateOption = {
          where: {
            id: ele?.id,
            fetchDate: null,
          },
        };
        await this.cibilScoreRepo.updateRowWhereData(
          { fetchDate },
          updateOption,
        );
      } catch (error) {}
    }
    return {};
  }

  async dpdAdditionInTransactions(body) {
    try {
      const startDate = body.startDate;
      if (!startDate) return kParamMissing('startDate');
      const endDate = body.endDate;
      if (!endDate) return kParamMissing('endDate');
      const emiInc = {
        model: EmiEntity,
        attributes: [
          'emi_date',
          'payment_done_date',
          'id',
          'payment_due_status',
          'penalty_days',
        ],
        where: { payment_due_status: '1' },
      };
      const loanInc = {
        model: loanTransaction,
        include: [emiInc],
        attributes: ['id'],
      };
      const TodayDate = new Date(startDate).toJSON();
      const tillDate = new Date(endDate).toJSON();
      const range = this.typeService.getUTCDateRange(TodayDate, tillDate);
      const dateRange = {
        [Op.gte]: range.fromDate,
        [Op.lte]: range.endDate,
      };
      const options = {
        where: { status: 'COMPLETED', createdAt: dateRange },
        include: [loanInc],
        order: [['id', 'DESC']],
      };
      const att = ['id', 'type', 'emiId', 'completionDate'];
      // Hit -> Query
      const transData = await this.transRepo.getTableWhereData(att, options);
      if (transData === k500Error) return kInternalError;

      for (let i = 0; i < transData.length; i++) {
        try {
          const element = transData[i];
          const emiData = element?.loanData?.emiData;
          const paymentDate: any = this.typeService.getGlobalDate(
            element.completionDate,
          );
          let transId = element.id;
          let maxDPD = 0;
          let instantaneousDelayDays = 0;
          emiData.forEach((ele) => {
            const emiDate: any = this.typeService.getGlobalDate(ele.emi_date);
            const emiDoneDate: any = ele.payment_done_date
              ? this.typeService.getGlobalDate(ele.payment_done_date)
              : null;
            if (
              emiDoneDate &&
              emiDoneDate < paymentDate &&
              ele?.payment_due_status != '1'
            )
              return;
            if (paymentDate > emiDate) {
              let delayDays = this.typeService.differenceInDays(
                paymentDate,
                emiDate,
              );
              if (
                emiDoneDate &&
                emiDoneDate < paymentDate &&
                ele?.payment_due_status == '1'
              )
                delayDays = ele?.penalty_days ?? 0;
              if (delayDays > 0) instantaneousDelayDays = delayDays;
              if (instantaneousDelayDays > maxDPD)
                maxDPD = instantaneousDelayDays;
            }
          });
          if (maxDPD === 0) continue;
          await this.transRepo.updateRowData({ maxDPD }, transId, true);
        } catch (error) {}
      }
    } catch (error) {}
  }

  async migrateActiveLoanAddress(limit: number, offset: number) {
    try {
      const query = `SELECT DISTINCT ON ("a"."userId") 
      "a".id, "a"."userId","a"."loanStatus", "b"."aadhaarAddress", "b"."aadhaarLatLong"
      FROM public."loanTransactions" as "a"
      INNER JOIN public."KYCEntities" as "b"
      ON "a"."userId" = "b"."userId"
      WHERE "a"."loanStatus" = 'Active'
      AND "b"."aadhaarAddress" is not null
      AND "b"."aadhaarLatLong" is not null
      AND "b"."aadhaarLatLong" != ''
      LIMIT ${limit}
      OFFSET ${offset};`;
      const loanData = await this.repoManager.injectRawQuery(
        loanTransaction,
        query,
      );
      if (!loanData || loanData == k500Error) throw new Error();

      const promise = loanData.map((loan) => {
        const userAddress = JSON.parse(loan.aadhaarAddress);
        const addressArray = Object.values(userAddress)
          .join(' ')
          .split(/[\s,-]+/);
        const address = addressArray.join(' ').trim();
        let latLngObject = JSON.parse(loan.aadhaarLatLong);
        let aadhaarLatLongPoint = null;
        if (latLngObject) {
          const lat = latLngObject['lat'];
          const lng = latLngObject['lng'];
          aadhaarLatLongPoint = `${lat},${lng}`;
        }
        return {
          aadhaarAddress: address,
          loanId: loan.id,
          userId: loan.userId,
          aadhaarLatLong: aadhaarLatLongPoint,
          isActive: true,
        };
      });

      const resolvedPromise = await Promise.all(promise);
      const data = await this.repoManager.bulkCreate(
        ActiveLoanAddressesEntity,
        resolvedPromise,
      );
      if (data == k500Error) throw new Error();

      if (loanData.length > 0)
        await this.migrateActiveLoanAddress(limit, offset + limit);
      return;
    } catch (error) {
      throw new Error(error);
    }
  }

  async addCompanyDataInCompanyRepo() {
    console.log('migration started');
    const rawQuery = `SELECT emp."companyName" as company1, google."companyName" as googComp
    FROM (
    SELECT UPPER("companyName") as "companyName" FROM public."EmploymentHistoryDetailsEntities"
    UNION
    SELECT UPPER("companyName") as "companyName" FROM public."employmentDetails"
    ) AS emp
    LEFT JOIN public."GoogleCompanyResultEntities" AS google
    ON emp."companyName" = google."companyName"
    WHERE google."companyName" IS NULL
    AND emp."companyName" != '' and  length(emp."companyName")<255
    LIMIT 5000`;

    const queryData: any = await this.repoManager.injectRawQuery(
      EmploymentHistoryDetailsEntity,
      rawQuery,
    );
    if (queryData == k500Error) throw new Error();
    if (queryData.length <= 0) return 'DONE';
    console.log('queryData.length', queryData.length);
    for (let i = 0; i < queryData.length; i++) {
      try {
        const element = queryData[i];
        const data = {
          companyName: element.company1.toUpperCase(),
          forMigration: true,
        };
        await this.companyRepository.createRowData(data);
      } catch (error) {
        console.log({ error });
        this.errorContextService.throwAndSetCtxErr(error);
      }
    }
    await this.addCompanyDataInCompanyRepo();
  }

  async migrateAadhaarAndRegDates() {
    const attr = ['userId'];
    const options = {
      where: {
        status: { aadhaar: 1 },
        dates: { aadhaar: null },
      },
      order: [['id', 'DESC']],
    };
    // userid of users does not have aadhaar & reg date
    const userIds = await this.masterRepo.getTableWhereData(attr, options);
    if (!userIds || userIds == k500Error) return kInternalError;

    const userIdArray = userIds.map((user) => user.userId);

    const userInclude = {
      model: registeredUsers,
      attributes: ['createdAt'],
    };
    const attributes = ['loanId', 'dates', 'userId'];
    const option = {
      where: {
        userId: { [Op.in]: userIdArray },
      },
      include: [userInclude],
      order: [['loanId', 'DESC']],
    };
    const userData = await this.masterRepo.getTableWhereData(
      attributes,
      option,
    );
    if (!userData || userData == k500Error) return kInternalError;

    // loans group by userId
    const loansByUser = userData.reduce((acc, loan) => {
      if (!acc[loan.userId]) {
        acc[loan.userId] = [];
      }
      acc[loan.userId].push(loan);
      return acc;
    }, {});

    for (const userId of userIdArray) {
      const loans = loansByUser[userId];
      if (loans.length < 1) continue;

      const userCreatedAt = loans[0].userData.createdAt;
      let aadhaarDate = null;
      let regDate = null;

      // Loop through all loans to find an Aadhaar date
      for (let loan of loans) {
        if (loan.dates.aadhaar) {
          aadhaarDate = loan.dates.aadhaar;
        }
        if (loan.dates.registration) {
          regDate = loan.dates.registration;
        }
        if (aadhaarDate || regDate) break;
      }

      // If no Aadhaar date is found, assign it from createdAt
      aadhaarDate = aadhaarDate || new Date(userCreatedAt).getTime();
      regDate = regDate || new Date(userCreatedAt).getTime();

      // If an Aadhaar date is found, update all loans with a missing Aadhaar date
      for (let loan of loans) {
        const updateData: any = { dates: { ...loan.dates } };
        let shouldUpdate = false;

        if (!loan.dates.aadhaar) {
          updateData.dates.aadhaar = aadhaarDate;
          shouldUpdate = true;
        }
        if (!loan.dates.registration) {
          updateData.dates.registration = regDate;
          shouldUpdate = true;
        }
        if (shouldUpdate) {
          const options = {
            where: {
              loanId: loan.loanId,
            },
          };
          await this.masterRepo.updateRowWhereData(updateData, options);
        }
      }
    }
    return;
  }

  async funMigratePurposeId(body) {
    const isUpdate = body?.isUpdate ?? false;
    if (body.type == 'MASTER') {
      const masterInc = {
        model: MasterEntity,
        attributes: ['id', 'miscData'],
        where: {
          miscData: {
            purposeId: { [Op.notIn]: ['0', ''] },
          },
        },
      };
      const attr = ['id', 'purposeId'];
      const opt = {
        where: {
          purposeId: null,
        },
        include: masterInc,
        order: [['id', 'desc']],
      };

      const loanList = await this.loanRepo.getTableWhereData(attr, opt);
      if (loanList == k500Error) throw new Error();

      for (let i = 0; i < loanList.length; i++) {
        try {
          const ele = loanList[i];
          const masterData = ele?.masterData;
          const miscData = masterData?.miscData;
          if (isUpdate) {
            const updatePurposeId = await this.loanRepo.updateRowData(
              { purposeId: miscData?.purposeId },
              ele?.id,
            );
            if (updatePurposeId == k500Error) continue;
          }
        } catch (error) {}
      }
      return loanList;
    } else {
      const esignInclude = {
        model: esignEntity,
        attributes: [
          'id',
          [
            Sequelize.literal(`"eSign_agree_data"::JSONB->>'purposeName'`),
            'purposeName',
          ],
        ],
        where: {
          [Op.and]: Sequelize.literal(
            `"eSign_agree_data"::JSONB->>'purposeName' IS NOT NULL`,
          ),
        },
      };

      const attr = ['id', 'purposeId'];
      const opt = {
        where: {
          purposeId: null,
          esign_id: { [Op.ne]: null },
          loanStatus: { [Op.in]: ['Active', 'Complete'] },
        },
        include: esignInclude,
        order: [['id', 'desc']],
      };
      const loanList = await this.loanRepo.getTableWhereData(attr, opt);
      if (loanList == k500Error) throw new Error();
      console.log({ loanList: loanList.length });

      const purposeData = await this.repoManager.getTableWhereData(
        loanPurpose,
        ['id', 'purposeName'],
        {},
      );
      if (purposeData == k500Error) throw new Error();
      if (!isUpdate) return loanList;

      for (let i = 0; i < loanList.length; i++) {
        try {
          const ele = loanList[i];
          const loanId = ele?.id;
          console.log({ loanId });
          const eSignData = ele?.eSignData;

          const matchedPurpose = purposeData.find(
            (purpose) => purpose?.purposeName === eSignData?.purposeName,
          );
          console.log({ matchedPurpose });

          if (matchedPurpose) {
            await this.loanRepo.updateRowData(
              { purposeId: matchedPurpose?.id },
              ele?.id,
              true,
            );
          }
        } catch (error) {}
      }
      return loanList;
    }
  }

  async migrateFirstInquiryDate(limit: number, offset: number) {
    try {
      console.log('migrateFirstInquiryDate start', offset, limit);
      const query = `SELECT id, "responsedata" FROM public."CibilScoreEntities"
      WHERE "oldestInquiryDate" is null AND enquiries is not null
                  ORDER BY ID DESC
                  LIMIT ${limit} OFFSET ${offset};`;
      const response = await this.repoManager.injectRawQuery(
        CibilScoreEntity,
        query,
      );
      if (response == k500Error) throw new Error();
      console.log('response', response.length);
      const oldestInquiryDateArray = response.map((data) => {
        const enquiries =
          Array.isArray(data?.responsedata?.consumerCreditData) &&
          data?.responsedata?.consumerCreditData[0]?.enquiries
            ? data.responsedata.consumerCreditData[0].enquiries
            : null;
        let enquiryDate: string | null;
        if (!enquiries || enquiries.length == 0) {
          enquiryDate = null;
        } else {
          enquiryDate = this.typeService.cibiDateToDBDate(
            enquiries[enquiries.length - 1].enquiryDate,
          );
        }
        return { enquiryDate, id: data.id };
      });
      console.log('oldestInquiryDateArray', oldestInquiryDateArray.length);

      const updatedQuery = oldestInquiryDateArray
        .map((data) => {
          const date = data?.enquiryDate ? `'${data.enquiryDate}'` : 'NULL';
          return `UPDATE public."CibilScoreEntities"
            SET "oldestInquiryDate" = ${date}
            WHERE id = ${data.id};`;
        })
        .join('');

      const updateData = await this.repoManager.injectRawQuery(
        CibilScoreEntity,
        updatedQuery,
      );
      if (updateData == k500Error) throw new Error();
      if (oldestInquiryDateArray.length > 0)
        await this.migrateFirstInquiryDate(limit, limit + offset);
      return true;
    } catch (error) {
      console.log({ error });
      this.errorContextService.throwAndSetCtxErr(error);
    }
  }

  async handleIncompleteKYCDetails() {
    const attributes = ['id', 'fullName', 'masterId'];
    const masterInclude = {
      model: MasterEntity,
      attributes: ['dates'],
    };
    const kycInclude = {
      model: KYCEntity,
      attributes: [
        'id',
        'maskedAadhaar',
        'aadhaarStatus',
        'aadhaarDOB',
        'aadhaarAddress',
        'aadhaarLatLong',
      ],
      where: {
        kyc_mode: 'DIGILOCKER_IN_HOUSE',
        aadhaarStatus: '1',
      },
    };
    const options = {
      where: {
        fullName: '',
        isDeleted: { [Op.ne]: '1' },
      },
      include: [kycInclude, masterInclude],
    };
    // get affected users
    const kycUsers = await this.userRepo.getTableWhereData(attributes, options);
    if (kycUsers === k500Error) return kInternalError;

    // change kyc dates of affected users
    const query = kycUsers
      .map((user) => {
        const currentDate = new Date(user.masterData.dates.aadhaar);
        currentDate.setFullYear(currentDate.getFullYear() - 1);
        const updatedDate = new Date(currentDate).getTime();
        return `UPDATE public."MasterEntities" SET "dates"= jsonb_set("dates", '{aadhaar}', '${updatedDate}'::jsonb, false) where "id" = '${user.masterId}';`;
      })
      .join('');

    // update kycDates
    const queryData = await this.repoManager.injectRawQuery(
      MasterEntity,
      query,
    );
    if (queryData == k500Error) return kInternalError;

    for (let i = 0; i < kycUsers.length; i++) {
      await this.userService.routeDetails({ id: kycUsers[i]?.id });
    }
    return true;
  }

  async migrateCoolOffUsers() {
    const date = new Date();
    date.setUTCHours(10, 0, 0, 0);

    const result = date.toISOString();
    const loanInclude = {
      model: loanTransaction,
      attributes: ['id', 'loanStatus'],
      where: {
        loanStatus: {
          [Op.in]: ['Accepted', 'InProcess'],
        },
      },
    };
    const attributes = ['userId', 'id', 'loanId', 'status', 'coolOffData'];
    const options = {
      where: {
        status: {
          loan: '-1',
        },
      },
      include: [loanInclude],
    };
    let userDetails = await this.masterRepo.getTableWhereData(
      attributes,
      options,
    );

    let users = [];
    userDetails.filter((data) => {
      if (data.coolOffData.coolOffEndsOn > result) {
        users.push(data);
      }
    });

    const masterQuery = users
      .map((user) => {
        return `UPDATE "MasterEntities" SET "status" = jsonb_set(jsonb_set("status", '{loan}', '2', false), '{eligibility}', '2', false) WHERE "id" = ${user.id};`;
      })
      .join('');

    const loanQuery = users
      .filter(
        (user) =>
          user?.loanData?.loanStatus == 'InProcess' ||
          user?.loanData?.loanStatus == 'Accepted',
      )
      .map((user) => {
        return `UPDATE "loanTransactions" SET "loanStatus" = 'Rejected' where "id" = ${user.loanId};`;
      })
      .join('');

    const masterUpdate = await this.repoManager.injectRawQuery(
      MasterEntity,
      masterQuery,
    );
    if (masterUpdate == k500Error) return kInternalError;

    const loanUpdate = await this.repoManager.injectRawQuery(
      loanTransaction,
      loanQuery,
    );
    if (loanUpdate == k500Error) return kInternalError;
    return true;
  }

  //#region Migration for FinanceBudhhaLeadsEntities
  async migratePANEncryption(offset = 0, limit = 1000) {
    console.log('migratePANEncryption Started');
    const rawQuery1 = `
      SELECT "id", "PAN" FROM "FinanceBudhhaLeadsEntities" 
        WHERE "PAN" IS NOT NULL 
        AND LENGTH("PAN") = 10
        LIMIT ${limit} OFFSET ${offset};
      `;
    const panList = await this.repoManager.injectRawQuery(
      LeadTrackingEntity,
      rawQuery1,
    );
    if (panList == k500Error) console.log('k500Error: ', rawQuery1);
    console.log(offset, offset + limit, panList.length);
    for (let i = 0; i < panList.length; i++) {
      const panId = panList[i].id;
      const panNumber = panList[i].PAN;
      const encryptedPAN = await this.cryptService.encryptText(panNumber);
      const encryptedHashPan = this.cryptService.getMD5Hash(panNumber);
      await this.leadTrackingRepo.updateRowData(
        { PAN: encryptedPAN, hashPAN: encryptedHashPan },
        panId,
      );
    }
    if (panList.length !== 0)
      await this.migratePANEncryption(offset + limit, limit);
    console.log('migratePANEncryption Completed');
    return true;
  }
  //#endregion

  async migratePhoneWithHashPhoneInProtean(
    limit: number,
    offset: number,
    isStop: boolean = false,
  ) {
    console.log('start migratePhoneWithHashPhoneInProtean');
    if (isStop) {
      console.log('end migrateAllPhoneAndOtherPhone');
      return true;
    }
    const query = `SELECT "hashPhone", "phone" FROM public."ProteanEntities"
                  ORDER BY "createdAt" ASC
                  LIMIT ${limit} OFFSET ${offset};`;
    const response = await this.repoManager.injectRawQuery(
      ProteanEntity,
      query,
    );
    if (response == k500Error) throw new Error();
    if (response.length == 0) {
      console.log('end migrateAllPhoneAndOtherPhone');
      return true;
    }
    console.log(
      `start migration response length ${response.length} for offset is ${offset} and limit is ${limit}`,
    );

    const updatedQuery = response
      .map((data) => {
        if (data.hashPhone.length > 10) {
          isStop = true;
          return '';
        }
        const hashPhone = this.cryptService.getMD5Hash(data.hashPhone);
        const encryptedPhone = this.cryptService.encryptPhone(data.hashPhone);
        return `UPDATE public."ProteanEntities"
            SET "hashPhone" = '${hashPhone}', "phone" = '${encryptedPhone}'
            WHERE "hashPhone" = '${data.hashPhone}'
            AND NOT EXISTS ( SELECT 1 FROM public."ProteanEntities" WHERE "hashPhone" = '${hashPhone}' );`;
      })
      .join('');

    if (updatedQuery) {
      const updateData = await this.repoManager.injectRawQuery(
        ProteanEntity,
        updatedQuery,
      );
      if (updateData == k500Error) throw new Error();
      console.log(
        `Complete migration of response length ${response.length} for offset is ${offset} and limit is ${limit}`,
      );
    }

    await this.migratePhoneWithHashPhoneInProtean(
      limit,
      limit + offset,
      isStop,
    );
  }

  async migrateAddPhoneForHashPhoneInProtean(
    limit: number,
    offset: number,
    isStop: boolean = false,
  ) {
    console.log('start migrateAddPhoneForHashPhoneInProtean');
    if (isStop) {
      console.log('end migrateAddPhoneForHashPhoneInProtean');
      return true;
    }
    const queryForProtean = `SELECT "hashPhone", "phone" FROM public."ProteanEntities"
                  ORDER BY "createdAt" DESC
                  LIMIT ${limit} OFFSET ${offset};`;

    const responseOfProtean = await this.repoManager.injectRawQuery(
      ProteanEntity,
      queryForProtean,
    );

    if (responseOfProtean == k500Error) throw new Error();
    if (responseOfProtean.length == 0) {
      console.log('end migrateAddPhoneForHashPhoneInProtean');
      return true;
    }

    console.log(
      `start migration responseOfProtean length ${responseOfProtean.length} for offset is ${offset} and limit is ${limit}`,
    );

    const hashPhoneArr = [];
    for (let i = 0; i < responseOfProtean.length; i++) {
      const ele = responseOfProtean[i];
      if (ele.hashPhone.length > 10 && !ele.phone) {
        hashPhoneArr.push(ele.hashPhone);
      }
    }

    const queryForUser = `SELECT "hashPhone", "phone" FROM public."registeredUsers"
                  WHERE "hashPhone" IN (${hashPhoneArr
                    .map((phone) => `'${phone}'`)
                    .join(',')});`;

    const responseOfUser = await this.repoManager.injectRawQuery(
      registeredUsers,
      queryForUser,
    );
    if (responseOfUser == k500Error) throw new Error();
    if (responseOfUser.length == 0) {
      console.log('end migrateAddPhoneForHashPhoneInProtean');
      return true;
    }

    const updatedQueryForProtean = responseOfProtean
      .map((data) => {
        const user = responseOfUser.find(
          (ele) => ele.hashPhone == data.hashPhone,
        );
        if (user) {
          return `UPDATE public."ProteanEntities"
              SET "phone" = '${user.phone}'
              WHERE "hashPhone" = '${data.hashPhone}';`;
        } else {
          return '';
        }
      })
      .join('');

    if (updatedQueryForProtean) {
      const updateData = await this.repoManager.injectRawQuery(
        ProteanEntity,
        updatedQueryForProtean,
      );
      if (updateData == k500Error) throw new Error();
      console.log(
        `Complete migration of responseOfProtean length ${responseOfProtean.length} for offset is ${offset} and limit is ${limit}`,
      );
    }

    await this.migrateAddPhoneForHashPhoneInProtean(
      limit,
      limit + offset,
      isStop,
    );
  }

  async migrateHashPhone() {
    const userData = await this.repoManager.getTableWhereData(
      registeredUsers,
      ['id', 'hashPhone'],
      {
        where: { hashPhone: { [Op.ne]: null } },
        order: [['uniqueId', 'ASC']],
      },
    );
    if (userData == k500Error) throw new Error();

    const bulkData = userData.map((user) => ({
      userId: user?.id,
      hashPhone: user?.hashPhone,
    }));
    const BATCH_SIZE = 10000; // Set a batch size

    const length = bulkData.length;
    console.log('bulkdatalength', { length });

    // Batch bulk creation to handle large datasets
    for (let i = 0; i < length; i += BATCH_SIZE) {
      const batch = bulkData.slice(i, i + BATCH_SIZE);
      const bulkCreate = await this.repoManager.bulkCreate(
        HashPhoneEntity,
        batch,
      );
      if (bulkCreate === k500Error) throw new Error();
    }
    return true;
  }

  async migratePanDates(reqData) {
    const isUpdate = reqData?.isUpdate;
    const loanId = reqData?.loanId;
    const userId = reqData?.userId;
    const attr = ['userId', 'loanId', 'dates', 'loanId'];
    const options: any = {
      where: {
        status: { pan: 1 },
        [Op.and]: [
          Sequelize.literal(`("dates"->>'disbursement')::numeric > 0`),
          Sequelize.literal(
            `("dates"->>'pan' IS NULL OR ("dates"->>'disbursement')::numeric = 0)`,
          ),
        ],
      },
      order: [['id', 'DESC']],
    };

    if (userId) options.where.userId = userId;
    if (loanId) options.where.loanId = loanId;

    // userid of users does not have aadhaar & reg date
    const masterData = await this.masterRepo.getTableWhereData(attr, options);
    if (!masterData || masterData == k500Error) return kInternalError;

    const userIdArray = [...new Set(masterData.map((user) => user.userId))];

    const attributes = ['userId', 'updatedAt'];
    const option = {
      where: {
        userId: { [Op.in]: userIdArray },
        panStatus: '1',
      },
      order: [['id', 'DESC']],
    };

    const kycData = await this.kycRepo.getTableWhereData(attributes, option);
    if (!kycData || kycData == k500Error) return kInternalError;

    // Create a Map for kycData indexed by userId for quick lookup
    const kycDataMap = new Map(
      kycData.map((el) => [el.userId, new Date(el.updatedAt).getTime()]),
    );

    let data = [];
    // Iterate over masterData and process updates
    const updatePromises = masterData.map(async (ele) => {
      try {
        const userId = ele?.userId;

        if (!userId || !kycDataMap.has(userId)) return; // Skip if no userId or not in kycDataMap

        // Retrieve panDates from the map
        const panDates = kycDataMap.get(userId);

        // Ensure dates object exists and update pan field
        let dates = ele?.dates;
        dates.pan = panDates;

        // Prepare update payload and options
        let updateData: any = { dates };

        //if isUpdate is true then update
        if (isUpdate) {
          const options = {
            where: {
              loanId: ele.loanId, // Ensure loanId exists
            },
          };
          // Update the row in the database
          await this.masterRepo.updateRowWhereData(updateData, options);
          let key = `TIMELINE_${ele.loanId}`;
          await this.redisService.del(key);
        }
        updateData.loanId = ele.loanId;
        updateData.userId = ele.userId;
        data.push(updateData);
      } catch (error) {}
    });

    // Wait for all updates to complete
    await Promise.all(updatePromises);

    return data;
  }

  async migrateAllPhoneAndOtherPhone(batchSize: number) {
    console.log('start migrateAllPhoneAndOtherPhone');
    const queryForGetData = `SELECT "id", "allPhone", "otherPhone" FROM public."registeredUsers"
    ORDER BY "createdAt" ASC;`;

    const userData = await this.repoManager.injectRawQuery(
      registeredUsers,
      queryForGetData,
    );
    if (userData == k500Error) throw new Error();

    console.log(`userData length ${userData.length}`);

    for (let i = 0; i < userData.length; i += batchSize) {
      const usersArr = userData.slice(i, i + batchSize);
      const encryptedData = usersArr.map((user) => {
        const tempObj: any = {
          id: user.id,
          allPhone: [],
          otherPhone: [],
        };
        if (user.allPhone && user.allPhone?.length > 0) {
          user.allPhone.forEach((ele) => {
            if (ele && ele?.length == 10) {
              const encryptedPhone = this.cryptService.encryptPhone(ele);
              tempObj.allPhone.push(encryptedPhone);
            } else if (ele && ele?.length > 10) tempObj.allPhone.push(ele);
          });
        }
        if (user.otherPhone && user.otherPhone?.length > 0) {
          user.otherPhone.forEach((ele) => {
            if (ele && ele?.length == 10) {
              const encryptedPhone = this.cryptService.encryptPhone(ele);
              tempObj.otherPhone.push(encryptedPhone);
            } else if (ele && ele?.length > 10) tempObj.otherPhone.push(ele);
          });
        }
        return tempObj;
      });

      const updatedQuery = encryptedData
        .map((data) => {
          const allPhone: any = data.allPhone
            .map((phone) => `'${phone}'`)
            .join(', ');
          const otherPhone: any = data.otherPhone
            .map((phone) => `'${phone}'`)
            .join(', ');

          return `UPDATE public."registeredUsers"
              SET "allPhone" =  ARRAY[${allPhone}]::TEXT[],
              "otherPhone" = ARRAY[${otherPhone}]::TEXT[]
              WHERE "id" =  '${data.id}';`;
        })
        .join('');

      const updateData = await this.repoManager.injectRawQuery(
        registeredUsers,
        updatedQuery,
      );
      if (updateData == k500Error) throw new Error();
      console.log(
        `Complete migration of userData for offset is ${i} and limit is ${batchSize}`,
      );
    }
    console.log('end migrateAllPhoneAndOtherPhone');
    return true;
  }

  async migrateFcmToken() {
    const BATCH_SIZE = 50000;
    const attributes = ['id', 'fcmToken'];
    const repeatFcm = [];
    const options = {
      where: {
        fcmToken: {
          [Op.and]: [
            { [Op.ne]: null },
            { [Op.ne]: '' },
            { [Op.ne]: 'NOT_FOUND' },
          ],
        },
      },
      order: [['id', 'desc']],
    };

    // Fetch all users with valid FCM tokens
    const fcmUsers = await this.userRepo.getTableWhereData(attributes, options);
    if (fcmUsers === k500Error) throw new Error();

    // Generate MD5 hashes for all FCM tokens and prepare lookup map
    const hashedTokens = fcmUsers.map((user) => ({
      id: user.id,
      fcmToken: user.fcmToken,
      hashedToken: this.cryptService.getMD5Hash(user.fcmToken),
    }));

    // Remove duplicates within the fetched users
    const uniqueHashedTokens = new Map();
    for (const user of hashedTokens) {
      if (!uniqueHashedTokens.has(user.hashedToken)) {
        uniqueHashedTokens.set(user.hashedToken, user);
      } else {
        repeatFcm.push(user);
      }
    }

    // Fetch existing hashed FCM tokens in bulk
    const existingTokens = await this.repoManager.getTableWhereData(
      fcmTokenEntity,
      ['fcmToken', 'userId'],
      {
        where: {
          fcmToken: { [Op.in]: [...uniqueHashedTokens.keys()] },
        },
      },
    );

    if (existingTokens === k500Error) throw new Error();

    // Create a map of existing tokens for quick lookup
    const existingTokenMap = new Map(
      existingTokens.map((token) => [token.fcmToken, token.userId]),
    );

    // Prepare for batch operations
    const toInsert = [];
    const toUpdate = [];

    for (const [hashedToken, user] of uniqueHashedTokens) {
      const { id } = user;

      if (!existingTokenMap.has(hashedToken)) {
        // New token, prepare for insertion
        toInsert.push({ userId: id, fcmToken: hashedToken });
      } else if (existingTokenMap.get(hashedToken) !== id) {
        // Token exists but linked to a different user, prepare for update
        repeatFcm.push({
          fcmToken: hashedToken,
          existingUserId: existingTokenMap.get(hashedToken),
          newUserId: id,
        });
        toUpdate.push({ userId: id, fcmToken: hashedToken });
      }
    }

    // Perform batch insert for new tokens

    const insertLength = toInsert.length;
    console.log('Insert Batch Length:', { insertLength });

    for (let i = 0; i < insertLength; i += BATCH_SIZE) {
      const batch = toInsert.slice(i, i + BATCH_SIZE);
      console.log('Next Batch');
      const insertResult = await this.repoManager.bulkCreate(
        fcmTokenEntity,
        batch,
      );
      if (insertResult === k500Error) throw new Error();
    }

    // Perform batch updates for repeated tokens
    const updatedQueryForFcmTokens = toUpdate
      .map((data) => {
        return `UPDATE public."fcmTokenEntities"
              SET "userId" = '${data.userId}'
              WHERE "fcmToken" = '${data.fcmToken}';`;
      })
      .join('');

    if (updatedQueryForFcmTokens) {
      const updateData = await this.repoManager.injectRawQuery(
        fcmTokenEntity,
        updatedQueryForFcmTokens,
      );
      if (updateData === k500Error)
        throw new Error('Error in batch updating FCM tokens');
    }

    // Return repeated FCM tokens
    return repeatFcm;
  }

  async funSelfieImageMigration(
    batchSize: number,
    startDate: any,
    endDate: any,
  ) {
    startDate = new Date(startDate);
    endDate = new Date(endDate);

    let arrOfLocalFileUrl: any = [];
    try {
      console.log('start funSelfieImageMigration');

      const googleCloudUrl = 'storage.googleapis.com';
      let imageDataObj: any = {};

      let isMigrationDone = false;
      let index = 0;
      const userIds = [];
      let length = 0;
      while (!isMigrationDone) {
        await this.typeService.delay(2500);
        // isMigrationDone = true;
        const allUserData = await this.repoManager.getTableWhereData(
          registeredUsers,
          ['id', 'image'],
          {
            where: {
              isMigrate: null,
              createdAt: {
                [Op.gte]: startDate,
                [Op.lte]: endDate,
              },
            },
            order: [['createdAt', 'ASC']],
            limit: batchSize,
            offset: index,
          },
        );

        if (allUserData == k500Error) throw new Error();
        if (!allUserData.length) {
          isMigrationDone = true;
          console.log('end funSelfieImageMigration');
          break;
        }

        const userIds = allUserData.map((user) => user.id);
        const allKycData = await this.repoManager.getTableWhereData(
          KYCEntity,
          [
            'id',
            'aadhaarFront',
            'aadhaarBack',
            'otherDocFront',
            'otherDocBack',
            'profileImage',
            'userId',
          ],
          {
            where: { userId: userIds },
          },
        );
        if (allKycData == k500Error) throw new Error();
        const allUserSelfieData = await this.repoManager.getTableWhereData(
          UserSelfieEntity,
          ['id', 'image', 'tempImage', 'response', 'extraData', 'userId'],
          {
            where: { userId: userIds },
          },
        );
        if (allUserSelfieData == k500Error) throw new Error();

        console.log(
          `start migration of user where offset ${index} and limit is ${batchSize}`,
        );
        for (let j = 0; j < allUserData.length; j++) {
          const user = allUserData[j];
          console.log({ user });
          imageDataObj = {};
          if (user.image && user.image?.includes(googleCloudUrl))
            imageDataObj[user.image] = '';

          console.log('imageDataObj', imageDataObj);

          const kycData = allKycData.filter((kyc) => kyc.userId == user.id);
          console.log(kycData);

          if (kycData.length > 0) {
            kycData.forEach((kyc) => {
              if (
                kyc.aadhaarFront &&
                kyc.aadhaarFront?.includes(googleCloudUrl)
              )
                imageDataObj[kyc.aadhaarFront] = '';
              if (kyc.aadhaarBack && kyc.aadhaarBack?.includes(googleCloudUrl))
                imageDataObj[kyc.aadhaarBack] = '';
              if (
                kyc.otherDocFront &&
                kyc.otherDocFront?.includes(googleCloudUrl)
              )
                imageDataObj[kyc.otherDocFront] = '';
              if (
                kyc.otherDocBack &&
                kyc.otherDocBack?.includes(googleCloudUrl)
              )
                imageDataObj[kyc.otherDocBack] = '';
              if (
                kyc.profileImage &&
                kyc.profileImage?.includes(googleCloudUrl)
              )
                imageDataObj[kyc.profileImage] = '';
            });
          }

          const userSelfieData = allUserSelfieData.filter(
            (selfie) => selfie.userId == user.id,
          );
          if (userSelfieData.length > 0) {
            userSelfieData.forEach((selfie) => {
              if (selfie.image && selfie.image?.includes(googleCloudUrl))
                imageDataObj[selfie.image] = '';
              if (
                selfie.tempImage &&
                selfie.tempImage?.includes(googleCloudUrl)
              )
                imageDataObj[selfie.tempImage] = '';
              if (selfie.response) {
                const response = JSON.parse(selfie.response);
                if (
                  response.imageA &&
                  response.imageA?.includes(googleCloudUrl)
                )
                  imageDataObj[response.imageA] = '';
                if (
                  response.imageB &&
                  response.imageB?.includes(googleCloudUrl)
                )
                  imageDataObj[response.imageB] = '';
              }
              if (selfie.extraData) {
                const extraData = JSON.parse(selfie.extraData);
                if (
                  extraData.sourceImage &&
                  extraData.sourceImage?.includes(googleCloudUrl)
                )
                  imageDataObj[extraData.sourceImage] = '';
                if (
                  extraData.targetImage &&
                  extraData.targetImage?.includes(googleCloudUrl)
                )
                  imageDataObj[extraData.targetImage] = '';
              }
            });
          }

          const arrOfGoogleFileUrl = Object.keys(imageDataObj);

          if (arrOfGoogleFileUrl.length) {
            arrOfLocalFileUrl = [];
            const tempResponse = await Promise.allSettled(
              arrOfGoogleFileUrl.map((el) =>
                this.fileService.fileUrlToFile(el),
              ),
            );

            console.log('tempResponse', tempResponse);

            tempResponse.forEach((el, idx) => {
              if (el.status == 'rejected') {
                const fileUrl = arrOfGoogleFileUrl[idx];
                arrOfGoogleFileUrl.splice(idx, 1);
                delete imageDataObj[fileUrl];
              } else arrOfLocalFileUrl.push(el.value);
            });
            console.log('=======');
            console.log('tempResponse', tempResponse);

            if (arrOfLocalFileUrl?.includes(k500Error)) throw new Error();

            if (!arrOfLocalFileUrl?.length) continue;
            const arrOfOrcleFileUrl = await Promise.all(
              arrOfLocalFileUrl.map((el) =>
                this.fileService.uploadFile(el, 'KYC_AND_SELFIE_IMAGE'),
              ),
            );
            if (arrOfOrcleFileUrl?.includes(k500Error)) throw new Error();

            //map imageDataObj
            arrOfGoogleFileUrl.forEach((el, idx) => {
              const googleFileUrl = arrOfGoogleFileUrl[idx];
              const orcleFileUrl = arrOfOrcleFileUrl[idx];
              imageDataObj[googleFileUrl] = orcleFileUrl;
            });

            let errorInUpdate: any = '';
            if (user.image && user.image?.includes(googleCloudUrl)) {
              errorInUpdate = this.repoManager.updateRowData(
                registeredUsers,
                { image: imageDataObj[user.image] },
                user.id,
                true,
              );
              if (errorInUpdate == k500Error) throw new Error();
            }
            if (kycData.length > 0) {
              const updatedQueryForKyc = kycData
                .map((kyc) => {
                  const arrForAttributes: any = [];
                  if (
                    kyc.aadhaarFront &&
                    kyc.aadhaarFront?.includes(googleCloudUrl)
                  )
                    arrForAttributes.push(
                      `"aadhaarFront" = PGP_SYM_ENCRYPT('${
                        imageDataObj[kyc.aadhaarFront]
                      }','${kCryptography}')`,
                    );
                  if (
                    kyc.aadhaarBack &&
                    kyc.aadhaarBack?.includes(googleCloudUrl)
                  )
                    arrForAttributes.push(
                      `"aadhaarBack" = PGP_SYM_ENCRYPT('${
                        imageDataObj[kyc.aadhaarBack]
                      }','${kCryptography}')`,
                    );
                  if (
                    kyc.otherDocFront &&
                    kyc.otherDocFront?.includes(googleCloudUrl)
                  )
                    arrForAttributes.push(
                      `"otherDocFront" = PGP_SYM_ENCRYPT('${
                        imageDataObj[kyc.otherDocFront]
                      }','${kCryptography}')`,
                    );
                  if (
                    kyc.otherDocBack &&
                    kyc.otherDocBack?.includes(googleCloudUrl)
                  )
                    arrForAttributes.push(
                      `"otherDocBack" = PGP_SYM_ENCRYPT('${
                        imageDataObj[kyc.otherDocBack]
                      }','${kCryptography}')`,
                    );
                  if (
                    kyc.profileImage &&
                    kyc.profileImage?.includes(googleCloudUrl)
                  )
                    arrForAttributes.push(
                      `"profileImage" = PGP_SYM_ENCRYPT('${
                        imageDataObj[kyc.profileImage]
                      }','${kCryptography}')`,
                    );
                  if (arrForAttributes.length) {
                    return `UPDATE public."KYCEntities"
                            SET ${arrForAttributes.join(',')}
                            WHERE "id" = '${kyc.id}';`;
                  } else return '';
                })
                .filter((query) => query != '')
                .join('');

              if (updatedQueryForKyc)
                errorInUpdate = await this.repoManager.injectRawQuery(
                  KYCEntity,
                  updatedQueryForKyc,
                );
              if (errorInUpdate == k500Error) throw new Error();
            }

            if (userSelfieData.length > 0) {
              const updatedQueryForSelfie = userSelfieData
                .map((selfie) => {
                  const arrForAttributes: any = [];
                  if (selfie.image && selfie.image?.includes(googleCloudUrl))
                    arrForAttributes.push(
                      `"image" = '${imageDataObj[selfie.image]}'`,
                    );
                  if (
                    selfie.tempImage &&
                    selfie.tempImage?.includes(googleCloudUrl)
                  )
                    arrForAttributes.push(
                      `"tempImage" = '${imageDataObj[selfie.tempImage]}'`,
                    );
                  if (selfie.response) {
                    const response = JSON.parse(selfie.response);
                    if (
                      response.imageA &&
                      response.imageA?.includes(googleCloudUrl)
                    )
                      response.imageA = imageDataObj[response.imageA];
                    if (
                      response.imageB &&
                      response.imageB?.includes(googleCloudUrl)
                    )
                      response.imageB = imageDataObj[response.imageB];
                    arrForAttributes.push(
                      `"response" = '${JSON.stringify(response)}'`,
                    );
                  }
                  if (selfie.extraData) {
                    const extraData = JSON.parse(selfie.extraData);
                    if (
                      extraData.sourceImage &&
                      extraData.sourceImage?.includes(googleCloudUrl)
                    )
                      extraData.sourceImage =
                        imageDataObj[extraData.sourceImage];
                    if (
                      extraData.targetImage &&
                      extraData.targetImage?.includes(googleCloudUrl)
                    )
                      extraData.targetImage =
                        imageDataObj[extraData.targetImage];
                    arrForAttributes.push(
                      `"extraData" = '${JSON.stringify(extraData)}'`,
                    );
                  }
                  if (arrForAttributes.length) {
                    return `UPDATE public."UserSelfieEntities"
                            SET ${arrForAttributes.join(',')}
                            WHERE "id" = '${selfie.id}';`;
                  } else return '';
                })
                .filter((query) => query != '')
                .join('');

              if (updatedQueryForSelfie)
                errorInUpdate = await this.repoManager.injectRawQuery(
                  UserSelfieEntity,
                  updatedQueryForSelfie,
                );
              if (errorInUpdate == k500Error) throw new Error();
            }

            await this.fileService.removeFiles(arrOfLocalFileUrl);
            const bulkCreateData = arrOfGoogleFileUrl.map((el) => ({
              fileUrl: el,
            }));
            const errorInCreate = await this.repoManager.bulkCreate(
              GoogleFileEntity,
              bulkCreateData,
            );
            if (errorInCreate == k500Error) throw new Error();

            // await Promise.all(
            //   arrOfGoogleFileUrl.map((el) =>
            //     this.fileService.deleteCloudFile(el),
            //   ),
            // );
          }

          userIds.push(user.id);
          length += 1;
          console.log('=============================', length);

          await this.repoManager.updateRowData(
            registeredUsers,
            { isMigrate: true },
            user.id,
            true,
          );
        }
        console.log(
          `end migration of user where offset ${index} and limit is ${batchSize}`,
        );
        //update index
        index += batchSize;
      }
      return { userIds, length };
    } catch (error) {
      console.log('error in main', error);
      arrOfLocalFileUrl = [...new Set(arrOfLocalFileUrl)];
      if (arrOfLocalFileUrl.length) {
        const errorIndex = arrOfLocalFileUrl.findIndex((el) => el == k500Error);
        if (errorIndex != -1) arrOfLocalFileUrl.splice(errorIndex, 1);
        await this.fileService.removeFiles(arrOfLocalFileUrl);
      }
      throw new Error();
    }
  }

  async rejectNotEligibleUsers(reqData) {
    const rawQuery = `SELECT "loan"."id" AS "Loan Id", "loan"."userId" AS "User Id", 
    "master"."otherInfo", "banking"."salary"
    FROM "loanTransactions" as "loan"

    INNER JOIN "MasterEntities" AS "master" ON "master"."loanId" = "loan"."id"
    LEFT JOIN "BankingEntities" AS "banking" ON "banking"."id" = "loan"."bankingId"
    
    WHERE "loan"."loanStatus" IN ('InProcess', 'Accepted') 
    AND "esign_id" IS NULL AND "loan"."completedLoan" = 0`;

    const outputList = await this.repoManager.injectRawQuery(
      loanTransaction,
      rawQuery,
    );
    if (outputList == k500Error) throw new Error();

    const isReadOnly = (reqData.readOnly ?? 'false') == 'true';
    const finalizedList = [];
    const nextDateForApply = new Date();
    nextDateForApply.setDate(nextDateForApply.getDate() + 90);
    for (let index = 0; index < outputList.length; index++) {
      try {
        const loanData = outputList[index];
        const otherInfo = loanData.otherInfo ?? {};
        let user_entered_salary =
          otherInfo.netPaySalary ?? otherInfo.salaryInfo;
        user_entered_salary = +user_entered_salary;
        const salary = loanData.salary ?? user_entered_salary;
        if (salary >= GLOBAL_RANGES.NEW_USER_MIN_SALARY) continue;

        const loanId = loanData['Loan Id'];
        const userId = loanData['User Id'];
        finalizedList.push({ loanId, salary });
        if (isReadOnly) continue;

        console.log({ total: outputList.length, current: index + 1 });
        await this.sharedEligibility.rejectLoan(
          SYSTEM_ADMIN_ID,
          loanId,
          MIN_SALARY_CRITERIA,
          userId,
          nextDateForApply,
        );
      } catch (error) {}
    }

    return { isReadOnly, rows: finalizedList };
  }

  async migratecompanyName(batchSize: number) {
    let isMigrate = false;
    console.log('start migratecompanyName');

    while (!isMigrate) {
      const getAllCompanyQuery = `SELECT "id","companyName","updatedAt"
       FROM  public."GoogleCompanyResultEntities"
       WHERE TRIM("companyName") IN(SELECT TRIM("companyName") FROM public."GoogleCompanyResultEntities" GROUP BY TRIM("companyName") HAVING COUNT("companyName") > 1)
       ORDER BY TRIM("companyName") ASC, "updatedAt" DESC LIMIT ${batchSize};`;

      const allCompanyData = await this.repoManager.injectRawQuery(
        GoogleCompanyResultEntity,
        getAllCompanyQuery,
      );
      if (allCompanyData == k500Error) throw new Error();
      if (!allCompanyData.length) {
        isMigrate = true;
        break;
      }

      let companyDataObj: any = {};
      for (let i = 0; i < allCompanyData.length; i = i + 2) {
        const ele1: any = allCompanyData[i];
        const ele2: any = allCompanyData[i + 1];

        let trimCompanyName = ele1.companyName.trim();
        if (trimCompanyName.includes("'"))
          trimCompanyName = trimCompanyName.replaceAll("'", "''");

        let withOutTrimId: any = '';
        let withTrimId: any = '';

        companyDataObj = {};
        if (ele1.CIN && !ele2.CIN) companyDataObj = ele1;
        else if (ele2.CIN && !ele1.CIN) companyDataObj = ele2;
        else companyDataObj = ele1;

        withTrimId = companyDataObj.id;
        withOutTrimId = companyDataObj.id == ele1.id ? ele2.id : ele1.id;

        let errorInOperation: any = '';
        const updateEmploymentQuery = `UPDATE public."employmentDetails"
        SET "companyName" = UPPER(TRIM("companyName")),
        "updatedCompanyName" = UPPER(TRIM("updatedCompanyName"))
        WHERE TRIM("companyName") ILIKE '${trimCompanyName}' OR TRIM("updatedCompanyName") ILIKE '${trimCompanyName}';`;

        errorInOperation = await this.repoManager.injectRawQuery(
          employmentDetails,
          updateEmploymentQuery,
        );
        if (errorInOperation == k500Error) throw Error();

        const updateEmploymentHistoryQuery = `UPDATE public."EmploymentHistoryDetailsEntities"
        SET "companyName" = UPPER(TRIM("companyName")),
        "updatedCompanyName" = UPPER(TRIM("updatedCompanyName"))
        WHERE TRIM("companyName") ILIKE '${trimCompanyName}' OR TRIM("updatedCompanyName") ILIKE '${trimCompanyName}';`;

        errorInOperation = await this.repoManager.injectRawQuery(
          EmploymentHistoryDetailsEntity,
          updateEmploymentHistoryQuery,
        );
        if (errorInOperation == k500Error) throw Error();

        const UpdateloanQuery = `UPDATE public."loanTransactions"
        SET "companyId" = ${withTrimId}
        WHERE "companyId" = ${withOutTrimId};`;

        errorInOperation = await this.repoManager.injectRawQuery(
          loanTransaction,
          UpdateloanQuery,
        );
        if (errorInOperation == k500Error) throw Error();

        errorInOperation = await this.repoManager.deleteSingleData(
          GoogleCompanyResultEntity,
          withOutTrimId,
          false,
        );
        if (errorInOperation == k500Error) throw Error();

        if (trimCompanyName.includes("''"))
          trimCompanyName = trimCompanyName.replaceAll("''", "'");
        errorInOperation = await this.repoManager.updateData(
          GoogleCompanyResultEntity,
          { companyName: trimCompanyName },
          { id: withTrimId },
          true,
        );
        if (errorInOperation == k500Error) throw Error();
      }
    }
    console.log('end migratecompanyName');
    return true;
  }

  async migrateDeleteFileOfGoogleCloud(batchSize: number) {
    console.log('start migrateDeleteFileOfGoogleCloud');
    let isMigrate = false;
    let index = 0;
    while (!isMigrate) {
      const fileData = await this.repoManager.getTableWhereData(
        GoogleFileEntity,
        ['fileUrl'],
        {
          limit: batchSize * 10,
          offset: index,
          order: [['id', 'ASC']],
        },
      );
      if (fileData == k500Error) throw new Error();
      if (!fileData.length) {
        isMigrate = true;
        break;
      }
      const fileUrls = fileData.map((el) => el.fileUrl);
      for (let j = 0; j < 10; j++) {
        const startPoint = j * batchSize;
        const tempArr = fileUrls.slice(startPoint, startPoint + batchSize);
        if (!tempArr.length) break;
        console.log(
          `start deleteFile in Cloud from offset is ${startPoint} and limit is ${batchSize}`,
        );
        await Promise.all(
          tempArr.map((el) => this.fileService.deleteCloudFile(el)),
        );
        console.log(
          `end deleteFile in Cloud from offset is ${startPoint} and limit is ${batchSize}`,
        );
      }
      index = index + batchSize * 10;
    }
    console.log('end migrateDeleteFileOfGoogleCloud');
    return true;
  }

  //#region Migration of user data from registeredUsers entity to loanTransactions entity.
  async migrateUserDetails(batchSize: number) {
    console.log('migrateUserDetails started');

    const response = {
      success: [],
      failure: [],
    };

    let isMigrationCompleted = false;

    let index = 0;
    let offset: any;

    while (!isMigrationCompleted) {
      try {
        offset = index;

        const rawQuery1 = `
        SELECT "id", "userId" FROM public."loanTransactions"
        WHERE
        "hashPhone" IS NULL
        ORDER BY "id" ASC
        LIMIT ${batchSize} OFFSET ${offset};
        `;

        const userLoanList = await this.repoManager.injectRawQuery(
          loanTransaction,
          rawQuery1,
        );

        if (userLoanList.length == 0) {
          isMigrationCompleted = true;
          break;
        }

        const updateOn = {};
        for (const { userId, id } of userLoanList) {
          if (!updateOn[userId]) {
            updateOn[userId] = [];
          }
          updateOn[userId].push(id);
        }

        const userIdsWithQuotes = Object.keys(updateOn)
          .map((key) => `'${key}'`)
          .join(',');

        const rawQuery2 = `
        SELECT 
          "id", 
          "fullName", 
          "email", 
          "phone", 
          "hashPhone" 
        FROM public."registeredUsers" 
          WHERE 
          "id" IN (${userIdsWithQuotes}) AND
          ("fullName" IS NOT NULL OR
          "email" IS NOT NULL OR
          "phone" IS NOT NULL OR
          "hashPhone" IS NOT NULL);
        `;

        const userDataList = await this.repoManager.injectRawQuery(
          registeredUsers,
          rawQuery2,
        );

        const totalUserToUpdate = userDataList.length;

        if (totalUserToUpdate == 0) {
          isMigrationCompleted = true;
          break;
        }

        const tempObj = {};
        for (const userDetails of userDataList) {
          tempObj[userDetails?.id] = {
            fullName: userDetails?.fullName,
            email: userDetails?.email,
            phone: userDetails?.phone,
            hashPhone: userDetails?.hashPhone,
          };
        }

        console.log(
          `Limit: ${batchSize}, Total users to update: ${totalUserToUpdate}`,
        );

        for (let i = 0; i < totalUserToUpdate; i++) {
          const ele = userDataList[i];

          const userData = tempObj[ele?.id];
          const loanIds = updateOn[ele?.id];

          try {
            const loanOptions: any = {
              where: { id: loanIds },
              silent: true,
            };

            await this.repoManager.updateRowWhereData(
              loanTransaction,
              userData,
              loanOptions,
            );

            response.success.push(...loanIds);
          } catch (error) {
            response.failure.push(...loanIds);
          }
        }
        console.log(totalUserToUpdate, 'users updated.');
        index = index + batchSize;
      } catch (error) {
        console.log({ error });
      }
    }
    console.log('migrateUserDetails ended');

    return response;
  }
  //#endregion Migration of user data from registeredUsers entity to loanTransactions entity.

  async migrateOldGSTData() {
    const batchSize = 10000; // Number of records to update in each batch
    const exceptionState =
      EnvConfig.nbfc.nbfcType === '1' ? 'Uttar Pradesh' : 'Gujarat';

    // Step 1: Fetch all loan data
    const allLoanDiabData = await this.loanRepo.getTableWhereData(
      ['id', 'userId', 'penaltyCharges'],
      {
        where: {
          loanStatus: ['Active', 'Complete'],
          // id:1333462
        },
        // limit: 10000
      },
    );

    if (allLoanDiabData == k500Error) throw new Error();

    // Step 2: Identify records without I_GST
    const loanIdsWithOutIgst = allLoanDiabData.filter(
      (data) =>
        !data?.penaltyCharges?.hasOwnProperty('I_GST') &&
        data?.userId &&
        data?.penaltyCharges,
    );

    const userIds = loanIdsWithOutIgst.map((entry) => entry.userId);

    // Step 3: Fetch user and KYC data
    const userData = await this.userRepo.getTableWhereData(['id', 'kycId'], {
      where: { id: userIds },
    });

    if (userData == k500Error) throw new Error();

    const kycData = await this.kycRepo.getTableWhereData(
      ['aadhaarState', 'aadhaarResponse', 'userId'],
      {
        where: { id: userData.map((user) => user?.kycId), aadhaarStatus: '1' },
      },
    );

    if (kycData == k500Error) throw new Error();

    // Step 4: Build a map of userId to state
    const kycMap = new Map();
    kycData.forEach((kyc) => {
      let parsedAadhaarResponse: any = {};
      try {
        if (!kyc?.aadhaarState) {
          parsedAadhaarResponse = JSON.parse(kyc?.aadhaarResponse) ?? {};
        }
      } catch (e) {}

      const aadhaarResponseState =
        parsedAadhaarResponse?.stateName ??
        parsedAadhaarResponse?.state ??
        parsedAadhaarResponse?.address?.state ??
        '';
      const state = kyc?.aadhaarState
        ? kyc?.aadhaarState
        : aadhaarResponseState;

      kycMap.set(kyc.userId, state);
    });

    // Step 5: Prepare the data for updates
    const updateData = loanIdsWithOutIgst.map((loanEntry) => {
      const state = kycMap.get(loanEntry?.userId) ?? null;
      const isExceptionState = state === exceptionState;

      return {
        id: loanEntry?.id,
        penaltyCharges: {
          ...loanEntry?.penaltyCharges,
          I_GST: !isExceptionState,
        },
      };
    });
    // Step 6: Update records in batches
    for (let i = 0; i < updateData.length; i += batchSize) {
      const batch = updateData.slice(i, i + batchSize);

      // Perform bulk update for the batch
      await Promise.all(
        batch.map((record) =>
          this.loanRepo.updateSilentData(
            { penaltyCharges: record.penaltyCharges },
            { id: record.id },
          ),
        ),
      );
    }

    return { data: updateData, updatedCount: updateData.length };
  }

  async migratePenalAndBounceGstOfWaver(body) {
    const loanId = body?.loanId;
    const isUpdate = body?.isUpdate ?? false;
    const attributes = [
      'id',
      'loanId',
      'unpaid_waiver',
      'paid_waiver',
      'waiver',
      'waived_penal',
      'waived_bounce',
    ];

    const options: any = {
      where: {
        payment_due_status: '1',
        payment_status: '1',
        waived_bounce: { [Op.gt]: ECS_BOUNCE_CHARGE },
      },
    };

    if (loanId) options.where.loanId = loanId;

    const emiData = await this.emiRepo.getTableWhereData(attributes, options);
    if (emiData == k500Error) throw new Error();
    if (emiData.length == 0) return kNoDataFound;

    const loanIds = [...new Set(emiData?.map((el) => el?.loanId))];
    if (!isUpdate) return loanIds;

    for (let i = 0; i < emiData.length; i++) {
      try {
        const ele = emiData[i];
        let updatedWaivedPenal = 0;

        if (ele?.waived_penal > 0)
          updatedWaivedPenal = ele?.waived_penal / 1.18;
        const updatedWaivedBounce = ele?.waived_bounce / 1.18;

        // Calculate total GST removed
        const penalGst = ele?.waived_penal
          ? ele.waived_penal - updatedWaivedPenal
          : 0;
        const bounceGst = ele?.waived_bounce
          ? ele.waived_bounce - updatedWaivedBounce
          : 0;
        const totalGst = penalGst + bounceGst;

        const updateData: any = {
          waived_penal: updatedWaivedPenal,
          waived_bounce: updatedWaivedBounce,
        };

        if (ele?.waiver > totalGst) updateData.waiver = ele?.waiver - totalGst;
        else if (ele?.paid_waiver > totalGst)
          updateData.paid_waiver = ele?.paid_waiver - totalGst;
        else if (ele?.unpaid_waiver > totalGst)
          updateData.unpaid_waiver = ele?.unpaid_waiver - totalGst;

        await this.repoManager.updateRowWhereData(EmiEntity, updateData, {
          where: { id: ele?.id },
        });
      } catch (error) {}
    }
  }
  async migrateCrmDispositionAndTitle() {
    const dispositionData = await this.repoManager.getTableWhereData(
      crmDisposition,
      ['id', 'statusId', 'departmentIds', 'title', 'templateList'],
      {
        where: { title: { [Op.in]: ['Call received', 'Call not received'] } },
        order: [['id', 'ASC']],
      },
    );

    if (dispositionData == k500Error || !dispositionData.length)
      throw new Error();

    const newdispositionData = [];
    let callReceivedId;
    let callNotReceivedId;
    dispositionData.forEach((ele) => {
      if (ele.title == 'Call received') {
        callReceivedId = ele.id;
        delete ele.id;
        newdispositionData.push({ ...ele, title: 'Call received by agent' });
        newdispositionData.push({ ...ele, title: 'Call received by user' });
      } else {
        callNotReceivedId = ele.id;
        delete ele.id;
        newdispositionData.push({
          ...ele,
          title: 'Call not received by agent',
        });
        newdispositionData.push({ ...ele, title: 'Call not received by user' });
      }
    });

    const newCrmDispositons = await this.repoManager.bulkCreate(
      crmDisposition,
      newdispositionData,
      { returning: true },
    );
    if (newCrmDispositons == k500Error) throw new Error();

    const receivedCallNewIds = newCrmDispositons.map((el) => el.id).slice(0, 2);
    const notReceivedCallNewIds = newCrmDispositons.map((el) => el.id).slice(2);

    const addColumnQuery = `ALTER TABLE IF EXISTS public."crmTitles"
    ADD COLUMN IF NOT EXISTS "crmDispositionIds" integer[] DEFAULT ARRAY[]::integer[];`;
    const errorInCreteColumn = await this.repoManager.injectRawQuery(
      crmTitle,
      addColumnQuery,
    );
    if (errorInCreteColumn == k500Error) throw new Error();

    const titleData = await this.repoManager.getTableWhereData(
      crmTitle,
      ['id', 'crmDispositionIds', 'crmDispositionId'],
      {
        where: { crmDispositionId: [callReceivedId, callNotReceivedId] },
        order: [['id', 'ASC']],
      },
    );
    if (titleData == k500Error || !titleData.length) throw new Error();

    for (let i = 0; i < titleData.length; i++) {
      const ele = titleData[i];
      const updatedData = {
        crmDispositionIds:
          ele.crmDispositionId == callReceivedId
            ? [callReceivedId, ...receivedCallNewIds]
            : [callNotReceivedId, ...notReceivedCallNewIds],
      };
      const errorInUpdate = await this.repoManager.updateRowData(
        crmTitle,
        updatedData,
        ele.id,
      );
      if (errorInUpdate == k500Error) throw new Error();
    }
    return true;
  }

  async migrateProteanEntityData(batchSize) {
    let isMigrationCompleted = false;
    let offset = 0;
    while (!isMigrationCompleted) {
      const proteanData: any = await this.repoManager.getTableWhereData(
        ProteanEntity,
        null,
        {
          where: {},
          order: [['createdAt', 'ASC']],
          limit: batchSize,
          offset,
        },
      );
      if (proteanData == k500Error) throw new Error();
      if (!proteanData?.length) {
        isMigrationCompleted = true;
        break;
      }
      let insertDataQuery = '';
      proteanData.forEach((ele, idx) => {
        if (idx == 0) {
          insertDataQuery = `INSERT INTO public."FetchDataEntities"(
            "phone", "hashPhone", "type" , "result", "response", "createdAt", "updatedAt")
            VALUES ('${ele?.phone}', '${ele?.hashPhone}',
            'MIGRATE_PROTEAN_DATA',${null},
            ${ele?.data ? `'${JSON.stringify(ele?.data)}'` : null}, 
            '${ele?.createdAt?.toJSON()}', '${ele?.updatedAt?.toJSON()}')`;
        } else {
          insertDataQuery += `,('${ele?.phone}', '${ele?.hashPhone}',
            'MIGRATE_PROTEAN_DATA',${null},
             ${ele?.data ? `'${JSON.stringify(ele?.data)}'` : null}, 
            '${ele?.createdAt?.toJSON()}', '${ele?.updatedAt?.toJSON()}')`;
        }
      });
      const errorInCreate = await this.repoManager.injectRawQuery(
        FetchDataEntity,
        insertDataQuery,
      );
      if (errorInCreate == k500Error) throw new Error();
      offset += batchSize;
    }
  }
  //#region Migrate lead's disbursement amount to Lead Tracking Table
  async funMigrateDisbursmentAmount(batchSize) {
    console.log('funMigrateDisbursmentAmount started');

    const response = {
      success: [],
      failure: [],
    };

    let isMigrationCompleted = false;

    let index = 0;
    let offset: any;

    while (!isMigrationCompleted) {
      try {
        offset = index;

        const leadOptions = {
          where: { leadStatus: 3, disbursement_amount: null },
          limit: batchSize,
          offset,
        };
        const leadData = await this.repoManager.getTableWhereData(
          LeadTrackingEntity,
          ['hashPhone'],
          leadOptions,
        );

        if (!leadData?.length) {
          isMigrationCompleted = true;
          break;
        }

        const hashPhoneArr = leadData.map((item) => item.hashPhone);
        const userData = await this.repoManager.getTableWhereData(
          HashPhoneEntity,
          ['userId', 'hashPhone'],
          { where: { hashPhone: hashPhoneArr } },
        );

        const userPhoneObj = {};
        for (const { userId, hashPhone } of userData)
          if (!userPhoneObj[hashPhone]) userPhoneObj[hashPhone] = userId;

        const userIdArr = userData.map((item) => item.userId);
        const disbursementOptions = {
          where: {
            userId: userIdArr,
            status: 'processed',
          },
        };
        const disbursementData = await this.repoManager.getTableWhereData(
          disbursementEntity,
          ['userId', 'amount'],
          disbursementOptions,
        );

        const userAmountObj = {};
        for (const { userId, amount } of disbursementData)
          if (!userAmountObj[userId]) userAmountObj[userId] = amount;

        const totalLeadsToUpdate = Object.keys(userAmountObj).length;
        console.log(
          `Limit: ${batchSize}, Total leads to update: ${totalLeadsToUpdate}`,
        );

        for (const hashPhone of Object.keys(userPhoneObj)) {
          if (!hashPhone) continue;
          const userId = userPhoneObj[hashPhone];
          const amount = userAmountObj[userId];

          try {
            const leadOptions = {
              where: { hashPhone },
              silent: true,
            };
            const updateData = {
              disbursement_amount: amount ? amount / 100 : null,
            };

            await this.repoManager.updateRowWhereData(
              LeadTrackingEntity,
              updateData,
              leadOptions,
            );
            response.success.push(hashPhone);
          } catch (error) {
            response.failure.push(hashPhone);
          }
        }
        console.log(totalLeadsToUpdate, 'leads updated.');
        index = index + batchSize;
      } catch (error) {
        console.log({ error });
      }
    }
    console.log('funMigrateDisbursmentAmount ended');

    return response;
  }
  //#endregion

  //#region Migrate State data on base of pincode for Lead Tracking Table.
  async funMigrateStatePincode(batchSize) {
    console.log('funMigrateStatePincode Started.');

    const pincodeList = this.leadTrackingService.pincodeList;

    if (!pincodeList?.length)
      return k400ErrorMessage('Pincode data not found.');

    const response = {
      success: [],
      failure: [],
    };

    let isMigrationCompleted = false;

    let index = 0;
    let offset: any;

    while (!isMigrationCompleted) {
      try {
        offset = index;

        const leadOptions = {
          where: {
            pincode: { [Op.ne]: null },
            state: { [Op.eq]: null },
          },
          limit: batchSize,
          offset,
        };
        const leadData = await this.repoManager.getTableWhereData(
          LeadTrackingEntity,
          ['id', 'pincode'],
          leadOptions,
        );

        if (!leadData?.length) {
          isMigrationCompleted = true;
          break;
        }

        console.log(
          `Limit: ${batchSize}, Total leads from DB: ${leadData?.length}`,
        );

        const stateUpadteObj: any = {};
        for (const { id, pincode } of leadData) {
          const data = pincodeList.find((item) => item.Pincode == pincode);
          const state = data?.State?.toLowerCase()?.trim() ?? null;
          if (!state) continue;
          if (stateUpadteObj[state]) stateUpadteObj[state].push(id);
          else stateUpadteObj[state] = [id];
        }

        let updateCount = 0;
        for (const state in stateUpadteObj) {
          const idArr = stateUpadteObj[state];
          if (!idArr || idArr?.length == 0) continue;
          if (!state) continue;
          try {
            const leadOptions = {
              where: { id: idArr },
              silent: true,
            };
            const updateData = { state };
            await this.repoManager.updateRowWhereData(
              LeadTrackingEntity,
              updateData,
              leadOptions,
            );
            updateCount++;
            response.success.push(...idArr);
          } catch (error) {
            response.failure.push(...idArr);
          }
        }
        console.log(updateCount, 'update count');
        index = index + batchSize;
      } catch (error) {
        console.log({ error });
      }
    }

    console.log('funMigrateStatePincode Ended.');

    return response;
  }
  //#region

  //#region penaltyMigrationForCreditUsers
  async penaltyMigrationForCreditUsers() {
    let users = [];

    let userWhereWrongData = [];
    let correctData = [];
    let loanNotFound = [];
    let transNotFound = [];
    let emiNotFound = [];
    let loanUpdated = [];
    let emisUpdated = [];
    for (let index = 0; index < users.length; index++) {
      const userId = users[index];

      if (index % 100 == 0) console.log('Len:', users.length, 'Curr:', index);

      // Loan Data
      let loan = await this.loanRepo.getRowWhereData(['id', 'penaltyCharges'], {
        where: {
          userId,
          loanStatus: 'Active',
        },
      });
      if (loan == k500Error) throw new Error('Error in Getting Loan');

      if (!loan) {
        loanNotFound.push(userId);
        continue;
      }
      const loanId = loan.id;
      let isNewUser = loan?.penaltyCharges?.MODIFICATION_CALCULATION ?? false;

      if (!isNewUser) {
        // Trans Data
        let trans = await this.transRepo.getTableWhereData(
          ['emiId', 'penaltyAmount'],
          {
            where: {
              status: kCompleted,
              loanId,
            },
          },
        );
        if (trans == k500Error) throw new Error('Error in Getting Trans');
        if (!trans) {
          transNotFound.push(loanId);
          continue;
        }

        // EMI Data
        let emis = await this.emiRepo.getTableWhereData(
          ['totalPenalty', 'penalty', 'id'],
          {
            where: {
              loanId,
              payment_status: '0',
            },
          },
        );
        if (emis == k500Error) throw new Error('Error in Getting EMIS');
        if (!emis) {
          emiNotFound.push(loanId);
          continue;
        }

        for (let index = 0; index < emis.length; index++) {
          let paidPenalty = 0;
          const emi: EmiEntity = emis[index];
          trans.forEach((t) => {
            if (emi.id == t.emiId) paidPenalty += t.penaltyAmount ?? 0;
          });
          let calRemainingPenalty = (emi.totalPenalty ?? 0) - paidPenalty;
          let remainingPenalty = emi.penalty ?? 0;
          let diff = calRemainingPenalty - remainingPenalty;
          if (paidPenalty > 0) {
            if (diff > 5 || diff < -5) {
              loanUpdated.push(loanId);
              let update = await this.emiRepo.updateRowData(
                {
                  penalty: calRemainingPenalty,
                },
                emi.id,
              );
              if (update == k500Error)
                return {
                  message: 'Error in Getting Trans.',
                  id: emi.id,
                  emisUpdated,
                };
              emisUpdated.push(emi.id);
            } else correctData.push(emi.id);
          }
        }
      }
    }
    loanUpdated = [...new Set(loanUpdated)];
    return {
      loanUpdatedL: loanUpdated.length,
      userWhereWrongData,
      emisUpdated,
    };
  }
  //#endregion

  //#region Migrate all lead's data to AcceptedLead and RejectedLead (Partition Tables)
  async funMigrateAllLeadsToPartition(batchSize) {
    console.log('funMigrateAllLeadsToPartition Started.');

    const response = {
      success: [],
      failure: [],
    };

    let isMigrationCompleted = false;

    let index = 0;
    let offset: any;
    let totalRecordsMigrated = 0;
    while (!isMigrationCompleted) {
      try {
        offset = index;
        console.log('Next round =========================================');

        const rejectedOptions = {
          where: {
            leadStatus: { [Op.in]: [2, 4] },
            isMigrated: { [Op.eq]: null },
          },
          limit: batchSize,
          offset,
        };

        const rejectedLeads = await this.repoManager.getTableWhereData(
          LeadTrackingEntity,
          null,
          rejectedOptions,
        );

        console.log(
          `Limit: ${batchSize}, Total rejected leads from DB: ${rejectedLeads?.length}`,
        );

        const acceptedOptions = {
          where: {
            leadStatus: { [Op.in]: [0, 1, 3] },
            isMigrated: { [Op.eq]: null },
          },
          limit: batchSize,
          offset,
        };

        const acceptedLeads = await this.repoManager.getTableWhereData(
          LeadTrackingEntity,
          null,
          acceptedOptions,
        );

        console.log(
          `Limit: ${batchSize}, Total accepted leads from DB: ${acceptedLeads?.length}`,
        );

        if (!rejectedLeads?.length && !acceptedLeads?.length) {
          isMigrationCompleted = true;
          break;
        }

        const successIds = [];
        const failureIds = [];
        let rejectResult;
        let acceptResult;
        try {
          /// Separate Accepted and Rejected Leads by phone series
          console.log('----------------------------------------');
          console.log('Total rejected leads:', rejectedLeads?.length);
          if (rejectedLeads?.length) {
            const rejectedSeries = this.funGroupLeadsBySeries(rejectedLeads);
            rejectResult = await this.funLeadBulkCreate(
              rejectedSeries?.groupBySeries,
              'rejected',
            );
            console.log(
              rejectResult?.migratedIds?.length ?? 0,
              'leads inserted in Rejected Lead Table',
            );
            if (rejectResult?.migratedIds?.length) {
              successIds.push(...rejectResult?.migratedIds);
              response.success.push(...rejectResult?.migratedIds);
            }
            if (rejectResult?.nonMigratedIds?.length) {
              failureIds.push(...rejectResult?.nonMigratedIds);
              response.failure.push(...rejectResult?.nonMigratedIds);
            }
            if (rejectedSeries?.invalidPhoneArr?.length) {
              failureIds.push(...rejectedSeries?.invalidPhoneArr);
              console.log(
                'Rejected invalid phone found:',
                rejectedSeries?.invalidPhoneArr?.length,
              );
              response.failure.push(...rejectedSeries?.invalidPhoneArr);
            }
          }

          console.log('----------------------------------------');
          console.log('Total accepted Leads:', acceptedLeads?.length);
          if (acceptedLeads?.length) {
            const acceptedSeries = this.funGroupLeadsBySeries(acceptedLeads);
            acceptResult = await this.funLeadBulkCreate(
              acceptedSeries?.groupBySeries,
              'accepted',
            );
            console.log(
              acceptResult?.migratedIds?.length ?? 0,
              'leads inserted in Accepted Lead Table',
            );
            if (acceptResult?.migratedIds?.length) {
              successIds.push(...acceptResult?.migratedIds);
              response.success.push(...acceptResult?.migratedIds);
            }
            if (acceptResult?.nonMigratedIds?.length) {
              failureIds.push(...acceptResult?.nonMigratedIds);
              response.failure.push(...acceptResult?.nonMigratedIds);
            }
            if (acceptedSeries?.invalidPhoneArr?.length) {
              failureIds.push(...acceptedSeries?.invalidPhoneArr);
              console.log(
                'Accepted invalid phone found:',
                acceptedSeries?.invalidPhoneArr?.length,
              );
              response.failure.push(...acceptedSeries?.invalidPhoneArr);
            }
          }

          /// Bulk update isMigrated = true
          console.log('----------------------------------------');
          console.log('Total leads created:', successIds?.length);

          const migrated = await this.repoManager.updateRowWhereData(
            LeadTrackingEntity,
            { isMigrated: true },
            { where: { id: successIds }, silent: true },
          );
          console.log('Migrated: ', migrated);

          const notMigrated = await this.repoManager.updateRowWhereData(
            LeadTrackingEntity,
            { isMigrated: false },
            { where: { id: failureIds }, silent: true },
          );
          console.log('Not migrated: ', notMigrated);
          totalRecordsMigrated = totalRecordsMigrated + successIds?.length;
          console.log(
            `--------------------[ Total Records Migrated: ${totalRecordsMigrated} ]`,
          );
        } catch (error) {}

        index = index + batchSize;
      } catch (error) {
        console.log({ error });
      }
    }
    console.log('funMigrateAllLeadsToPartition Ended.');

    return response;
  }
  //#endregion

  //#region funMigrateAllLeadsToPartition - Group Accepted and Rejected Leads by table series
  private funGroupLeadsBySeries(leadsData) {
    /// Gather id(s) in case of invalid phone number
    const invalidPhoneArr = [];

    const groupBySeries = {};
    for (const element of leadsData) {
      let leadTableSeries;
      const decryptedPhone =
        this.cryptService.decryptPhone(element?.phone) == k500Error
          ? null
          : this.cryptService.decryptPhone(element?.phone);
      if (decryptedPhone)
        leadTableSeries = funGetLeadTableSeries(decryptedPhone);
      else leadTableSeries = 0;

      if (isNaN(leadTableSeries)) {
        invalidPhoneArr.push(element?.id);
        continue;
      }

      if (!groupBySeries[leadTableSeries]) groupBySeries[leadTableSeries] = [];

      groupBySeries[leadTableSeries].push(element);
    }
    return { groupBySeries, invalidPhoneArr };
  }
  //#endregion

  //#region funMigrateAllLeadsToPartition - insert leads using bulk create
  private async funLeadBulkCreate(seriesData, leadStatus) {
    const migratedIds = [];
    const nonMigratedIds = [];

    for (const series of Object.keys(seriesData)) {
      /// Get series wise data
      const seriesDataArr = seriesData[series];
      if (!seriesDataArr?.length) continue;
      /// Extract id(s) from series data
      const idsArr = seriesDataArr?.map((item) => item?.id);
      try {
        let leadTable;
        if (leadStatus == 'accepted') leadTable = acceptedLeadModels[series];
        if (leadStatus == 'rejected') leadTable = rejectedLeadModels[series];
        const isCreated = await this.repoManager.bulkCreate(
          leadTable,
          seriesDataArr,
        );
        if (isCreated == k500Error) nonMigratedIds.push(...idsArr);
        if (isCreated?.length == idsArr?.length) migratedIds.push(...idsArr);
        else if (isCreated?.length < idsArr?.length) {
          const createdIdsArr = isCreated?.map((item) => item.id);
          /// Find which Id(s) were not created
          const missingIds = idsArr.filter((id) => !createdIdsArr.includes(id));
          if (missingIds?.length) {
            nonMigratedIds.push(...missingIds);
          }
        }
      } catch (error) {
        nonMigratedIds.push(...idsArr);
      }
    }
    return { migratedIds, nonMigratedIds };
  }
  //#endregion
  async migrateCompanyIdInLoan() {
    const loanDataQuery = `SELECT "loan"."id",
       "loan"."userId"
        FROM  public."loanTransactions" AS "loan"
        INNER JOIN public."employmentDetails" AS "emp"
        ON "emp"."userId" = "loan"."userId"
        WHERE  "companyId" != (SELECT "id"
                              FROM   public."GoogleCompanyResultEntities"
                              WHERE  "companyName" = UPPER(TRIM("emp"."companyName")))
                AND "loan"."id" = (SELECT "lastLoanId"
                          FROM   public."registeredUsers"
                          WHERE  "id" = "emp"."userId");`;

    const loanData = await this.repoManager.injectRawQuery(
      loanTransaction,
      loanDataQuery,
    );
    if (loanData == k500Error) throw new Error();

    const userIds = loanData.map((ele) => ele.userId);

    const employementData = await this.repoManager.getTableWhereData(
      employmentDetails,
      ['userId', 'companyName'],
      {
        where: { userId: userIds },
      },
    );
    if (employementData == k500Error) throw new Error();

    const companyNames = employementData.map((ele) =>
      ele?.companyName?.trim()?.toUpperCase(),
    );
    const companyData = await this.repoManager.getTableWhereData(
      GoogleCompanyResultEntity,
      ['id', 'companyName'],
      {
        where: { companyName: companyNames },
      },
    );
    if (companyData == k500Error) throw new Error();

    const tempObj = {};
    loanData.forEach((loan) => {
      const companyName = employementData
        ?.find((emp) => emp.userId == loan.userId)
        ?.companyName?.trim()
        ?.toUpperCase();
      const companyId = companyData.find(
        (company) => company.companyName?.trim()?.toUpperCase() == companyName,
      )?.id;
      if (companyId) tempObj[loan.id] = companyId;
    });

    let updateQuery = '';
    for (const loanId in tempObj) {
      updateQuery += `UPDATE public."loanTransactions" SET "companyId" = '${tempObj[loanId]}' WHERE "id" = '${loanId}';`;
    }

    const errorInUpdate = await this.repoManager.injectRawQuery(
      loanTransaction,
      updateQuery,
    );
    if (errorInUpdate == k500Error) throw new Error();
    return true;
  }

  // region migrate cibil req data to archive
  async migrateCibilReqData() {
    const attributes = ['id', 'loanId', 'userId', 'requestdata'];
    const options: any = {
      where: {
        requestdata: { [Op.ne]: null },
        status: '1',
      },
      order: [['id', 'DESC']],
    };
    const cibilData = await this.repoManager.getTableWhereData(
      CibilScoreEntity,
      attributes,
      options,
    );
    if (cibilData == k500Error) throw new Error();

    const length = cibilData.length;
    const totalLists = this.typeService.splitToNChunks(cibilData, 1000);
    for (let index = 0; index < totalLists.length; index++) {
      const targetList = totalLists[index];
      // bulk create
      const bulkCreate = await this.repoManager.bulkCreate(
        CibilScoreArchive,
        targetList,
        { ignoreDuplicates: true },
      );
      if (bulkCreate === k500Error) throw new Error();

      // Extract IDs and update original records
      const idsToUpdate = targetList.map((item) => item.id);
      const updateResult = await this.repoManager.updateRowData(
        CibilScoreEntity,
        { requestdata: null },
        idsToUpdate,
      );
      if (updateResult === k500Error) throw new Error();
    }
    return length;
  }
  // #endregion

  //#region update creditNote for activeUsers
  async updateCreditNoteforUsers() {
    let user = JSON.parse(fs.readFileSync(CRYPT_PATH.activeUsers, 'utf8'));
    let completedLoanUser = JSON.parse(
      fs.readFileSync(CRYPT_PATH.completedLoanCreditData, 'utf8'),
    );

    // Active-Ontime Loan Data
    let userErrorArr = [];
    for (let { userId, creditNote } of user) {
      try {
        let credit = Math.round(creditNote);
        const isUpdated = await this.repoManager.updateRowData(
          registeredUsers,
          { totalCredit: credit, remainingCredit: credit },
          userId,
          true,
        );
        if (isUpdated == k500Error) userErrorArr.push(userId);
      } catch (error) {
        console.log(`Failed to update creditNote for userId: ${userId}`, error);
      }
    }

    // Completed Loan Data
    let completedLoanUserErrorArr = [];
    let count = 0;
    for (let { userId, creditNote } of completedLoanUser) {
      try {
        let credit = Math.round(creditNote);
        if (credit < 10) console.log({ userId, credit });
        const isUpdated = await this.repoManager.updateRowData(
          registeredUsers,
          { totalCredit: credit, remainingCredit: credit },
          userId,
          true,
        );
        if (isUpdated == k500Error) completedLoanUserErrorArr.push(userId);
        count = count + 1;
      } catch (error) {
        console.log(`Failed to update creditNote for userId: ${userId}`, error);
      }
    }
    return { completedLoanUserErrorArr, userErrorArr };
  }

  async migrateFilingDates(query) {
    const isMigrate = query?.isMigrate === 'true';
    const isneedData = query?.isneedData === 'true';

    const legalDataQuery = `SELECT id, "caseDetails", "filingDate" FROM "LegalCollectionEntities"
        WHERE "filingDate" IS NULL AND "caseDetails"->>'caseFiledDate' IS NOT NULL AND  "caseDetails"->>'caseFiledDate' != ''
      `;
    const allLegalCases = await this.repoManager.injectRawQuery(
      LegalCollectionEntity,
      legalDataQuery,
    );
    if (allLegalCases == k500Error) throw new Error();
    if (!allLegalCases || allLegalCases?.length == 0) return kNoDataFound;
    if (!isMigrate) {
      if (isneedData)
        return {
          result: allLegalCases,
          totalToMigrate: allLegalCases?.length ?? 0,
        };
      else
        return {
          totalToMigrate: allLegalCases?.length ?? 0,
        };
    }

    let migratedCount = 0;
    for (const legalCase of allLegalCases) {
      const caseFiledDate = legalCase?.caseDetails?.caseFiledDate;
      if (caseFiledDate) {
        await this.legalRepo.updateRowData(
          { filingDate: new Date(Number(caseFiledDate)).toJSON() },
          legalCase.id,
          true,
        );
        migratedCount++;
      }
    }

    return { migratedCount };
  }

  async migrateZoopKycData(query) {
    const limit = parseInt(query?.limit ?? 1000);

    const kycAttr = ['panResponse', 'hashPan', 'userId'];
    let options: any = {
      where: {
        panResponse: { [Op.iLike]: '%request_id%' },
      },
      limit,
      useMaster: false,
    };

    const kycData = await this.kycRepo.getTableWhereData(kycAttr, options);
    if (kycData === k500Error) return kInternalError;

    const userIds = kycData.map((el) => el.userId);
    const userData = await this.userRepo.getTableWhereData(
      ['id', 'phone', 'hashPhone'],
      {
        where: { id: userIds },
        useMaster: false,
      },
    );
    if (userData === k500Error) return kInternalError;

    const userMap = new Map();
    userData.forEach((user) => {
      userMap.set(user.id, {
        phone: user.phone,
        hashPhone: user.hashPhone,
      });
    });

    const createdUserPan = [];
    /// pre pare the data
    for (let i = 0; i < kycData.length; i++) {
      const ele = kycData[i];
      const userData = userMap.get(ele?.userId);
      const panResponse = JSON.parse(ele?.panResponse);
      const panResponseTime = panResponse?.response_timestamp ?? new Date();
      const logTrackData = {
        phone: userData?.phone,
        hashPhone: userData?.hashPhone,
        hashPan: ele?.hashPan,
        result: null,
        requestData: JSON.stringify({
          panNumber: ele.hashPan,
        }),
        response: JSON.stringify(panResponse),
        type: 'ZOOP_PAN_VALIDATION',
        createdAt: panResponseTime,
      };
      const res = await this.repoManager.createRowData(
        FetchDataEntity,
        logTrackData,
      );
      if (res?.id) createdUserPan.push(ele?.userId);
    }
    return createdUserPan;
  }

  async addBankTransferCreditData() {
    const bankTransferCreditData = JSON.parse(
      fs.readFileSync(CRYPT_PATH.bankTranserCreditData, 'utf8'),
    );

    if (!bankTransferCreditData?.length)
      return k400ErrorMessage('Bank Transfer Credit Data not found.');

    const response = {
      success: [],
      failure: [],
    };

    for (const ele of bankTransferCreditData) {
      try {
        let {
          userId,
          bankName,
          accountNumber,
          utr,
          amount,
          totalCredit,
          remainingCredit,
        } = ele;
        if (!userId) {
          response.failure.push(ele);
          continue;
        }

        if (!totalCredit || !remainingCredit) {
          // Fetch totalCredit and remainingCredit from registeredUsers
          const data = await this.repoManager.getRowWhereData(
            registeredUsers,
            ['totalCredit', 'remainingCredit'],
            { where: { id: userId } },
          );
          if (data == k500Error || !data) {
            response.failure.push(ele);
            continue;
          }
          totalCredit = data?.totalCredit ?? 0;
          remainingCredit = data?.remainingCredit ?? 0;
        }
        amount = Math.round(amount);
        if (amount > totalCredit || totalCredit <= 0) {
          response.failure.push(ele);
          continue;
        }

        remainingCredit -= amount;
        if (remainingCredit < 0) {
          response.failure.push(ele);
          continue;
        }
        let creditData = {
          bankName: bankName,
          accountNumber: accountNumber,
          utr: utr,
          amount,
        };

        const isUpdated = await this.repoManager.updateRowData(
          registeredUsers,
          { creditData, totalCredit, remainingCredit },
          userId,
          true,
        );
        if (isUpdated == k500Error) throw new Error();
        response.success.push(userId);
      } catch (error) {
        response.failure.push(ele);
      }
    }
    return response;
  }

  //#region Run a CRON job to expire 'Accepted' leads as 'Expired', for those leads whose lead status is still 'Accepted', even after 30 days of lead's creation date.
  async funExpireOnlyAcceptedLeads(batchSize) {
    console.log('funExpireOnlyAcceptedLeads Started.');

    const acceptedErrors = [];
    const updateErrors = [];

    /// Get the date 30 days ago ['2025-06-24T15:20:33.974Z' => '2025-05-25T00:00:00.000Z']
    const today = new Date();

    const date = new Date(
      Date.UTC(today.getUTCFullYear(), today.getUTCMonth(), today.getUTCDate()),
    );

    date.setUTCDate(date.getUTCDate() - LEAD_DATE_RANGE_LIMIT_DAYS);

    let toUpdateCount = 0;
    let totalUpdatedCount = 0;

    //#region Table index loop starts
    for (let tableNo = 0; tableNo < 10; tableNo++) {
      console.log(
        `Update Round for table [ ${tableNo} ] =========================================`,
      );
      let isMigrationCompleted = false;
      //#region Data to update form single table starts
      while (!isMigrationCompleted) {
        try {
          const getOptions = {
            where: {
              createdAt: {
                [Op.lt]: date,
              },
              leadStatus: kLeadStatus.Accepted,
            },
            limit: batchSize,
          };

          /// 'true' for Accepted Leads Table
          const acceptedLeadList =
            await this.leadTrackingService.funGetLeadCount(
              getOptions,
              true,
              tableNo,
              ['hashPhone', 'fullName', 'createdAt', 'leadStatus'],
            );
          if (acceptedLeadList == k500Error) {
            acceptedErrors.push(tableNo);
          }

          console.log('Total leads data to update:', acceptedLeadList?.length);

          if (!acceptedLeadList?.length) {
            isMigrationCompleted = true;
            break;
          }

          toUpdateCount += acceptedLeadList?.length;

          const hashPhoneArr = acceptedLeadList?.map((lead) => lead?.hashPhone);

          const updateOptions = {
            where: {
              hashPhone: { [Op.in]: hashPhoneArr },
            },
            silent: true,
          };
          const dataToUpdate = {
            leadStatus: kLeadStatus.Expired,
          };
          const updateResult = await this.repoManager.updateRowWhereData(
            acceptedLeadModels[tableNo],
            dataToUpdate,
            updateOptions,
          );
          if (updateResult == k500Error) {
            updateErrors.push(tableNo);
          } else {
            console.log('Total leads updated:', updateResult);
            console.log(
              '--------------------------------------- Next batch size',
            );
            totalUpdatedCount = totalUpdatedCount + (updateResult?.[0] ?? 0);
          }
        } catch (error) {
          console.log('Error:', { error });
        }
      }
      //#endregion
    }
    //#endregion
    console.log('funExpireAcceptedLeads Completed.');

    const errors = acceptedErrors?.length
      ? {
          'Accepted Lead table indexes with INTERNAL_SERVER_ERROR errors:':
            acceptedErrors,
        }
      : '';

    return {
      'Error:': errors,
      'Update report:': {
        toUpdateCount,
        totalUpdatedCount,
      },
    };
  }
  //#endregion

  /// DO NOT REMOVE THIS MIGRATION CODE ==================================================================
  //#region Might need migration for expiring leads in case of expiring in-process(1) or disbursed(3) leads
  // Update lead status as 'Expired', for those leads whose lead status is not 'Disbursed', within 30 days of lead's creation date.
  // async funExpireAllLeadsInProgress(batchSize) {
  //   console.log('funExpireAllLeadsInProgress Started.');
  //   const acceptedErrors = [];
  //   const updateErrors = [];

  //   /// Get the date 30 days ago ['2025-06-24T15:20:33.974Z' => '2025-05-25T00:00:00.000Z']
  //   const today = new Date();

  //   const date = new Date(
  //     Date.UTC(today.getUTCFullYear(), today.getUTCMonth(), today.getUTCDate()),
  //   );

  //   date.setUTCDate(date.getUTCDate() - LEAD_DATE_RANGE_LIMIT_DAYS);

  //   let toUpdateCount = 0;
  //   let totalUpdatedCount = 0;

  //   //#region Table index loop starts
  //   for (let tableNo = 0; tableNo < 10; tableNo++) {
  //     console.log(
  //       `Update Round for table [ ${tableNo} ] =========================================`,
  //     );

  //     let isMigrationCompleted = false;

  //     while (!isMigrationCompleted) {
  //       try {
  //         const getOptions = {
  //           where: {
  //             createdAt: {
  //               [Op.lt]: date,
  //             },
  //             leadStatus: {
  //               [Op.in]: [kLeadStatus.Accepted, kLeadStatus.InProcess],
  //             },
  //           },
  //           limit: batchSize,
  //         };
  //         /// 'true' for Accepted Leads Table
  //         const acceptedLeadList =
  //           await this.leadTrackingService.funGetLeadCount(
  //             getOptions,
  //             true,
  //             tableNo,
  //             ['hashPhone', 'fullName', 'createdAt', 'leadStatus'],
  //           );

  //         if (acceptedLeadList == k500Error) {
  //           acceptedErrors.push(tableNo);
  //         }

  //         console.log('Total leads data to update:', acceptedLeadList?.length);

  //         if (!acceptedLeadList?.length) {
  //           isMigrationCompleted = true;
  //           break;
  //         }

  //         toUpdateCount += acceptedLeadList?.length;

  //         //#region Might need below lead tracking code in case of expiring disbursed leads
  //         /// Take out leads data whose lead status is 'Disbursed'
  //         // const disbursedLeads = acceptedLeadList.filter(
  //         //   (item) => kLeadStatus.Disbursed == item?.leadStatus,
  //         // );

  //         // let expiredLeads: any = [];
  //         /// If there are disbursed leads then only check for expire leads
  //         // if (disbursedLeads?.length) {
  //         //   expiredLeads = await this.funGetDisbursedExpiredLeads(
  //         //     disbursedLeads,
  //         //   );
  //         //   if (!expiredLeads?.length || expiredLeads == k500Error) {
  //         //     expiredLeads = [];
  //         //   }
  //         // }
  //         //#endregion

  //         /// take out leads who are still 'Accepted' or 'In Process'
  //         const inProcessLeads = acceptedLeadList
  //           ?.filter((item) => item?.leadStatus == kLeadStatus.InProcess)
  //           ?.map((lead) => lead?.hashPhone);
  //         const nonDisbursedLeads = acceptedLeadList?.map(
  //           (lead) => lead?.hashPhone,
  //         );

  //         /// Reset lead-user's lead source to null, if lead gets expired
  //         if (inProcessLeads?.length) {
  //           const updateExpiredUsers =
  //             await this.repoManager.updateRowWhereData(
  //               registeredUsers,
  //               { leadSource: null },
  //               { where: { hashPhone: inProcessLeads }, silent: true },
  //             );

  //           if (updateExpiredUsers != k500Error) {
  //             console.log(
  //               'Lead source reset into registered user entity for leads:',
  //               updateExpiredUsers?.[0],
  //             );
  //           }
  //         }

  //         if (nonDisbursedLeads?.length) {
  //           const updateOptions = {
  //             where: {
  //               hashPhone: nonDisbursedLeads,
  //             },
  //             silent: true,
  //           };

  //           const dataToUpdate = {
  //             leadStatus: kLeadStatus.Expired,
  //           };

  //           const updateResult = await this.repoManager.updateRowWhereData(
  //             acceptedLeadModels[tableNo],
  //             dataToUpdate,
  //             updateOptions,
  //           );
  //           if (updateResult == k500Error) {
  //             updateErrors.push(tableNo);
  //           } else {
  //             console.log('Total leads updated:', updateResult);
  //             console.log(
  //               '--------------------------------------- Next batch size',
  //             );
  //             totalUpdatedCount = totalUpdatedCount + (updateResult?.[0] ?? 0);
  //           }
  //         }
  //       } catch (error) {
  //         console.log('Error:', { error });
  //       }
  //     }
  //   }
  //   //#endregion
  //   console.log('funExpireAllLeadsInProgress Completed.');

  //   const errors = acceptedErrors?.length
  //     ? {
  //         'Accepted Lead table indexes with INTERNAL_SERVER_ERROR errors:': {
  //           acceptedErrors,
  //           updateErrors,
  //         },
  //       }
  //     : '';
  //   return {
  //     'Error:': errors,
  //     'Update report:': {
  //       toUpdateCount,
  //       totalUpdatedCount,
  //     },
  //   };
  // }
  //#endregion
  /// DO NOT REMOVE THIS MIGRATION CODE ==================================================================
  //#region Might need below lead tracking code in case of expiring disbursed leads
  // private async funGetDisbursedExpiredLeads(disbursedLeads) {
  //   try {
  //     const disbursedPhoneList = disbursedLeads?.map((lead) => lead?.hashPhone);

  //     const userData = await this.repoManager.getTableWhereData(
  //       registeredUsers,
  //       ['lastLoanId', 'hashPhone'],
  //       {
  //         where: {
  //           hashPhone: disbursedPhoneList,
  //         },
  //       },
  //     );
  //     if (!userData?.length || userData == k500Error) return [];

  //     const lastLoanIds = userData.map((item) => item?.lastLoanId);

  //     const disbursementData = await this.repoManager.getTableWhereData(
  //       disbursementEntity,
  //       ['createdAt', 'loanId', 'userId'],
  //       {
  //         where: { loanId: lastLoanIds, status: 'processed' },
  //       },
  //     );
  //     if (!disbursementData?.length || disbursementData == k500Error) return [];

  //     const expiredLeads = [];
  //     disbursedLeads.map((lead) => {
  //       const user = userData.find(
  //         (user) => user?.hashPhone == lead?.hashPhone,
  //       );
  //       const disbursedLoan = disbursementData.find(
  //         (disb) => disb?.loanId == user?.lastLoanId,
  //       );

  //       if (lead?.createdAt && disbursedLoan?.createdAt && lead?.hashPhone) {
  //         const isExpired = this.dateService.funCalcDaysDiff(
  //           lead?.createdAt,
  //           disbursedLoan?.createdAt,
  //           LEAD_EXPIRE_DAYS,
  //         );
  //         if (isExpired) expiredLeads.push(lead?.hashPhone);
  //       }
  //     });
  //     return expiredLeads;
  //   } catch (error) {
  //     return k500Error;
  //   }
  // }
  //#endregion
  /// DO NOT REMOVE THIS MIGRATION CODE ==================================================================

  async migrateLspId() {
    const attributes = ['hashPhone'];
    const userData = await this.userRepo.getTableWhereData(attributes, {
      where: {
        appType: 0,
        lspId: { [Op.eq]: null },
      },
      limit: 100,
    });

    if (userData === k500Error) throw new Error();
    if (!userData || !userData.length) return kNoDataFound;

    const hashPhone = [...new Set(userData.map((el) => el?.hashPhone))];
    const url = `${EnvConfig.url.lspBaseLink}/v4/shared/migrateUserId`;
    const lspIdData = await this.api.post(url, { hashPhone });

    if (lspIdData === k500Error) throw new Error();

    const lspIds = lspIdData?.data;
    if (!lspIds || !lspIds.length) return kNoDataFound;

    for (let i = 0; i < lspIds.length; i++) {
      const ele = lspIds[i];
      const phone = ele?.phone;
      const lspId = ele?.id;
      if (lspId)
        await this.userRepo.updateRowWhereData(
          { lspId },
          { where: { hashPhone: phone } },
        );
    }

    // Call the function recursively to process the next batch
    return await this.migrateLspId();
  }
}
