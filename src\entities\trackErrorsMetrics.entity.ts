import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { registeredUsers } from './user.entity';
import { TrackStepCategory } from './trackStepCategory.entity';
@Table({})
export class TrackErrorMetrics extends Model<TrackErrorMetrics> {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    allowNull: false,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  apiEndPoint: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  errorMessage: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  errorStatusCode: number;

  @ForeignKey(() => TrackStepCategory)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  stepId: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  errorCount: number;

  @Column({
    type: DataType.UUID,
    allowNull: true,
  })
  userId: string;

  @BelongsTo(() => TrackStepCategory)
  trackStepCategory: TrackStepCategory;
}
