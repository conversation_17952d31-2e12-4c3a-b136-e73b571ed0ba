import { Model, Table, Column, DataType } from 'sequelize-typescript';

@Table({})
export class EPFODetailsEntity extends Model<EPFODetailsEntity> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.SMALLINT,
    allowNull: false,
    defaultValue: 1,
    comment: '1 for protean, 2 for BEFISC',
  })
  typeOfService: number;

  @Column({
    type: DataType.SMALLINT,
    allowNull: true,
  })
  tenureOfEmployment: number;

  @Column({
    type: DataType.SMALLINT,
    allowNull: true,
  })
  totalTenureOfEmployment: number;

  @Column({ type: DataType.DATE })
  joiningDate: Date;

  @Column({ type: DataType.DATE })
  lastPFDate: Date;

  @Column({ type: DataType.DATE })
  exitDate: Date;

  @Column({ type: DataType.UUID })
  userId: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  hashPhone: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  phone: string;

  @Column({ type: DataType.TEXT })
  latestCompanyName: string;

  @Column({ type: DataType.TEXT })
  employeeName: string;

  @Column({ type: DataType.TEXT })
  exitReason: string;

  @Column({ type: DataType.ARRAY(DataType.TEXT) })
  uanList: string;

  @Column({ type: DataType.TEXT })
  allEmploymentData: string;
}
