pipeline {
    agent any

    environment {
        APPROVAL_LINK = "${env.JENKINS_URL}job/${env.JOB_NAME.replaceAll('/', '/job/')}/${env.BUILD_NUMBER}/input/"
        JENKINS_CONSOLE_LINK = "${env.JENKINS_URL}job/${env.JOB_NAME.replaceAll('/', '/job/')}/${env.BUILD_NUMBER}/console"
        SCRIPT_PATH = "/home/<USER>/scripts/backend_deploy.sh"
    }

    stages {
        stage('Commit Info') {
            steps {
                script {
                    withCredentials([string(credentialsId: 'APPROVAL_USERS', variable: 'APPROVAL_USERS')]) {
                        env.COMMIT_MESSAGE = sh(script: 'git log -1 --pretty=%B', returnStdout: true).trim()
                        env.COMMIT_MESSAGE = env.COMMIT_MESSAGE.replaceAll(/[\'\"\\]/, '') 
                        def committer = sh(script: 'git log -1 --pretty=%cn', returnStdout: true).trim()
                        if (committer.toLowerCase() == 'github') {
                            committer = sh(script: 'git log -1 --pretty=%an', returnStdout: true).trim()
                        }
                        env.COMMITTER_NAME = committer
                        env.REPO_NAME = sh(script: "basename \$(git config --get remote.origin.url) | sed 's/\\.git\$//'", returnStdout: true).trim()
                        env.BRANCH_NAME = env.GIT_BRANCH ?: sh(script: 'git rev-parse --abbrev-ref HEAD', returnStdout: true).trim()
                        env.APPROVAL = sh(script: "cat ${APPROVAL_USERS}", returnStdout: true).trim()
                        env.HOSTNAME = sh(script: 'hostname', returnStdout: true).trim()
                    }
                }
            }
        }

        stage('Approval Notification') {
            steps {
                script {                        
                    withCredentials([string(credentialsId: 'SLACK_TOKEN', variable: 'SLACK_TOKEN'),
                                     string(credentialsId: 'APPROVAL_CHANNEL', variable: 'APPROVAL_CHANNEL')]) {
                        sh """
                            curl -X POST -H 'Authorization: Bearer ${SLACK_TOKEN}' -H 'Content-Type: application/json' \
                            --data '{"text": "*Deployment Approval Required ${env.BRANCH_NAME} `${env.REPO_NAME}` (${env.HOSTNAME}):grey_question:* \n*Committer:* `${env.COMMITTER_NAME}` \n*Commit Message:* ${env.COMMIT_MESSAGE} \n*Click to Approve:* <${APPROVAL_LINK}|Approve Deployment>", "channel": "${APPROVAL_CHANNEL}"}' \
                            https://slack.com/api/chat.postMessage
                        """
                    }
                }
            }
        }

        stage('Approval') {
            steps {
                script {
                    try {
                        timeout(time: 30, unit: 'MINUTES') {
                            def approvers
                            withCredentials([string(credentialsId: 'APPROVAL_USERS', variable: 'APPROVAL_USERS')]) {
                                approvers = sh(script: "cat ${APPROVAL_USERS}", returnStdout: true).trim()
                            }
                            def approvalParams = input(
                                message: 'Approve deployment?',
                                ok: 'Approve',
                                submitter: approvers,
                                submitterParameter: 'submitter',
                                parameters: [string(name: 'Reason', defaultValue: '', description: 'Reason for approval')]
                            )
                            env.APPROVAL_SUBMITTER = approvalParams.submitter
                            env.APPROVAL_REASON = approvalParams.Reason
                            env.APPROVAL_REASON = env.APPROVAL_REASON.replaceAll(/[\'\"\\]/, '') 
                            if (!approvers.split(",").contains(env.APPROVAL_SUBMITTER.trim())) {
                                error("Unauthorized approver: ${env.APPROVAL_SUBMITTER}. Deployment rejected!")
                            }
                        }
                    } catch (e) {
                        env.APPROVAL_ABORTED = "true"
                        env.APPROVAL_REASON = e.getMessage() ?: "Approval aborted or timed out"
                        error("Approval was not given: ${env.APPROVAL_REASON}")
                        env.APPROVAL_REASON = env.APPROVAL_REASON.replaceAll(/[\'\"\\]/, '') 
                    }
                }
            }
        }

        stage('Deploy') {
            steps {
                sh "bash ${SCRIPT_PATH}"
            }
        }
    }

    post {
        success {
            script {               
                withCredentials([string(credentialsId: 'SLACK_TOKEN', variable: 'SLACK_TOKEN'),
                                 string(credentialsId: 'slackChannel', variable: 'SLACK_CHANNEL')]) {
                    sh """
                        curl -X POST -H 'Authorization: Bearer ${SLACK_TOKEN}' -H 'Content-Type: application/json' \
                        --data '{"text": "*Update -> New ${env.BRANCH_NAME} `${env.REPO_NAME}`(${env.HOSTNAME}) changes are deployed :rocket:* \n*Committer:* `${env.COMMITTER_NAME}` \n*Commit Message:* ${env.COMMIT_MESSAGE} \n*Approved By:* `${env.APPROVAL_SUBMITTER}` \n*Reason:* ${env.APPROVAL_REASON} \n*Click to Check:* <${JENKINS_CONSOLE_LINK}|Click Here >", "channel": "${SLACK_CHANNEL}"}' \
                        https://slack.com/api/chat.postMessage
                    """
                }
            }
        }

        failure {
            script {
                withCredentials([string(credentialsId: 'SLACK_TOKEN', variable: 'SLACK_TOKEN'),
                                 string(credentialsId: 'slackChannel', variable: 'SLACK_CHANNEL')]) {
                    def message = env.APPROVAL_ABORTED == "true" ? "*Approval was aborted or timed out* :stopwatch:" : "Build Failed :x:"
                    sh """
                        curl -X POST -H 'Authorization: Bearer ${SLACK_TOKEN}' -H 'Content-Type: application/json' \
                        --data '{"text": "${message} ${env.BRANCH_NAME} `${env.REPO_NAME}`(${env.HOSTNAME}) \n*Committer:*  `${env.COMMITTER_NAME}` \n*Commit Message:* ${env.COMMIT_MESSAGE}\n*Reason:* ${env.APPROVAL_REASON} \n*Click to Check:* <${JENKINS_CONSOLE_LINK}|Click Here >", "channel": "${SLACK_CHANNEL}"}' \
                        https://slack.com/api/chat.postMessage
                    """
                }
            }
        }
    }
}
