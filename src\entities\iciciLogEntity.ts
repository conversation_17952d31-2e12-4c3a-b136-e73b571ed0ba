import { Table, Column, Model, DataType } from 'sequelize-typescript';

@Table({})
export class iciciLogEntity extends Model<iciciLogEntity> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  loanId: number;

  @Column({
    type: DataType.JSONB,
    allowNull: false,
  })
  request: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  response: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  encrypted_request: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  decrypted_response: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: false,
  })
  is_success: string;

  @Column({
    type: DataType.SMALLINT, //0 for payout 1 for check status ICICI_REQUEST_TYPE
    allowNull: true,
  })
  request_type: string;
}
