import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Res,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { EmployeeEligibilityService } from './employeeEligibility.service';
import { kSuccessData } from 'src/constants/responses';
import { FileInterceptor } from '@nestjs/platform-express';
import { kUploadFileObj } from 'src/constants/objects';

@Controller('admin/employeeEligibility')
export class EmployeeEligibilityController {
  constructor(private service: EmployeeEligibilityService) {}

  //#region Below POST API works for both create and update operations
  @Post('create')
  async create(@Res() res, @Body() body) {
    const data = await this.service.createOrUpdateRow(body);
    if (data?.message) return res.send(data);
    return res.send({ ...kSuccessData, data });
  }
  //#endregion

  //#region Below GET API works for getting and finding operations
  @Get('get')
  async get(@Res() res, @Query() query) {
    const data = await this.service.getEmployeeDetails(query);
    return res.send({ ...kSuccessData, data });
  }

  //#region Below POST API works for creating records by uploading excel file.
  @Post('upload')
  @UseInterceptors(FileInterceptor('file', kUploadFileObj()))
  async upload(@Res() res, @UploadedFile() file, @Body() body) {
    const data = await this.service.uploadExcel(file, body);
    if (data?.message) return res.send(data);
    return res.send({ ...kSuccessData, data });
  }
  //#endregion
}
