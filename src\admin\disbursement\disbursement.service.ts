// Imports
import { Injectable } from '@nestjs/common';
import { Op, Sequelize } from 'sequelize';
import { gIsPROD } from 'src/constants/globals';
import { k500Error } from 'src/constants/misc';
import {
  k403Forbidden,
  k422ErrorMessage,
  kInternalError,
  kInvalidParamValue,
  kParamMissing,
} from 'src/constants/responses';
import {
  kCashfree,
  kCompleted,
  kCryptography,
  KICICI,
  kInitiated,
  kNoDataFound,
  kPGPDecrypt,
  kRefund,
  kRuppe,
} from 'src/constants/strings';
import { disbursementEntity } from 'src/entities/disbursement.entity';
import { registeredUsers } from 'src/entities/user.entity';
import { LoanRepository } from 'src/repositories/loan.repository';
import { UserServiceV4 } from 'src/v4/user/user.service.v4';
import { LoanService } from '../loan/loan.service';
import { UserRepository } from 'src/repositories/user.repository';
import { kMonths, kRazorpayM2Auth } from 'src/constants/objects';
import {
  nFetchPayoutDetails,
  nPullFirebaseAPI,
  nRazorTransactions,
  nTallyFirebaseAPI,
  nTrackerApi,
} from 'src/constants/network';
import { APIService } from 'src/utils/api.service';
import { DisbursmentRepository } from 'src/repositories/disbursement.repository';
import { DateService } from 'src/utils/date.service';
import { SharedNotificationService } from 'src/shared/services/notification.service';
import { TypeService } from 'src/utils/type.service';
import { TransactionRepository } from 'src/repositories/transaction.repository';
import { EmiEntity } from 'src/entities/emi.entity';
import { CalculationSharedService } from 'src/shared/calculation.service';
import { UserLogTrackerRepository } from 'src/repositories/userLogTracker.repository';
import * as fs from 'fs';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { ActiveLoanAddressesEntity } from 'src/entities/activeLoanAddress.entity';
import { KYCEntity } from 'src/entities/kyc.entity';
import { CryptService } from 'src/utils/crypt.service';
import { EnvConfig } from 'src/configs/env.config';
import { BankingService } from '../banking/banking.service';
import { RedisService } from 'src/redis/redis.service';
import { NUMBERS } from 'src/constants/numbers';
import { ErrorContextService } from 'src/utils/error.context.service';
import { ReportService } from '../report/report.service';
import { loanTransaction } from 'src/entities/loan.entity';
import { LeadTrackingServices } from 'src/thirdParty/leadTracking/leadTracking.service';

@Injectable()
export class DisbursementService {
  constructor(
    private readonly loanRepo: LoanRepository,
    private readonly loanService: LoanService,
    private readonly userRepo: UserRepository,
    private readonly userService: UserServiceV4,
    private readonly api: APIService,
    private readonly disRepo: DisbursmentRepository,
    private readonly dateService: DateService,
    private readonly sharedNotification: SharedNotificationService,
    private readonly typeService: TypeService,
    private readonly transRepo: TransactionRepository,
    // Shared
    private readonly calculation: CalculationSharedService,
    private readonly userLogTrackerRepository: UserLogTrackerRepository,
    private readonly repoManager: RepositoryManager,
    private readonly cryptService: CryptService,
    private readonly banking: BankingService,
    private readonly redisService: RedisService,
    private readonly errorContextService: ErrorContextService,
    private readonly reportService: ReportService,
    private readonly leadTrackingServices: LeadTrackingServices,
  ) {}

  async markDisbursementAsComplete(reqData) {
    try {
      // Params validation
      const loanId = reqData.loanId;
      if (!loanId) return kParamMissing('loanId');

      // Purpose -> Analysis
      this.banking.syncTransactionsSummary({ loanId }).catch((err) => {});

      //Add  Dibursement Data in Tally Report
      this.addTallyData(loanId).catch((err) => {
        console.log('err', err);
      });

      const disbursementInclude: any = { model: disbursementEntity };
      disbursementInclude.attributes = [
        'updatedAt',
        'bank_name',
        'account_number',
        'utr',
        'source',
        'requestdata',
        'amount',
        'status',
      ];
      const userInclude: any = { model: registeredUsers };
      userInclude.attributes = [
        'appsFlyerId',
        'id',
        'recentDeviceId',
        'typeOfDevice',
        'phone',
        'hashPhone',
        'appType',
        'trackerId',
        'email',
        'fullName',
      ];
      const include = [disbursementInclude, userInclude];
      const attributes = [
        'userId',
        'loanStatus',
        'loanAmount',
        'appType',
        'insuranceOptValue',
        'loan_disbursement_date',
      ];
      const options = { include, where: { id: loanId } };
      const loanData = await this.loanRepo.getRowWhereData(attributes, options);
      if (!loanData) return k422ErrorMessage(kNoDataFound);
      if (loanData == k500Error) return kInternalError;

      // Update LoanStatus in User Entity (disbursed = Ontime)
      if (loanData.userId) {
        const updatedData = { loanStatus: 1 };
        const ontimeOptions = { where: { id: loanData.userId } };
        await this.userRepo.updateRowWhereData(updatedData, ontimeOptions);
      }

      const userData = loanData?.registeredUsers;
      if (!userData) return k422ErrorMessage('No user data found');
      await this.userService.routeDetails({ id: userData.id });
      // Insurance proposal (One time)
      if (loanData?.insuranceOptValue)
        this.loanService.insuranceProposal({ loanId }).catch((_) => {});

      let payoutAccount = '';
      const bankDetails = fs.readFileSync('bankDetails.json', 'utf8');
      const kTallyPayoutBanks = bankDetails ? JSON.parse(bankDetails) : {};

      let disbursedData: any = {};
      if (loanData.disbursementData?.length > 0) {
        disbursedData = loanData.disbursementData[0];
        const rawResponse = disbursedData.response;
        // Cashfree
        if (disbursedData.source == kCashfree) {
          if (rawResponse) {
            const response = JSON.parse(rawResponse);
            if (response.paymentInstrumentId) {
              payoutAccount =
                kTallyPayoutBanks[response.paymentInstrumentId] ?? '-';
            }
          }
        }
        // Razorpay M2
        else if (disbursedData.source == 'RAZORPAY_M2') {
          payoutAccount = kTallyPayoutBanks['RAZORPAY_M2_DEFAULT_ACC'];
        }
        /// ICICI
        else if (disbursedData?.source == KICICI) {
          payoutAccount = kTallyPayoutBanks['ICICI_DISBURSEMENT_ACCOUNT'];
        }
        // Razorpay M1
        else {
          payoutAccount = 'RBL BANK - 8149';
        }
      } else {
        // Default to Razorpay M1 if no disbursement data is found
        payoutAccount = 'RBL BANK - 8149';
      }
      // Check for account number in request data
      const gatewayAcc = disbursedData.requestdata?.account_number;
      if (gatewayAcc && kTallyPayoutBanks[gatewayAcc]) {
        payoutAccount = kTallyPayoutBanks[gatewayAcc];
      }

      const otherDetails = {
        loanAmount: loanData?.loanAmount,
        bankAccount:
          loanData?.disbursementData[0]?.bank_name +
          '-' +
          loanData?.disbursementData[0]?.account_number,
        utr: loanData?.disbursementData[0]?.utr,
        payoutAccount,
      };

      const passData = {
        userId: loanData.registeredUsers.id,
        stage: `Loan Disbursement`,
        loanId: reqData?.loanId,
        ip: '-',
        deviceId: '-',
        city: '-',
        ipLocation: '-',
        ipCountry: '-',
        otherDetails,
      };
      await this.userLogTrackerRepository.create(passData);

      // if (!gIsPROD) return k403Forbidden;

      if (loanData.loanStatus != 'Active' && loanData.loanStatus != 'Complete')
        return k422ErrorMessage('Loan is not active or completed');

      const disbursementDate = loanData.disbursementData[0].updatedAt;
      if (!disbursementDate)
        return k422ErrorMessage('disbursementDate is not found');

      // send review link msg
      // code comment due to link length issue
      // await this.sharedNotification.sendReviewMessage({
      //   phoneNumber: userData?.phone,
      //   loanId,
      //   appType: loanData?.appType,
      //   typeOfDevice: userData?.typeOfDevice,
      // });
      await this.calculation.calculateCLTV({ loanIds: [loanId] });

      // store user data for check nbfs status
      const phone = this.cryptService.decryptPhone(
        loanData?.registeredUsers?.phone,
      );
      if (loanData?.appType != 0) {
        const url = `${EnvConfig.url.lspBaseLink}/v4/user/checkUserNBFCStatus`;
        await this.api.post(url, { phone, nbfcType: +EnvConfig.nbfcType });
      }

      // traccker api hit for marketing
      if (gIsPROD && loanData?.registeredUsers?.trackerId) {
        const body = {
          customer_uid: loanData?.userId ?? loanId,
          customer_email: loanData?.registeredUsers?.email,
          customer_name: loanData?.registeredUsers?.fullName,
          customer_phone: phone,
          app_token:
            loanData.registeredUsers.appType == '0'
              ? EnvConfig.trackerDetails.lspTokenKey
              : EnvConfig.trackerDetails.cflTokenKey,
          tr_id: `${loanData?.registeredUsers?.trackerId}`,
          event_id:
            loanData.registeredUsers.appType == '0'
              ? EnvConfig.trackerDetails.lspEventId
              : EnvConfig.trackerDetails.cflEventId,
        };
        const headers = {
          'x-api-key':
            loanData.registeredUsers.appType == '0'
              ? EnvConfig.trackerDetails.lspHeaderKey
              : EnvConfig.trackerDetails.cflHeaderKey,
        };
        const trackerData = await this.api.post(nTrackerApi, body, headers);
        console.log('TRACKER_API_RESPONSE======>', trackerData);
      }

      // add address in active loan address
      const kycInclude = {
        model: KYCEntity,
        attributes: [
          'aadhaarAddress',
          'aadhaarLatLong',
          [
            Sequelize.fn(
              kPGPDecrypt,
              Sequelize.cast(Sequelize.col('panCardNumber'), 'bytea'),
              kCryptography,
            ),
            'panCardNumber',
          ],
        ],
      };
      const option = {
        where: { id: userData.id },
        include: kycInclude,
      };

      const data = await this.userRepo.getRowWhereData(['id', 'phone'], option);
      if (!data || data == k500Error) throw new Error();
      if (data?.kycData?.aadhaarAddress) {
        const address = JSON.parse(data.kycData.aadhaarAddress);
        const trimmedAddress = Object.values(address)
          .join(' ')
          .split(/[\s,-]+/g);
        const aadhaarAddress = trimmedAddress.join(' ').trim();
        let latLngObject = JSON.parse(data.kycData.aadhaarLatLong);
        let aadhaarLatLongPoint = null;
        if (latLngObject) {
          const lat = latLngObject['lat'];
          const lng = latLngObject['lng'];
          aadhaarLatLongPoint = `${lat},${lng}`;
        }

        try {
          const createdData = {
            loanId,
            userId: userData.id,
            aadhaarAddress,
            aadhaarLatLong: aadhaarLatLongPoint,
            isActive: true,
          };
          await this.repoManager.createRowData(
            ActiveLoanAddressesEntity,
            createdData,
          );
        } catch (error) {}
      }

      //#region Logic for Lead Tracking - Update disbursement data to lead table
      const disbData =
        loanData?.disbursementData?.find(
          (item) => item.status === 'processed',
        ) || null;

      if (data?.phone && loanData?.appType === 1) {
        const phone = this.cryptService.decryptPhone(data.phone);
        const leadTrackingPayload = {
          phone,
          appType: loanData?.appType,
          userId: userData.id,
          disbAmount: disbData?.amount ? disbData.amount / 100 : null,
        };
        this.leadTrackingServices
          .handleDisbursedUser(leadTrackingPayload)
          .catch((_) => {});
      }
      //#endregion
      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async reLogMissedEvents() {
    try {
      const userInclude: any = { model: registeredUsers };
      userInclude.where = { appsFlyerId: { [Op.ne]: null } };
      const include = [userInclude];
      const attributes = ['id'];
      const options = {
        include,
        where: { loanStatus: { [Op.or]: ['Active', 'Complete'] } },
        limit: 1,
      };

      const loanList = await this.loanRepo.getTableWhereData(
        attributes,
        options,
      );
      if (loanList == k500Error) return kInternalError;

      for (let index = 0; index < loanList.length; index++) {
        try {
          const loanId = loanList[index].id;
          await this.markDisbursementAsComplete({ loanId });
        } catch (error) {}
      }

      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async disbursementSettlement(body) {
    // Query preparation
    const startDate = body?.startDate ?? new Date();
    const endDate = body?.endDate ?? new Date();
    const range = await this.dateService.unixDateRange(startDate, endDate);
    const account_number = process.env.RAZORPAY_M2_ACCOUNT_NUMBER;
    if (!account_number) return kParamMissing('account_number');

    //For razorpay M2
    const auth = kRazorpayM2Auth;
    const razorpayTransactions = await this.getRazorpayTransactions(
      account_number,
      range,
      auth,
    );

    let loanData = {};
    for (let i = 0; i < razorpayTransactions.length; i++) {
      try {
        const ele = razorpayTransactions[i];
        const transactionId = ele.id;

        // Fetch transaction details
        const transUrl = nRazorTransactions + '/' + transactionId;
        const transDetails = await this.api.get(transUrl, null, null, {
          auth,
        });
        if (transDetails === k500Error) continue;

        // Fetch payout details
        const body = {
          payout_id: transDetails?.source?.id,
          source: 'RAZORPAY_M2',
        };
        let payoutDetails = await this.api.requestPost(
          nFetchPayoutDetails.replace('//thirdParty', '/thirdParty'),
          body,
        );
        if (payoutDetails === k500Error) continue;
        if (payoutDetails.valid == false) continue;
        payoutDetails = payoutDetails.data;

        const textToRemove = 'Loan Disbursement of ';
        const startIndex =
          payoutDetails.narration.indexOf(textToRemove) + textToRemove.length;
        const loanId = payoutDetails.narration.slice(startIndex);
        if (!loanId) continue;
        const transactionDetails = {
          created_at: ele.created_at,
          paymentDetails: payoutDetails,
        };
        if (loanData[loanId]) loanData[loanId].push(transactionDetails);
        else loanData[loanId] = [transactionDetails];
      } catch (error) {}
    }
    const filteredLoanData = Object.keys(loanData).reduce((result, loanId) => {
      const filteredDetails = loanData[loanId].filter(
        (item) => item.paymentDetails.status === 'processed',
      );
      if (filteredDetails.length > 0) {
        filteredDetails.sort((a, b) => {
          return +new Date(b.created_at) - +new Date(a.created_at);
        });

        const latestData = filteredDetails[0];
        result[loanId] = latestData;
      }
      return result;
    }, {});

    for (const loan in filteredLoanData) {
      try {
        const data = filteredLoanData[loan];
        const settlementData = JSON.stringify(data?.paymentDetails);

        const updatedData = {
          settlementDate: new Date(data?.created_at * 1000),
          settlementData,
        };
        await this.disRepo.updateRowWhereData(updatedData, {
          where: { loanId: loan },
        });
      } catch (error) {}
    }

    return {};
  }

  private async getRazorpayTransactions(
    account_number,
    range,
    auth,
    skip = 0,
    totalTransactions = [],
  ) {
    const params = {
      account_number,
      from: range.minRange,
      to: range.maxRange,
      count: 100,
      skip,
    };
    const razorpay2: any = await this.api.get(
      nRazorTransactions,
      params,
      null,
      {
        auth,
      },
    );
    if (razorpay2 === k500Error) return kInternalError;
    if (razorpay2.count === 0) return totalTransactions;
    totalTransactions.push(...(razorpay2.items ?? []));
    if (razorpay2.count != 100) return totalTransactions;
    return await this.getRazorpayTransactions(
      account_number,
      range,
      auth,
      skip + 100,
      totalTransactions,
    );
  }

  //#region Cron for fiscal year summary disbursement card
  async getFiscalSummaryDisbursementCron() {
    const reqData: any = {};
    let response: any = {};
    const fiscalPeriod = 12;
    reqData.isRefresh = 'true';
    reqData.year = fiscalPeriod;
    response = await this.getFiscalSummaryDisbursement(reqData);
    response.lastUpdated = new Date().toJSON();
    await this.setFiscalSummaryDisbursementFor12Years(response);
    console.log(
      `Summary disbursement data for fiscal period ${fiscalPeriod} cached successfully.`,
    );
    return { lastUpdated: response.lastUpdated };
  }
  //#endregion

  //#region Set Fiscal Disbursement Summary of 12 years
  private async setFiscalSummaryDisbursementFor12Years(response) {
    const key = `FISCAL_SUMMARY_DISBURSEMENT_PERIOD_12`;
    await this.redisService.set(
      key,
      JSON.stringify(response),
      NUMBERS.THREE_DAYS_IN_SECONDS,
    );
  }
  //#endregion

  async getFiscalSummaryDisbursement(reqData) {
    try {
      let year = reqData?.year;
      if (!year) return kParamMissing('year');

      const isRefresh = reqData?.isRefresh === 'true' ? true : false;

      // Find data from Redis
      if (!isRefresh) {
        let response = await this.redisService.get(
          'FISCAL_SUMMARY_DISBURSEMENT_PERIOD_12',
        );
        if (response) response = await JSON.parse(response);
        if (response?.data?.length) {
          response.data = response.data.slice(0, year);
          response.lastUpdated = response.lastUpdated;
          return response;
        }
      }

      // Get data from database
      year = 12;
      const today = new Date();
      const years = new Date(
        today.getFullYear() - year,
        today.getMonth(),
        today.getDate(),
      );

      const startDate: any = this.typeService.getGlobalDate(years).toJSON();
      const endDate: any = this.typeService.getGlobalDate(today).toJSON();

      const loanData: any = await this.getLoanData(startDate, endDate);
      if (loanData == k500Error) return kInternalError;

      const transData = await this.getTransData(startDate, endDate);
      if (transData === k500Error) return kInternalError;

      const userData = await this.getUserData(startDate, endDate);
      if (userData === k500Error) return kInternalError;

      const defaulterData = await this.getDefaulterUserData(startDate, endDate);
      if (defaulterData === k500Error) return kInternalError;

      const loanExpectedData = await this.loanExpectedData(startDate, endDate);
      if (loanExpectedData === k500Error) return kInternalError;

      const dataCollection = {
        loanData,
        transData,
        defaulterData,
        userData,
        loanExpectedData,
      };

      const response: any = {};
      response.data = await this.prepareFiscalData(dataCollection, year);
      response.lastUpdated = new Date().toJSON();

      if (!isRefresh) {
        // set data to Redis, if not found in Redis
        await this.setFiscalSummaryDisbursementFor12Years(response);
        if (response?.data?.length) {
          response.data = response.data.slice(0, reqData?.year);
          response.lastUpdated = response.lastUpdated;
          return response;
        }
      }

      return response;
    } catch (error) {
      console.error(error);
      return kInternalError;
    }
  }

  private async getLoanData(startDate, endDate) {
    try {
      const options: any = {
        where: {
          loan_disbursement: '1',
          loan_disbursement_date: {
            [Op.gte]: startDate,
            [Op.lte]: endDate,
          },
          loanStatus: { [Op.or]: ['Active', 'Complete'] },
        },
      };
      const disDate = 'loan_disbursement_date';
      const group = [
        this.typeService.manageDateAttr('month', '', disDate, false),
        this.typeService.manageDateAttr('year', '', disDate, false),
        'loanTransaction.id',
      ];
      const attributes = [
        this.typeService.manageDateAttr('month', '', disDate),
        this.typeService.manageDateAttr('year', '', disDate),
        [
          Sequelize.fn('COUNT', Sequelize.col('loanTransaction.id')),
          'totalcount',
        ],
        [
          Sequelize.fn(
            'SUM',
            Sequelize.cast(
              Sequelize.col('netApprovedAmount'),
              'double precision',
            ),
          ),
          'netTotal',
        ],
      ];

      options.group = group;
      return await this.loanRepo.getTableWhereData(attributes, options);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async getTransData(startDate, endDate) {
    try {
      const transOptions: any = {
        where: {
          status: kCompleted,
          type: { [Op.ne]: kRefund },
          completionDate: {
            [Op.gte]: startDate,
            [Op.lte]: endDate,
          },
        },
      };
      const completionDate = 'completionDate';
      const transGroup = [
        this.typeService.manageDateAttr('month', '', completionDate, false),
        this.typeService.manageDateAttr('year', '', completionDate, false),
      ];
      const transAttributes: any = [
        this.typeService.manageDateAttr('month', '', completionDate),
        this.typeService.manageDateAttr('year', '', completionDate),
        [
          Sequelize.fn(
            'SUM',
            Sequelize.cast(
              Sequelize.col('principalAmount'),
              'double precision',
            ),
          ),
          'totalPrincAmount',
        ],
        [
          Sequelize.fn(
            'SUM',
            Sequelize.cast(Sequelize.col('interestAmount'), 'double precision'),
          ),
          'totalIntAmount',
        ],
        [
          Sequelize.fn(
            'SUM',
            Sequelize.cast(Sequelize.col('penaltyAmount'), 'double precision'),
          ),
          'totalPenaltyAmount',
        ],
        [
          Sequelize.fn(
            'SUM',
            Sequelize.cast(Sequelize.col('legalCharge'), 'double precision'),
          ),
          'totalLegalCharge',
        ],
        [
          Sequelize.fn(
            'SUM',
            Sequelize.cast(Sequelize.col('bounceCharge'), 'double precision'),
          ),
          'totalBounceCharge',
        ],
        [
          Sequelize.fn(
            'SUM',
            Sequelize.cast(Sequelize.col('penalCharge'), 'double precision'),
          ),
          'totalPenalCharge',
        ],
        [
          Sequelize.fn(
            'SUM',
            Sequelize.cast(
              Sequelize.col('regInterestAmount'),
              'double precision',
            ),
          ),
          'totalDefInterest',
        ],
        [
          Sequelize.fn(
            'SUM',
            Sequelize.cast(
              Sequelize.col('forClosureAmount'),
              'double precision',
            ),
          ),
          'totalForClousreCharge',
        ],
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'totalcount'],
      ];
      transOptions.group = transGroup;
      return await this.transRepo.getTableWhereData(
        transAttributes,
        transOptions,
      );
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async getUserData(startDate, endDate) {
    try {
      const range = this.typeService.getUTCDateRange(startDate, endDate);

      const userOptions: any = {
        where: {
          createdAt: {
            [Op.gte]: range.fromDate,
            [Op.lte]: range.endDate,
          },
        },
      };
      const userGroup = [
        this.typeService.manageDateAttr(
          'month',
          '"registeredUsers".',
          '',
          false,
        ),
        this.typeService.manageDateAttr(
          'year',
          '"registeredUsers".',
          '',
          false,
        ),
      ];
      const userAttributes: any = [
        this.typeService.manageDateAttr('month', '"registeredUsers".'),
        this.typeService.manageDateAttr('year', '"registeredUsers".'),
        [
          Sequelize.fn('COUNT', Sequelize.col('registeredUsers.id')),
          'userCount',
        ],
      ];
      userOptions.group = userGroup;
      return await this.userRepo.getTableWhereData(userAttributes, userOptions);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async getDefaulterUserData(startDate, endDate) {
    try {
      const disDate = 'loan_disbursement_date';
      const loanOptions: any = {
        where: {
          loan_disbursement_date: {
            [Op.gte]: startDate,
            [Op.lte]: endDate,
          },
          loanStatus: 'Active',
        },
        include: [
          {
            model: EmiEntity,
            attributes: [],
            required: true,
            where: {
              payment_due_status: '1',
              payment_status: '0',
            },
          },
        ],
      };
      const loanGroup = [
        this.typeService.manageDateAttr('month', '', disDate, false),
        this.typeService.manageDateAttr('year', '', disDate, false),
        'loanTransaction.id',
      ];
      const loanAttributes = [
        this.typeService.manageDateAttr('month', '', disDate),
        this.typeService.manageDateAttr('year', '', disDate),
        [
          Sequelize.fn(
            'COUNT',
            Sequelize.literal('DISTINCT "loanTransaction"."userId"'),
          ),
          'totalDefaultCount',
        ],
        [
          Sequelize.fn(
            'SUM',
            Sequelize.cast(
              Sequelize.col('emiData.principalCovered'),
              'double precision',
            ),
          ),
          'totalExpectedPrincAmount', // Alias for SUM operation
        ],
        [
          Sequelize.fn(
            'SUM',
            Sequelize.cast(
              Sequelize.col('emiData.paid_principal'),
              'double precision',
            ),
          ),
          'totalPaidPrincAmount', // Alias for SUM operation
        ],
      ];
      loanOptions.group = loanGroup;
      return await this.loanRepo.getTableWhereData(loanAttributes, loanOptions);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async loanExpectedData(startDate, endDate) {
    try {
      const options: any = {
        where: {
          loan_disbursement: '1',
          loan_disbursement_date: {
            [Op.gte]: startDate,
            [Op.lte]: endDate,
          },
        },
        include: [
          {
            model: EmiEntity,
            as: 'emiData',
            attributes: [],
          },
        ],
      };
      const disDate = 'loan_disbursement_date';
      const group = [
        this.typeService.manageDateAttr('month', '', disDate, false),
        this.typeService.manageDateAttr('year', '', disDate, false),
      ];
      const attributes = [
        this.typeService.manageDateAttr('month', '', disDate),
        this.typeService.manageDateAttr('year', '', disDate),
        [
          Sequelize.fn('SUM', Sequelize.col('emiData.interestCalculate')),
          'totalInterestAmount',
        ],
      ];
      options.group = group;
      options.raw = true;
      return await this.loanRepo.getTableWhereData(attributes, options);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async prepareFiscalData(data, years) {
    try {
      const { loanData, transData, defaulterData, userData, loanExpectedData } =
        data;
      const startMonth = 4;
      const endMonth = 3;
      const financialYearData = [];

      const currentMonth = new Date().getMonth() + 1;
      const currentYear = new Date().getFullYear();
      const startYear =
        currentMonth > endMonth ? currentYear - years + 1 : currentYear - years;

      for (let year = startYear; year <= new Date().getFullYear(); year++) {
        const financialYear = await this.calculateFinancialYear(
          year,
          currentMonth,
          startMonth,
          currentYear,
        );
        if (
          loanData.some((item) => item.loan_disbursement_dateyear === year) &&
          transData.some((item) => item.completionDateyear === year) &&
          userData.some((item) => item.year === year) &&
          financialYear != null
        ) {
          const [
            netPrincipalAmount,
            disbursedLoan,
            netInterestAmount,
            netRepaidPrincAmount,
            netRepaidIntAmount,
            netRepaidPenaltyAmount,
            netUserCount,
            netDefaultData,
            netLegalCharge,
            netBounceCharge,
            netPenalCharge,
            netDeffInterest,
            netForClousreCharge,
          ] = await Promise.all([
            this.filterAndSum(
              loanData,
              'loan_disbursement_date',
              'netTotal',
              year,
              startMonth,
              endMonth,
            ),
            this.filterAndSum(
              loanData,
              'loan_disbursement_date',
              'totalcount',
              year,
              startMonth,
              endMonth,
            ),
            this.filterAndSum(
              loanExpectedData,
              'loan_disbursement_date',
              'totalInterestAmount',
              year,
              startMonth,
              endMonth,
            ),
            this.filterAndSum(
              transData,
              'completionDate',
              'totalPrincAmount',
              year,
              startMonth,
              endMonth,
            ),
            this.filterAndSum(
              transData,
              'completionDate',
              'totalIntAmount',
              year,
              startMonth,
              endMonth,
            ),
            this.filterAndSum(
              transData,
              'completionDate',
              'totalPenaltyAmount',
              year,
              startMonth,
              endMonth,
            ),
            this.filterAndSum(
              userData,
              '',
              'userCount',
              year,
              startMonth,
              endMonth,
            ),
            this.filterAndSum(
              defaulterData,
              'loan_disbursement_date',
              'totalDefaultCount',
              year,
              startMonth,
              endMonth,
            ),
            this.filterAndSum(
              transData,
              'completionDate',
              'totalLegalCharge',
              year,
              startMonth,
              endMonth,
            ),
            this.filterAndSum(
              transData,
              'completionDate',
              'totalBounceCharge',
              year,
              startMonth,
              endMonth,
            ),
            this.filterAndSum(
              transData,
              'completionDate',
              'totalPenalCharge',
              year,
              startMonth,
              endMonth,
            ),
            this.filterAndSum(
              transData,
              'completionDate',
              'totalDefInterest',
              year,
              startMonth,
              endMonth,
            ),
            this.filterAndSum(
              transData,
              'completionDate',
              'totalForClousreCharge',
              year,
              startMonth,
              endMonth,
            ),
          ]);

          const totalPenaltyAmount =
            netRepaidPenaltyAmount +
            netBounceCharge +
            netPenalCharge +
            netLegalCharge +
            netDeffInterest +
            netForClousreCharge;

          const monthData = await this.getMonthlyData(
            year,
            startMonth,
            endMonth,
            loanData,
            transData,
            defaulterData,
          );
          const defaulter: any =
            Number(((netDefaultData / disbursedLoan) * 100).toFixed(2)) || 0;

          financialYearData.push({
            fiscalYear: `FY (${financialYear})`,
            registration: `${this.typeService.amountNumberWithCommas(
              netUserCount,
            )}`,
            loanDisbursed: `${this.typeService.amountNumberWithCommas(
              disbursedLoan,
            )}`,
            principalAmount: `${kRuppe}${this.typeService.amountNumberWithCommas(
              Math.floor(netPrincipalAmount),
            )}`,
            interestAmount: `${kRuppe}${this.typeService.amountNumberWithCommas(
              Math.floor(netInterestAmount),
            )}`,
            receivedPrincipal: `${kRuppe}${this.typeService.amountNumberWithCommas(
              Math.floor(netRepaidPrincAmount),
            )}`,
            recievedInterest: `${kRuppe}${this.typeService.amountNumberWithCommas(
              Math.floor(netRepaidIntAmount),
            )}`,
            recievedPenalty: `${kRuppe}${this.typeService.amountNumberWithCommas(
              Math.floor(totalPenaltyAmount),
            )}`,
            defaulterUserPercent: `${defaulter}%`,
            monthData,
          });
        }
      }

      financialYearData.sort((a, b) =>
        b.fiscalYear.localeCompare(a.fiscalYear),
      );

      return financialYearData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }

  private async calculateFinancialYear(year, month, startMonth, currentYear) {
    return year === currentYear && month < startMonth
      ? null
      : `${year}-${year + 1}`;
  }

  private async filterAndSum(
    data,
    dateField,
    sumField,
    year,
    startMonth,
    endMonth,
    specificMonth = null,
  ) {
    const fromDate = this.typeService.getGlobalDate(
      new Date(year, startMonth - 1),
    );
    const toDate = this.typeService.getGlobalDate(
      new Date(year + 1, endMonth - 1),
    );

    const filteredData = data.filter((item) => {
      const itemDate = this.typeService.getGlobalDate(
        new Date(item[dateField + 'year'], item[dateField + 'month'] - 1),
      );
      return itemDate >= fromDate && itemDate <= toDate;
    });

    if (specificMonth !== null) {
      const specificMonthFromDate = this.typeService.getGlobalDate(
        new Date(year, specificMonth - 1),
      );
      const specificMonthToDate = this.typeService.getGlobalDate(
        new Date(year, specificMonth),
      );

      const monthFilteredData = data.filter((item) => {
        const itemDate = this.typeService.getGlobalDate(
          new Date(item[dateField + 'year'], item[dateField + 'month'] - 1),
        );
        return (
          itemDate >= specificMonthFromDate && itemDate < specificMonthToDate
        );
      });

      const monthSum = monthFilteredData.reduce(
        (total, item) => total + (+item[sumField] || 0),
        0,
      );
      return monthSum;
    }
    const sum = filteredData.reduce(
      (total, item) => total + (+item[sumField] || 0),
      0,
    );
    return sum;
  }

  // Reusable function to process monthly data for disbursement, defaults, and repayments
  async getMonthlyData(
    year,
    startMonth,
    endMonth,
    loanData,
    transData,
    defaulterData,
  ) {
    const disbursement = [];
    const Default = [];
    const repayment = [];

    let previousDisbursementAmount = 0;
    let previousTotalRepayment = 0;
    let previousDisbursedCount = 0;
    let previousRepaidCount = 0;
    let previousDefaultPrincipalAmt = 0;
    let previousDefaultCount = 0;
    let isFirstMonth = true;

    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1;

    // Helper function to loop through months (April-December & January-March)
    const processMonth = async (month, yearOffset = 0) => {
      const currentYearToUse = year + yearOffset;
      if (currentYearToUse === currentYear && month > currentMonth) {
        return; // Skip future months
      }

      if (currentYearToUse > currentYear) {
        return; // Skip future months of the next fiscal year
      }
      const [
        disbursementAmount,
        disbursementCount,
        defaulteCount,
        principalAmount,
        netRepaidIntAmount,
        netRepaidPenaltyAmount,
        netLegalCharge,
        netBounceCharge,
        netPenalCharge,
        netDeffInterest,
        netForClousreCharge,
        repaidLoan,
        defaultExpectedPrincipal,
        defaultPaidPrincipal,
      ] = await Promise.all([
        this.filterAndSum(
          loanData,
          'loan_disbursement_date',
          'netTotal',
          currentYearToUse,
          startMonth,
          endMonth,
          month,
        ),
        this.filterAndSum(
          loanData,
          'loan_disbursement_date',
          'totalcount',
          currentYearToUse,
          startMonth,
          endMonth,
          month,
        ),
        this.filterAndSum(
          defaulterData,
          'loan_disbursement_date',
          'totalDefaultCount',
          currentYearToUse,
          startMonth,
          endMonth,
          month,
        ),
        this.filterAndSum(
          transData,
          'completionDate',
          'totalPrincAmount',
          currentYearToUse,
          startMonth,
          endMonth,
          month,
        ),
        this.filterAndSum(
          transData,
          'completionDate',
          'totalIntAmount',
          currentYearToUse,
          startMonth,
          endMonth,
          month,
        ),
        this.filterAndSum(
          transData,
          'completionDate',
          'totalPenaltyAmount',
          currentYearToUse,
          startMonth,
          endMonth,
          month,
        ),
        this.filterAndSum(
          transData,
          'completionDate',
          'totalLegalCharge',
          currentYearToUse,
          startMonth,
          endMonth,
          month,
        ),
        this.filterAndSum(
          transData,
          'completionDate',
          'totalBounceCharge',
          currentYearToUse,
          startMonth,
          endMonth,
          month,
        ),
        this.filterAndSum(
          transData,
          'completionDate',
          'totalPenalCharge',
          currentYearToUse,
          startMonth,
          endMonth,
          month,
        ),
        this.filterAndSum(
          transData,
          'completionDate',
          'totalDefInterest',
          currentYearToUse,
          startMonth,
          endMonth,
          month,
        ),
        this.filterAndSum(
          transData,
          'completionDate',
          'totalForClousreCharge',
          currentYearToUse,
          startMonth,
          endMonth,
          month,
        ),
        this.filterAndSum(
          transData,
          'completionDate',
          'totalcount',
          currentYearToUse,
          startMonth,
          endMonth,
          month,
        ),
        this.filterAndSum(
          defaulterData,
          'loan_disbursement_date',
          'totalExpectedPrincAmount',
          currentYearToUse,
          startMonth,
          endMonth,
          month,
        ),
        this.filterAndSum(
          defaulterData,
          'loan_disbursement_date',
          'totalPaidPrincAmount',
          currentYearToUse,
          startMonth,
          endMonth,
          month,
        ),
      ]);

      const totalRepayment =
        netRepaidPenaltyAmount +
        netBounceCharge +
        netPenalCharge +
        netLegalCharge +
        principalAmount +
        netRepaidIntAmount +
        netDeffInterest +
        netForClousreCharge;

      const totalDefaultAmount = Math.abs(
        defaultExpectedPrincipal - defaultPaidPrincipal,
      );

      const repaymentData = Math.floor(totalRepayment);
      const months = kMonths.map((month) => month.trim());

      // Creating objects for monthly data
      disbursement.push({
        month: months[month - 1],
        amount: disbursementAmount,
        count: disbursementCount,
        amountDiff: isFirstMonth
          ? 0
          : Math.floor(disbursementAmount - previousDisbursementAmount) ?? 0,
        countDiff: isFirstMonth
          ? 0
          : Math.floor(disbursementCount - previousDisbursedCount) ?? 0,
      });
      Default.push({
        month: months[month - 1],
        count: defaulteCount,
        amount: totalDefaultAmount,
        amountDiff: isFirstMonth
          ? 0
          : Math.floor(totalDefaultAmount - previousDefaultPrincipalAmt) ?? 0,
        countDiff: isFirstMonth
          ? 0
          : Math.floor(defaulteCount - previousDefaultCount) ?? 0,
      });
      repayment.push({
        month: months[month - 1],
        amount: repaymentData,
        count: repaidLoan,
        amountDiff: isFirstMonth
          ? 0
          : Math.floor(totalRepayment - previousTotalRepayment) ?? 0,
        countDiff: isFirstMonth
          ? 0
          : Math.floor(repaidLoan - previousRepaidCount) ?? 0,
      });

      previousDisbursementAmount = disbursementAmount;
      previousTotalRepayment = totalRepayment;
      previousDisbursedCount = disbursementCount;
      previousRepaidCount = repaidLoan;
      previousDefaultPrincipalAmt = totalDefaultAmount;
      previousDefaultCount = defaulteCount;
      isFirstMonth = false;
    };

    // Process months from April to December (current year) and January to March (next year)
    for (let month = startMonth; month <= 12; month++) {
      await processMonth(month);
    }
    for (let month = 1; month <= endMonth; month++) {
      await processMonth(month, 1); // Offset year for January to March
    }

    return { disbursement, Default, repayment };
  }

  async manualMarkAsDisbursement(reqData: any) {
    try {
      const loanIdArr = reqData.loanIdArr ?? [];
      if (!loanIdArr || !Array.isArray(loanIdArr))
        return kInvalidParamValue('loanIdArr');

      console.log('loanIdArr.length', loanIdArr.length);

      for (let i = 0; i < loanIdArr.length; i++) {
        try {
          console.log(i);

          const loanId = loanIdArr[i];
          if (isNaN(loanId)) continue;
          const loanAttr = ['id', 'loanStatus'];
          const loanOptions = {
            where: { id: loanId, loanStatus: 'Accepted' },
          };
          const loanData = await this.loanRepo.getRowWhereData(
            loanAttr,
            loanOptions,
          );
          console.log('loanData', loanData);

          if (!loanData || loanData == k500Error) continue;

          const dispAttr = ['id', 'status'];
          const dispOptions = {
            where: { loanId, status: 'processed' },
            order: [['id', 'DESC']],
          };
          const disbData = await this.disRepo.getRowWhereData(
            dispAttr,
            dispOptions,
          );
          console.log('disbData', disbData);

          if (!disbData || disbData == k500Error) continue;

          // Update loan data
          const updatedData = {
            loan_disbursement: '1',
            loan_disbursement_date: this.typeService
              .getGlobalDate(new Date())
              .toJSON(),
            loan_disbursement_id: disbData.id,
            loanStatus: 'Active',
          };
          const updatedResult = await this.loanRepo.updateRowData(
            updatedData,
            loanId,
          );
          if (updatedResult == k500Error) continue;
          console.log('LOAN UPDATE DONE');

          const markDisbPayload = {
            loanId,
          };
          await this.markDisbursementAsComplete(markDisbPayload);
          console.log('MARK DISB DOINE!!!');
        } catch (error) {
          console.log(error);
        }
      }
    } catch (error) {
      return kInternalError;
    }
  }

  async addTallyData(loanId) {
    if (!gIsPROD) return {};

    const prefix = EnvConfig.nbfc.nbfcShortName + '_';

    const today = new Date();
    const sevenDaysBefore = new Date();
    const sevenDaysAfter = new Date();

    // Adjust the dates
    sevenDaysBefore.setDate(today.getDate() - 7);
    sevenDaysAfter.setDate(today.getDate() + 7);

    // Format the dates (using your typeService or a custom function)
    const beforeDate = this.typeService.getGlobalDate(sevenDaysBefore).toJSON();
    const afterDate = this.typeService.getGlobalDate(sevenDaysAfter).toJSON();

    const disbDat = await this.reportService.tallyDisbursement({
      startDate: beforeDate,
      endDate: afterDate,
      searchText: `l-${loanId}`,
    });
    if (disbDat == k500Error) throw new Error('Error in Disbursement Report');

    const disbDate = disbDat?.rows[0]?.disbursement_date
      .split('-')
      .reverse()
      .join('-');

    const preparedData = {
      check_for_uniqueness: true,
      operation_mode: 'TALLY_DISBURSEMENT',
      collection: prefix + 'Tally_Disbursement',
      document: disbDate.slice(0, 7),
      subCollection: disbDate,
      subDocument: `L-${loanId}`,
      target_data: {
        request_payload: disbDat?.rows[0],
        status: kInitiated,
        subStatus: '',
        initiated_at: this.dateService.getGlobalDate(new Date()).toJSON(),
        completed_at: '',
      },
      recent_collection: prefix + 'Recent_Tally_Disbursement',
      recent_doc: `L-${loanId}`,
      recent_target_data: {
        loanId: loanId,
        status: kInitiated,
        disbursement_date: disbDate,
        source: prefix + 'Tally_Disbursement',
      },
    };
    await this.api.post(nTallyFirebaseAPI, preparedData);

    return true;
  }

  async reSyncDisbursementInTally(reqData) {
    const collection =
      EnvConfig.nbfc.nbfcShortName + '_Recent_Tally_Disbursement';
    const target_date =
      reqData.target_date ??
      this.typeService.getGlobalDate(new Date()).toJSON();

    const attributes = ['id'];
    const options = { where: { loan_disbursement_date: target_date } };
    const loanList = await this.repoManager.getTableWhereData(
      loanTransaction,
      attributes,
      options,
    );
    if (loanList == k500Error) throw new Error();

    let totalMissed = 0;
    for (let index = 0; index < loanList.length; index++) {
      const loanId = loanList[index].id;
      const body = { collection, document: 'L-' + loanId };
      const response = await this.api.post(nPullFirebaseAPI, body);
      if (!response?.snapshot) {
        totalMissed++;
        await this.addTallyData(loanId);
        console.log('Adding entry for - ', loanId);
      }
    }

    return { totalMissed };
  }
}
