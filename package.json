{"name": "backend", "version": "5.0.4", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "cross-env NODE_OPTIONS=--max_old_space_size=8192 && nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "cross-env NODE_OPTIONS=--max-old-space-size=8120 && nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node --max-old-space-size=8120 dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@aws-sdk/client-rekognition": "^3.485.0", "@clickhouse/client": "^1.0.1", "@google-cloud/storage": "^7.11.2", "@ilovepdf/ilovepdf-nodejs": "^0.2.6", "@nestjs/common": "^10.4.16", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.2.7", "@nestjs/jwt": "^10.2.0", "@nestjs/mongoose": "^10.0.6", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.2.7", "@supercharge/request-ip": "^1.2.0", "adm-zip": "^0.5.16", "aes-ecb": "^1.3.15", "axios": "^1.7.2", "base64topdf": "^1.1.8", "bcrypt": "^5.0.1", "bwip-js": "^3.3.0", "cassandra-driver": "^4.7.2", "cheerio": "^1.0.0-rc.12", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cluster": "^0.7.7", "compression": "^1.7.4", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "dotenv": "^16.4.5", "excel4node": "^1.8.2", "exceljs": "^4.4.0", "express-sse": "^0.5.3", "fcm-node": "^1.6.1", "firebase": ">=10.9.0", "firebase-admin": "^12.1.1", "form-data": "^4.0.0", "fs": "^0.0.1-security", "get-orientation": "^1.1.2", "googleapis": "^105.0.0", "handlebars": "^4.7.8", "hbs": "^4.2.0", "html-pdf": "^3.0.1", "jimp": "^0.22.7", "jsbarcode": "^3.11.5", "jsdom": "^21.1.0", "kafkajs": "^2.2.4", "mixpanel": "^0.16.0", "mongoose": "^8.4.3", "morgan": "^1.10.0", "multer": "^2.0.0", "node-forge": "^1.3.1", "node-telegram-bot-api": "^0.64.0", "nodemailer": "^6.9.13", "oci-common": "^2.98.1", "oci-objectstorage": "^2.99.0", "passport-jwt": "^4.0.0", "pdf-image": "^2.0.0", "pdf-lib": "^1.17.1", "pdf-to-base64": "^1.0.3", "pdf2pic": "^2.1.4", "pg": "^8.12.0", "puppeteer": "^19.7.2", "read-excel-file": "^5.6.1", "redis": "^4.6.14", "reflect-metadata": "^0.1.13", "request-ip": "^3.3.0", "rimraf": "^3.0.2", "rxjs": "^7.8.1", "sequelize": "^6.37.3", "sequelize-cli": "^6.4.1", "sequelize-typescript": "^2.1.6", "sharp": "^0.32.6", "uuid": "^8.3.2", "xml-js": "^1.6.11", "xml2js": "^0.6.2"}, "devDependencies": {"@nestjs/cli": "^10.2.1", "@nestjs/schematics": "^10.1.0", "@nestjs/testing": "^10.2.7", "@types/estree": "^1.0.5", "@types/express": "^4.17.13", "@types/jest": "^29.5.1", "@types/node": "^20.12.7", "@types/node-telegram-bot-api": "^0.64.7", "@types/request-ip": "^0.0.37", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "cross-env": "^7.0.3", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "29.5.0", "prettier": "^2.3.2", "razorpay": "^2.9.4", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "^29.1.1", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.2.0", "typescript": "5.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}