import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { getClientIp } from '@supercharge/request-ip/dist';
import { EnvConfig } from 'src/configs/env.config';

export const IPConfig = createParamDecorator(
  (_: unknown, ctx: ExecutionContext) => {
    try {
      const request = ctx.switchToHttp().getRequest();
      let ip: any =
        request?.headers['x-forwarded-for'] ??
        request?.socket?.remoteAddress ??
        request?.headers?.lspip ??
        null;
      if (typeof ip == 'string') ip = ip.replace(/f/g, '').replace(/:/g, '');
      if (request?.headers?.lspip) {
        try {
          if (EnvConfig.whiteListedIPs.includes(ip)) {
            if (typeof request.headers.lspip == 'string') {
              ip = request.headers.lspip?.split(',')[0]?.trim();
            }
          }
        } catch (error) {}
      }
      if (ip == '::1') ip = '**************';
      if (ip == '1') return '***************';
      if (!ip) {
        ip = getClientIp(request);
        ip = ip.replace(/f/g, '').replace(/:/g, '');
      }
      return ip;
    } catch (error) {}
  },
);
