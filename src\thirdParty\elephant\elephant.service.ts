// Imports
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { k500Error } from 'src/constants/misc';
import { APIService } from 'src/utils/api.service';
import {
  nElephantCDBalance,
  nElephantCheckStatus,
  nElephantCOI,
  nElephantProposal,
  nElephantToken,
} from 'src/constants/network';
import {
  k422ErrorMessage,
  kInternalError,
  kParamMissing,
} from 'src/constants/responses';
import { TypeService } from 'src/utils/type.service';
import { EnvConfig } from 'src/configs/env.config';
import { gIsPROD, INSURANCE_SERVICES, NBFC_NAME } from 'src/constants/globals';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { InsuranceEntity } from 'src/entities/insurance.entity';
import {
  ElephantCDBalanceToken,
  kNbfcUrl,
  kNoDataFound,
} from 'src/constants/strings';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { col, fn, Op } from 'sequelize';
import { FileService } from 'src/utils/file.service';
import { KInsuranceFailedTemplate } from 'src/constants/directories';
import * as fs from 'fs';
import { SharedNotificationService } from 'src/shared/services/notification.service';
import { RedisService } from 'src/redis/redis.service';
import { NUMBERS } from 'src/constants/numbers';
@Injectable()
export class ElephantService {
  constructor(
    private readonly api: APIService,
    private readonly typeService: TypeService,

    @Inject(forwardRef(() => CommonSharedService))
    private readonly commonSharedService: CommonSharedService,
    private readonly repoManager: RepositoryManager,
    @Inject(forwardRef(() => FileService))
    private readonly fileService: FileService,
    private readonly sharedNotification: SharedNotificationService,
    private readonly redisService: RedisService,
  ) {}

  async generateAuthToken(type: 'token' | 'status') {
    try {
      // URL
      const url = nElephantToken;
      // Body preparation
      const body = {
        username: `${EnvConfig.nbfc.nbfcShortName.toLowerCase()}.${type}`,
        password: process.env.ELEPHANT_PASS,
        type: 1,
      };
      // API call
      const response = await this.api.post(url, body);
      if (response == k500Error) return kInternalError;
      if (!response.utoken) return k422ErrorMessage('field utoken is missing');

      return response;
    } catch (error) {
      return kInternalError;
    }
  }

  async initiateProposal(reqData) {
    try {
      // Params validation
      const name = reqData.name?.toLowerCase();
      if (!name) return kParamMissing('name');
      const phone = reqData.phone;
      if (!phone) return kParamMissing('phone');
      const email = reqData.email;
      if (!email) return kParamMissing('email');
      let dob = reqData.dob;
      if (!dob) return kParamMissing('dob');
      dob = this.typeService.dateToJsonStr(dob);
      let gender = reqData.gender;
      if (!gender) return kParamMissing('gender');
      const bankAccNumber = reqData.bankAccNumber;
      if (!bankAccNumber) return kParamMissing('bankAccNumber');
      const aadhaarAddress = reqData.aadhaarAddress;
      if (!aadhaarAddress) return kParamMissing('aadhaarAddress');
      const approvedAmount = reqData.approvedAmount?.toString();
      if (!approvedAmount) return kParamMissing('approvedAmount');
      const loanTenure = reqData.loanTenure?.toString();
      if (!loanTenure) return kParamMissing('loanTenure');
      const loanId = reqData.loanId;
      if (!loanId) return kParamMissing('loanId');
      const pincode = reqData.pincode;
      if (!pincode) return kParamMissing('pincode');
      let disbursementDate = reqData.disbursementDate ?? new Date().toJSON();
      if (!disbursementDate) return kParamMissing('disbursementDate');
      if (typeof disbursementDate != 'string')
        disbursementDate = disbursementDate.toJSON();
      if (disbursementDate.includes('T'))
        disbursementDate =
          this.typeService.jsonToReadableDate(disbursementDate);
      const nomineeDetails = {
        Nominee_First_Name: 'Legal',
        Nominee_Last_Name: 'Heir',
      };
      const insuranceDetails = reqData.insuranceDetails;
      if (!insuranceDetails) return kParamMissing('insuranceDetails');
      const totalPremium = insuranceDetails.totalPremium?.toString();
      if (!totalPremium) return kParamMissing('totalPremium');
      let SumInsuredData = [];
      if (
        INSURANCE_SERVICES.ACCEDENT_AND_EMIP == true &&
        INSURANCE_SERVICES.LOSS_OF_JOB == true
      )
        SumInsuredData = [
          {
            PlanCode: 4212,
            SumInsured: insuranceDetails?.planASumInsured?.toString(),
            Shortcode: 'GPA',
            Premium: insuranceDetails?.planAPremium?.toString(),
          },
          {
            PlanCode: 4217,
            SumInsured: insuranceDetails?.planBSumInsured?.toString(),
            Shortcode: 'EMIP',
            Premium: insuranceDetails?.planBPremium?.toString(),
          },
          {
            PlanCode: 4218,
            SumInsured: insuranceDetails?.planCSumInsured?.toString(),
            Shortcode: 'LOE',
            Premium: insuranceDetails?.planCPremium?.toString(),
          },
        ];
      else if (
        INSURANCE_SERVICES.ACCEDENT_AND_EMIP == true &&
        INSURANCE_SERVICES.LOSS_OF_JOB == false
      ) {
        SumInsuredData = [
          {
            PlanCode: 4212,
            SumInsured: insuranceDetails?.planASumInsured?.toString(),
            Shortcode: 'GPA',
            Premium: insuranceDetails?.planAPremium?.toString(),
          },
          {
            PlanCode: 4217,
            SumInsured: insuranceDetails?.planBSumInsured?.toString(),
            Shortcode: 'EMIP',
            Premium: insuranceDetails?.planBPremium?.toString(),
          },
        ];
      } else if (
        INSURANCE_SERVICES.ACCEDENT_AND_EMIP == false &&
        INSURANCE_SERVICES.LOSS_OF_JOB == true
      ) {
        SumInsuredData = [
          {
            PlanCode: 4218,
            SumInsured: insuranceDetails?.planCSumInsured?.toString(),
            Shortcode: 'LOE',
            Premium: insuranceDetails?.planCPremium?.toString(),
          },
        ];
      }
      // URL
      const url = nElephantProposal;
      // Authorization
      const authData = await this.generateAuthToken('token');
      if (authData?.message) return authData;

      // Body preparation
      const splittedNames = name.split(' ');
      let first_name = '';
      let middle_name = '';
      let last_name = '';
      if (splittedNames.length > 0) {
        // Skip middle name
        if (splittedNames.length <= 2) {
          first_name = splittedNames[0];
          last_name = splittedNames[1] ?? '';
        }
        // Handle middle name
        else {
          for (let index = 0; index < splittedNames.length; index++) {
            if (index == splittedNames.length - 1) {
              last_name = splittedNames[index];
              first_name = first_name.trim();
            } else first_name += `${splittedNames[index]} `;
          }
        }
      }
      if (first_name.includes('.'))
        first_name = first_name.split('.').join(' ');
      if (last_name == '') last_name = '.';
      let salutation = 'Mr';
      gender = gender.toLowerCase()[0];
      if (gender == 'f') {
        salutation = 'Ms';
        gender = 'Female';
      } else gender = 'Male';
      const unique_id = loanId;
      const body = {
        token: authData.utoken,
        ClientCreation: {
          partner: `${EnvConfig.nbfc.nbfcName}`,
          plan: `${insuranceDetails.plane}`,
          unique_id,
          salutation,
          first_name,
          middle_name,
          last_name,
          gender,
          dob,
          email_id: email,
          mobile_number: phone,
          tenure: '1',
          is_coapplicant: 'No',
          coapplicant_no: '',
          userId: authData.user_id,
          sm_location: 'Mumbai',
          alternateMobileNo: null,
          homeAddressLine1: aadhaarAddress.replace(/[-,/]/g, ' '),
          homeAddressLine2: null,
          homeAddressLine3: null,
          pincode,
        },
        QuoteRequest: {
          NoOfLives: '1',
          adult_count: '1',
          child_count: '0',
          LoanDetails: {
            LoanDisbursementDate: disbursementDate,
            LoanAmount: insuranceDetails?.planBSumInsured?.toString(),
            LoanAccountNo: loanId,
            LoanTenure: loanTenure,
          },
          SumInsuredData: SumInsuredData,
        },
        MemObj: {
          Member: [
            {
              MemberNo: 1,
              Salutation: salutation,
              First_Name: first_name,
              Middle_Name: middle_name,
              Last_Name: last_name,
              Gender: gender,
              DateOfBirth: dob,
              Relation_Code: '1',
            },
          ],
        },
        ReceiptCreation: {
          modeOfEntry: 'Direct',
          PaymentMode: '4',
          bankName: '',
          branchName: '',
          bankLocation: '',
          chequeType: '',
          ifscCode: '',
        },
        Nominee_Detail: nomineeDetails,
        PolicyCreationRequest: {
          IsPolicyIssuance: '1',
          TransactionNumber: 'LoanId-' + loanId,
          TransactionRcvdDate: this.typeService.jsonToReadableDate(
            new Date().toJSON(),
          ),
          CollectionAmount: totalPremium,
          PaymentMode: 'CD Balance',
        },
      };
      // API call
      let response = await this.api.post(url, body);
      // k500Error not handle for the we need store body of the insuranse Proposal
      if (response == k500Error) response = { statusCode: response };
      const data: any = {
        body: JSON.stringify(body),
        response: JSON.stringify(response),
      };
      if (
        response?.acko?.coi_no &&
        response?.acko?.coi_url &&
        response?.care?.coi_no &&
        response?.care?.coi_url
      ) {
        data.insuranceURL = response?.care.coi_url;
        data.insuranceURL1 = response?.acko.coi_url;
        data.status = 1;
        data.completionDate = this.typeService
          .getGlobalDate(new Date())
          .toJSON();
      } else data.status = 2;
      return data;
    } catch (error) {
      return kInternalError;
    }
  }

  //#region get COI
  async getCOI(Lan_number) {
    try {
      // Authorization
      const authData = await this.generateAuthToken('token');
      if (authData?.message) return authData;
      const body = { token: authData.utoken, Lan_number };
      const url = nElephantCOI;
      // API call
      const response = await this.api.post(url, body);
      if (!response || response == k500Error) return kInternalError;
      const data = { success: false, url: '', url1: '' };
      if (response?.success != false) {
        response.forEach((ele) => {
          try {
            if (ele['COI_url']) {
              if (!data.url) data.url = ele['COI_url'];
              else data.url1 = ele['COI_url'];
              data.success = true;
            }
          } catch (error) {}
        });
      }
      return data;
    } catch (error) {
      return kInternalError;
    }
  }

  // check failed policy status
  async funCheckInsuranceStatus(body) {
    const loanId = body.loanId;
    const checkType = body.checkType;
    const insuranceOption: any = {
      where: {
        createdAt: { [Op.gte]: '2024-08-09 18:30:02.019+00' },
        ...(checkType != 'FULL' && { status: 2 }),
      },
    };
    if (checkType == 'FULL') {
      insuranceOption.where[Op.or] = [
        { insuranceURL: { [Op.eq]: null } },
        { insuranceURL1: { [Op.eq]: null } },
      ];
      insuranceOption.limit = 100;
    }
    //if loanId is comming from body
    if (loanId) insuranceOption.where.loanId = loanId;

    const insuranceList = await this.repoManager.getTableWhereData(
      InsuranceEntity,
      ['id', 'loanId', 'body', 'response'],
      insuranceOption,
    );
    if (insuranceList === k500Error) throw new Error();
    if (!insuranceList) return kNoDataFound;

    // Fetch the plan IDs for insurance
    const planIds: any = await this.commonSharedService.refreshInsuranceArray(
      true,
    );
    if (planIds.message) return planIds;
    // Iterate through the list of insurance records
    const length = insuranceList.length;
    for (let i = 0; i < length; i++) {
      try {
        const ele = insuranceList[i];
        const insuranceId = ele?.id;
        const unique_id = ele?.loanId;
        const insuranceBody = ele?.body ? JSON.parse(ele?.body) : {};

        // Extract plan number and get the corresponding plan ID
        const planNumber = insuranceBody?.ClientCreation?.plan;
        const planId = planIds?.planIds[planNumber];
        if (!planId || !unique_id) continue;

        // Generate authentication token to check insurance status
        const authData = await this.generateAuthToken('status');
        if (authData?.message) continue;

        // Prepare request body for the API call
        const body = {
          utoken: authData.utoken,
          plan_id: planId, // Use the fetched plan ID
          unique_id, // Use the actual unique_id from the request
        };

        // Make API call to check insurance status
        const url = nElephantCheckStatus;
        const response = await this.api.post(url, body);
        if (response === k500Error) continue;

        // If COI (Certificate of Insurance) details are available, update the insurance details
        if (
          response?.acko?.coi_no &&
          response?.acko?.coi_url &&
          response?.care?.coi_no &&
          response?.care?.coi_url
        ) {
          // Prepare update data
          const updateData: any = {
            insuranceURL: response?.care?.coi_url,
            insuranceURL1: response?.acko?.coi_url,
            status: 1,
            response: JSON.stringify(response),
            completionDate: this.typeService.getGlobalDate(new Date()).toJSON(),
          };

          // Update the insurance entity in the database
          await this.repoManager.updateRowData(
            InsuranceEntity,
            updateData,
            insuranceId,
          );
          const insuranceRedisKey = 'INSURANCE_DATA_' + unique_id;
          await this.redisService.del(insuranceRedisKey);
        }
      } catch (error) {}
    }
    return {};
  }
  //#endregion

  // insurance status count
  async insuranceCount(query) {
    const startDate = query?.startDate ?? new Date();
    const endDate = query?.endDate ?? new Date();
    const range = this.typeService.getUTCDateRange(startDate, endDate);
    const dateRange = { [Op.gte]: range.fromDate, [Op.lte]: range.endDate };
    const opts = { where: { createdAt: dateRange }, group: 'status' };
    const attr: any = ['status', [fn('COUNT', col('status')), 'count']];
    const count = await this.repoManager.getTableWhereData(
      InsuranceEntity,
      attr,
      opts,
    );
    if (count === k500Error) throw new Error();
    return count;
  }

  async sendInsuranceFailureEmail() {
    try {
      if (!gIsPROD) return {};
      const attributes: any = [
        ['loanId', 'uniqueId'],
        'status',
        'body',
        'response',
        'createdAt',
      ];
      const options = {
        where: {
          status: 2,
          createdAt: { [Op.gte]: '2024-08-01T10:00:00.000Z' },
        },
        useMaster: false,
      };
      const insuranceList = await this.repoManager.getTableWhereData(
        InsuranceEntity,
        attributes,
        options,
      );
      if (insuranceList == k500Error) throw new Error();
      if (insuranceList?.length == 0) return {};

      const modifiedInsuranceList = insuranceList.map((item) => ({
        ...item,
        status: 'failed',
      }));
      const rawExcelData = {
        sheets: ['local-reports'],
        data: [modifiedInsuranceList],
        sheetName: 'Pending Insurance List.xlsx',
      };

      const url: any = await this.fileService.objectToExcelURL(rawExcelData);
      if (url?.message) return url;

      const subject =
        'List of Users with Pending Insurance Policies Generation';
      let htmlcontent: any = fs.readFileSync(KInsuranceFailedTemplate, 'utf-8');

      const toDate = this.typeService.dateToJsonStr(new Date());
      htmlcontent = htmlcontent.replace('##DATE##', toDate);
      htmlcontent = htmlcontent.replace(
        '##TOTALUSERS##',
        modifiedInsuranceList.length,
      );
      htmlcontent = htmlcontent.replace('##NBFCSHORTNAME##', NBFC_NAME);
      htmlcontent = htmlcontent.replace('##NBFCURL##', kNbfcUrl);
      htmlcontent = htmlcontent.replace('##NBFCLOGO##', EnvConfig.url.nbfcLogo);
      // return htmlcontent;
      const EMAILS = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];
      const ccMail: any = process.env.ADMIN_EMAILS.split(',');
      await this.sharedNotification.sendMailFromSendinBlue(
        EMAILS,
        subject,
        htmlcontent,
        null,
        ccMail,
        [{ path: url }],
      );
      return {};
    } catch (error) {}
  }

  async funGetICCDBalanceCheck() {
    const body = {
      utoken: ElephantCDBalanceToken,
      PartnerName: EnvConfig.nbfc.nbfcName,
      PlanName: 'Plan 1',
      InsurerName: 'Care Health Insurance Company limited',
    };
    const url = nElephantCDBalance;
    const res = await this.api.post(url, body);
    if (!res || res == k500Error) return kInternalError;
    const balance = +(res?.intAgentFloatBalanceBpmIO?.floatBalance ?? 0);
    return { ICCDBalance: Math.round(balance) };
  }

  //#region Get all insurance data based on loan id
  async getInsuranceDataOfLoan(loanId) {
    if (!loanId) return kParamMissing('loanId');

    const redisKey = `INSURANCE_DATA_${loanId}`;

    let insuranceData = await this.redisService.get(redisKey);
    if (insuranceData) return JSON.parse(insuranceData);

    insuranceData = await this.repoManager.getRowWhereData(
      InsuranceEntity,
      null,
      { where: { loanId } },
    );
    if (insuranceData == k500Error) return;

    if (insuranceData?.id)
      await this.redisService.set(
        redisKey,
        JSON.stringify(insuranceData),
        NUMBERS.THIRTY_DAYS_IN_SECONDS,
      );
    return insuranceData;
  }
  //#endregion

  //#region Collect insurance data based on insurance id(s)
  async funGetInsuranceData(ids) {
    if (!ids.length) return kParamMissing('ids');
    const data = await this.repoManager.getTableWhereData(
      InsuranceEntity,
      null,
      { where: { id: ids } },
    );
    if (data == k500Error) return data;
    return data;
  }
  //#endregion
}
