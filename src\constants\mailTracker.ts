import { MailTrackerEntity } from 'src/entities/mail.tracker.entity';
import {
  Notification_0,
  Notification_1,
  Notification_2,
  Notification_3,
  Notification_4,
  Notification_5,
  Notification_6,
  Notification_7,
  Notification_8,
  Notification_9,
} from 'src/entities/notification.partition.entity';
import {
  SMS_0,
  SMS_1,
  SMS_2,
  SMS_3,
  SMS_4,
  SMS_5,
  SMS_6,
  SMS_7,
  SMS_8,
  SMS_9,
} from 'src/entities/sms.partition.entity';
import {
  WhatsApp_0,
  WhatsApp_1,
  WhatsApp_2,
  WhatsApp_3,
  WhatsApp_4,
  WhatsApp_5,
  WhatsApp_6,
  WhatsApp_7,
  WhatsApp_8,
  WhatsApp_9,
} from 'src/entities/whatsapp.partition.entity';

//#region MAIL TRACKER PARTITION

export const alertCategory = {
  TEXT: 'TEXT',
  NOTIFICATION: 'NOTIFICATION',
  WHATSAPP: 'WHATSAPP',
  EMAIL: 'EMAIL',
  ALL: 'ALL',
};

export const smsModels = [
  SMS_0,
  SMS_1,
  SMS_2,
  SMS_3,
  SMS_4,
  SMS_5,
  SMS_6,
  SMS_7,
  SMS_8,
  SMS_9,
];

export const notificationModels = [
  Notification_0,
  Notification_1,
  Notification_2,
  Notification_3,
  Notification_4,
  Notification_5,
  Notification_6,
  Notification_7,
  Notification_8,
  Notification_9,
];

export const whatsappModels = [
  WhatsApp_0,
  WhatsApp_1,
  WhatsApp_2,
  WhatsApp_3,
  WhatsApp_4,
  WhatsApp_5,
  WhatsApp_6,
  WhatsApp_7,
  WhatsApp_8,
  WhatsApp_9,
];

//#endregion

export const kMailTrackerType = {
  EMAIL: 0,
  TEXT: 1,
  NOTIFICATION: 2,
  WHATSAPP: 3,
};

export const kMailTrackerStatus = {
  Sent: 0,
  Done: 1,
  Process: 2,
  Reject: 3,
  Received: 4,
};

export const kMailTrackerSubStatus = {
  Done: 1,
  blocked: 2,
  click: 3,
  'country code blocked': 4,
  deferred: 5,
  delivered: 6,
  error: 7,
  failed: 8,
  hard_bounce: 9,
  ndnc: 10,
  notSent: 11,
  open: 12,
  opened: 13,
  proxy_open: 14,
  read: 15,
  rejected: 16,
  request: 17,
  sent: 18,
  soft_bounce: 19,
  unique_opened: 20,
  unknown: 21,
  unsubscribe: 22,
  unsubscribed: 23,
};

export const mapAllKeys = (targetObj, passType?) => {
  if (targetObj?.status !== null && !isNaN(targetObj?.status))
    targetObj.status = getKeyByValue(targetObj.status, kMailTrackerStatus);
  if (targetObj?.subStatus !== null && !isNaN(targetObj?.subStatus))
    targetObj.subStatus = getKeyByValue(
      targetObj.subStatus,
      kMailTrackerSubStatus,
    );
  if (passType !== null && !isNaN(passType)) {
    passType = getKeyByValue(passType, kMailTrackerType);
    targetObj.type = passType;
  }
  return targetObj;
};

const getKeyByValue = (val, targetObj) => {
  return Object.keys(targetObj).find(
    (key) => targetObj[key as keyof typeof targetObj] === val,
  );
};

export const mapObj = {
  [alertCategory.TEXT]: smsModels,
  [alertCategory.NOTIFICATION]: notificationModels,
  [alertCategory.WHATSAPP]: whatsappModels,
  [alertCategory.EMAIL]: MailTrackerEntity,
};

export const getCategoryFromClassName = (classItem, isName?) => {
  let str: number | string = 0;
  if (classItem.name.includes('SMS'))
    str = isName ? getKeyByValue(1, kMailTrackerType) : 1;
  else if (classItem.name.includes('Notification'))
    str = isName ? getKeyByValue(2, kMailTrackerType) : 2;
  else if (classItem.name.includes('WhatsApp'))
    str = isName ? getKeyByValue(3, kMailTrackerType) : 3;
  return str;
};

export const getSafeTime = (dateStr: string | null | undefined): number => {
  const time = new Date(dateStr || '').getTime();
  return isNaN(time) ? -Infinity : time; // Treat invalid/missing dates as very old
};
