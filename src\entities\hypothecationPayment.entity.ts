import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { HypothecationEntity } from './hypothecation.entity';
import { SYSTEM_ADMIN_ID } from 'src/constants/globals';
@Table({})
export class HypothecationPaymentsEntity extends Model<HypothecationPaymentsEntity> {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    allowNull: false,
  })
  id: number;

  @Column({
    type: DataType.SMALLINT,
    allowNull: false,
    unique: 'unique_lender_emi',
  })
  emi_no: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  emi_date: any;

  @Column({
    type: DataType.DOUBLE,
    allowNull: false,
  })
  openingBalance: number;

  @Column({
    type: DataType.DOUBLE,
    allowNull: false,
  })
  emiAmount: number;

  @Column({
    type: DataType.DOUBLE,
    allowNull: false,
  })
  interestAmount: number;

  @Column({
    type: DataType.DOUBLE,
    allowNull: false,
  })
  principalAmount: number;

  @Column({
    type: DataType.DOUBLE,
    allowNull: false,
  })
  closingBalance: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  })
  emiPaid: boolean;

  @ForeignKey(() => HypothecationEntity)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    unique: 'unique_lender_emi',
  })
  lender_id: number;

  // Defining the association
  @BelongsTo(() => HypothecationEntity)
  lender: HypothecationEntity;

  //admin id of one who updated last
  @Column({
    allowNull: true,
    type: DataType.SMALLINT,
  })
  adminId: number;
}
