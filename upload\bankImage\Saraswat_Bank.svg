<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 23.0.5, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 50 50" style="enable-background:new 0 0 50 50;" xml:space="preserve">
<style type="text/css">
	.st0{fill:url(#XMLID_2_);}
	.st1{fill:url(#XMLID_3_);}
</style>
<radialGradient id="XMLID_2_" cx="-225.2088" cy="-233.7197" r="0.5682" gradientTransform="matrix(38.059 0 0 38.059 8722.6172 9368.9453)" gradientUnits="userSpaceOnUse">
	<stop  offset="0" style="stop-color:#FDB913"/>
	<stop  offset="0.2921" style="stop-color:#FDB913"/>
	<stop  offset="0.9775" style="stop-color:#F47920"/>
	<stop  offset="0.9775" style="stop-color:#F47920"/>
	<stop  offset="1" style="stop-color:#F47920"/>
</radialGradient>
<polygon id="XMLID_50_" class="st0" points="3.5,12.7 3.5,28.5 25.1,16 33.1,20.6 46.7,12.7 25.1,0.3 "/>
<radialGradient id="XMLID_3_" cx="-225.2266" cy="-234.2963" r="0.5682" gradientTransform="matrix(37.8609 0 0 37.8609 8678.9463 9370.3652)" gradientUnits="userSpaceOnUse">
	<stop  offset="0" style="stop-color:#ED1A3B"/>
	<stop  offset="0.309" style="stop-color:#ED1C24"/>
	<stop  offset="0.309" style="stop-color:#ED1C24"/>
	<stop  offset="0.9775" style="stop-color:#A70A25"/>
	<stop  offset="1" style="stop-color:#A70A25"/>
</radialGradient>
<polygon id="XMLID_51_" class="st1" points="25.1,34.5 17.1,29.8 3.7,37.6 3.7,37.8 24.4,49.7 25.9,49.7 46.7,37.7 46.7,22 "/>
</svg>
