// Imports
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { befiscConsentObj, validAppTypeValues } from 'src/constants/globals';
import { k500Error } from 'src/constants/misc';
import {
  kBefiscMoblileToUanURL,
  kBefiscProfileBasicURL,
  kBefiscUanToEmploymentURL,
} from 'src/constants/network';
import { kBefiscHeader } from 'src/constants/objects';
import {
  kInternalError,
  kInvalidParamValue,
  kParamMissing,
} from 'src/constants/responses';
import { kNoDataFound } from 'src/constants/strings';
import { FetchDataEntity } from 'src/entities/fetchData.entity';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { EmploymentSharedService } from 'src/shared/employment.shared.service';
import { APIService } from 'src/utils/api.service';
import { CryptService } from 'src/utils/crypt.service';
import { TypeService } from 'src/utils/type.service';

@Injectable()
export class BefiscService {
  constructor(
    private readonly api: APIService,
    private readonly cryptService: CryptService,
    private readonly typeService: TypeService,
    @Inject(forwardRef(() => EmploymentSharedService))
    private readonly employmentSharedService: EmploymentSharedService,
    private readonly repoManager: RepositoryManager,
  ) {}

  async mobileToUan(reqData) {
    const mobileNo = reqData?.mobileNo;
    const appType = reqData?.appType;
    if (!mobileNo) return kParamMissing('mobileNo');
    if (typeof mobileNo != 'string') return kInvalidParamValue('mobileNo');
    if (!validAppTypeValues.includes(appType))
      return kInvalidParamValue('appType');
    const data = await this.api.post(
      kBefiscMoblileToUanURL,
      { mobile: mobileNo, multiple: 1 },
      kBefiscHeader(appType),
    );

    const logTrackData = {
      phone: this.cryptService.encryptPhone(mobileNo),
      hashPhone: this.cryptService.getMD5Hash(mobileNo),
      result: null,
      requestData: JSON.stringify({ mobile: mobileNo, multiple: 1 }),
      response: JSON.stringify(data),
      type: 'BEFISC_MOBILE_TO_UAN',
    };
    await this.repoManager.createRowData(FetchDataEntity, logTrackData);
    if (
      data == k500Error ||
      !data?.result ||
      (data?.status != 1 && data?.status != 2)
    )
      return kInternalError;
    if (Array.isArray(data?.result?.uan)) return data?.result;
    return { uan: [] };
  }

  async uanToEmployment(reqData) {
    const uanNumber = reqData?.uanNumber;
    const appType = reqData?.appType;
    if (!uanNumber) return kParamMissing('uanNumber');
    if (typeof uanNumber != 'string') return kInvalidParamValue('uanNumber');
    if (!validAppTypeValues.includes(appType))
      return kInvalidParamValue('appType');
    const consent = befiscConsentObj.uanToEmployment;
    const data = await this.api.post(
      kBefiscUanToEmploymentURL,
      { uan: uanNumber, ...consent },
      kBefiscHeader(appType),
    );
    const logTrackData = {
      phone: null,
      hashPhone: null,
      result: null,
      requestData: JSON.stringify({ uan: uanNumber, ...consent }),
      response: JSON.stringify(data),
      type: 'BEFISC_UAN_TO_EMPLOYMENT',
    };
    await this.repoManager.createRowData(FetchDataEntity, logTrackData);
    if (
      data == k500Error ||
      !data?.result ||
      (data?.status != 1 && data?.status != 2)
    )
      return kInternalError;
    if (Array.isArray(data?.result)) return data?.result;
    return [];
  }

  // for user other number, email and address (Profile Basic)
  async profileBasic(body) {
    const userList = body?.userList ?? [];
    const phoneNo = body?.mobileNo;
    const userName = body?.fullName;
    const appType = body?.appType;
    if (typeof userList !== 'object') return kInvalidParamValue('userList');
    if (!validAppTypeValues.includes(appType))
      return kInvalidParamValue('appType');
    if (phoneNo && userName)
      userList.push({ fullName: userName, mobileNo: phoneNo });

    const url = kBefiscProfileBasicURL;
    const type = 'BEFISC_PROFILE_BASIC';
    const length = userList.length;
    for (let index = 0; index < length; index++) {
      const ele = userList[index];
      const mobile = ele?.mobileNo;
      const first_name = ele?.fullName;
      if (!mobile || !first_name) continue;
      const createDataObj = {
        hashPhone: this.cryptService.getMD5Hash(mobile),
        phone: this.cryptService.encryptPhone(mobile),
        type,
      };
      const createData = await this.repoManager.createRowData(
        FetchDataEntity,
        createDataObj,
      );
      if (createData === k500Error) continue;
      const consent = befiscConsentObj.profileConsent;
      const reqData = { mobile, first_name, ...consent };
      const response = await this.api.requestPost(
        url,
        reqData,
        kBefiscHeader(appType),
      );
      const result = response?.result ?? {};
      const createId = createData?.id;
      if (createId) {
        const updateData = { response: JSON.stringify(response), result };
        await this.repoManager.updateRowData(
          FetchDataEntity,
          updateData,
          createId,
        );
      }
    }
    return true;
  }

  async uanDetails(reqData: any) {
    const mobileNo = reqData?.mobileNo;
    const appType = reqData?.appType;
    if (!mobileNo) return kParamMissing('mobileNo');
    if (typeof mobileNo != 'string') return kInvalidParamValue('mobileNo');
    if (!validAppTypeValues.includes(appType))
      return kInvalidParamValue('appType');
    const data = await this.employmentSharedService.getUanDetails({
      ...reqData,
      typeOfService: 2,
    });
    return data;
  }

  async getEPFODataUsingUanToEmployment(uanList: Array<String>, appType: any) {
    const employers = [];
    //get employment details
    for (let index = 0; index < uanList.length; index++) {
      const uan = uanList[index];
      const uanValidationData = await this.uanToEmployment({
        uanNumber: uan,
        appType,
      });
      if (uanValidationData?.message) return kInternalError;
      employers.push(...uanValidationData);
    }

    //format final response data
    let lastEmployer = null;
    let latestJoiningDate = null;
    let totalTenureOfEmployment = null;

    //get lastEmployer, totalTenureOfEmployment
    for (const emp of employers) {
      const startDate =
        emp?.date_of_joining?.length == 10
          ? new Date(emp?.date_of_joining?.split('/')?.reverse()?.join('-'))
          : null;
      const endDate =
        emp?.last_pf_submitted?.length == 10
          ? new Date(emp?.last_pf_submitted?.split('/')?.reverse()?.join('-'))
          : null;
      //change latestJoiningDate and lastEmployer if need
      if (startDate && (!lastEmployer || startDate > latestJoiningDate)) {
        latestJoiningDate = startDate;
        lastEmployer = emp;
      }
      //add total totalTenureOfEmployment
      if (startDate && endDate)
        totalTenureOfEmployment += this.typeService.dateDifference(
          startDate,
          endDate,
          'Month',
        );
    }

    let tenureOfEmployment = null;
    let exitDate = null;
    let joiningDate = null;
    let lastPFDate = null;

    //add exitDate and tenureOfEmployment,joiningDate,lastPFDate
    if (lastEmployer) {
      exitDate =
        lastEmployer?.date_of_exit?.length == 10
          ? new Date(
              lastEmployer?.date_of_exit?.split('/')?.reverse()?.join('-'),
            )
          : null;

      joiningDate =
        lastEmployer?.date_of_joining?.length == 10
          ? new Date(
              lastEmployer?.date_of_joining?.split('/')?.reverse()?.join('-'),
            )
          : null;
      lastPFDate =
        lastEmployer?.last_pf_submitted?.length == 10
          ? new Date(
              lastEmployer?.last_pf_submitted?.split('/')?.reverse()?.join('-'),
            )
          : null;

      if (joiningDate && lastPFDate)
        tenureOfEmployment += this.typeService.dateDifference(
          joiningDate,
          lastPFDate,
          'Month',
        );

      exitDate = exitDate ? this.typeService.getGlobalDate(exitDate) : null;
      joiningDate = joiningDate
        ? this.typeService.getGlobalDate(joiningDate)
        : null;
      lastPFDate = lastPFDate
        ? this.typeService.getGlobalDate(lastPFDate)
        : null;
    }

    const data = {
      typeOfService: 2,
      tenureOfEmployment,
      totalTenureOfEmployment,
      joiningDate,
      lastPFDate,
      exitDate,
      latestCompanyName: lastEmployer?.establishment_name?.trim()?.length
        ? lastEmployer?.establishment_name?.trim()?.toUpperCase()
        : null,
      employeeName: lastEmployer?.name?.trim()?.length
        ? lastEmployer?.name?.trim()?.toUpperCase()
        : null,
      exitReason: null,
      uanList,
      allEmploymentData: JSON.stringify(employers),
    };
    return data;
  }
}
