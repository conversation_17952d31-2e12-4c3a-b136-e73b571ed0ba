// Imports
import { Body, Controller, Get, Post, Query, Res } from '@nestjs/common';

import { kInternalError, kSuccessData } from 'src/constants/responses';
import { WAChatService } from './waChat.service';

@Controller('admin/wachat')
export class WAChatController {
  constructor(private readonly waChatService: WAChatService) {}

  @Post('getValueOfTemplate')
  async getValueOfTemplate(@Body() body, @Res() res) {
    try {
      const data: any = await this.waChatService.getValueOfTemplate(body);
      if (data.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Post('adminWASendMessage')
  async adminWASendMessage(@Body() body, @Res() res) {
    try {
      const data: any = await this.waChatService.adminWASendMessage(body);
      if (data?.message) return res.send(data);
      return res.json({ ...kSuccessData, data });
    } catch (err) {
      return kInternalError;
    }
  }

  @Post('userChatList')
  async userChatList(@Body() body, @Res() res) {
    try {
      const response: any = await this.waChatService.userChatList(body);
      if (response?.message) return res.send(response);
      return res.json({ ...kSuccessData, data: response });
    } catch (err) {
      return kInternalError;
    }
  }

  @Get('getUserChat')
  async getUserChat(@Query() query, @Res() res) {
    try {
      const response: any = await this.waChatService.getUserChat(query);
      if (response?.message) return res.send(response);
      return res.json({ ...kSuccessData, data: response });
    } catch (err) {
      return kInternalError;
    }
  }

  @Get('readMessage')
  async readMessage(@Query() query, @Res() res) {
    try {
      const response: any = await this.waChatService.readMessage(query);
      if (response?.message) return res.send(response);
      return res.json({ ...kSuccessData, data: response });
    } catch (err) {
      return kInternalError;
    }
  }

  @Post('sendTemplateMsg')
  async sendTemplateMsg(@Body() body, @Res() res) {
    try {
      const response = await this.waChatService.sendTemplateMsg(body);
      if (response?.message) return res.send(response);
      return res.json({ ...kSuccessData, data: response });
    } catch (err) {
      res.send(kInternalError);
    }
  }

  @Get('getAllTemplates')
  async getAllTemplates(@Query() query, @Res() res) {
    try {
      const data: any = await this.waChatService.getAllTemplates(query);
      if (data?.message) return res.send(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Get('getTemplateByName')
  async getTemplateByName(@Query() query, @Res() res) {
    try {
      const data: any = await this.waChatService.getTemplateByName(query);
      if (data?.message) return res.send(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }

  @Post('uploadFile')
  async uploadFile(@Body() body, @Res() res) {
    try {
      const data: any = await this.waChatService.fileToUrl(body);
      if (data?.message) return res.send(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      return res.json(kInternalError);
    }
  }
}
