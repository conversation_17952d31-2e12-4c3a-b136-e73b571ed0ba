// Imports
import { Body, Controller, Get, Post, Query, Res } from '@nestjs/common';
import { DisbursementService } from './disbursement.service';
import { kInternalError, kSuccessData } from 'src/constants/responses';
import { DashboardDisbursement } from './disbursementDeshboard.service';
import { ErrorContextService } from 'src/utils/error.context.service';

@Controller('admin/disbursement')
export class DisbursementController {
  constructor(
    private readonly service: DisbursementService,
    private readonly dashboardDisbursement: DashboardDisbursement,
    private readonly errorContextService: ErrorContextService,
  ) {}

  @Post('markDisbursementAsComplete')
  async funMarkDisbursementAsComplete(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.markDisbursementAsComplete(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }

  // Fallback -> Relog the appsflyer disbursement event
  @Post('ReLogMissedEvents')
  async funRelogMissedEvents(@Res() res) {
    try {
      await this.service.reLogMissedEvents();
      return res.send(kSuccessData);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }

  // reDirect user in Esing after DisbursementFailed
  @Post('reEsingAfterDisbursementFailed')
  async reEsingAfterDisbursementFailed(@Body() body, @Res() res) {
    try {
      const data: any =
        await this.dashboardDisbursement.reEsingAfterDisbursementFailed(body);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  // fiscal year summary disbursement card
  @Get('fiscalSummaryDisbursement')
  async getFiscalSummaryDisbursement(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.getFiscalSummaryDisbursement(query);
      if (data?.message) return res.send(data);
      return res.json({ ...kSuccessData, ...data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  //#region Cron for fiscal year summary disbursement card
  @Get('fiscalSummaryDisbursementCron')
  async getFiscalSummaryDisbursementCron(@Res() res) {
    try {
      const data: any = await this.service.getFiscalSummaryDisbursementCron();
      if (data?.message) return res.send(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }
  //#endregion

  // cron for disbursement settlement
  @Post('disbursementSettlement')
  async disbursementSettlement(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.disbursementSettlement(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  // Manual Loan Status Active and MarkasDisbursement call
  @Post('manualMarkAsDisbursement')
  async funManualMarkAsDisbursement(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.manualMarkAsDisbursement(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Post('reSyncDisbursementInTally')
  async funReSyncDisbursementInTally(@Body() body) {
    return await this.service.reSyncDisbursementInTally(body);
  }
}
