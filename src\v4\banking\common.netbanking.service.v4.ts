import { Injectable } from '@nestjs/common';
import puppeteer from 'puppeteer';
import { EnvConfig } from 'src/configs/env.config';
import {
  nGoogleSearch,
  nRazorpayIFSC,
  puppeteerConfig,
  PUPPETER_SERVICE,
} from 'src/constants/globals';
import { k500Error } from 'src/constants/misc';
import { nFetchIFSC, nInsertLog } from 'src/constants/network';
import { BrowserData } from 'src/main';
import { APIService } from 'src/utils/api.service';
const cheerio = require('cheerio');

@Injectable()
export class commonNetBankingServiceV4 {
  constructor(private readonly api: APIService) {}
  htmlToJSON(html: string): any {
    const $ = cheerio.load(html);

    function parseElement(element): any {
      const result: any = {};

      // Parse element attributes
      const attributes: any = {};
      try {
        for (const attr of element.attribs) {
          attributes[attr] = element.attribs[attr];
        }
        result.attributes = attributes;
      } catch (error) {}

      // Parse element content
      result.content = [];

      element.children.forEach((child) => {
        if (child.type === 'tag') {
          result.content.push({
            tag: child.name,
            ...parseElement(child),
          });
        } else if (child.type === 'text') {
          result.content.push({
            text: child.data,
          });
        }
      });

      return result.content;
    }

    // Start parsing from the root element (usually 'body' in HTML)
    const rootElement = $('body').get(0);
    return parseElement(rootElement);
  }

  // Get IFSC details from google and razorpay
  async fetchIFSCFromBranchName(branchName: string, bankName) {
    try {
      let url: any = '';
      if (PUPPETER_SERVICE == true) {
        url = `${EnvConfig.server.encServerBaseUrl}getIfscCode`;
        const response = await this.api.post(url, { branchName, bankName });
        console.log(response);
        if (!response || response == k500Error) throw new Error();
        const ifscCode = response.data;
        return ifscCode;
      } else {
        const activePages = ((await BrowserData.browserInstance?.pages()) ?? [])
          .length;
        if (activePages == 0)
          BrowserData.browserInstance = await puppeteer.launch(puppeteerConfig);
        url = `https://www.google.com/search?q=${bankName} branch CODE FOR ${branchName} RAZORPAY`;
        const page = await BrowserData.browserInstance.newPage();
        await page.goto(url);
        var html = await page.content();
        if (!html.includes('Page Expired')) {
          const response = html;
          if (response != k500Error) {
            const rawHTML: string = response.toString();
            const pattern = /[A-Z]{4}0[A-Z0-9]{6}/g;
            const matches = rawHTML.match(pattern);
            if (matches?.length > 0) {
              const ifscData = await this.api.get(nRazorpayIFSC + matches[0]);
              if (ifscData != k500Error && ifscData.BANK) {
                return ifscData.IFSC;
              }
            }
          }
        }
        await page.close();
      }
    } catch (error) {
      console.log('error : ', error);
    }
  }
  periodicFetchConsoleData(reqData, console, step) {
    return {
      combinations: [console],
      apiTriggers: [
        {
          url: nInsertLog,
          method: 'POST',
          needWebResponse: false,
          body: {
            loanId: reqData.loanId,
            userId: reqData.userId,
            type: 1,
            subType: 1,
            status: 2,
            values: {
              bankCode: reqData.bankCode,
              periodicFetch: true,
              step: step,
            },
          },
        },
      ],
    };
  }

  JS_TRIGGERS = {
    PERIODIC_HTML_FETCH: `setInterval(()=> {
      try {
        const dateStr = new Date().toJSON();
        console.log(dateStr + "PERIODIC_HTML_FETCH ->" +  document.documentElement.innerHTML);
      } catch (error) {} }, 1000);`,
  };

  // fetch ifsc code with trueshield.ai
  async fetchIFSC({ branchName, bankName }) {
    let ifsc = '0';
    if (!branchName || !bankName) return ifsc;

    const body = {
      bank: bankName,
      branch: branchName,
    };

    const res = await this.api.post(nFetchIFSC, body);
    if (res.valid && res?.data) ifsc = res.data?.IFSC_code ?? '0';
    return ifsc;
  }

  getRelevantLoaderSteps(totalSteps: number) {
    try {
      totalSteps = +totalSteps;
      const loaderSteps = [];
      for (let i = 0; i < totalSteps; i++) {
        try {
          if (i == totalSteps - 1) {
            loaderSteps.push(100);
          } else {
            loaderSteps.push(Math.floor(100 / totalSteps) * (i + 1));
          }
        } catch (error) {}
      }
      return loaderSteps;
    } catch (error) {}
  }
}
