import {
  Body,
  Controller,
  Get,
  Headers,
  Post,
  Query,
  Res,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  k422ErrorMessage,
  kInternalError,
  kSuccessData,
} from 'src/constants/responses';
import { FileInterceptor } from '@nestjs/platform-express';
import { kUploadFileObj } from 'src/constants/objects';
import { LeadOpsGuard } from 'src/authentication/lead.guard';
import { LeadTrackingServices } from './leadTracking.service';
import { LEAD_SOURCE } from 'src/constants/leadTracking';

@UseGuards(LeadOpsGuard)
@Controller('thirdParty/leadTracking')
export class LeadTrackingController {
  constructor(private service: LeadTrackingServices) {}

  @Post('createRecords')
  @UseInterceptors(FileInterceptor('file', kUploadFileObj()))
  async create(
    @Res() res,
    @Body() body,
    @UploadedFile() file,
    @Headers() headers,
  ) {
    try {
      // return res.send(k422ErrorMessage('Under maintenance!'));
      const leadSource = headers['lead-source'];
      if (
        leadSource == null ||
        !Object.keys(LEAD_SOURCE).includes(String(leadSource))
      )
        return res.send(k422ErrorMessage('Unauthorized access!'));
      const data: any = await this.service.createData(body, file, leadSource);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      console.log(error);
      return res.send(kInternalError);
    }
  }

  //#region Get lead's loan status based on phone number
  @Get('getLoanStatus')
  async getLeadsLoanStatus(@Query() query, @Headers() headers, @Res() res) {
    try {
      const leadSource = headers['lead-source'];

      if (
        leadSource == null ||
        !Object.keys(LEAD_SOURCE).includes(String(leadSource))
      ) {
        return k422ErrorMessage('Unauthorized access!');
      }

      query.leadSource = leadSource;
      const data: any = await this.service.getLeadsLoanStatus(query);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }
  //#endregion

  @Get('getDashboardData')
  async getDashboardData(@Query() query, @Res() res) {
    const data: any = await this.service.getDashboardData(query);
    if (data?.message) return res.send(data);
    return res.send({ ...kSuccessData, data });
  }

  @Get('getLeadCountsReport')
  async getLeadCountsReport(@Query() query, @Res() res) {
    const data: any = await this.service.getLeadCountsReport(query);
    if (data?.message) return res.send(data);
    return res.send({ ...kSuccessData, data });
  }

  @Post('getPhoneNumberByUserId')
  async getPhoneNumberByUserId(@Body() body, @Res() res) {
    const data: any = await this.service.getPhoneNumberByUserId(body);
    if (data?.message) return res.send(data);
    return res.send({ ...kSuccessData, data });
  }

  @Get('migrateLostDataFromMongo')
  async funMigrateLostDataFromMongo(@Query() query, @Res() res) {
    const data: any = await this.service.migrateLostDataFromMongo(query);
    if (data?.message) return res.send(data);
    return res.send({ ...kSuccessData, data });
  }

  @Get('getLeadTableSeries')
  funGetLeadTableSeries(@Query() query, @Res() res) {
    const data = this.service.funGetLeadTrackingTableSeries(query);
    return res.send({ ...kSuccessData, data });
  }

  //#region Get report of lead's loan status, on base of filters of date range and lead's IP address.
  @Get('getLeadStatusReport')
  async funGetLeadStatusReport(@Query() query, @Headers() headers, @Res() res) {
    const leadSource = headers['lead-source'];

    if (
      leadSource == null ||
      !Object.keys(LEAD_SOURCE).includes(String(leadSource))
    ) {
      return res.send(k422ErrorMessage('Unauthorized access!'));
    }

    query.leadSource = leadSource;
    const data: any = await this.service.funGetLeadStatusReport(query);
    if (data?.message) return res.send(data);
    return res.send({ ...kSuccessData, data });
  }
  //#endregion
}
