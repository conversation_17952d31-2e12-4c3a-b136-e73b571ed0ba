import { Model, Table } from 'sequelize-typescript';
import { Column, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { registeredUsers } from './user.entity';

@Table({})
export class OthersBankStatementEntity extends Model<OthersBankStatementEntity> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ForeignKey(() => registeredUsers)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  url: string;

  @BelongsTo(() => registeredUsers)
  user: registeredUsers;
}
