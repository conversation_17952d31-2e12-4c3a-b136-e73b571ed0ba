Banks with low success rate: [
  {
    "bankName": "AXIS",
    "consentGiven": 41,
    "correctResponse": 28,
    "successRatePercentage": 68.29
  },
  {
    "bankName": "FEDERAL",
    "consentGiven": 14,
    "correctResponse": 9,
    "successRatePercentage": 64.29
  },
  {
    "bankName": "ICICI",
    "consentGiven": 8,
    "correctResponse": 5,
    "successRatePercentage": 62.5
  },
  {
    "bankName": "KOTAK",
    "consentGiven": 62,
    "correctResponse": 35,
    "successRatePercentage": 56.45
  },
  {
    "bankName": "UNION_BANK",
    "consentGiven": 2,
    "correctResponse": 1,
    "successRatePercentage": 50
  },
  {
    "bankName": "BANK_OF_BARODA",
    "consentGiven": 17,
    "correctResponse": 7,
    "successRatePercentage": 41.18
  },
  {
    "bankName": "HDFC",
    "consentGiven": 15,
    "correctResponse": 6,
    "successRatePercentage": 40
  }
]




  

  // async getFinvuCamsDailyStats() {
  //   try {
  //     const type = 'T';
  //     const dates: any = await this.collectionService.startEndDate(
  //       type,
  //       false,
  //       null,
  //     );

  //     const dateRange: any = this.typeService.getUTCDateRange(
  //       dates.startDate.toString(),
  //       dates.endDate.toString(),
  //     );
  //     console.log(`DATE RANGE:-->>`, {
  //       fromDate: dateRange.fromDate,
  //       endDate: dateRange.endDate,
  //     });

  //     const bankingData = await this.repo.getTableWhereData(
  //       BankingEntity,
  //       ['bank', 'consentId', 'consentMode', 'userId', 'createdAt'],
  //       {
  //         where: {
  //           consentMode: ['FINVU', 'CAMS'],
  //           consentId: { [Op.ne]: null },
  //           createdAt: {
  //             [Op.gte]: dateRange.fromDate,
  //             [Op.lte]: dateRange.endDate,
  //           },
  //         },
  //       },
  //     );
  //     if (bankingData === k500Error) throw new Error();
  //     console.log('Banking Data Count-->>:', bankingData.length);

  //     const periodicData = await this.repo.getTableWhereData(
  //       PeriodicEntity,
  //       ['consentId', 'status', 'source', 'createdAt'],
  //       {
  //         where: {
  //           type: [3, 6],
  //           source: [1, 2],
  //           status: [0, 1, 2, 3, 4],
  //           createdAt: {
  //             [Op.gte]: dateRange.fromDate,
  //             [Op.lte]: dateRange.endDate,
  //           },
  //         },
  //       },
  //     );
  //     if (periodicData === k500Error) throw new Error();

  //     console.log('Periodic Data Count:-->>', periodicData.length);

  //     const lowSuccess = this.calculateStats(bankingData, periodicData);

  //     console.log(' Final Stats Before Filter:', lowSuccess.length);
  //     console.log(' Sample Final Stats:', lowSuccess.slice(0, 3));

  //     return lowSuccess;
  //   } catch (error) {
  //     this.errorContextService.throwAndSetCtxErr(error);
  //     return kInternalError;
  //   }
  // }

  // private calculateStats(bankingData, periodicData) {
  //   const bankStats = {};
  //   bankingData.forEach((bank) => {
  //     const key = `${bank.bank}_${bank.consentMode}`;
  //     if (!bankStats[key]) {
  //       bankStats[key] = {
  //         bankName: bank.bank,
  //         consentMode: bank.consentMode,
  //         totalUsers: 0,
  //         consentIds: new Set(),
  //         userIds: new Set(),
  //       };
  //     }
  //     bankStats[key].userIds.add(bank.userId);
  //     bankStats[key].consentIds.add(bank.consentId);
  //   });

  //   // Calculate total users per bank
  //   Object.keys(bankStats).forEach((key) => {
  //     bankStats[key].totalUsers = bankStats[key].userIds.size;
  //   });

  //   const periodicStats = {};
  //   periodicData.forEach((periodic) => {
  //     // Find which bank this consentId belongs to
  //     const relatedBanking = bankingData.find(
  //       (b) => b.consentId === periodic.consentId,
  //     );
  //     if (relatedBanking) {
  //       const bankKey = `${relatedBanking.bank}_${relatedBanking.consentMode}`;

  //       if (!periodicStats[bankKey]) {
  //         periodicStats[bankKey] = {
  //           totalRequests: 0,
  //           successCount: 0,
  //           failureCount: 0,
  //         };
  //       }

  //       periodicStats[bankKey].totalRequests++;
  //       const expectedSource = relatedBanking.consentMode === 'FINVU' ? 1 : 2;
  //       if (periodic.source === expectedSource) {
  //         if (periodic.status >= 3) {
  //           periodicStats[bankKey].successCount++;
  //         } else {
  //           periodicStats[bankKey].failureCount++;
  //         }
  //       }
  //     }
  //   });

  //   // Merge both stats for final result
  //   const finalStats = [];
  //   for (const key in bankStats) {
  //     const bank = bankStats[key];
  //     const periodic = periodicStats[key] || {
  //       totalRequests: 0,
  //       successCount: 0,
  //       failureCount: 0,
  //     };

  //     // Calculate consent stats and success rate
  //     const consentGiven = periodic.totalRequests;
  //     const correctResponse = periodic.successCount;
  //     const successRatePercentage =
  //       consentGiven > 0
  //         ? Math.round((correctResponse / consentGiven) * 100 * 100) / 100
  //         : 0;

  //     finalStats.push({
  //       consentMode: bank.consentMode,
  //       bankName: bank.bankName,
  //       consentGiven,
  //       correctResponse,
  //       successRatePercentage,
  //       totalUsers: bank.totalUsers,
  //       failureResponse: periodic.failureCount,
  //       status: correctResponse > 0 ? 'Success' : 'Failure',
  //     });
  //   }

  //   // Sort by success rate (highest first) and filter low success banks
  //   finalStats.sort(
  //     (a, b) => b.successRatePercentage - a.successRatePercentage,
  //   );

  //   const lowSuccessRate = finalStats.filter(
  //     (stat) => stat.successRatePercentage < 80 && stat.consentGiven > 0,
  //   );

  //   return lowSuccessRate;
  // }