// Imports
import { Injectable } from '@nestjs/common';
import { DisbursementMasterReportDTO } from '../report.dto';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { LOAN_VERIFICATION, MASTER_REPORT_TYPE } from 'src/constants/objects';
import { TypeService } from 'src/utils/type.service';
import * as fs from 'fs';
import { CryptService } from 'src/utils/crypt.service';
import { EnvConfig } from 'src/configs/env.config';
import { StringService } from 'src/utils/string.service';
import { DateService } from 'src/utils/date.service';
import {
  CURRENT_EMI_COUNT,
  legalString,
  PAGE_LIMIT,
  ptpCrmIds,
} from 'src/constants/globals';
import { LoanRepository } from 'src/repositories/loan.repository';
import { Op, Sequelize } from 'sequelize';
import { k500Error } from 'src/constants/misc';
import { DisbursmentRepository } from 'src/repositories/disbursement.repository';
import { employmentDetails } from 'src/entities/employment.entity';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { UserRepository } from 'src/repositories/user.repository';
import { BankingEntity } from 'src/entities/banking.entity';
import { MasterRepository } from 'src/repositories/master.repository';
import { KYCEntity } from 'src/entities/kyc.entity';
import { EMIRepository } from 'src/repositories/emi.repository';
import { InsuranceEntity } from 'src/entities/insurance.entity';
import { CibilScoreRepository } from 'src/repositories/cibil.score.repository';
import { FileService } from 'src/utils/file.service';
import { ReportHistoryRepository } from 'src/repositories/reportHistory.repository';
import { CrmRepository } from 'src/repositories/crm.repository';
import {
  kCashfree,
  kCompleted,
  kEMIPay,
  kFullPay,
  KICICI,
  kPartPay,
  kRefund,
  userCategoryTag,
} from 'src/constants/strings';
import { WaiverEntity } from 'src/entities/waiver.entity';
import { CommonService } from 'src/utils/common.service';
import { TransactionRepository } from 'src/repositories/transaction.repository';
import { loanTransaction } from 'src/entities/loan.entity';
import { employmentSector } from 'src/entities/sector.entity';
import { employmentDesignation } from 'src/entities/designation.entity';
import { employmentType } from 'src/entities/employment.type';
import { EmploymentRepository } from 'src/repositories/employment.repository';
import { SubscriptionRepository } from 'src/repositories/subscription.repository';
import { BankingRepository } from 'src/repositories/banking.repository';
import { SlackService } from 'src/thirdParty/slack/slack.service';
import { kInternalError } from 'src/constants/responses';
import { ErrorContextService } from 'src/utils/error.context.service';
import { EmiEntity } from 'src/entities/emi.entity';
import { CalculationSharedService } from 'src/shared/calculation.service';
import { ElephantService } from 'src/thirdParty/elephant/elephant.service';
import { LEAD_SOURCE } from 'src/constants/leadTracking';

@Injectable()
export class ReportMasterService {
  constructor(
    private readonly userRepo: UserRepository,
    private readonly emiRepo: EMIRepository,
    private readonly masterRepo: MasterRepository,
    private readonly loanRepo: LoanRepository,
    private readonly disburesementRepo: DisbursmentRepository,
    private readonly cryptService: CryptService,
    private readonly commonSharedService: CommonSharedService,
    private readonly typeService: TypeService,
    private readonly strService: StringService,
    private readonly repoManager: RepositoryManager,
    private readonly dateService: DateService,
    private readonly cibilScoreRepo: CibilScoreRepository,
    private readonly fileService: FileService,
    private readonly reportHistoryRepo: ReportHistoryRepository,
    private readonly crmRepo: CrmRepository,
    private readonly transRepo: TransactionRepository,
    private readonly common: CommonService,
    private readonly employmentRepo: EmploymentRepository,
    private readonly subscriptionRepo: SubscriptionRepository,
    private readonly bankingRepo: BankingRepository,
    private readonly slackService: SlackService,
    private readonly repository: TransactionRepository,
    private readonly errorContextService: ErrorContextService,
    private readonly calculation: CalculationSharedService,
    private readonly elephantService: ElephantService,
  ) {}

  //#region
  async disbursementMasterReport(reqData: DisbursementMasterReportDTO) {
    reqData.reportType = MASTER_REPORT_TYPE.DISBURSEMENT_REPORT;
    const details: any = await this.fetchAllData(reqData);
    details.download = reqData?.download ?? false;
    const prepared_data = await this.prepareDisbursementReport(details);
    if (!reqData?.download)
      return { rows: prepared_data, count: details.count };
    return await this.downloadReport(
      reqData,
      prepared_data,
      'DisbursementMasterData.xlsx',
    );
  }
  //#endregion

  //#region userMasterReport
  async userMasterReport(reqData) {
    const isDownload = reqData?.download == 'true';
    reqData.download = isDownload;
    reqData.reportType = MASTER_REPORT_TYPE.MASTER_REPORT;
    const data = await this.getDataForUserMasterReport(reqData);
    const preparedData = await this.prepareUserMasterReport(data, isDownload);
    if (isDownload)
      return await this.downlaodReport(
        reqData,
        preparedData,
        'UserMasterReport.xlsx',
      );
    else return { rows: preparedData, count: data.count };
  }
  //#endregion

  //#region get report details
  private async fetchAllData(reqData) {
    // Fetch loan data
    let loanData = await this.fetchLoanDetails(reqData);
    const count = loanData?.count ?? '';
    loanData = loanData?.rows ?? [];
    if (loanData == k500Error) throw new Error();

    const loanIds = loanData.map((data) => data.id);
    const userIds = loanData.map((data) => data.userId);
    const cibilIds = loanData.map((data) => data?.cibilId);

    /// Get insurance id(s) from data, when insurance id is found
    const ids = loanData
      .filter((item) => item.insuranceId !== null)
      .map((item) => item.insuranceId);

    //get predictions details
    const predictionData = await this.commonSharedService.fetchPredictionData(
      loanIds,
    );
    if (predictionData == k500Error) throw new Error();

    // Fetch other related data
    const disbursementData = await this.getDisbursementDetails(
      loanIds,
      reqData.reportType,
    );
    if (disbursementData == k500Error) throw new Error();

    const userData = await this.fetchUserDataByIds(userIds, reqData.reportType);
    if (userData == k500Error) throw new Error();

    // Fetch employment details and related designation data
    const empDetails = await this.fetchEmploymentDetailsByUserIds(userIds);
    if (empDetails == k500Error) throw new Error();

    // Fetch banking data and EMI data
    const bankingData = await this.fetchBankingData(loanIds);
    if (bankingData == k500Error) throw new Error();

    const emiData = await this.getEmiData(loanIds, reqData.reportType);
    if (emiData == k500Error) throw new Error();

    // Fetch KYC and master data
    const kycIds = userData.map((user) => user.kycId);
    const kycData = await this.getKycData(kycIds);
    if (kycData == k500Error) throw new Error();

    const masterData = await this.getMasterData(loanIds);
    if (masterData == k500Error) throw new Error();

    const cibilData = await this.getCibilDetails(cibilIds);
    if (cibilData == k500Error) throw new Error();

    /// Get insurance data based on collected insurance id(s)
    let insuranceData = [];
    if (ids.length)
      insuranceData = await this.elephantService.funGetInsuranceData([
        ...new Set(ids),
      ]);

    const ptpData = await this.fetchPTPDetails(loanIds);
    if (ptpData == k500Error) throw new Error();

    return {
      loanData,
      cibilData,
      disbursementData,
      userData,
      empDetails,
      insuranceData,
      bankingData,
      emiData,
      kycData,
      masterData,
      ptpData,
      count,
      predictionData,
    };
  }
  //#endregion

  //#region
  private async prepareDisbursementReport(data) {
    const {
      loanData,
      userData,
      disbursementData,
      masterData,
      kycData,
      cibilData,
      empDetails,
      bankingData,
      emiData,
      insuranceData,
      ptpData,
      download,
      predictionData,
    } = data;

    const loanPurpose = await this.commonSharedService.fetchLoanPurpose();

    const designationData = await this.commonSharedService.fetchDesignation();

    const sectorList = await this.repoManager.getTableWhereData(
      employmentSector,
      ['id', 'sectorName'],
      { useMaster: false },
    );
    if (sectorList == k500Error) throw new Error();
    const sectorData = {};
    sectorList.forEach((el) => {
      if (el.id) {
        sectorData[el.id] = el.sectorName ?? '';
      }
    });

    const bankDetails = fs.readFileSync('bankDetails.json', 'utf8');
    const kTallyPayoutBanks = bankDetails ? JSON.parse(bankDetails) : {};

    const response = [];
    for (let index = 0; index < loanData.length; index++) {
      const loan = loanData[index];

      const user = userData.find((user) => user.id === loan.userId) ?? {};

      const disbursements: any = disbursementData.find(
        (disb) => disb.loanId === loan.id,
      );

      const prediction =
        predictionData.find((pre) => pre.loanId === loan.id) ?? {};

      const master =
        masterData.find((master) => master.loanId === loan.id) ?? {};

      const kyc = kycData.find((kyc) => kyc.userId === loan.userId) ?? {};

      const cibil = cibilData.find((cibil) => cibil.id == loan.cibilId) ?? {};

      const findPtp = ptpData.find((item) => item.loanId == loan.id);

      const employment =
        empDetails.find((emp) => emp.userId === loan.userId) ?? {};

      let designation = null;
      if (employment?.userId)
        designation = designationData.find(
          (desig) => desig.id === employment.designationId,
        );

      let sector = null;
      if (employment?.sectorId) {
        sector = sectorData[employment?.sectorId];
      }

      // Retrieve banking and EMI data for the loan
      const banking = bankingData.find((bank) => bank.loanId === loan.id) ?? {};

      let emi = emiData.filter((emi) => emi.loanId === loan.id);
      emi.sort((a, b) => a.id - b.id);

      const insurance =
        insuranceData.find((insurance) => insurance.loanId === loan.id) ?? {};

      // Calculate total interest and expected amounts from EMI data
      let totalInterestAmount = 0;
      let totalExpectedAmount = 0;
      let totalWaiver = 0;

      // Iterate over EMI entries to calculate required totals
      const allEmisData: any = await this.processEmiData(emi);

      // Determine if the loan is settled
      const isSettled = allEmisData?.totalWaiver > 0;
      totalInterestAmount = allEmisData?.totalInterestAmount ?? '-';
      totalExpectedAmount = allEmisData?.totalExpectedAmount ?? '-';
      const lastEmi = emi[emi.length - 1] ?? {};

      // Extract insurance details and calculate expiry date
      let policyExpiryDate = '-';

      if (insurance) {
        try {
          const insuranceData = JSON.parse(insurance.response || '{}');
          const policyEndDate =
            insuranceData?.care?.policy_end_date ??
            insuranceData?.acko?.policy_end_date;
          policyExpiryDate = policyEndDate
            ? this.typeService.dateToJsonStr(policyEndDate)
            : '-';
        } catch {}
      }

      // Determine risk category based on loan data
      let riskCategory;
      if (loan?.categoryTag || loan?.categoryTag == 0)
        riskCategory = userCategoryTag[loan?.categoryTag];
      else if (prediction?.categorizationTag)
        riskCategory = prediction?.categorizationTag?.slice(0, -5);

      const { address, state } = this.prepareAddressAndState(kyc);

      // Calculate processing fees and additional charges
      const processingFees = this.typeService.manageAmount(
        (+loan?.netApprovedAmount * loan?.processingFees) / 100,
      );

      // Determine insurance premium
      const insurancePremium = loan?.insuranceId
        ? Math.round(+loan?.insuranceDetails?.totalPremium || 0)
        : 0;

      // Determine verifiedBy field based on banking data
      const verifiedBy =
        banking?.aaDataStatus !== 3 &&
        banking?.bankStatement != null &&
        !banking?.consentMode
          ? 'BANKINGPRO'
          : banking?.consentMode;

      // Fetch quality parameters
      const qualityParameters = loan?.qualityParameters || {};
      const qualityAdminId = qualityParameters?.adminId;

      const completedLoan = loan?.completedLoan;

      const disbursementBank = this.prepareDisbursementBank(
        disbursements,
        kTallyPayoutBanks,
      );

      const purpose = loanPurpose.find((p) => p.id === loan.purposeId);

      const formattedEmiData = download
        ? allEmisData?.emiData.reduce((acc, emi, index) => {
            acc = {
              ...acc,
              [`EMI ${index + 1} Amount`]: emi.Amount ?? '-',
              [`EMI ${index + 1} Due Date`]: emi['Due date'] ?? '-',
              [`EMI ${index + 1} Expected Principal`]:
                emi['Expected Principal'] ?? '-',
              [`EMI ${index + 1} Expected Interest`]:
                emi['Expected Interest'] ?? '-',
              [`EMI ${index + 1} Expected Deferred Interest`]:
                emi['Expected Deferred Interest'] ?? '-',
              [`EMI ${index + 1} Expected ECS`]: emi['Expected ECS'] ?? '-',
              [`EMI ${index + 1} Expected Penal`]: emi['Expected Penal'] ?? '-',
              [`EMI ${index + 1} Expected Legal`]: emi['Expected Legal'] ?? '-',
              [`EMI ${index + 1} Paid Principal`]: emi['Paid Principal'] ?? '-',
              [`EMI ${index + 1} Paid Interest`]: emi['Paid Interest'] ?? '-',
              [`EMI ${index + 1} Paid Deferred Interest`]:
                emi['Paid Deferred Interest'] ?? '-',
              [`EMI ${index + 1} Paid ECS`]: emi['Paid ECS'] ?? '-',
              [`EMI ${index + 1} Paid Penal`]: emi['Paid Penal'] ?? '-',
              [`EMI ${index + 1} Paid Legal`]: emi['Paid Legal'] ?? '-',
              [`EMI ${index + 1} Delay Days as on Date`]:
                emi['Delay days : as on date'] ?? '-',
              [`EMI ${index + 1} EMI Completed Date`]:
                emi['EMI Completed Date'] ?? '-',
            };
            return acc;
          }, {})
        : allEmisData?.emiData;
      if (!download) {
        response.push({
          'General Information': {
            userId: loan.userId,
            'Loan ID': loan.id,
            'User Name': user?.fullName ?? '-',
            Email: user?.email ?? '-',
            'Mobile Number': this.cryptService.decryptPhone(user?.phone) ?? '-',
            Gender: user?.gender ?? '-',
            DOB: kyc?.aadhaarDOB
              ? this.typeService.getDateFormated(
                  await this.typeService.getDateAsPerAadhaarDOB(
                    kyc?.aadhaarDOB,
                  ),
                )
              : '-',
            'Aadhar Card Number': kyc?.maskedAadhaar ?? '-',
            'KYC Address': address,
            'Device Type':
              user?.typeOfDevice == '1'
                ? 'IOS'
                : user?.typeOfDevice == '0'
                ? 'Android'
                : 'Web',
            Platform:
              loan?.appType == 1
                ? EnvConfig.nbfc.nbfcShortName
                : EnvConfig.nbfc.appName,
            State: state,
            'User Verification Type':
              LOAN_VERIFICATION[loan?.manualVerification] ?? '-',
            'AA User': banking.consentId ? 'Yes' : 'No',
            'Lead Partner':
              user?.leadSource != null && completedLoan === 0
                ? LEAD_SOURCE[user?.leadSource]
                : '-',
          },

          'CIBIL & Risk Information': {
            'CIBIL Score': cibil?.cibilScore ?? '-',
            'CIBIL Refreshed Date': cibil?.fetchDate
              ? this.typeService.dateToJsonStr(cibil?.fetchDate)
              : '-',
            'PL Score': cibil?.plScore ?? '-',
            'PL Account': cibil?.PLAccounts ?? '-',
            'PL Outstanding Balance': cibil?.PLOutstanding ?? '-',
            'Overdue Account': cibil?.overdueAccounts ?? '-',
            'Overdue Amount': cibil?.overdueBalance ?? '-',
            'Delay Days (CIBIL)': cibil?.totalOverdueDays ?? '-',
            'Enquiry in Last 30 Days': cibil?.inquiryPast30Days ?? '-',
            'Total Outstanding Balance': cibil?.totalOutstanding ?? '-',
            'Risk Category': riskCategory ?? '-',
          },

          'Employment Information': {
            'Company Name': employment?.companyName ?? '-',
            Designation: designation?.designationName ?? '-',
            Sector: sector ?? '-',
            'Salary Date': loan?.verifiedSalaryDate ?? '-',
            'Approved Salary Amount':
              banking?.salary ??
              banking?.adminSalary ??
              banking?.otherDetails?.salary?.average ??
              '-',
          },

          'Bank & Mandate Information': {
            'Account Number': banking?.accountNumber ?? '-',
            'Bank Name': banking?.bank ?? '-',
            'IFSC Code': banking?.ifsCode ?? '-',
            'Bank Statement Verification Source': verifiedBy,
            'Bank Statement Approved By':
              (await this.commonSharedService.getAdminData(banking?.adminId))
                ?.fullName ?? '-',
            'Bank Statement Similarity Percentage':
              banking?.nameSimilarity ?? '-',
          },

          'Loan Information': {
            'Loan Purpose': purpose?.purposeName ?? '-',
            'Completed Loan': loan?.completedLoan ?? '-',
            'Loan ID Creation Date':
              this.typeService.dateToJsonStr(loan?.createdAt) ?? '-',
            'Loan Applied Date':
              this.typeService.dateToJsonStr(loan?.createdAt) ?? '-',
            'Loan Approved By':
              (
                await this.commonSharedService.getAdminData(
                  loan?.manualVerificationAcceptId,
                )
              )?.fullName ?? '-',
            'Loan Status':
              loan.loanStatus === 'Complete' && isSettled
                ? 'Settled'
                : loan.loanStatus ?? '-',
            'Loan Tenure': loan?.approvedDuration ?? '-',
            'Interest Rate (Per Day)': loan?.interestRate ?? '-',
            'Interest Rate (Per Annum)':
              (+loan?.interestRate * 365).toFixed(1) + '%' || '-',
            'Expected Loan Closure Date': lastEmi?.emi_date
              ? this.typeService.dateToJsonStr(lastEmi?.emi_date)
              : '-',
            'Loan Closure Date': loan?.loanCompletionDate
              ? this.typeService.dateToJsonStr(loan?.loanCompletionDate)
              : '-',
            'Total Approved Amount':
              +(+loan?.netApprovedAmount).toFixed() || '-',
            'Total Expected': totalExpectedAmount,
            'Total Interest': totalInterestAmount,
            'Total EMI': emi.length,
            'Loan Approved Amount':
              +(+loan?.netApprovedAmount).toFixed() || '-',
          },

          'Charges Information': {
            'Processing Charge': processingFees,
            'Document Charge': Math.round(+loan?.charges?.doc_charge_amt || 0),
            'Online Convenience Charge': Math.round(
              +loan?.charges?.insurance_fee || 0,
            ),
            'Risk Assessment Charge': Math.round(
              +loan?.charges?.risk_assessment_charge || 0,
            ),
            'Stamp Duty': Math.round(+(loan?.stampFees ?? 0)),
            'GST Amount': Math.round(+loan?.charges?.gst_amt || 0),
            CGST: Math.round(+loan?.charges?.cgst_amt || 0),
            SGST: Math.round(+loan?.charges?.sgst_amt || 0),
            IGST: Math.round(+loan?.charges?.igst_amt || 0),
          },

          'Disbursement Information': {
            'Disbursed UTR': disbursements?.utr ?? '-',
            'Disbursed Payout Bank': disbursementBank,
            'Disbursement Date':
              this.typeService.getDateFormatted(loan?.loan_disbursement_date) ??
              '-',
            'Total Disbursed Amount':
              +(disbursements?.amount / 100).toFixed(2) || '-',
          },

          'Insurance Information': {
            Insurance: insurance?.id ? 'Yes' : 'No',
            'Insurance Premium': insurancePremium,
            'Insurance End Date': policyExpiryDate,
          },

          'Quality and CRM Information': {
            'Quality Status': qualityAdminId ? 'Checked' : 'Not checked',
            'Quality Admin': !qualityAdminId
              ? '-'
              : (await this.commonSharedService.getAdminData(qualityAdminId))
                  .fullName ?? '-',
            'Last CRM by': user?.lastCrm?.adminName ?? '-',
            Title: user?.lastCrm?.statusName ?? '-',
            'CRM date': user?.lastCrm?.createdAt
              ? this.typeService.getDateFormatted(user?.lastCrm?.createdAt)
              : '-',
            Remark: user?.lastCrm?.remark ?? '-',
            Disposition: user?.lastCrm?.dispositionName ?? '-',
            'Assigned CSE':
              (await this.commonSharedService.getAdminData(master?.assignedCSE))
                ?.fullName ?? '-',
          },

          'PTP Information': {
            'Last PTP amount': findPtp?.amount
              ? this.strService.readableAmount(findPtp?.amount)
              : '-',
            'Last PTP due date': findPtp?.due_date
              ? this.dateService.dateToReadableFormat(findPtp?.due_date)
                  .readableStr
              : '-',
          },

          ...(download
            ? formattedEmiData || {}
            : { EMI: allEmisData?.emiData ?? [] }),
        });
      } else {
        response.push({
          userId: loan.userId,
          'Loan ID': loan.id,
          'User Name': user?.fullName ?? '-',
          Email: user?.email ?? '-',
          'Mobile Number': this.cryptService.decryptPhone(user?.phone) ?? '-',
          Gender: user?.gender ?? '-',
          DOB: kyc?.aadhaarDOB
            ? this.typeService.getDateFormated(
                await this.typeService.getDateAsPerAadhaarDOB(kyc?.aadhaarDOB),
              )
            : '-',
          'Aadhar Card Number': kyc?.maskedAadhaar ?? '-',
          'KYC Address': address,
          'User Verification Type (Manual / Automatic)':
            LOAN_VERIFICATION[loan?.manualVerification] ?? '-',
          'CIBIL Score': cibil?.cibilScore ?? '-',
          'CIBIL Refreshed Date': cibil?.fetchDate
            ? this.typeService.dateToJsonStr(cibil?.fetchDate)
            : '-',
          'PL Score': cibil?.plScore ?? '-',
          'PL Account': cibil?.PLAccounts ?? '-',
          'PL Outstanding Balance': cibil?.PLOutstanding ?? '-',
          'Overdue Account': cibil?.overdueAccounts ?? '-',
          'Overdue Amount': cibil?.overdueBalance ?? '-',
          'Delay Days (CIBIL)': cibil?.totalOverdueDays ?? '-',
          'Enquiry in Last 30 Days': cibil?.inquiryPast30Days ?? '-',
          'Total Outstanding Balance': cibil?.totalOutstanding ?? '-',
          'Company Name': employment?.companyName ?? '-',
          Designation: designation?.designationName ?? '-',
          'Salary Date': loan?.verifiedSalaryDate ?? '-',
          'Approved Salary Amount':
            banking?.salary ??
            banking?.adminSalary ??
            banking?.otherDetails?.salary?.average ??
            '-',
          'Account Number': banking?.accountNumber ?? '-',
          'Bank Name': banking?.bank ?? '-',
          'IFSC Code': banking?.ifsCode ?? '-',
          'Loan Purpose': purpose?.purposeName ?? '-',
          'Risk Category': riskCategory ?? '-',
          'Device Type':
            user?.typeOfDevice == '1'
              ? 'IOS'
              : user?.typeOfDevice == '0'
              ? 'Android'
              : 'Web',
          Platform:
            loan?.appType == 1
              ? EnvConfig.nbfc.nbfcShortName
              : EnvConfig.nbfc.appName,
          State: state,
          'Completed Loan': loan?.completedLoan ?? '-',
          'Loan ID Creation Date':
            this.typeService.dateToJsonStr(loan?.createdAt) ?? '-',
          'Loan Applied Date':
            this.typeService.dateToJsonStr(loan?.createdAt) ?? '-',
          'Loan Approved By':
            (
              await this.commonSharedService.getAdminData(
                loan?.manualVerificationAcceptId,
              )
            )?.fullName ?? '-',
          'Total Approved Amount': +(+loan?.netApprovedAmount).toFixed() || '-',
          'Total Disbursed Amount':
            +(disbursements?.amount / 100).toFixed(2) || '-',
          'Total Expected': totalExpectedAmount,
          'Total Interest': totalInterestAmount,
          'Total EMI': emi.length,
          'Processing Charge': processingFees,
          'Document Charge': Math.round(+loan?.charges?.doc_charge_amt || 0),
          'Online Convenience Charge': Math.round(
            +loan?.charges?.insurance_fee || 0,
          ),
          'Risk Assessment Charge': Math.round(
            +loan?.charges?.risk_assessment_charge || 0,
          ),
          'Stamp Duty': Math.round(+(loan?.stampFees ?? 0)),
          'GST Amount': Math.round(+loan?.charges?.gst_amt || 0),
          CGST: Math.round(+loan?.charges?.cgst_amt || 0),
          SGST: Math.round(+loan?.charges?.sgst_amt || 0),
          IGST: Math.round(+loan?.charges?.igst_amt || 0),
          'Loan Tenure': loan?.approvedDuration ?? '-',
          'Interest Rate (Per Day)': loan?.interestRate ?? '-',
          'Interest Rate (Per Annum)':
            (+loan?.interestRate * 365).toFixed(1) + '%' || '-',
          'Loan Approved Amount': +(+loan?.netApprovedAmount).toFixed() || '-',
          'Expected Loan Closure Date': lastEmi?.emi_date
            ? this.typeService.dateToJsonStr(lastEmi?.emi_date)
            : '-',
          'Loan Closure Date': loan?.loanCompletionDate
            ? this.typeService.dateToJsonStr(loan?.loanCompletionDate)
            : '-',
          'Disbursed UTR': disbursements?.utr ?? '-',
          'Disbursed Payout Bank': disbursementBank,
          'Disbursement Date':
            this.typeService.getDateFormatted(loan?.loan_disbursement_date) ??
            '-',
          'Loan Status':
            loan.loanStatus === 'Complete' && isSettled
              ? 'Settled'
              : loan.loanStatus ?? '-',
          'Bank Statement Verification Source': verifiedBy,
          'Bank Statement Approved By':
            (await this.commonSharedService.getAdminData(banking?.adminId))
              ?.fullName ?? '-',
          'Bank Statement Similarity Percentage':
            banking?.nameSimilarity ?? '-',
          Insurance: insurance?.id ? 'Yes' : 'No',
          'Insurance Premium': insurancePremium,
          'Insurance End Date': policyExpiryDate,
          'Quality Status': qualityAdminId ? 'Checked' : 'Not checked',
          'Quality Admin': !qualityAdminId
            ? '-'
            : (await this.commonSharedService.getAdminData(qualityAdminId))
                .fullName ?? '-',
          'Last CRM by': user?.lastCrm?.adminName ?? '-',
          Title: user?.lastCrm?.statusName ?? '-',
          'CRM date': user?.lastCrm?.createdAt
            ? this.typeService.getDateFormatted(user?.lastCrm?.createdAt)
            : '-',
          Remark: user?.lastCrm?.remark ?? '-',
          Disposition: user?.lastCrm?.dispositionName ?? '-',
          'Assigned CSE':
            (await this.commonSharedService.getAdminData(master?.assignedCSE))
              ?.fullName ?? '-',
          'AA User': banking.consentId ? 'Yes' : 'No',
          'Lead Partner':
            user?.leadSource != null && completedLoan === 0
              ? LEAD_SOURCE[user?.leadSource]
              : '-',
          'Last PTP amount': findPtp?.amount
            ? this.strService.readableAmount(findPtp?.amount)
            : '-',
          'Last PTP due date': findPtp?.due_date
            ? this.dateService.dateToReadableFormat(findPtp?.due_date)
                .readableStr
            : '-',
          ...(download
            ? formattedEmiData || {}
            : { EMI: allEmisData?.emiData ?? [] }),
          ['Registration Date']:
            this.dateService.dateToReadableFormat(user?.createdAt)
              .readableStr ?? '-',
          Sector: sector ?? '-',
        });
      }
    }
    return response;
  }
  //#endregion

  //#region  Fetch Disbursement Data by Loan IDs
  private async getDisbursementDetails(loanIds, reportType) {
    let attributes = ['utr', 'loanId', 'amount', 'source', 'requestdata'];

    if (reportType == MASTER_REPORT_TYPE.DISBURSEMENT_REPORT)
      attributes = [...attributes, 'response'];
    else if (reportType == MASTER_REPORT_TYPE.MASTER_REPORT)
      attributes = [...attributes, 'bank_name', 'account_number', 'ifsc'];

    const options = { where: { loanId: loanIds } };
    const disbursementData = await this.disburesementRepo.getTableWhereData(
      attributes,
      options,
    );
    if (disbursementData === k500Error)
      throw new Error('Error fetching disbursement data');
    return disbursementData;
  }
  //#endregion

  //#region  Fetch User Data by User IDs
  private async fetchUserDataByIds(userIds, reportType, whereOtpion = {}) {
    let attributes = [
      'createdAt',
      'id',
      'fullName',
      'email',
      'phone',
      'gender',
      'kycId',
      'typeOfDevice',
      'lastCrm',
      'appType',
      'categoryScore',
      'leadSource',
    ];
    let options = { where: { id: userIds } };
    if (reportType == MASTER_REPORT_TYPE.MASTER_REPORT)
      attributes = [...attributes, 'completedLoans', 'loanStatus', 'gmvAmount'];

    if (reportType == MASTER_REPORT_TYPE.REPAYMENT_REPORT) {
      attributes = [...attributes, 'city', 'state'];
      options.where = { ...options.where, ...whereOtpion };
    }

    return await this.userRepo.getTableWhereData(attributes, options);
  }
  //#endregion

  //#region  Fetch Employment Details by User IDs
  private async fetchEmploymentDetailsByUserIds(userIds) {
    const attributes = [
      'userId',
      'companyName',
      'salary',
      'designationId',
      'salaryDate',
      'sectorId',
    ];
    const options = { where: { userId: userIds }, order: [['id', 'desc']] };

    return await this.repoManager.getTableWhereData(
      employmentDetails,
      attributes,
      options,
    );
  }
  //#endregion

  //#region  Fetch Banking Data by Loan IDs
  private async fetchBankingData(loanIds) {
    const attributes = [
      'adminSalary',
      'otherDetails',
      'salary',
      'salaryDate',
      'adminId',
      'loanId',
      'accountNumber',
      'bank',
      'ifsCode',
      'aaDataStatus',
      'bankStatement',
      'consentMode',
      'consentId',
      'nameSimilarity',
    ];
    const options = { where: { loanId: loanIds }, order: [['id', 'desc']] };

    return await this.repoManager.getTableWhereData(
      BankingEntity,
      attributes,
      options,
    );
  }
  //#endregion

  //#region
  private async fetchPTPDetails(loanIds) {
    const options = {
      where: {
        loanId: loanIds,
        relationData: { titleId: { [Op.in]: ptpCrmIds } },
        // due_date: { [Op.gte]: this.typeService.getGlobalDate(new Date()) },
      },
      order: [['id', 'DESC']],
    };

    return await this.crmRepo.getTableWhereData(
      ['id', 'loanId', 'due_date', 'amount'],
      options,
    );
  }
  //#endregion

  //#region download report common function
  private async downloadReport(reqData, data, sheetName = 'Report.xlsx') {
    const rawExcelData = {
      sheets: ['local-reports'],
      data: [data],
      sheetName,
      needFindTuneKey: false,
      reportStore: true,
      startDate: reqData.startDate,
      endDate: reqData.endDate,
    };
    const url: any = await this.fileService.objectToExcelURL(rawExcelData);
    if (url?.message) return url;
    if (reqData?.onSlack == true) {
      reqData.sheetName = sheetName;
      const text = `*${sheetName} REPORT*`;
      const body = { reqData };
      const threads = [
        `Body details -> ${JSON.stringify(body)}`,
        `URL -> ${JSON.stringify(url)}`,
      ];
      this.slackService.sendMsg({ text, threads });
    }

    const updatedData = { downloadUrl: url, status: '1' };
    const downloadId = reqData?.downloadId;
    await this.reportHistoryRepo.updateRowData(updatedData, downloadId);
    return { fileUrl: url };
  }
  //#endregion

  //#region prepare disburesement bank gateway
  private prepareDisbursementBank(disbursements, kTallyPayoutBanks) {
    // Handle disbursement bank gateway and account details
    let disbursementBankGateway = '';
    if (disbursements?.source === kCashfree) {
      const response = disbursements?.response
        ? JSON.parse(disbursements?.response)
        : {};
      disbursementBankGateway =
        kTallyPayoutBanks[response?.paymentInstrumentId] || '';
    } else if (disbursements?.source === 'RAZORPAY_M2') {
      disbursementBankGateway = kTallyPayoutBanks['RAZORPAY_M2_DEFAULT_ACC'];
    } else if (disbursements?.source === KICICI) {
      disbursementBankGateway = kTallyPayoutBanks['ICICI_DISBURSEMENT_ACCOUNT'];
    } else {
      disbursementBankGateway = kTallyPayoutBanks['RBL - bacc_JRtlJu0ZyNn17I'];
    }

    const gatewayAcc = disbursements?.requestdata?.account_number;
    if (gatewayAcc && kTallyPayoutBanks[gatewayAcc]) {
      disbursementBankGateway = kTallyPayoutBanks[gatewayAcc];
    }
    return disbursementBankGateway;
  }
  //#endregion

  //#region
  private prepareAddressAndState(kyc) {
    const response = {
      address: null,
      state: null,
    };
    // Safely parse Aadhaar response
    let parsedAadhaarResponse: any = {};
    try {
      if (!kyc?.aadhaarState) {
        parsedAadhaarResponse = JSON.parse(kyc?.aadhaarResponse || '{}');
      }
    } catch {}

    const aadhaarResponseState = parsedAadhaarResponse?.stateName ?? '-';
    response['state'] = kyc?.aadhaarState ?? aadhaarResponseState;

    // Extract Aadhaar address
    const aadhaarAddress = kyc?.aadhaarAddress || '-';
    const aadhaarAddressResponse = kyc?.aadhaarAddressResponse || '-';
    response['address'] = this.typeService.getAadhaarAddress({
      aadhaarAddress,
      aadhaarAddressResponse,
    }).address;
    return response;
  }
  //#endregion

  //#region
  private processEmiData(emiList) {
    const emiData = [];
    let totalInterestAmount = 0;
    let totalExpectedAmount = 0;
    let totalWaiver = 0;

    for (let index = 0; index < CURRENT_EMI_COUNT; index++) {
      const emi = emiList[index] || {};
      const emiDetails = this.generateEmiDetails(emi);
      emiData.push(emiDetails?.emiEntry);

      // Update totals
      totalInterestAmount += emiDetails.interestAmount;
      totalExpectedAmount += emiDetails.expectedAmount;
      totalWaiver += emiDetails.waiverAmount;
    }

    return {
      emiData,
      totalInterestAmount,
      totalExpectedAmount,
      totalWaiver,
    };
  }
  //#endregion

  //#region
  private generateEmiDetails(emi) {
    const expectedAmount =
      +(emi?.principalCovered || 0) + +(emi?.interestCalculate || 0);

    const expectedDeferredInt =
      (emi?.regInterestAmount ?? 0) + (emi?.waived_regInterest ?? 0);
    const expectedEcs =
      emi?.penalty > 0
        ? 0
        : (emi?.bounceCharge ?? 0) +
          (emi?.gstOnBounceCharge ?? 0) +
          (emi?.waived_bounce ?? 0);
    const expectedPenalCharge =
      (emi?.dpdAmount ?? 0) +
      (emi?.penaltyChargesGST ?? 0) +
      (emi?.totalPenalty ?? 0) +
      (emi?.waived_penal ?? 0);
    const expectedLegalCharge =
      (emi?.legalCharge ?? 0) +
      (emi?.legalChargeGST ?? 0) +
      (emi?.waived_legal ?? 0);

    const paidPenalCharge =
      (emi?.paidPenalCharge ?? 0) + (emi?.paid_penalty ?? 0);

    const emiEntry = {
      Amount: expectedAmount,
      'Due date': emi?.emi_date
        ? this.typeService.dateToJsonStr(emi?.emi_date)
        : '-',
      'Expected Principal': emi?.principalCovered ?? 0,
      'Expected Interest': emi?.interestCalculate ?? 0,
      'Expected Deferred Interest': expectedDeferredInt,
      'Expected ECS': expectedEcs,
      'Expected Penal': expectedPenalCharge,
      'Expected Legal': expectedLegalCharge,
      'Paid Principal': emi?.paid_principal || 0,
      'Paid Interest': emi?.paid_interest || 0,
      'Paid Deferred Interest': emi?.paidRegInterestAmount || 0,
      'Paid ECS': emi?.paidBounceCharge || 0,
      'Paid Penal': paidPenalCharge,
      'Paid Legal': emi?.paidLegalCharge ?? 0,
      'Delay days : as on date': emi?.penalty_days ?? 0,
      'EMI Completed Date': emi?.payment_done_date
        ? this.typeService.dateToJsonStr(emi?.payment_done_date)
        : '-',
    };

    const interestAmount = emi?.interestCalculate || 0;
    const waiverAmount =
      (emi?.waiver || 0) + (emi?.paid_waiver || 0) + (emi?.unpaid_waiver || 0);

    return { emiEntry, expectedAmount, interestAmount, waiverAmount };
  }
  //#endregion

  //#region fetchDataForUserMasterReportt
  private async getDataForUserMasterReport(reqData) {
    // Fetch the loan data based on the above options
    let loanData = await this.fetchLoanDetails(reqData);
    const count = loanData?.count ?? '';
    loanData = loanData?.rows ?? [];
    if (loanData == k500Error) throw new Error();

    // Extracting IDs from the fetched loan data
    const loanIds = loanData.map((data) => data.id);
    const userIds = loanData.map((data) => data.userId);
    const cibilIds = loanData.map((data) => data?.cibilId);
    const companyIds = loanData
      .filter((data: any) => data.companyId)
      .map((data) => data.companyId);

    // Fetch user data based on user IDs
    const userData = await this.fetchUserDataByIds(userIds, reqData.reportType);
    if (userData == k500Error) throw new Error();

    // Fetch master data related to the loans
    const masterData = await this.getMasterData(loanIds);
    if (masterData == k500Error) throw new Error();

    // Extract KYC IDs and fetch KYC data
    const kycIds = userData.map((user) => user.kycId);
    const kycData = await this.getKycData(kycIds);
    if (kycData == k500Error) throw new Error();

    // Employment Data
    const empData = await this.getEmploymentData(userIds);
    if (empData == k500Error) throw new Error();

    // Disbursement Data
    const disbursementData = await this.getDisbursementDetails(
      loanIds,
      reqData.reportType,
    );
    if (disbursementData == k500Error) throw new Error();

    // Bank Data
    const bankData = await this.getBankDetails(loanIds);
    if (bankData == k500Error) throw new Error();

    // Mandate Data
    const subcriptionIds = loanData.map((data) => data.subscriptionId);
    const mandateData = await this.getMandateDetails(subcriptionIds);
    if (mandateData == k500Error) throw new Error();

    // Company Data
    const companyData =
      (companyIds ?? []).length > 0
        ? await this.getCompanyData(companyIds)
        : [];
    if (companyData == k500Error) throw new Error();

    // EMI Data
    const emiData = await this.getEmiData(loanIds, reqData.reportType);
    if (emiData == k500Error) throw new Error();

    // CIBIL Data
    const cibilData = await this.getCibilDetails(cibilIds);
    if (cibilData == k500Error) throw new Error();

    // Transactions Data
    const transactionData = await this.getTransactionData(loanIds);
    if (transactionData == k500Error) throw new Error();

    // Waiver Data
    const waiverData = await this.getWaiverData(loanIds);
    if (waiverData == k500Error) throw new Error();

    //get predictions details
    const predictionData = await this.commonSharedService.fetchPredictionData(
      loanIds,
    );
    if (predictionData == k500Error) throw new Error();

    const predictionMap = new Map(
      predictionData.map((pre) => [pre.loanId, pre]),
    );
    const loanMap = new Map(loanData.map((loan) => [loan.id, loan]));
    const userMap = new Map(userData.map((user) => [user.id, user]));
    const kycMap = new Map(kycData.map((kyc) => [kyc.userId, kyc]));
    const empMap = new Map(empData.map((user) => [user.userId, user]));
    const companyMap = new Map(
      companyData.map((comp) => [comp.companyId, comp]),
    );
    const masterMap = new Map(masterData.map((user) => [user.userId, user]));
    const disbursementMap = new Map(
      disbursementData.map((loan) => [loan.loanId, loan]),
    );
    const bankMap = new Map(bankData.map((loan) => [loan.loanId, loan]));
    const mandateMap = new Map(
      mandateData.map((mandates) => [mandates.id, mandates]),
    );
    const cibilMap = new Map(cibilData.map((cibil) => [cibil.id, cibil]));
    const emiMap = new Map();
    emiData.forEach((loan) => {
      if (!emiMap.has(loan.loanId)) {
        emiMap.set(loan.loanId, []);
      }
      emiMap.get(loan.loanId).push(loan);
    });
    const transactionMap = new Map();
    transactionData.forEach((tran) => {
      if (!transactionMap.has(tran.loanId)) {
        transactionMap.set(tran.loanId, []);
      }
      transactionMap.get(tran.loanId).push(tran);
    });
    const waiverMap = new Map();
    waiverData.forEach((waiver) => {
      if (!waiverMap.has(waiver.loanId)) {
        waiverMap.set(waiver.loanId, []);
      }
      waiverMap.get(waiver.loanId).push(waiver);
    });
    const loanPurposes = await this.commonSharedService.fetchLoanPurpose();
    const hypothecationLenders =
      await this.commonSharedService.fetchHypothecationLenders();

    return {
      loanMap,
      userMap,
      kycMap,
      empMap,
      masterMap,
      disbursementMap,
      emiMap,
      cibilMap,
      waiverMap,
      companyMap,
      mandateMap,
      bankMap,
      transactionMap,
      loanPurposes,
      hypothecationLenders,
      loanData,
      count,
      predictionMap,
    };
  }
  //#endregion

  //#region prepareUserMasterReport
  private async prepareUserMasterReport(data, isDownload = false) {
    const {
      loanMap,
      userMap,
      kycMap,
      empMap,
      masterMap,
      disbursementMap,
      emiMap,
      cibilMap,
      waiverMap,
      companyMap,
      mandateMap,
      bankMap,
      transactionMap,
      hypothecationLenders,
      loanPurposes,
      loanData,
      predictionMap,
    } = data;

    let finalData = [];

    // Helper Function to Optimize Below Code
    // const addFields = (obj, fields) => {
    //   fields.forEach(({ key, value }) => {
    //     if (isDownload) {
    //       obj[key] = value;
    //     } else {
    //       obj['General Info.'] = obj['General Info.'] || {};
    //       obj['General Info.'][key] = value;
    //     }
    //   });
    // };

    for (let index = 0; index < loanData.length; index++) {
      // Data Loan Wise
      const loan = loanData[index];
      const userData: any = userMap.get(loan.userId) ?? {};
      const kycData: any = kycMap.get(loan.userId) ?? {};
      const empData: any = empMap.get(loan.userId) ?? {};
      const masterData: any = masterMap.get(loan.userId) ?? {};
      const disbursementData: any = disbursementMap.get(loan.id) ?? {};
      const emiData: any = emiMap.get(loan.id) ?? {};
      const cibilData: any = cibilMap.get(loan.cibilId) ?? {};
      const transData: any = transactionMap.get(loan.id);
      const waiverData: any = waiverMap.get(loan.id);
      const bankData: any = bankMap.get(loan.id) ?? {};
      const companyData: any = companyMap.get(loan.companyId) ?? {};
      const mandateData: any = mandateMap.get(loan.subscriptionId) ?? {};
      const predictionData: any = predictionMap.get(loan.id) ?? {};

      // Variables
      // General
      const appPlatfrom =
        loan?.appType == 0
          ? EnvConfig?.nbfc?.appName
          : EnvConfig?.nbfc?.nbfcShortName;
      const phone = this.cryptService.decryptPhone(userData?.phone);
      const educationInfo = masterData?.otherInfo?.educationInfo
        ? masterData?.otherInfo?.educationInfo
        : '-';
      const vehicle = masterData?.otherInfo?.vehicleInfo ?? '-';
      const vehicleInfo =
        typeof vehicle == 'string'
          ? vehicle
          : vehicle.length > 0
          ? vehicle.join(', ')
          : '-';
      const deviceType =
        userData?.typeOfDevice == '0'
          ? 'Android'
          : userData?.typeOfDevice == '1'
          ? 'IOS'
          : 'Web App';
      let parsedAadhaarResponse: any = {};
      try {
        if (!kycData?.aadhaarState)
          parsedAadhaarResponse = JSON.parse(kycData?.aadhaarResponse) ?? {};
      } catch (e) {}
      let aadhaarResponseState = parsedAadhaarResponse?.stateName ?? '-';
      let state = kycData?.aadhaarState ?? aadhaarResponseState;
      const hypothicatedTo = loan?.hypothecation_lender_id
        ? hypothecationLenders.find(
            (lenders) => (lenders.id = loan?.hypothecation_lender_id),
          ).lenderName
        : '-';
      let completedLoans = userData.completedLoans;
      if (!completedLoans) completedLoans = 0;
      let loanDueStatus = userData?.loanStatus ?? '-';
      if (loanDueStatus == 1 || loanDueStatus == 0) loanDueStatus = 'On Time';
      else if (loanDueStatus == 2) loanDueStatus = 'Delayed';
      else if (loanDueStatus == 3) loanDueStatus = 'Defaulter';
      const legalStatus = legalString[loan?.legalType] ?? '-';
      let companyCltv = companyData?.totalGmv ?? '-';
      if (typeof companyCltv == 'number')
        companyCltv =
          companyCltv < 0
            ? '-₹' + this.typeService.amountNumberWithCommas(companyCltv * -1)
            : '₹' + this.typeService.amountNumberWithCommas(companyCltv);
      const loanPurpose =
        loanPurposes.find((purpose) => purpose?.id == loan?.purposeId)
          ?.purposeName ?? '-';
      const approvedSalary =
        bankData?.adminSalary ??
        bankData?.salary ??
        bankData?.otherDetails?.salary?.average ??
        '-';
      const salaryVerification =
        bankData?.salaryVerification == '3' ? 'Manual' : 'Auto';
      let finalVerification = 'Auto';
      let loanApprovedBy = 'System';
      const interestRatePerAnnum = (+loan?.interestRate * 365).toFixed(1) + '%';
      if (loan?.manualVerification == '3') {
        finalVerification = 'Manual';
        loanApprovedBy = (
          await this.commonSharedService.getAdminData(
            loan?.manualVerificationAcceptId,
          )
        ).fullName;
      }
      const processingFeePerc = loan?.processingFees;
      const netLoanAmount = +loan?.netApprovedAmount;
      const disbursedAmount = (disbursementData?.amount ?? 0) / 100;
      const processingFees = (netLoanAmount * processingFeePerc) / 100;
      const isInsuranceOpt = loan?.insuranceId ? 'Yes' : 'No';
      const loanDisbursedDate =
        this.dateService.dateToReadableFormat(loan?.loan_disbursement_date)
          .readableStr ?? '-';
      let insuranceEndDate: any = new Date(loan?.loan_disbursement_date);
      insuranceEndDate.setFullYear(insuranceEndDate.getFullYear() + 1);
      insuranceEndDate = insuranceEndDate
        ? this.dateService.dateToReadableFormat(insuranceEndDate).readableStr
        : '-';

      let riskCategoryMl;
      if (loan?.categoryTag || loan?.categoryTag == 0)
        riskCategoryMl = userCategoryTag[loan?.categoryTag];
      else if (predictionData?.categorizationTag)
        riskCategoryMl = predictionData?.categorizationTag?.slice(0, -5);

      let riskCategoryKyc = userData?.categoryScore ?? '-';
      if (userData?.categoryScore) {
        if (userData?.categoryScore < 0) riskCategoryKyc = 'High Risk';
        else if (userData.categoryScore >= 0 && userData.categoryScore <= 25)
          riskCategoryKyc = 'Moderate Risk';
        else if (userData.categoryScore > 25) riskCategoryKyc = 'Low Risk';
      }
      let qualityParameterAdminId = '';
      let qualityParameterAdminName = '-';
      if (loan?.qualityParameters?.adminId) {
        qualityParameterAdminId = loan?.qualityParameters?.adminId
          ? loan?.qualityParameters?.adminId
          : null;
        qualityParameterAdminName =
          (await this.commonSharedService.getAdminData(qualityParameterAdminId))
            .fullName ?? '-';
      }
      let crm = '';
      let crmTitle = userData?.lastCrm?.titleName ?? null;
      let crmStatus = userData?.lastCrm?.statusName ?? null;
      if (crmTitle) crm += crmTitle;
      if (crmStatus && crmTitle) crm += '-' + crmStatus;
      if (!crmTitle && !crmStatus) crm = '-';
      const loanAppliedDate =
        this.dateService.dateToReadableFormat(loan?.createdAt).readableStr ??
        '-';

      let obj = {};

      const bankDetails = fs.readFileSync('bankDetails.json', 'utf8');
      const kTallyPayoutBanks = bankDetails ? JSON.parse(bankDetails) : {};
      const disburesementBank = this.prepareDisbursementBank(
        disbursementData,
        kTallyPayoutBanks,
      );

      //#region  Master Report !Downlaod
      if (!isDownload) {
        // General Info
        obj['General Information'] = {
          'App Platform': appPlatfrom ?? '-',
          'User Id': loan?.userId ?? '-',
          'Loan Id': loan?.id ?? '-',
          Name: userData?.fullName ?? '-',
          Phone: phone ?? '-',
          'Device Type': deviceType ?? '-',
          DOB: kycData?.aadhaarDOB ?? '-',
          Email: userData?.email ?? '-',
          'PAN Number': kycData?.panCardNumber ?? '-',
          'KYC Address':
            this.typeService.getAadhaarAddress(kycData)?.address ?? '-',
          'Educational Information': educationInfo,
          'Vehicle Information': vehicleInfo ?? '-',
          Gender: userData?.gender ?? '-',
          State: state,
          'User CLTV': userData?.gmvAmount ?? '-',
        };

        // Loan Info
        obj['Loan Information'] = {
          'Loan Status': loan?.loanStatus ?? '-',
          'Loan Due Status': loanDueStatus,
          'Loan Completion Date': loan?.loanCompletionDate
            ? this.dateService.dateToReadableFormat(loan?.loanCompletionDate)
                .readableStr
            : '-',
          Hypothecated: loan?.hypothecation_assigned_date ? 'Yes' : 'No',
          'Hypothecated to': hypothicatedTo,
          'Completed Loans': completedLoans,
          'Loan Purpose': loanPurpose,
          'Legal Status': legalStatus,
        };

        // Employment Information
        obj['Employment Information'] = {
          'Employment Information':
            empData?.employementTypeData?.typeName ?? '-',
          'Company Name': empData?.companyName ?? '-',
          'Company CLTV': companyCltv,
          Designation: empData?.designation?.designationName ?? '-',
          'Salary Date': loan?.verifiedSalaryDate ?? '-',
          'Approved Salary': approvedSalary,
          'Bank Statement Verified by': bankData?.consentMode ?? 'BANKINGPRO',
          'Salary Verification': salaryVerification,
          FV: finalVerification,
        };

        // Loan Approval Details
        obj['Loan Approval Details'] = {
          'Loan Approved by': loanApprovedBy ?? '-',
          'Interest Rate(Per Day)': loan?.interestRate + '%',
          'Interest Rate(Per Annum)': interestRatePerAnnum ?? '-',
          'Loan Tenure (Days)': loan?.approvedDuration ?? '-',
          'Loan Applied Date': loanAppliedDate,
          'Approved Amount':
            loan?.loanAmount ?? loan?.approvedLoanAmount ?? '-',
          'User Selected Amount': netLoanAmount ?? '-',
          'Disbursed Amount': disbursedAmount ? disbursedAmount : '-',
          'Disbursement UTR': disbursementData?.utr ?? '-',
          'Disbursement Payout Bank': disburesementBank ?? '-',
          'Disbursement Date': loanDisbursedDate,
          'Total Interest Amount': 0,
          'Total EMI': emiData.length ?? '-',
        };

        // Charges Information:
        obj['Charges Information'] = {
          'Processing Charge': processingFees ?? '-',
          'Document Charges': loan?.charges?.doc_charge_amt ?? '-',
          'Online Convenience Charge': loan?.charges?.insurance_fee ?? '-',
          'Risk Assessment Charge':
            loan?.charges?.risk_assessment_charge ?? '-',
          Insurance: isInsuranceOpt,
          'Insurance Premium': loan?.insuranceDetails?.totalPremium ?? '-',
          'Insurance Claim Amount':
            loan?.insuranceDetails?.planASumInsured ?? '-',
          'EMI Protector Amount':
            loan?.insuranceDetails?.planBSumInsured ?? '-',
          'Insurance End Date': insuranceEndDate,
          'Stamp Duty Fees': loan?.stampFees ?? '-',
          CGST: loan?.charges?.cgst_amt ?? '-',
          SGST: loan?.charges?.sgst_amt ?? '-',
          IGST: loan?.charges?.igst_amt ?? '-',
        };

        // CIBIL Information
        obj['CIBIL & Risk Information'] = {
          'CIBIL Score': cibilData?.cibilScore ?? '-',
          'CIBIL Refreshed Date': cibilData?.fetchDate
            ? this.dateService.dateToReadableFormat(cibilData?.fetchDate)
                .readableStr
            : '-',
          'Pl Score': cibilData?.plScore ?? '-',
          'PL Account': cibilData?.PLAccounts ?? '-',
          'PL Outstanding Balance': cibilData?.PLOutstanding ?? '-',
          'Overdue Account': cibilData?.overdueAccounts ?? '-',
          'Overdue Amount': cibilData?.overdueBalance ?? '-',
          'Delay Days(CIBIL)': cibilData?.totalOverdueDays ?? '-',
          'Enquiry in Last 30 Days': cibilData?.inquiryPast30Days ?? '-',
          'Total Outstanding Balance': cibilData?.totalOutstanding ?? '-',
        };

        // Bank & Mandate Information:
        obj['Bank & Mandate Information'] = {
          'Account Number': disbursementData?.account_number ?? '-',
          'Bank Name': disbursementData?.bank_name ?? '-',
          'Ifsc Code': disbursementData?.ifsc ?? '-',
          'Mandate Type': mandateData?.mode ?? '-',
        };

        // Risk Category
        obj['Risk Category'] = {
          'Risk Category ML': riskCategoryMl ?? '-',
          'Risk Category KYC': riskCategoryKyc ?? '-',
        };

        // Quality and CRM Information
        obj['Quality and CRM Information'] = {
          'Quality Status': qualityParameterAdminId ? 'Checked' : 'Not checked',
          'Admin Name': qualityParameterAdminName,
          'Last CRM by': userData?.lastCrm?.adminName ?? '-',
          Title: crm ?? '-',
          'CRM Date': userData?.lastCrm?.createdAt
            ? this.typeService.getDateFormatted(userData?.lastCrm?.createdAt)
            : '-',
          Remark: userData?.lastCrm?.remark ?? '-',
          Disposition: userData?.lastCrm?.dispositionName ?? '-',
          'Assigned CSE':
            (
              await this.commonSharedService.getAdminData(
                masterData?.assignedCSE,
              )
            )?.fullName ?? '-',
        };
        const repaymentData = await this.preparePaymentDetails(
          emiData,
          waiverData,
          transData,
          obj,
          isDownload,
        );
        // Quality and CRM Information
        obj['Repayment Details'] = {
          'Total Interest Amount': repaymentData.totalExpectedInterest, // Updating...
          'Total Overdue Amount': repaymentData.totalOverdueAmount,
          'Total Repaid Amount': repaymentData.totalRepaidAmount,
          'Total Expected Principal': repaymentData.totalExpectedPrincipal,
          'Total Paid Principal': repaymentData.totalPaidPrincipal,
          'Total Expected Interest': repaymentData.totalExpectedInterest,
          'Total Paid Interest': repaymentData.totalPaidInterest,
          'Total Exp. Deferred Int.':
            repaymentData.totalExpectedDeferredInterest,
          'Total Paid Deferred Int.': repaymentData.totalPaidDeferredInterest,
          'Total Exp. ECS Charge': repaymentData.totalExpectedEcsCharge,
          'Total Paid ECS Charge': repaymentData.totalPaidEcsCharge,
          'Total Exp. Penal Charge': repaymentData.totalExpectedPenalCharge,
          'Total Paid Penal Charge': repaymentData.totalPaidPenalCharge,
          'Exp. Legal Charge': repaymentData.totalExpectedLegalCharge,
          'Paid Legal Charge': repaymentData.totalPaidLegalCharge,
          'Total Delay Days (as on Today )':
            repaymentData.totalDelaydaysAsOntoday,
          'Total Waive Off Amount': repaymentData.totalWaiveOffAmount,
          'Follower Name':
            (await this.commonSharedService.getAdminData(loan?.followerId))
              ?.fullName ?? '-',
        };

        // EMI Details
        obj['EMI Details'] = {
          'Total Principal': repaymentData.totalExpectedPrincipal,
          'Total Paid Amount': repaymentData.totalRepaidAmount,
          'Total EMI Amount':
            repaymentData.totalExpectedPrincipal +
            repaymentData.totalExpectedInterest,
        };

        // Foreclosure Details
        obj['Foreclosure Details'] = {
          'Foreclosure Charge': repaymentData.foreclosureCharge,
          'Foreclosure Date': repaymentData.foreclosureDate,
          'Foreclosure UTR': repaymentData.foreclosureUtr,
          'Foreclosure Payment Mode': repaymentData.foreclosurePaymentMode,
        };

        // EMI Details
        obj['EMI'] = repaymentData.emiArray;
      }
      //#endregion
      //#region  Master Report Downlaod
      else {
        // General Info.
        obj['App Platform'] = appPlatfrom ?? '-';
        obj['User Id'] = loan?.userId ?? '-';
        obj['Loan Id'] = loan?.id ?? '-';
        obj['Name'] = userData?.fullName ?? '-';
        obj['Phone'] = phone ?? '-';
        obj['Device Type'] = deviceType ?? '-';
        obj['DOB'] = kycData?.aadhaarDOB ?? '-';
        obj['Email'] = userData?.email ?? '-';
        obj['PAN Number'] = kycData?.panCardNumber ?? '-';
        obj['KYC Address'] =
          this.typeService.getAadhaarAddress(kycData)?.address ?? '-';
        obj['Educational Information'] = educationInfo;
        obj['Vehicle Information'] = vehicleInfo ?? '-';
        obj['Gender'] = userData?.gender ?? '-';
        obj['State'] = state;
        obj['User CLTV'] = userData?.gmvAmount ?? '-';

        // Loan Info.
        obj['Loan Status'] = loan?.loanStatus ?? '-';
        obj['Loan Due Status'] = loanDueStatus;
        obj['Loan Completion Date'] = loan?.loanCompletionDate
          ? this.dateService.dateToReadableFormat(loan?.loanCompletionDate)
              .readableStr
          : '-';
        obj['Hypothecated'] = loan?.hypothecation_assigned_date ? 'Yes' : 'No';
        obj['Hypothecated to'] = hypothicatedTo;
        obj['Completed Loans'] = completedLoans;
        obj['Loan Purpose'] = loanPurpose;
        obj['Legal Status'] = legalStatus;

        // Emp. Info.
        obj['Employment Information'] =
          empData?.employementTypeData?.typeName ?? '-';
        obj['Company Name'] = empData?.companyName ?? '-';
        obj['Company CLTV'] = companyCltv;
        obj['Designation'] = empData?.designation?.designationName ?? '-';
        obj['Salary Date'] = loan?.verifiedSalaryDate ?? '-';
        obj['Approved Salary'] = approvedSalary;
        obj['Bank Statement Verified by'] =
          bankData?.consentMode ?? 'BANKINGPRO';
        obj['Salary Verification'] = salaryVerification;
        obj['FV'] = finalVerification;

        // Loan Approval Details:
        obj['Loan Approved by'] = loanApprovedBy ?? '-';
        obj['Interest Rate(Per Day)'] = loan?.interestRate + '%';
        obj['Interest Rate(Per Annum)'] = interestRatePerAnnum ?? '-';
        obj['Loan Tenure (Days)'] = loan?.approvedDuration ?? '-';
        obj['Loan Applied Date'] = loanAppliedDate;
        obj['Approved Amount'] =
          loan?.loanAmount ?? loan?.approvedLoanAmount ?? '-';
        obj['User Selected Amount'] = netLoanAmount ?? '-';
        obj['Disbursed Amount'] = disbursedAmount ? disbursedAmount : '-';
        obj['Disbursement UTR'] = disbursementData?.utr ?? '-';
        obj['Disbursement Payout Bank'] = disburesementBank ?? '-';
        obj['Disbursement Date'] = loanDisbursedDate;
        obj['Total Interest Amount'] = 0;
        obj['Total EMI'] = emiData.length ?? '-';

        // Charges Information:
        obj['Processing Charge'] = processingFees ?? '-';
        obj['Document Charges'] = loan?.charges?.doc_charge_amt ?? '-';
        obj['Online Convenience Charge'] = loan?.charges?.insurance_fee ?? '-';
        obj['Risk Assessment Charge'] =
          loan?.charges?.risk_assessment_charge ?? '-';
        obj['Insurance'] = isInsuranceOpt;
        obj['Insurance Premium'] = loan?.insuranceDetails?.totalPremium ?? '-';
        obj['Insurance Claim Amount'] =
          loan?.insuranceDetails?.planASumInsured ?? '-';
        obj['EMI Protector Amount'] =
          loan?.insuranceDetails?.planBSumInsured ?? '-';
        obj['Insurance End Date'] = insuranceEndDate;
        obj['Stamp Duty Fees'] = loan?.stampFees ?? '-';
        obj['CGST'] = loan?.charges?.cgst_amt ?? '-';
        obj['SGST'] = loan?.charges?.sgst_amt ?? '-';
        obj['IGST'] = loan?.charges?.igst_amt ?? '-';

        // CIBIL & Risk Information:
        obj['CIBIL Score'] = cibilData?.cibilScore ?? '-';
        obj['CIBIL Refreshed Date'] = cibilData?.fetchDate
          ? this.dateService.dateToReadableFormat(cibilData?.fetchDate)
              .readableStr
          : '-';
        obj['Pl Score'] = cibilData?.plScore ?? '-';
        obj['PL Account'] = cibilData?.PLAccounts ?? '-';
        obj['PL Outstanding Balance'] = cibilData?.PLOutstanding ?? '-';
        obj['Overdue Account'] = cibilData?.overdueAccounts ?? '-';
        obj['Overdue Amount'] = cibilData?.overdueBalance ?? '-';
        obj['Delay Days(CIBIL)'] = cibilData?.totalOverdueDays ?? '-';
        obj['Enquiry in Last 30 Days'] = cibilData?.inquiryPast30Days ?? '-';
        obj['Total Outstanding Balance'] = cibilData?.totalOutstanding ?? '-';

        // Bank & Mandate Information:
        obj['Account Number'] = disbursementData?.account_number ?? '-';
        obj['Bank Name'] = disbursementData?.bank_name ?? '-';
        obj['Ifsc Code'] = disbursementData?.ifsc ?? '-';
        obj['Mandate Type'] = mandateData?.mode ?? '-';

        // Risk Category:
        obj['Risk Category ML'] = riskCategoryMl ?? '-';
        obj['Risk Category KYC'] = riskCategoryKyc ?? '-';

        // Quality and CRM Information:
        obj['Quality Status'] = qualityParameterAdminId
          ? 'Checked'
          : 'Not checked';
        obj['Admin Name'] = qualityParameterAdminName;
        obj['Last CRM by'] = userData?.lastCrm?.adminName ?? '-';
        obj['Title'] = crm ?? '-';
        obj['CRM Date'] = userData?.lastCrm?.createdAt
          ? this.typeService.getDateFormatted(userData?.lastCrm?.createdAt)
          : '-';
        obj['Remark'] = userData?.lastCrm?.remark ?? '-';
        obj['Disposition'] = userData?.lastCrm?.dispositionName ?? '-';
        obj['Assigned CSE'] =
          (await this.commonSharedService.getAdminData(masterData?.assignedCSE))
            ?.fullName ?? '-';
        const repaymentData = await this.preparePaymentDetails(
          emiData,
          waiverData,
          transData,
          obj,
          isDownload,
        );

        // Repayment Details
        obj['Total Interest Amount'] = repaymentData.totalExpectedInterest; // Updating...
        obj['Total Overdue Amount'] = repaymentData.totalOverdueAmount;
        obj['Total Repaid Amount'] = repaymentData.totalRepaidAmount;
        obj['Total Expected Principal'] = repaymentData.totalExpectedPrincipal;
        obj['Total Paid Principal'] = repaymentData.totalPaidPrincipal;
        obj['Total Expected Interest'] = repaymentData.totalExpectedInterest;
        obj['Total Paid Interest'] = repaymentData.totalPaidInterest;
        obj['Total Exp. Deferred Int.'] =
          repaymentData.totalExpectedDeferredInterest;
        obj['Total Paid Deferred Int.'] =
          repaymentData.totalPaidDeferredInterest;
        obj['Total Exp. ECS Charge'] = repaymentData.totalExpectedEcsCharge;
        obj['Total Paid ECS Charge'] = repaymentData.totalPaidEcsCharge;
        obj['Total Exp. Penal Charge'] = repaymentData.totalExpectedPenalCharge;
        obj['Total Paid Penal Charge'] = repaymentData.totalPaidPenalCharge;
        obj['Exp. Legal Charge'] = repaymentData.totalExpectedLegalCharge;
        obj['Paid Legal Charge'] = repaymentData.totalPaidLegalCharge;
        obj['Total Delay Days (as on Today )'] =
          repaymentData.totalDelaydaysAsOntoday;
        obj['Total Waive Off Amount'] = repaymentData.totalWaiveOffAmount;
        obj['Follower Name'] =
          (await this.commonSharedService.getAdminData(loan?.followerId))
            ?.fullName ?? '-';

        // EMI Details:
        obj['Total Principal'] = repaymentData.totalExpectedPrincipal;
        obj['Total Paid Amount'] = repaymentData.totalRepaidAmount;
        obj['Total EMI Amount'] =
          repaymentData.totalExpectedPrincipal +
          repaymentData.totalExpectedInterest;

        // Foreclosure Details:
        obj['Foreclosure Charge'] = repaymentData.foreclosureCharge;
        obj['Foreclosure Date'] = repaymentData.foreclosureDate;
        obj['Foreclosure UTR'] = repaymentData.foreclosureUtr;
        obj['Foreclosure Payment Mode'] = repaymentData.foreclosurePaymentMode;
      }
      //#endregion

      finalData.push(obj);
    }
    return finalData;
  }
  //#endregion

  //#region preparePaymentDetails
  private async preparePaymentDetails(
    emiData,
    waiverData,
    transData,
    obj,
    isDownload,
  ) {
    let noOfUpcomningEmis = 0;
    let totalOverdueAmount = 0;
    let totalRepaidAmount = 0;
    // Exp.
    let totalExpected = 0;
    let totalExpectedPrincipal = 0;
    let totalExpectedInterest = 0;
    let totalExpectedDeferredInterest = 0;
    let totalExpectedEcsCharge = 0;
    let totalExpectedPenalCharge = 0;
    let totalExpectedLegalCharge = 0;
    // Paid
    let totalPaidPrincipal = 0;
    let totalPaidInterest = 0;
    let totalPaidDeferredInterest = 0;
    let totalPaidEcsCharge = 0;
    let totalPaidPenalCharge = 0;
    let totalPaidLegalCharge = 0;

    let totalDelaydaysAsOntoday = 0;
    let foreclosureCharge = 0;
    let foreclosureDate = '';
    let foreclosureUtr = '';
    let foreclosurePaymentMode = '';
    let totalWaiveOffAmount = 0;
    let emiArray = [];
    let emiTransData = {};

    if (transData) transData?.sort((a, b) => a.id - b.id);
    if (waiverData) waiverData?.sort((a, b) => b.id - a.id);
    // Loop Over Transactions
    transData?.forEach((trans) => {
      if (trans?.type != kRefund) {
        totalRepaidAmount += trans?.paidAmount;
        foreclosureCharge +=
          (trans?.forClosureAmount ?? 0) +
          (trans?.sgstForClosureCharge ?? 0) +
          (trans?.cgstForClosureCharge ?? 0) +
          (trans?.igstForClosureCharge ?? 0);
        if (trans?.type == kFullPay && trans?.forClosureAmount) {
          foreclosureDate = this.dateService.dateToReadableFormat(
            trans?.completionDate,
          ).readableStr;
          foreclosureUtr = trans?.utr;
          foreclosurePaymentMode = trans?.source;
        }
      }
      if (trans?.type == kPartPay) {
        emiTransData[trans?.emiId + 'partPaidDate'] = trans?.completionDate;
        if (emiTransData[trans?.emiId + 'partPaidAmt'])
          emiTransData[trans?.emiId + 'partPaidAmt'] += trans?.paidAmount;
        else emiTransData[trans?.emiId + 'partPaidAmt'] = trans?.paidAmount;
      }
      if (trans?.type == kEMIPay)
        emiTransData[trans?.emiId + 'emiPaidDate'] = trans?.completionDate;
      if (trans?.type == kFullPay) {
        emiTransData['fullPaidDate'] = trans?.completionDate;
        emiTransData['fullPayMode'] = trans?.source;
        emiTransData['fullPayTransId'] = trans?.utr;
      }
      emiTransData[trans?.emiId + 'utr'] = trans?.utr;
      emiTransData[trans?.emiId + 'payMode'] = trans?.source;
    });

    // Data Preparation from EMI Data
    for (let index = 0; index < EnvConfig.loan.maxEMIs; index++) {
      const ele = emiData[index];
      let emiObj = {};
      const prefix = isDownload ? `EMI ${index + 1} ` : '';
      if (index >= emiData.length && isDownload) {
        obj[`${prefix}Amount`] = '-';
        obj[`${prefix}Repaid Amount`] = '-';
        obj[`${prefix}PartPay Amount`] =
          emiTransData[ele?.id + 'partPaidAmt'] ?? '-';
        obj[`${prefix}Part Pay Date`] = '-';
        obj[`${prefix}Status`] = '-';
        obj[`${prefix}Due Date`] = '-';
        obj[`${prefix}Payment Mode`] = '-';
        obj[`${prefix}Repayment UTR`] = '-';
        obj[`${prefix}Expected Principal`] = '-';
        obj[`${prefix}Expected Interest`] = '-';
        obj[`${prefix}Expected Deferred Interest`] = '-';
        obj[`${prefix}Expected ECS`] = '-';
        obj[`${prefix}Expected Penal`] = '-';
        obj[`${prefix}Expected Legal`] = '-';
        obj[`${prefix}Paid Principal`] = '-';
        obj[`${prefix}Paid Interest`] = '-';
        obj[`${prefix}Paid Deferred Interest`] = '-';
        obj[`${prefix}Paid ECS`] = '-';
        obj[`${prefix}Paid Penal`] = '-';
        obj[`${prefix}Paid Legal`] = '-';
        obj[`${prefix}Demand Letter`] = '-';
        obj[`${prefix}Waive-Off Amount`] = '-';
        obj[`${prefix}Waive-Off Given By`] = '-';
        obj[`${prefix}Delay Days`] = '-';
        obj[`${prefix}Completed Date`] = '-';
        continue;
      } else if (index >= emiData.length && !isDownload) {
        emiObj[`${prefix}Amount`] = '-';
        emiObj[`${prefix}Repaid Amount`] = '-';
        emiObj[`${prefix}PartPay Amount`] =
          emiTransData[ele?.id + 'partPaidAmt'] ?? '-';
        emiObj[`${prefix}Part Pay Date`] = '-';
        emiObj[`${prefix}Status`] = '-';
        emiObj[`${prefix}Due Date`] = '-';
        emiObj[`${prefix}Payment Mode`] = '-';
        emiObj[`${prefix}Repayment UTR`] = '-';
        emiObj[`${prefix}Expected Principal`] = '-';
        emiObj[`${prefix}Expected Interest`] = '-';
        emiObj[`${prefix}Expected Deferred Interest`] = '-';
        emiObj[`${prefix}Expected ECS`] = '-';
        emiObj[`${prefix}Expected Penal`] = '-';
        emiObj[`${prefix}Expected Legal`] = '-';
        emiObj[`${prefix}Paid Principal`] = '-';
        emiObj[`${prefix}Paid Interest`] = '-';
        emiObj[`${prefix}Paid Deferred Interest`] = '-';
        emiObj[`${prefix}Paid ECS`] = '-';
        emiObj[`${prefix}Paid Penal`] = '-';
        emiObj[`${prefix}Paid Legal`] = '-';
        emiObj[`${prefix}Demand Letter`] = '-';
        emiObj[`${prefix}Waive-Off Amount`] = '-';
        emiObj[`${prefix}Waive-Off Given By`] = '-';
        emiObj[`${prefix}Delay Days`] = '-';
        emiObj[`${prefix}Completed Date`] = '-';
        emiArray.push(emiObj);
        continue;
      }
      const todayDate = this.typeService.getGlobalDate(new Date()).toJSON();
      let emiDate = this.typeService
        .getGlobalDate(new Date(ele?.emi_date))
        .toJSON();

      // Expected Variables
      const expectedPrincipal =
        (ele?.principalCovered ?? 0) + (ele?.waived_principal ?? 0);
      const expectedInterest =
        (ele?.interestCalculate ?? 0) + (ele?.waived_interest ?? 0);
      const expectedDeferredInt =
        (ele?.regInterestAmount ?? 0) + (ele?.waived_regInterest ?? 0);
      const expectedEcs =
        (ele?.totalPenalty ?? 0) > 0
          ? 0
          : (ele?.bounceCharge ?? 0) +
            (ele?.gstOnBounceCharge ?? 0) +
            (ele?.waived_bounce ?? 0);
      const expectedPenalCharge =
        (ele?.dpdAmount ?? 0) +
        (ele?.penaltyChargesGST ?? 0) +
        (ele?.totalPenalty ?? 0) +
        (ele?.waived_penal ?? 0);
      const expectedLegalCharge =
        (ele?.legalCharge ?? 0) +
        (ele?.legalChargeGST ?? 0) +
        (ele?.waived_legal ?? 0);
      const totalExpectedForThisEMI =
        expectedPrincipal +
        expectedInterest +
        expectedDeferredInt +
        expectedEcs +
        expectedPenalCharge +
        expectedLegalCharge;

      // Paid Variables
      const paidPrincipal = ele?.paid_principal ?? 0;
      const paidInterest = ele?.paid_interest ?? 0;
      const paidDeferredInt = ele?.paidRegInterestAmount ?? 0;
      const paidEcs = ele?.paidBounceCharge ?? 0;
      const paidPenal = (ele?.paidPenalCharge ?? 0) + (ele?.paid_penalty ?? 0);
      const paidLegal = ele?.paidLegalCharge ?? 0;
      const totalPaidForThisEMI =
        paidPrincipal +
        paidInterest +
        paidDeferredInt +
        paidEcs +
        paidPenal +
        paidLegal;

      // Waiver Data
      const waivedAmount =
        (ele?.waiver ?? 0) +
        (ele?.paid_waiver ?? 0) +
        (ele?.unpaid_waiver ?? 0);
      let lastWaiverGivenAdminId =
        waivedAmount > 0
          ? waiverData?.find((waiver) => ele?.id == waiver.emiId)?.followerId ??
            null
          : null;
      let lastWaiverGivenBy = lastWaiverGivenAdminId
        ? (await this.commonSharedService.getAdminData(lastWaiverGivenAdminId))
            .fullName
        : '-';
      if (!lastWaiverGivenAdminId && waivedAmount > 0) {
        lastWaiverGivenAdminId =
          waiverData?.find((waiver) => waiver.emiId == null)?.followerId ??
          null;
        lastWaiverGivenBy = lastWaiverGivenAdminId
          ? (
              await this.commonSharedService.getAdminData(
                lastWaiverGivenAdminId,
              )
            ).fullName
          : '-';
      }

      // EMI Payment Status
      let emiPaymentStatus =
        ele?.payment_status == '1' && ele?.payment_due_status == '1'
          ? 'Paid: Postpaid'
          : ele?.payment_status == '1' && ele?.payment_due_status == '0'
          ? 'Paid: Prepaid'
          : ele?.payment_status == '0' && ele?.payment_due_status == '1'
          ? 'Unpaid: Delayed'
          : 'Unpaid';
      if (emiTransData[ele?.id + 'emiPaidDate']) {
        if (emiDate == emiTransData[ele?.id + 'emiPaidDate'])
          emiPaymentStatus = 'Paid: On Due Date';
      } else if (emiTransData['fullPaidDate']) {
        if (emiDate == emiTransData['fullPaidDate'])
          emiPaymentStatus = 'Paid: On Due Date';
      } else if (emiTransData[ele?.id + 'partPaidDate']) {
        if (ele?.payment_status == '0') emiPaymentStatus = 'Unpaid: PartPay';
      }
      const emiDelayDays = ele?.penalty_days ?? 0;
      if (ele?.payment_due_status != '1' && emiDate > todayDate)
        noOfUpcomningEmis++;
      emiDate =
        this.dateService.dateToReadableFormat(ele?.emi_date).readableStr ?? '-';
      const partPayDate = emiTransData[ele?.id + 'partPaidDate']
        ? this.dateService.dateToReadableFormat(
            emiTransData[ele?.id + 'partPaidDate'],
          ).readableStr
        : '-';
      const emiDoneDate = ele?.payment_done_date
        ? this.dateService.dateToReadableFormat(ele?.payment_done_date)
            .readableStr
        : '-';
      let paymentTransId = emiTransData[ele?.id + 'utr'];
      if (ele?.pay_type == kFullPay && !paymentTransId)
        paymentTransId = emiTransData['fullPayTransId'];

      // EMI wise Columns
      if (!isDownload) {
        emiObj['Amount'] = expectedPrincipal + expectedInterest;
        emiObj['Repaid Amount'] = totalPaidForThisEMI;
        emiObj['PartPay Amount'] = emiTransData[ele?.id + 'partPaidAmt'] ?? '-';
        emiObj['Part Pay Date'] = partPayDate;
        emiObj['Status'] = emiPaymentStatus;
        emiObj['Due Date'] = emiDate;
        emiObj['Payment Mode'] =
          emiTransData[ele?.id + 'payMode'] ??
          emiTransData['fullPayMode'] ??
          '-';
        emiObj['Repayment UTR'] = paymentTransId ?? '-';
        emiObj['Expected Principal'] = expectedPrincipal;
        emiObj['Expected Interest'] = expectedInterest;
        emiObj['Expected Deferred Interest'] = expectedDeferredInt;
        emiObj['Expected ECS'] = expectedEcs;
        emiObj['Expected Penal'] = expectedPenalCharge;
        emiObj['Expected Legal'] = expectedLegalCharge;
        emiObj['Paid Principal'] = paidPrincipal;
        emiObj['Paid Interest'] = paidInterest;
        emiObj['Paid Deferred Interest'] = paidDeferredInt;
        emiObj['Paid ECS'] = paidEcs;
        emiObj['Paid Penal'] = paidPenal;
        emiObj['Paid Legal'] = paidLegal;
        emiObj['Demand Letter'] = ele?.legalType == '1' ? 'Sent' : '-';
        emiObj['Waive-Off Amount'] = waivedAmount;
        emiObj['Waive-Off Given By'] = lastWaiverGivenBy;
        emiObj['Delay Days'] = emiDelayDays;
        emiObj['Completed Date'] = emiDoneDate;
      } else {
        obj[`${prefix}Amount`] = expectedPrincipal + expectedInterest;
        obj[`${prefix}Repaid Amount`] = totalPaidForThisEMI;
        obj[`${prefix}PartPay Amount`] =
          emiTransData[ele?.id + 'partPaidAmt'] ?? '-';
        obj[`${prefix}Part Pay Date`] = partPayDate;
        obj[`${prefix}Status`] = emiPaymentStatus;
        obj[`${prefix}Due Date`] = emiDate;
        obj[`${prefix}Payment Mode`] =
          emiTransData[ele?.id + 'payMode'] ??
          emiTransData['fullPayMode'] ??
          '-';
        obj[`${prefix}Repayment UTR`] = paymentTransId ?? '-';
        obj[`${prefix}Expected Principal`] = expectedPrincipal;
        obj[`${prefix}Expected Interest`] = expectedInterest;
        obj[`${prefix}Expected Deferred Interest`] = expectedDeferredInt;
        obj[`${prefix}Expected ECS`] = expectedEcs;
        obj[`${prefix}Expected Penal`] = expectedPenalCharge;
        obj[`${prefix}Expected Legal`] = expectedLegalCharge;
        obj[`${prefix}Paid Principal`] = paidPrincipal;
        obj[`${prefix}Paid Interest`] = paidInterest;
        obj[`${prefix}Paid Deferred Interest`] = paidDeferredInt;
        obj[`${prefix}Paid ECS`] = paidEcs;
        obj[`${prefix}Paid Penal`] = paidPenal;
        obj[`${prefix}Paid Legal`] = paidLegal;
        obj[`${prefix}Demand Letter`] = ele?.legalType == '1' ? 'Sent' : '-';
        obj[`${prefix}Waive-Off Amount`] = waivedAmount;
        obj[`${prefix}Waive-Off Given By`] = lastWaiverGivenBy;
        obj[`${prefix}Delay Days`] = emiDelayDays;
        obj[`${prefix}Completed Date`] = emiDoneDate;
      }
      emiArray.push(emiObj);

      // Loan wise Calculations
      totalExpected += totalExpectedForThisEMI;
      totalExpectedPrincipal += expectedPrincipal;
      totalExpectedInterest += expectedInterest;
      totalExpectedDeferredInterest += expectedDeferredInt;
      totalExpectedEcsCharge += expectedEcs;
      totalExpectedPenalCharge += expectedPenalCharge;
      totalExpectedLegalCharge += expectedLegalCharge;
      totalWaiveOffAmount += waivedAmount;

      totalPaidPrincipal += paidPrincipal;
      totalPaidInterest += paidInterest;
      totalPaidDeferredInterest += paidDeferredInt;
      totalPaidEcsCharge += paidEcs;
      totalPaidPenalCharge += paidPenal;
      totalPaidLegalCharge += paidLegal;

      // Cal. for Delay Users
      if (ele?.payment_due_status == '1' && ele?.payment_status == '0') {
        if ((ele?.penalty_days ?? 0) > totalDelaydaysAsOntoday)
          totalDelaydaysAsOntoday = ele?.penalty_days ?? 0;
        totalOverdueAmount += totalExpectedForThisEMI - totalPaidForThisEMI;
      }
    }
    return {
      emiArray,
      noOfUpcomningEmis,
      totalOverdueAmount,
      totalRepaidAmount,
      totalExpected,
      totalExpectedPrincipal,
      totalExpectedInterest,
      totalExpectedDeferredInterest,
      totalExpectedEcsCharge,
      totalExpectedPenalCharge,
      totalExpectedLegalCharge,
      totalPaidPrincipal,
      totalPaidInterest,
      totalPaidDeferredInterest,
      totalPaidEcsCharge,
      totalPaidPenalCharge,
      totalPaidLegalCharge,
      totalDelaydaysAsOntoday,
      foreclosureCharge,
      foreclosureDate,
      foreclosureUtr,
      foreclosurePaymentMode,
      totalWaiveOffAmount,
    };
  }
  //#endregion

  //#region getLoanDataForMasterReport
  private async fetchLoanDetails(reqData) {
    const options: any = {
      where: {},
      order: [['id', 'DESC']],
    };
    let attributes = [
      'id',
      'charges',
      'cibilId',
      'loan_disbursement_date',
      'userId',
      'netApprovedAmount',
      'interestRate',
      'loanStatus',
      'stampFees',
      'processingFees',
      'insuranceDetails',
      'approvedLoanAmount',
      'categoryTag',
      'createdAt',
      'approvedDuration',
      'loanCompletionDate',
      'manualVerification',
      'insuranceId',
      'qualityParameters',
      'purposeId',
      'appType',
      'manualVerificationAcceptId',
      'verifiedSalaryDate',
      'completedLoan',
    ];

    if (reqData?.reportType == MASTER_REPORT_TYPE.DISBURSEMENT_REPORT) {
      attributes = [
        ...attributes,
        'TotalRepayAmount',
        'repaidAmount',
        'loanFees',
      ];
      options.where.loan_disbursement_date = {
        [Op.gte]: reqData?.startDate.toJSON(),
        [Op.lte]: reqData?.endDate.toJSON(),
      };
      // Purpose -> Debugging, On demand report
      if (reqData.loanIds?.length > 0) {
        options.where = { id: reqData.loanIds };
      }
    }
    if (reqData?.reportType == MASTER_REPORT_TYPE.MASTER_REPORT) {
      attributes = [
        ...attributes,
        'loanAmount',
        'followerId',
        'companyId',
        'subscriptionId',
        'legalType',
      ];

      options.where.loanStatus = ['Active', 'Complete'];
      options.where.loan_disbursement_date = {
        [Op.gte]: reqData?.startDate,
        [Op.lte]: reqData?.endDate,
      };
    }

    if (reqData?.reportType == MASTER_REPORT_TYPE.REPAYMENT_REPORT) {
      attributes = [...attributes, 'completedLoan', 'followerId'];
      options.where.id = reqData?.loanIds;
    }

    const page = +(reqData?.page ?? 1);
    const searchText = reqData?.searchText;
    if (searchText) {
      const firstTwoLetters = searchText.substring(0, 2).toLowerCase();
      const restOfString = searchText.substring(2);
      if (firstTwoLetters == 'l-') options.where.id = +restOfString;
    }
    if (!reqData?.download) {
      options.offset = page * PAGE_LIMIT - PAGE_LIMIT;
      options.limit = PAGE_LIMIT;
    }

    return await this.loanRepo.getTableWhereDataWithCounts(attributes, options);
  }
  //#endregion

  //#region fetchTransactionData
  private async getTransactionData(loanIds) {
    return await this.transRepo.getTableWhereData(
      [
        'id',
        'emiId',
        'loanId',
        'paidAmount',
        'completionDate',
        'source',
        'type',
        'utr',
        'forClosureAmount',
        'sgstForClosureCharge',
        'cgstForClosureCharge',
        'igstForClosureCharge',
      ],
      {
        where: {
          loanId: loanIds,
          status: kCompleted,
        },
        order: [['id', 'desc']],
      },
    );
  }
  //#endregion

  //#region fetchWaiverData
  private async getWaiverData(loanIds) {
    return await this.repoManager.getTableWhereData(
      WaiverEntity,
      ['loanId', 'emiId', 'adminId', 'id', 'followerId'],
      {
        where: {
          loanId: loanIds,
          type: {
            [Op.or]: [
              {
                [Op.ne]: 'WAIVER_REVERSED',
              },
              { [Op.eq]: null },
            ],
          },
        },
        order: [['id', 'desc']],
      },
    );
  }
  //#endregion

  //#region fetchKycData
  private async getKycData(kycIds) {
    return await this.repoManager.getTableWhereData(
      KYCEntity,
      [
        'maskedAadhaar',
        'aadhaarAddressResponse',
        'aadhaarAddress',
        'userId',
        'aadhaarState',
        'aadhaarDOB',
        'panCardNumber',
        'aadhaarNumber',
      ],
      { where: { id: kycIds }, order: [['id', 'desc']] },
    );
  }
  //#endregion

  //#region fetchMasterData
  private async getMasterData(loanIds) {
    return await this.masterRepo.getTableWhereData(
      ['loanId', 'assignedCSE', 'otherInfo', 'miscData', 'userId'],
      {
        where: { loanId: loanIds },
        order: [['id', 'desc']],
      },
    );
  }
  //#endregion

  //#region fetchEmploymentData
  private async getEmploymentData(userIds) {
    return await this.employmentRepo.getTableWhereData(
      ['id', 'companyAddress', 'companyName', 'userId'],
      {
        where: { userId: userIds },
        include: [
          { model: employmentSector, attributes: ['sectorName'] },
          { model: employmentDesignation, attributes: ['designationName'] },
          { model: employmentType, attributes: ['typeName'] },
        ],
        order: [['id', 'desc']],
      },
    );
  }
  //#endregion

  //#region fetchBankDetails
  private async getBankDetails(loanIds) {
    return await this.bankingRepo.getTableWhereData(
      [
        'adminSalary',
        'salary',
        'otherDetails',
        'loanId',
        'consentMode',
        'salaryVerification',
      ],
      {
        where: { loanId: loanIds },
      },
    );
  }
  //#endregion

  //#region fetchMandateDetails
  private async getMandateDetails(subcriptionIds) {
    return await this.subscriptionRepo.getTableWhereData(['mode', 'id'], {
      where: { id: subcriptionIds },
    });
  }
  //#endregion

  //#region fetchCompanyData
  private async getCompanyData(companyIds) {
    const rawQuery = `SELECT 
    "companyId",
    SUM("loanGmv") AS "totalGmv"
    FROM "loanTransactions"
    WHERE "loanStatus" IN ('Active', 'Complete')
    AND "companyId" IN (${companyIds})
    GROUP BY "companyId"`;
    return await this.repoManager.injectRawQuery(loanTransaction, rawQuery, {
      source: 'REPLICA',
    });
  }
  //#endregion

  //#region  Fetch EMI Data by Loan IDs
  private async getEmiData(loanIds, reportType) {
    let attributes = [
      'id',
      'principalCovered',
      'interestCalculate',
      'waiver',
      'paid_waiver',
      'unpaid_waiver',
      'payment_status',
      'payment_due_status',
      'payment_done_date',
      'penalty_days',
      'emi_date',
      'loanId',
      'bounceCharge',
      'penalty',
      'legalCharge',
      'legalChargeGST',
      'regInterestAmount',
      'dpdAmount',
      'penaltyChargesGST',
      'gstOnBounceCharge',
      'waived_regInterest',
      'waived_bounce',
      'waived_penal',
      'waived_legal',
      'paid_principal',
      'paid_interest',
      'paidBounceCharge',
      'paidPenalCharge',
      'paidLegalCharge',
      'paidRegInterestAmount',
      'legalType',
      'pay_type',
    ];

    if (reportType == MASTER_REPORT_TYPE.DISBURSEMENT_REPORT) {
      attributes = [
        ...attributes,
        'emi_amount',
        'payment_due_status',
        'totalPenalty',
        'partPaymentPenaltyAmount',
        'fullPayPrincipal',
        'fullPayPenalty',
        'fullPayInterest',
        'fullPayLegalCharge',
        'fullPayRegInterest',
        'fullPayBounce',
        'paid_penalty',
      ];
    }

    if (reportType == MASTER_REPORT_TYPE.REPAYMENT_REPORT) {
      attributes = [...attributes, 'emi_amount'];
    }
    const options = { where: { loanId: loanIds }, order: [['id', 'asc']] };
    return await this.emiRepo.getTableWhereData(attributes, options);
  }
  //#region

  //#region getCibilDetails
  private async getCibilDetails(cibilIds) {
    const attributes = [
      'id',
      'cibilScore',
      'loanId',
      'overdueAccounts',
      'overdueBalance',
      'inquiryPast30Days',
      'plScore',
      'PLAccounts',
      'PLOutstanding',
      'fetchDate',
      'totalOutstanding',
      'totalOverdueDays',
      'userId',
    ];
    const options = {
      where: { id: cibilIds, status: '1' },
      order: [['id', 'desc']],
    };
    return await this.cibilScoreRepo.getTableWhereData(attributes, options);
  }
  //#endregion

  //#region download report common function
  private async downlaodReport(reqData, data, sheetName = 'Report.xlsx') {
    const rawExcelData = {
      sheets: ['local-reports'],
      data: [data],
      sheetName,
      needFindTuneKey: false,
      reportStore: true,
      startDate: reqData.startDate,
      endDate: reqData.endDate,
    };
    const url: any = await this.fileService.objectToExcelURL(rawExcelData);
    if (url?.message) return url;

    const updatedData = { downloadUrl: url, status: '1' };
    const downloadId = reqData?.downloadId;
    await this.reportHistoryRepo.updateRowData(updatedData, downloadId);
    return { fileUrl: url };
  }
  //#endregion

  //#region
  private async processExcelData(data, searchText, localPath) {
    const smaData = await this.fileService.downloadAndReadExcel(
      data,
      localPath,
    );
    let excelData: any = await this.fileService.excelToArray(
      smaData,
      {},
      false,
      true,
    );
    if (excelData?.message) return excelData;

    if (searchText) {
      const searchData: any = this.common.getSearchData(searchText);
      if (searchData?.message) return searchData;

      if (searchData.text !== '' && searchData.type === 'Name') {
        excelData = excelData.filter((item) =>
          item['Borrower Name']
            ?.toLowerCase()
            .includes(searchData.text.toLowerCase()),
        );
      } else if (searchData.text !== '' && searchData.type === 'LoanId') {
        excelData = excelData.filter((item) =>
          item['Loan ID'].toString().includes(searchData.text),
        );
      }
    }
    if (fs.existsSync(smaData)) {
      fs.unlinkSync(smaData);
    }
    return excelData;
  }

  //#region get repaid data
  async getAllRepaidLoans(query) {
    const isDownload = query?.download == 'true';
    query.startDate = this.typeService.getGlobalDate(query.startDate);
    query.endDate = this.typeService.getGlobalDate(query.endDate);
    const result: any = await this.getRepaidReportData(query);

    if (result?.message) return result;
    const rows: any = await this.prePareAllRepaidLoans(result.rows, isDownload);
    if (rows?.message) return result;
    const total: any = await this.getAllRepaidAmount(query);
    if (total?.message) return result;
    if (isDownload && query?.allData == 'true')
      return { count: result.count, rows, total };
    // Download -> Report

    if (isDownload)
      return await this.downlaodReport(query, rows, 'RepaymentReport.xlsx');
    else return { count: result.count, rows, total };
  }
  //#endregion

  //#region find repaid loan data
  private async findRepaidLoanData(options) {
    const att = [
      'id',
      'emiId',
      'userId',
      'loanId',
      'updatedAt',
      'completionDate',
      'penaltyAmount',
      'principalAmount',
      'interestAmount',
      'utr',
      'paidAmount',
      'source',
      'type',
      'subSource',
      'penaltyAmount',
      'createdAt',

      'forClosureAmount',
      'sgstForClosureCharge',
      'cgstForClosureCharge',
      'igstForClosureCharge',

      'penalCharge',
      'sgstOnPenalCharge',
      'cgstOnPenalCharge',
      'igstOnPenalCharge',

      'bounceCharge',
      'sgstOnBounceCharge',
      'cgstOnBounceCharge',
      'igstOnBounceCharge',

      'legalCharge',
      'sgstOnLegalCharge',
      'cgstOnLegalCharge',
      'igstOnLegalCharge',

      'regInterestAmount',
      'response',
      'transactionId',
      'followerId',
    ];
    const result = await this.repository.getTableWhereDataWithCounts(
      att,
      options,
    );
    if (result === k500Error) throw new Error();
    return result;
  }
  //#endregion

  //#region prepare all repaod loan
  private async prePareAllRepaidLoans(tranList, isDownload = false) {
    const loanIds = [...new Set(tranList.map((el) => el?.loanId))];
    const attr = [
      'loanId',
      [Sequelize.fn('SUM', Sequelize.col('paidAmount')), 'totalPaid'],
    ];
    const opts = {
      where: { loanId: loanIds, status: kCompleted },
      group: ['loanId'],
    };

    const attributes = ['emiId', 'type', 'paidAmount'];
    const options = {
      where: { loanId: loanIds, status: kCompleted, type: 'REFUND' },
    };
    const allTransData = await this.repository.getTableWhereData(
      attributes,
      options,
    );

    const refunObject = {};
    allTransData.forEach((trans) => {
      if (trans.type === 'REFUND') {
        if (!refunObject[trans.emiId]) {
          refunObject[trans.emiId] = {
            amount: trans.paidAmount,
          };
        }
      }
    });

    const totalPaid = await this.repository.getTableWhereData(attr, opts);
    if (totalPaid === k500Error) throw new Error();

    const finalList = [];
    for (let index = 0; index < tranList.length; index++) {
      const ele = tranList[index];
      const loan = ele.loanData;
      const loanStatus = loan?.loanStatus;
      const loanId = ele.loanId;
      const userData = ele?.userData;
      const crmData = userData?.lastCrm ?? {};
      let crmDate = '-';
      if (crmData?.createdAt) {
        const dateInfo = this.dateService.dateToReadableFormat(
          crmData?.createdAt,
        );
        crmDate = dateInfo.readableStr;
      }

      const createdAt = this.typeService.getDateFormatted(ele?.createdAt);
      const disbursAmount = (loan?.disbursementData[0]?.amount ?? 0) / 100;
      const disDate = this.typeService.getDateFormatted(
        loan?.loan_disbursement_date,
      );
      const emp = userData?.employmentData;
      const mobileNumber = this.cryptService.decryptPhone(userData?.phone);
      const tPaid = totalPaid.find((f) => f.loanId == loanId)?.totalPaid;
      const foreclosure = ele?.forClosureAmount ?? 0;
      let emiData = loan?.emiData ?? [];
      const completionDate = new Date(ele?.completionDate ?? ele?.updatedAt);
      const repaidDate = this.typeService.getDateFormatted(completionDate);
      const data = this.funRepaidDealyDayFlag(ele, emiData);

      let riskCategoryMl;
      if (loan?.categoryTag || loan?.categoryTag == 0)
        riskCategoryMl = userCategoryTag[loan?.categoryTag];
      else if (loan?.predictionData?.categorizationTag)
        riskCategoryMl = loan?.predictionData?.categorizationTag?.slice(0, -5);

      let riskCategoryKyc = userData?.categoryScore ?? '-';
      if (userData?.categoryScore) {
        if (userData?.categoryScore < 0) riskCategoryKyc = 'High Risk';
        else if (userData.categoryScore >= 0 && userData.categoryScore <= 25)
          riskCategoryKyc = 'Moderate Risk';
        else if (userData.categoryScore > 25) riskCategoryKyc = 'Low Risk';
      }

      const transDate = this.typeService
        .getGlobalDate(completionDate)
        .getTime();
      let interestRate = +loan?.interestRate;
      let prepaidPrincipal = 0;
      let prepaidInterest = 0;
      let emi;
      let nextEmiDate;

      // Check Next EMI Date
      emiData.forEach((e) => {
        if (
          !nextEmiDate &&
          new Date(e.emi_date).getTime() > new Date().getTime()
        ) {
          nextEmiDate = this.typeService.getDateFormatted(e.emi_date);
        }
      });

      // Full Pay
      if (ele?.type == 'FULLPAY') {
        let upcomingPrincipal = 0;
        emiData.forEach((e) => {
          if (new Date(e.emi_date).getTime() > transDate) {
            prepaidPrincipal += e.principalCovered ?? 0;
            upcomingPrincipal += e.principalCovered ?? 0;
          }
        });
        let pastEmis =
          emiData.filter((e) => new Date(e.emi_date).getTime() <= transDate) ??
          [];
        pastEmis = pastEmis.sort((a, b) => a.id - b.id);
        let pastEmiDate = pastEmis[pastEmis.length - 1]?.emi_date;
        emiData = emiData.sort((a, b) => a.id - b.id);
        // Calculating Int. from Loan Disbursment Date
        if (
          pastEmis.length == 0 &&
          new Date(emiData[0].emi_date) > new Date(ele?.completionDate)
        )
          pastEmiDate = new Date(loan?.loan_disbursement_date);
        let daysdiff = this.typeService.dateDifference(
          new Date(ele.completionDate),
          pastEmiDate,
        );
        prepaidInterest = this.typeService.manageAmount(
          (upcomingPrincipal * interestRate * daysdiff) / 100,
        );
        if (
          pastEmis.length == 0 &&
          new Date(emiData[0].emi_date) > new Date(ele?.completionDate)
        )
          prepaidInterest = ele?.interestAmount;
        if (prepaidPrincipal > ele?.principalAmount)
          prepaidPrincipal = ele?.principalAmount;
        if (prepaidInterest > ele?.interestAmount)
          prepaidInterest = ele?.interestAmount;
      }
      // Emi Pay
      else if (ele?.type == 'EMIPAY') {
        emi = emiData.filter(
          (el) =>
            new Date(el.emi_date).getTime() > transDate && el.id == ele?.emiId,
        );
        prepaidPrincipal = emi.length == 0 ? 0 : emi[0].principalCovered;
        prepaidInterest = emi.length == 0 ? 0 : emi[0].interestCalculate;
      }
      // Part Pay
      else if (ele?.type == 'PARTPAY') {
        emi = emiData.filter(
          (el) =>
            new Date(el.emi_date).getTime() > transDate && el.id == ele?.emiId,
        );
        prepaidPrincipal = emi.length == 0 ? 0 : ele?.principalAmount;
        prepaidInterest = emi.length == 0 ? 0 : ele?.interestAmount;
      }
      let isRefund = 'No';
      if (
        refunObject[ele.emiId] &&
        ele.paidAmount == Math.abs(refunObject[ele.emiId].amount)
      ) {
        isRefund = 'Yes';
        delete refunObject[ele.emiId];
      }

      // Adhoc -> As per requirement given by Accounts team need below id for Cashfree (22/06/2024)
      let orderId;
      if (ele.source == kCashfree && ele.response) {
        try {
          const response = JSON.parse(ele.response ?? null);
          if (response?.payment?.orderId) orderId = response?.payment?.orderId;
        } catch (error) {}
      }

      // Calculate processing fees and additional charges
      const processingFees = this.typeService.manageAmount(
        (+loan?.netApprovedAmount * loan?.processingFees) / 100,
      );

      const kycAddress = this.typeService.getAadhaarAddress({
        aadhaarAddress: userData?.kycData?.aadhaarAddress,
        aadhaarAddressResponse: userData?.kycData?.aadhaarAddressResponse,
      });
      let actualDealyDays = 0;
      if (data?.repaidFlag != 'Pre-Paid') {
        if (data?.dueDate && completionDate && data?.dueDate != '-') {
          actualDealyDays = this.typeService.differenceInDays(
            new Date(completionDate), // Repaid Date
            new Date(data?.dueDate),
          );
        }
      }
      let tempData: any = {};
      if (!isDownload) {
        tempData['Loan Details'] = {
          'Loan ID': loanId,
          'User Id': ele.userId,
          Name: userData?.fullName ?? '-',
          'Mobile Number': mobileNumber ?? '-',
          'Follower Name':
            (await this.commonSharedService.getAdminData(ele?.followerId))
              ?.fullName ?? '-',
          'Completed Loans': loan?.completedLoan ?? 0,
          'Loan Status': loanStatus,
          'Loan Tenure': loan?.approvedDuration ?? '-',
          'Approved Salary Date': loan?.verifiedSalaryDate ?? '-',
          'PAN / Aadhaar':
            `${userData?.kycData?.panCardNumber ?? '-'} / ${
              userData?.kycData?.maskedAadhaar ?? '-'
            }` || '-',
        };

        tempData['Loan Approval & Disbursement'] = {
          'Approved Amount': loan?.netApprovedAmount ?? 0,
          'Processing Charge (%)': loan?.processingFees ?? '-',
          'Processing Charge Amount': processingFees ?? 0,
          'Document Charges': loan?.charges?.doc_charge_amt ?? '-',
          'Online Convenience Charges': loan?.charges?.insurance_fee ?? '-',
          'Stamp Duty Fees': loan?.stampFees ?? 0,
          'Risk Assessment Charge': Math.round(
            +loan?.charges?.risk_assessment_charge || 0,
          ),
          'Amount Disbursed': disbursAmount,
          'Disbursement Date': disDate ?? '-',
          'Loan Interest (%)': loan?.interestRate,
          'Loan Approved By':
            (
              await this.commonSharedService.getAdminData(
                loan?.manualVerificationAcceptId,
              )
            )?.fullName ?? '-',
        };

        tempData['Repayment Details'] = {
          'Repaid Amount': ele?.paidAmount ?? 0,
          'Total Paid Amount': tPaid ?? 0,
          'Paid Principal': ele?.principalAmount ?? 0,
          'Paid Interest': ele?.interestAmount ?? 0,
          'Prepaid Principal': prepaidPrincipal,
          'Prepaid Interest': prepaidInterest,
          'Expected Interest': data?.emiInterest ?? '-',
          'Expected Principal': data?.emiPrincipal ?? '-',
          // 'Total Foreclosure Amount': fullpayData.totalAmount ?? 0,
          'Total EMI': emiData?.length ?? 0,
          'EMI Amount': emiData[0]?.emi_amount ?? 0,
          'EMI Types': data?.emiNo ?? '-',
          'Next EMI Date': nextEmiDate ?? '-',
          'Total Waive-Off': data?.totalWaiveOff ?? '-',
          'Waive-Off Given By': data?.totalWaiveOff
            ? (await this.commonSharedService.getAdminData(loan?.followerId))
                ?.fullName ?? '-'
            : '-',
          'Repayment Via': ele?.subSource ?? '-',
          'Payment Mode': ele?.source ?? '-',
          'Payment ID': ele?.utr ?? '-',
          'Transaction ID': ele.transactionId ?? '-',
          'Order ID': orderId ?? '-',
          'Repaid Flag': data?.repaidFlag ?? '-',
          'Repaid Date': repaidDate,
          'Due date': data?.dueDate ?? '-',
          'Total Delay Days (As on Today)': data?.delayDay ?? 0,
          'Actual Delay Days (Repaid – Due Date)': actualDealyDays ?? 0,
          'Created Date': createdAt ?? '-',
        };

        tempData['Legal & Penalty Charges'] = {
          'ECS Charge': this.typeService.manageAmount(ele?.bounceCharge ?? 0),
          'Deferred Interest': this.typeService.manageAmount(
            ele?.regInterestAmount ?? 0,
          ),

          'Penal Charges': this.typeService.manageAmount(
            (ele?.penaltyAmount ?? 0) + (ele?.penalCharge ?? 0),
          ),
          'Foreclosure Charge': foreclosure ?? 0,
          'Legal Charge': this.typeService.manageAmount(ele?.legalCharge ?? 0),

          // GST charges -> Legal charge
          'Legal Charge (IGST)': ele?.igstOnLegalCharge ?? 0,
          'Legal Charge (CGST)': ele?.cgstOnLegalCharge ?? 0,
          'Legal Charge (SGST)': ele?.sgstOnLegalCharge ?? 0,
          // GST charges -> Penal charge
          'Penal Charge (IGST)': ele?.igstOnPenalCharge ?? 0,
          'Penal Charge (CGST)': ele?.cgstOnPenalCharge ?? 0,
          'Penal Charge (SGST)': ele?.sgstOnPenalCharge ?? 0,

          // old Bounce charge
          // GST charges -> Bounce charge
          'ECS Charge (IGST)': ele?.igstOnBounceCharge ?? 0,
          'ECS Charge (CGST)': ele?.cgstOnBounceCharge ?? 0,
          'ECS Charge (SGST)': ele?.sgstOnBounceCharge ?? 0,

          'Foreclosure Charge (IGST)': ele?.igstForClosureCharge ?? 0,
          'Foreclosure Charge (CGST)': ele?.cgstForClosureCharge ?? 0,
          'Foreclosure Charge (SGST)': ele?.sgstForClosureCharge ?? 0,
        };

        tempData['Risk Category'] = {
          'Risk Category ML': riskCategoryMl ?? '-',
          'Risk Category KYC': riskCategoryKyc ?? '-',
        };

        tempData['Employment & Personal Details'] = {
          'Company Name': emp?.companyName ?? '-',
          Designation: emp?.designation?.designationName ?? '-',
          Purpose: loan?.purpose?.purposeName ?? '-',
          Gender: userData?.gender ?? '-',
          'Last Fetched City': userData?.city ?? '-',
          'Last Fetched State': userData?.state ?? '-',
          'KYC City': kycAddress?.dist ?? '-',
          'KYC State': kycAddress?.state ?? '-',
        };

        tempData['Transaction & CRM Details'] = {
          'Refund Status': isRefund,
          'Last CRM By': crmData?.adminName ?? '-',
          'Last CRM Date': crmDate,
          Disposition: crmData?.dispositionName ?? '-',
        };
      } else {
        tempData['Loan ID'] = loanId;
        tempData['User Id'] = ele.userId;
        tempData['Name'] = userData?.fullName ?? '-';
        tempData['Mobile Number'] = mobileNumber ?? '-';
        tempData['Follower Name'] =
          (await this.commonSharedService.getAdminData(ele?.followerId))
            ?.fullName ?? '-';
        tempData['Completed Loans'] = loan?.completedLoan ?? 0;
        tempData['Loan Status'] = loanStatus;
        tempData['Loan Tenure'] = loan?.approvedDuration ?? '-';
        tempData['Approved Salary Date'] = loan?.verifiedSalaryDate ?? '-';
        tempData['PAN / Aadhaar'] = `${
          userData?.kycData?.panCardNumber ?? '-'
        } / ${userData?.kycData?.maskedAadhaar ?? '-'}`;

        tempData['Approved Amount'] = loan?.netApprovedAmount ?? 0;
        tempData['Processing Charge (%)'] = loan?.processingFees ?? '-';
        tempData['Processing Charge Amount'] = processingFees ?? 0;
        tempData['Document Charges'] = loan?.charges?.doc_charge_amt ?? '-';
        tempData['Online Convenience Charges'] =
          loan?.charges?.insurance_fee ?? '-';
        tempData['Stamp Duty Fees'] = loan?.stampFees ?? 0;
        tempData['Risk Assessment Charge'] = Math.round(
          +loan?.charges?.risk_assessment_charge || 0,
        );
        tempData['Amount Disbursed'] = disbursAmount;
        tempData['Disbursement Date'] = disDate ?? '-';
        tempData['Loan Interest (%)'] = loan?.interestRate;
        tempData['Loan Approved By'] =
          (
            await this.commonSharedService.getAdminData(
              loan?.manualVerificationAcceptId,
            )
          )?.fullName ?? '-';

        tempData['Repaid Amount'] = ele?.paidAmount ?? 0;
        tempData['Total Paid Amount'] = tPaid ?? 0;
        tempData['Paid Principal'] = ele?.principalAmount ?? 0;
        tempData['Paid Interest'] = ele?.interestAmount ?? 0;
        tempData['Prepaid Principal'] = prepaidPrincipal;
        tempData['Prepaid Interest'] = prepaidInterest;
        tempData['Expected Interest'] = data?.emiInterest ?? '-';
        tempData['Expected Principal'] = data?.emiPrincipal ?? '-';
        // tempData['Total Foreclosure Amount'] = fullpayData.totalAmount ?? 0;
        tempData['Total EMI'] = emiData?.length ?? 0;
        tempData['EMI Amount'] = emiData[0]?.emi_amount ?? 0;
        tempData['EMI Types'] = data?.emiNo ?? '-';
        tempData['Next EMI Date'] = nextEmiDate ?? '-';
        tempData['Total Waive-Off'] = data?.totalWaiveOff ?? '-';
        tempData['Waive-Off Given By'] = data?.totalWaiveOff
          ? (await this.commonSharedService.getAdminData(loan?.followerId))
              ?.fullName ?? '-'
          : '-';
        tempData['Repayment Via'] = ele?.subSource ?? '-';
        tempData['Payment Mode'] = ele?.source ?? '-';
        tempData['Payment ID'] = ele?.utr ?? '-';
        tempData['Transaction ID'] = ele.transactionId ?? '-';
        tempData['Order ID'] = orderId ?? '-';
        tempData['Repaid Flag'] = data?.repaidFlag ?? '-';
        tempData['Repaid Date'] = repaidDate;
        tempData['Due date'] = data?.dueDate ?? '-';
        tempData['Total Delay Days (As on Today)'] = data?.delayDay ?? 0;
        tempData['Actual Delay Days (Repaid – Due Date)'] =
          actualDealyDays ?? 0;
        tempData['Created Date'] = createdAt ?? '-';

        tempData['ECS Charge'] = this.typeService.manageAmount(
          ele?.bounceCharge ?? 0,
        );
        tempData['Deferred Interest'] = this.typeService.manageAmount(
          ele?.regInterestAmount ?? 0,
        );

        tempData['Penal Charges'] = this.typeService.manageAmount(
          (ele?.penaltyAmount ?? 0) + (ele?.penalCharge ?? 0),
        );
        tempData['Foreclosure Charge'] = foreclosure ?? 0;
        tempData['Legal Charge'] = this.typeService.manageAmount(
          ele?.legalCharge ?? 0,
        );

        // GST charges -> Legal charge
        tempData['Legal Charge (IGST)'] = ele?.igstOnLegalCharge ?? 0;
        tempData['Legal Charge (CGST)'] = ele?.cgstOnLegalCharge ?? 0;
        tempData['Legal Charge (SGST)'] = ele?.sgstOnLegalCharge ?? 0;
        // GST charges -> Penal charge
        tempData['Penal Charge (IGST)'] = ele?.igstOnPenalCharge ?? 0;
        tempData['Penal Charge (CGST)'] = ele?.cgstOnPenalCharge ?? 0;
        tempData['Penal Charge (SGST)'] = ele?.sgstOnPenalCharge ?? 0;

        // old Bounce charge
        // GST charges -> Bounce charge
        tempData['ECS Charge (IGST)'] = ele?.igstOnBounceCharge ?? 0;
        tempData['ECS Charge (CGST)'] = ele?.cgstOnBounceCharge ?? 0;
        tempData['ECS Charge (SGST)'] = ele?.sgstOnBounceCharge ?? 0;

        tempData['Foreclosure Charge (IGST)'] = ele?.igstForClosureCharge ?? 0;
        tempData['Foreclosure Charge (CGST)'] = ele?.cgstForClosureCharge ?? 0;
        tempData['Foreclosure Charge (SGST)'] = ele?.sgstForClosureCharge ?? 0;

        tempData['Risk Category ML'] = riskCategoryMl ?? '-';
        tempData['Risk Category KYC'] = riskCategoryKyc ?? '-';

        tempData['Company Name'] = emp?.companyName ?? '-';
        tempData['Designation'] = emp?.designation?.designationName ?? '-';
        tempData['Purpose'] = loan?.purpose?.purposeName ?? '-';
        tempData['Gender'] = userData?.gender ?? '-';
        tempData['Last Fetched City'] = userData?.city ?? '-';
        tempData['Last Fetched State'] = userData?.state ?? '-';
        tempData['KYC City'] = kycAddress?.dist ?? '-';
        tempData['KYC State'] = kycAddress?.state ?? '-';

        tempData['Refund Status'] = isRefund;
        tempData['Last CRM By'] = crmData?.adminName ?? '-';
        tempData['Last CRM Date'] = crmDate;
        tempData['Disposition'] = crmData?.dispositionName ?? '-';
      }

      finalList.push(tempData);
    }
    return finalList;
  }
  //#endregion

  //#region  this function call for repaid flag or dueDate or dealy day
  private funRepaidDealyDayFlag(ele, emiData) {
    let repaidFlag = '-';
    let dueDate = '-';
    let delayDay = 0;
    let emiNo = '-';
    let emiPrincipal = 0;
    let emiInterest = 0;
    let totalWaiveOff = 0;
    try {
      const paidDate = new Date(ele?.completionDate ?? ele?.updatedAt);
      emiData.forEach(
        (emi) =>
          (totalWaiveOff +=
            (emi?.waiver || 0) +
            (emi?.unpaid_waiver || 0) +
            (emi?.paid_waiver || 0)),
      );
      let emi;
      emiData.sort((a, b) => a.id - b.id);
      if (ele?.emiId) {
        emi = emiData.find((f) => f.id === ele?.emiId);
        emiPrincipal = emi?.principalCovered ?? 0;
        emiInterest = emi?.interestCalculate ?? 0;
        const index = emiData.findIndex((el) => el.id == ele?.emiId);
        emiNo = (index + 1).toString();
      } else {
        const filter = emiData.filter((f) => f.pay_type === 'FULLPAY');
        filter.sort((a, b) => a.id - b.id);
        emi = filter[0];
        emiNo = '';
        emiData.forEach((el, index) => {
          if (el.pay_type == 'FULLPAY') {
            if (!emiNo) emiNo = (index + 1).toString();
            else emiNo += ', ' + (index + 1).toString();
            emiPrincipal += el?.principalCovered ?? 0;
            emiInterest += el?.interestCalculate ?? 0;
          }
        });
      }
      if (emi) {
        dueDate = this.typeService.dateToFormatStr(emi?.emi_date);
        delayDay = emi?.penalty_days ?? 0;
        const emiDate = new Date(emi?.emi_date).getTime();
        if (paidDate.getTime() < emiDate) repaidFlag = 'Pre-Paid';
        else if (paidDate.getTime() > emiDate) repaidFlag = 'Delayed';
        else repaidFlag = 'On-Time';
      }
      emiNo = ele?.type + ' ' + emiNo;
    } catch (error) {}
    return {
      repaidFlag,
      dueDate,
      delayDay,
      emiNo,
      emiPrincipal,
      emiInterest,
      totalWaiveOff,
    };
  }

  //#region get all repaid amount total
  private async getAllRepaidAmount(query) {
    try {
      const data = {
        totalPaidAmount: 0,
        onTimePaidAmount: 0,
        dealyPaidAmount: 0,
        count: 0,
        onTimePaidCounts: 0,
        delayPaidCounts: 0,
      };
      if (query?.getTotal == 'true' && query?.download != 'true') {
        const startDate = query.startDate.toJSON();
        const endDate = query.endDate.toJSON();
        /// find total of repay transaction
        const options: any = {
          where: {
            status: 'COMPLETED',
            completionDate: { [Op.gte]: startDate, [Op.lte]: endDate },
            type: { [Op.ne]: 'REFUND' },
          },
          group: ['status'],
        };

        let att: any = [
          [Sequelize.fn('SUM', Sequelize.col('paidAmount')), 'amount'],
          [Sequelize.fn('COUNT', Sequelize.col('status')), 'COUNT'],
        ];
        const total = await this.repository.getRowWhereData(att, options);
        if (!total || total === k500Error) return kInternalError;
        /// find emi delay emi from transaction
        const emiModel: any = {
          model: EmiEntity,
          attributes: [],
          where: { penalty_days: { [Op.gte]: 1 } },
        };
        options.include = [emiModel];
        const emi = await this.repository.getRowWhereData(att, options);
        if (emi === k500Error) return kInternalError;

        /// find emi delay emi from transaction for fullpay
        att = ['id', 'paidAmount'];
        options.where.type = 'FULLPAY';
        delete options.group;
        emiModel.where.pay_type = 'FULLPAY';
        const loanModel = {
          model: loanTransaction,
          attributes: [],
          include: [emiModel],
          required: true,
        };
        options.include = [loanModel];
        const loan = await this.repository.getTableWhereData(att, options);
        if (!loan || loan === k500Error) return kInternalError;
        let delyaAmount = 0;
        let dealyCount = 0;
        for (let index = 0; index < loan.length; index++) {
          try {
            const ele = loan[index];
            delyaAmount += ele.paidAmount;
            dealyCount += 1;
          } catch (error) {}
        }
        delyaAmount += emi?.amount ?? 0;
        dealyCount += +(emi?.COUNT ?? 0);
        data.totalPaidAmount = Math.floor(total?.amount ?? 0);
        data.onTimePaidAmount = Math.floor((total?.amount ?? 0) - delyaAmount);
        data.dealyPaidAmount = Math.floor(delyaAmount);
        data.count = +(total?.COUNT ?? 0);
        data.onTimePaidCounts = (total?.COUNT ?? 0) - dealyCount;
        data.delayPaidCounts = dealyCount;
      }
      return data;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region get delay transaction id
  private async getDelayEmiPaymenst(query) {
    const tranList = [];
    try {
      if (query?.type == 'delay' || query?.type == 'onTime') {
        const startDate = query.startDate.toJSON();
        const endDate = query.endDate.toJSON();
        const options: any = {
          where: {
            status: 'COMPLETED',
            completionDate: { [Op.gte]: startDate, [Op.lte]: endDate },
            type: { [Op.ne]: 'REFUND' },
          },
        };
        /// get find delay emi
        const emiModel: any = {
          model: EmiEntity,
          attributes: [],
          where: { penalty_days: { [Op.gte]: 1 } },
        };
        options.include = [emiModel];
        const att = ['id'];
        const emiTran = await this.repository.getTableWhereData(att, options);
        /// find full pay delay emi
        options.where.type = 'FULLPAY';
        delete options.group;
        emiModel.where.pay_type = 'FULLPAY';
        const loanModel = {
          model: loanTransaction,
          attributes: [],
          include: [emiModel],
          required: true,
        };
        options.include = [loanModel];
        const fullTran = await this.repository.getTableWhereData(att, options);

        if (emiTran && emiTran != k500Error)
          emiTran.forEach((ele) => {
            tranList.push(ele.id);
          });

        if (fullTran && fullTran != k500Error)
          fullTran.forEach((ele) => {
            tranList.push(ele.id);
          });
      }
    } catch (error) {}
    return tranList;
  }

  async getRepaidReportData(query) {
    const isDownload = query?.download == 'true';
    const tranList = await this.getDelayEmiPaymenst(query);

    /// where condition
    const startDate = query.startDate;
    const endDate = query.endDate;
    const page = +(query?.page ?? 1);
    const type = query?.type ?? '';

    let userSearch: any = {};
    let loanId;
    let utr;
    let search = (query?.searchText ?? '').toLowerCase();
    if (search) {
      if (search.startsWith('l-')) loanId = search.replace('l-', '');
      else if (search.startsWith('u-')) utr = search.replace('u-', '');
      else if (!isNaN(search)) {
        search = this.cryptService.encryptPhone(search);
        if (search == k500Error) return k500Error;
        search = search.split('===')[1];
        userSearch.phone = { [Op.like]: '%' + search + '%' };
      } else userSearch.fullName = { [Op.iRegexp]: query?.searchText };
    }

    const options: any = {
      where: {
        status: 'COMPLETED',
        completionDate: {
          [Op.gte]: startDate.toJSON(),
          [Op.lte]: endDate.toJSON(),
        },
        type: { [Op.ne]: 'REFUND' },
        subStatus: {
          [Op.or]: [
            {
              [Op.ne]: 'REVERSE_SETTLEMENT',
            },
            { [Op.eq]: null },
          ],
        },
      },
      order: [['createdAt', 'DESC']],
    };

    if (loanId) options.where.loanId = loanId;
    if (utr) options.where.utr = { [Op.iRegexp]: utr };
    if (type && type != 'delay' && type != 'onTime') options.where.type = type;
    if (tranList.length > 0) {
      if (query?.type == 'delay') options.where.id = tranList;
      else if (query?.type == 'onTime') {
        const idArray = [];
        tranList.forEach((id) => {
          idArray.push({ id: { [Op.ne]: id } });
        });
        options.where = { ...options.where, [Op.and]: idArray };
      }
    }
    if (query?.download != 'true') {
      options.offset = page * PAGE_LIMIT - PAGE_LIMIT;
      options.limit = PAGE_LIMIT;
    }

    // transaction data
    const result = await this.findRepaidLoanData(options);

    const loanIds = result?.rows?.map((data) => data.loanId);
    const userIds = result?.rows?.map((data) => data.userId);

    // // Fetch loan data
    let loanPromise = this.fetchLoanDetails({
      loanIds,
      reportType: MASTER_REPORT_TYPE.REPAYMENT_REPORT,
      userSearch, // where conditions
      download: isDownload,
    });

    // Fetch user Data
    const userPromise = this.fetchUserDataByIds(
      userIds,
      MASTER_REPORT_TYPE.REPAYMENT_REPORT,
    );

    // Fetch other disbursement data
    const disbursementPromise = this.getDisbursementDetails(
      loanIds,
      MASTER_REPORT_TYPE.REPAYMENT_REPORT,
    );

    // fetch emi data
    const emiPromise = this.getEmiData(
      loanIds,
      MASTER_REPORT_TYPE.REPAYMENT_REPORT,
    );

    // Fetch employment details and related designation data
    const empPromise = this.fetchEmploymentDetailsByUserIds(userIds);
    const loanPurposePromise = this.commonSharedService.fetchLoanPurpose();
    const designationPromise = this.commonSharedService.fetchDesignation();
    const predictionPromise =
      this.commonSharedService.fetchPredictionData(loanIds);

    const [
      loanData = [],
      userData = [],
      disbursementData = [],
      emiData = [],
      empDetails = [],
      loanPurpose = [],
      designationData = [],
      predictionData = [],
    ] = await Promise.all([
      loanPromise,
      userPromise,
      disbursementPromise,
      emiPromise,
      empPromise,
      loanPurposePromise,
      designationPromise,
      predictionPromise,
    ]);

    if (
      [
        loanData,
        userData,
        disbursementData,
        emiData,
        empDetails,
        loanPurpose,
        designationData,
        predictionData,
      ].includes(k500Error)
    )
      return kInternalError;

    // Fetch KYC data
    const kycIds = userData.map((user) => user.kycId);
    const kycData = await this.getKycData(kycIds);

    const loanMap = new Map();
    loanData?.rows.forEach((row) => {
      if (!loanMap.has(row.id)) {
        loanMap.set(row.id, row);
      }
    });

    const userMap = new Map();
    userData.forEach((row) => {
      if (!userMap.has(row.id)) {
        userMap.set(row.id, row);
      }
    });

    const disbursementMap = new Map();
    disbursementData.forEach((item) => {
      if (!disbursementMap.has(item.loanId)) {
        disbursementMap.set(item.loanId, []);
      }
      disbursementMap.get(item.loanId).push(item);
    });

    const emiMap = new Map();
    emiData.forEach((item) => {
      if (!emiMap.has(item.loanId)) {
        emiMap.set(item.loanId, []);
      }
      emiMap.get(item.loanId).push(item);
    });

    const empMap = new Map();
    empDetails.forEach((item) => {
      if (!empMap.has(item.userId)) {
        empMap.set(item.userId, item);
      }
    });

    const kycMap = new Map();
    kycData.forEach((item) => {
      if (!kycMap.has(item.userId)) {
        kycMap.set(item.userId, item);
      }
    });

    const loanPurposeMap = new Map();
    loanPurpose.forEach((item) => {
      if (!loanPurposeMap.has(item.id)) {
        loanPurposeMap.set(item.id, item);
      }
    });

    const designationMap = new Map();
    designationData.forEach((item) => {
      if (!designationMap.has(item.id)) {
        designationMap.set(item.id, item);
      }
    });

    const predictionMap = new Map();
    predictionData.forEach((item) => {
      if (!predictionMap.has(item.loanId)) {
        predictionMap.set(item.loanId, item);
      }
    });

    const finalData = result.rows.map((row) => {
      let designation = null;
      const loan = loanMap.get(row?.loanId) ?? {};
      const user = userMap.get(row?.userId);
      const disbursement = disbursementMap.get(row?.loanId);
      const emi = emiMap.get(row?.loanId) ?? [];
      const emp = empMap.get(row?.userId);
      const kyc = kycMap.get(row?.userId);
      const purpose = loanPurposeMap.get(loan?.purposeId);
      const prediction = predictionMap.get(row?.loanId);

      if (emp?.userId) designation = designationMap.get(emp?.designationId);
      if (loan) row.loanData = loan;
      if (user) row.userData = user;
      if (loan && emi) row.loanData.emiData = emi;
      if (loan && disbursement) row.loanData.disbursementData = disbursement;
      if (user && emp) row.userData.employmentData = emp;
      if (user && kyc) row.userData.kycData = kyc;
      if (loan && purpose) row.loanData.purpose = purpose;
      if (user && emp && designation)
        row.userData.employmentData.designation = designation;
      if (loan && prediction) row.loanData.predictionData = prediction;

      return row;
    });

    return { count: result.count ?? 0, rows: finalData };
  }
  //#endregion
}
