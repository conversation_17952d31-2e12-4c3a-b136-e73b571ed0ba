import { Column, DataType, Model, Table } from 'sequelize-typescript';

@Table({})
export class ExperianScoreEntity extends Model<ExperianScoreEntity> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.SMALLINT,
    allowNull: true,
  })
  experianScore: number;

  @Column({
    type: DataType.SMALLINT,
    allowNull: true,
  })
  overdueAccounts: number;

  @Column({
    type: DataType.SMALLINT,
    allowNull: true,
  })
  inquiryPast30Days: number;

  @Column({
    type: DataType.SMALLINT,
    allowNull: true,
  })
  PLAccounts: number;

  @Column({
    type: DataType.SMALLINT,
    allowNull: true,
    comment: '1-postgresql, 2-redis',
  })
  internal_source: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  overdueAmount: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  totalDelayDays: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  PLOutstanding: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  totalOutstanding: number;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  experianFetchDate: Date;

  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  requestData: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  responseData: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  formattedResponse: string;
}
