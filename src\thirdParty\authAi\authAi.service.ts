// Imports
import * as fs from 'fs';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { k500Error } from 'src/constants/misc';
import {
  GET_COMPARE_ACCOUNT,
  kGetFraudUsers,
  kRemovePasswordFile,
  kSyncTrans,
  nGetPdfUrl,
  PRIMARY_BANKINGPRO_URL,
  SECONDARY_BANKINGPRO_URL,
  UPDATE_ACCOUNT_NUMBER,
  UPDATE_ENC_ACCOUNT_NUMBER,
} from 'src/constants/network';
import { kBankingProHeaders, kDevBankAccs } from 'src/constants/objects';
import { APIService } from 'src/utils/api.service';
import { k422ErrorMessage, kInternalError } from 'src/constants/responses';
import { gIsPROD } from 'src/constants/globals';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { kSomthinfWentWrong } from 'src/constants/strings';
import { FileService } from 'src/utils/file.service';
import { CLOUD_FOLDER_PATH } from 'src/constants/objects';

@Injectable()
export class AuthAiService {
  constructor(
    private readonly api: APIService,
    private readonly commonService: CommonSharedService,
    @Inject(forwardRef(() => FileService))
    private readonly fileService: FileService,
  ) {}

  async removePassword(filePath: string, password: string) {
    try {
      if (!filePath || !fs.existsSync(filePath)) return kInternalError;

      const fileBuffer = fs.readFileSync(filePath);
      const apiUrl = PRIMARY_BANKINGPRO_URL + kRemovePasswordFile;
      const headers = kBankingProHeaders;
      const storage = CLOUD_FOLDER_PATH.default;

      const formData = {
        pdfFile: {
          value: fileBuffer,
          options: {
            filename: 'document.pdf',
            contentType: 'application/pdf',
          },
        },
        password: password || '',
      };

      const response = await this.api.requestPost(
        apiUrl,
        {},
        headers,
        formData,
      );
      try {
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
      } catch (error) {}
      if (response === k500Error) return kInternalError;
      if (!response?.valid) return k422ErrorMessage(response.message);
      const fileUrl = await this.fileService.base64ToFileURL(
        response.data,
        'pdf',
        storage,
      );
      if (!fileUrl || fileUrl === kInternalError)
        return k422ErrorMessage(kSomthinfWentWrong);

      return fileUrl;
    } catch (error) {
      return kInternalError;
    }
  }

  async getPdfURL(filePath, accNumber = '') {
    try {
      let url = PRIMARY_BANKINGPRO_URL + nGetPdfUrl;
      const headers = kBankingProHeaders;
      const body = { fileName: filePath.replace('src/uploads/', '') };

      let response = await this.api.requestPost(url, body, headers);
      if (response == k500Error) return kInternalError;
      if (!response.valid) {
        url = SECONDARY_BANKINGPRO_URL + nGetPdfUrl;
        response = await this.api.requestPost(url, body, headers);
        if (response == k500Error) return kInternalError;
        if (!response.valid) {
          // For development purpose only
          if (!gIsPROD && kDevBankAccs.includes(accNumber))
            return 'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/*************.pdf';
          return kInternalError;
        }
      }
      return response.url;
    } catch (error) {
      return kInternalError;
    }
  }

  async syncTransactions(data: any) {
    try {
      const url = PRIMARY_BANKINGPRO_URL + kSyncTrans;
      const body = data;
      const headers = kBankingProHeaders;

      const response = await this.api.requestPost(url, body, headers);
      if (response == k500Error) return kInternalError;
      if (!response.valid) return k422ErrorMessage(response.message);
      return response.data;
    } catch (error) {
      return kInternalError;
    }
  }

  async getFraudUsers(accountId) {
    let url = PRIMARY_BANKINGPRO_URL + kGetFraudUsers;
    if (accountId) url = url + `?accountId=${accountId}`;
    const headers = kBankingProHeaders;
    const response = await this.api.requestGet(url, headers);
    if (response == k500Error) throw new Error();

    return response.data;
  }

  //#region [note add bankCode while this function call]
  async updateMaskedAccNumber(
    maskAccountNumber: string,
    accountNumber: string,
    bankCode?: string,
    salary: number = 0,
    companyName: string = '',
  ) {
    try {
      const url = UPDATE_ACCOUNT_NUMBER;
      const body = {
        maskAccountNumber,
        accountNumber,
        bankCode,
        salary,
        companyName,
      };
      const result = await this.api.requestPost(url, body, kBankingProHeaders);
      if ((result?.valid ?? false) === true) return true;
      if (result == k500Error) return kInternalError;
      return false;
    } catch (error) {
      return kInternalError;
    }
  }
  //#endregion

  async getCompareAccounts(query) {
    try {
      const url = GET_COMPARE_ACCOUNT + query;
      const headers = kBankingProHeaders;

      const response = await this.api.requestGet(url, headers);
      if (response == k500Error) return kInternalError;
      if (!response.valid) return k422ErrorMessage(response.message);
      return response;
    } catch (error) {
      return kInternalError;
    }
  }

  async findCompareTransaction(loanId, isCheckAddional = false) {
    try {
      let url = await this.commonService.getTansactionQueryParams(
        loanId,
        isCheckAddional,
      );
      if (url?.message) return url;
      url = GET_COMPARE_ACCOUNT + url;

      const result = await this.api.requestGet(url, kBankingProHeaders);
      if ((result?.valid ?? false) === true) return result;
      return k422ErrorMessage(kSomthinfWentWrong);
    } catch (error) {
      return kInternalError;
    }
  }

  async updateEncryptedAccNumber(
    oldAccountNumber: string,
    newAccountNumber: string,
    bankCode: string,
  ) {
    try {
      const url = UPDATE_ENC_ACCOUNT_NUMBER;
      const body = { oldAccountNumber, newAccountNumber, bankCode };
      const result = await this.api.requestPost(url, body, kBankingProHeaders);
      if ((result?.valid ?? false) === true) return true;
      if (result == k500Error) return kInternalError;
      return false;
    } catch (error) {
      return kInternalError;
    }
  }
}
