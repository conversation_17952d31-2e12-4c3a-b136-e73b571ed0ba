// Imports
import { Table, Model, Column, DataType } from 'sequelize-typescript';

@Table({})
export class FetchDataEntity extends Model<FetchDataEntity> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  phone: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  hashPhone: string;

  @Column({
    type: DataType.STRING(32),
    allowNull: true,
  })
  type: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
    defaultValue: {},
  })
  result: any;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  response: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  requestData: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  hashPan: string;
}
