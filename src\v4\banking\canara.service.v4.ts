// Imports
import { Injectable } from '@nestjs/common';
import {
  nInsertLog,
  nNetbankingTriggers,
  nSyncTransactions,
} from 'src/constants/network';
import { DateService } from 'src/utils/date.service';
import { TransactionJSON } from './transaction.interface.v4';
import { commonNetBankingServiceV4 } from './common.netbanking.service.v4';

@Injectable()
export class canaraBankingServiceV4 {
  constructor(
    private readonly dateService: DateService,
    private readonly commonNetbankingService: commonNetBankingServiceV4,
  ) {}

  getCanaraNetbankingData(reqData) {
    const todayDateInfo = this.dateService.dateToReadableFormat(
      new Date(),
      'YYYY-MM-DD',
    );
    const today = new Date();
    today.setDate(today.getDate() - 120);
    const fromDate = new Date(today);
    const fromDateInfo = this.dateService.dateToReadableFormat(
      fromDate,
      'YYYY-MM-DD',
    );

    const totalSteps = this.commonNetbankingService.getRelevantLoaderSteps(5);
    return {
      title: 'Verify your bank',
      initialURL: 'https://online.canarabank.in/',
      initialLoader: true,
      type: 'BANK',
      jsTriggers: {
        'https://online.canarabank.in/': {
          onLoadStart: {
            state: { isLoader: false, isProcessing: true },
          },

          onLoadStop: {
            state: { isLoader: false, isProcessing: true },
            triggers: [
              `(() => {
  let stepInitInterval;

  stepInitInterval = setInterval(() => {
    console.info("START");
    const urlSearch = document.location.search;
    if (urlSearch === "?module=login") {
      // login alert popup
      const closeAlertPop = document.querySelector(
        'div[id="popup"] div[class="close-btn"]'
      );
      if (closeAlertPop) closeAlertPop.click();

      const sideMenu = document.querySelector('a[aria-label="Options Menu"]');
      const otherBtn = document.querySelector('div[class="useful"]');
      const serviceLinks = document.querySelector(
        'div[class="oj-flex center customButtons2"]'
      );
      const footer = document.querySelector('div[class="footer"]');
      const socialBtn = document.evaluate(
        '//p[text()="Connect with us:"]/parent::div',
        document,
        null,
        XPathResult.FIRST_ORDERED_NODE_TYPE,
        null
      ).singleNodeValue;

      let langSelectBtn = document.querySelectorAll(
        'oj-select-one[id="switchLanguage"]'
      );
      if (langSelectBtn?.length > 1) {
        langSelectBtn = langSelectBtn[1];
        const langdiv = langSelectBtn.closest("div");
        if (langdiv) langdiv.style.display = "none";
      }

      const resetBtn = document.evaluate(
        '//span[text()="Create/Reset Login Password"]/parent::div/parent::button/parent::oj-button',
        document,
        null,
        XPathResult.FIRST_ORDERED_NODE_TYPE,
        null
      ).singleNodeValue;

      const botBtn = document.querySelector('div[id="bot-icon"]');

      if (sideMenu) sideMenu.style.display = "none";
      if (otherBtn) otherBtn.style.display = "none";
      if (serviceLinks) serviceLinks.style.display = "none";
      if (footer) footer.style.display = "none";
      if (socialBtn) socialBtn.style.display = "none";
      if (resetBtn) resetBtn.style.display = "none";
      if (botBtn) {
        setTimeout(() => { 
          botBtn.style.display = "none";
          console.info("stopProcessing");
          clearInterval(stepInitInterval);
         }, 1000)
      }
    }
  }, 1000);
})()`,

              `(() => {
  let stepOneInterval;
  let stepSecondInterval;
  let stepThreeInterval;
  let accountData = {
    status: "PENDING",
    profile: {},
    transactions: null,
  };

  stepOne();

  function stepOne() {
    stepOneInterval = setInterval(() => {
      console.info("STEP-> #01");
      // check when route is change after the login
      const urlSearch = document.location.search;

      if (urlSearch === "?module=customer") {
        console.info("startProcessing");
        console.info("STEP-> #02");
        const closeDownAppPop = document.evaluate(
          '//span[text()="Scan to Download Canara ai1 App"]/following-sibling::a',
          document,
          null,
          XPathResult.FIRST_ORDERED_NODE_TYPE,
          null
        ).singleNodeValue;
        if (closeDownAppPop) closeDownAppPop.click();

        const statementBtn = document.evaluate(
          '//span[text()="View Statement"]',
          document,
          null,
          XPathResult.FIRST_ORDERED_NODE_TYPE,
          null
        ).singleNodeValue;

        if (statementBtn) {
          clearInterval(stepOneInterval);
          statementBtn.click();
          stepTwo();
        }
      }
    }, 1000);
  }

  function stepTwo() {
    stepSecondInterval = setInterval(async () => {

      const accountNumberEle = document.evaluate(
        '//span[text()="Account Number"]/parent::div/following-sibling::span',
        document,
        null,
        XPathResult.FIRST_ORDERED_NODE_TYPE,
        null
      ).singleNodeValue;

      const customerNameEle = document.evaluate(
        '//span[text()="Customer Name"]/parent::div/following-sibling::span',
        document,
        null,
        XPathResult.FIRST_ORDERED_NODE_TYPE,
        null
      ).singleNodeValue;

      const branchNameEle = document.evaluate(
        '//span[text()="Branch Name"]/parent::div/following-sibling::span',
        document,
        null,
        XPathResult.FIRST_ORDERED_NODE_TYPE,
        null
      ).singleNodeValue;

      if (accountNumberEle)
        accountData.profile.accountNumber = accountNumberEle.innerText;

      if (customerNameEle)
        accountData.profile.customerName = customerNameEle.innerText;
      if (branchNameEle)
        accountData.profile.branchName = branchNameEle.innerText;

      const selectfilterBtn = document.querySelectorAll("oj-select-one");

      if (selectfilterBtn) {
        selectfilterBtn[2].value = "SPD";

        await new Promise((resolve) => setTimeout(resolve, 1000));

        document.querySelectorAll("oj-input-date")[0].value =
          "${fromDateInfo?.readableStr}";
        document.querySelectorAll("oj-input-date")[1].value =
          "${todayDateInfo?.readableStr}";

        await new Promise((resolve) => setTimeout(resolve, 1000));

        const filterBtn = document.evaluate(
          '//span[text()="Apply Filter"]',
          document,
          null,
          XPathResult.FIRST_ORDERED_NODE_TYPE,
          null
        ).singleNodeValue;

        if (filterBtn) {
          clearInterval(stepSecondInterval);
          filterBtn.click();
          stepThree();
        }
      }
    }, 1000);
  }

  function stepThree() {
    stepThreeInterval = setInterval(() => {
      console.info("STEP-> #03");
      const listEle = document.querySelectorAll("oj-list-view")[1];
      if (listEle) {
        clearInterval(stepThreeInterval);
        const transList = listEle?.data.dataSource._data;
        accountData.transactions = transList;
        accountData.status = "COMPLETED";
        console.info(JSON.stringify(accountData));
      }
    }, 1000);
  }
})()`,
            ],
          },

          consoles: [
            {
              combinations: ['stopProcessing'],
              state: { isProcessing: false },
            },
            {
              combinations: ['startProcessing'],
              state: { isProcessing: true },
            },
            // Step -> 01 -> Page loads
            {
              combinations: ['STEP-> #01'],
              apiTriggers: [
                {
                  url: nInsertLog,
                  method: 'POST',
                  needWebResponse: false,
                  body: {
                    loanId: reqData.loanId,
                    userId: reqData.userId,
                    type: 1,
                    subType: 1,
                    status: 2,
                    values: { bankCode: 'CANARA', step: 1 },
                  },
                },
              ],
            },
            {
              combinations: ['STEP-> #02'],
              jsTriggers: [`LITT_SKIP_JS_LOADER_PERC_${totalSteps[0]}`],
              apiTriggers: [
                {
                  url: nInsertLog,
                  method: 'POST',
                  needWebResponse: false,
                  body: {
                    loanId: reqData.loanId,
                    userId: reqData.userId,
                    type: 1,
                    subType: 1,
                    status: 2,
                    values: { bankCode: 'CANARA', step: 2 },
                  },
                },
              ],
            },

            {
              combinations: ['STEP-> #03'],
              jsTriggers: [`LITT_SKIP_JS_LOADER_PERC_${totalSteps[1]}`],
              apiTriggers: [
                {
                  url: nInsertLog,
                  method: 'POST',
                  needWebResponse: false,
                  body: {
                    loanId: reqData.loanId,
                    userId: reqData.userId,
                    type: 1,
                    subType: 1,
                    status: 2,
                    values: { bankCode: 'CANARA', step: 3 },
                  },
                },
              ],
            },
            {
              combinations: ['COMPLETED'],
              jsTriggers: [`LITT_SKIP_JS_LOADER_PERC_${totalSteps[2]}`],
              apiTriggers: [
                {
                  url: nInsertLog,
                  method: 'POST',
                  needWebResponse: true,
                  body: {
                    loanId: reqData.loanId,
                    userId: reqData.userId,
                    type: 1,
                    subType: 1,
                    status: 2,
                    values: { bankCode: 'CANARA', step: 4 },
                  },
                },
                {
                  url: nNetbankingTriggers,
                  method: 'POST',
                  body: {
                    bankCode: 'CANARA',
                    loanId: reqData.loanId,
                    userId: reqData.userId,
                    companyName: reqData?.companyName ?? '',
                    salary: reqData?.salary ?? 0,
                  },
                },
              ],
            },
          ],
        },
      },
    };
  }

  async kCanaraFlow(reqData) {
    const totalSteps = this.commonNetbankingService.getRelevantLoaderSteps(5);
    const data = JSON.parse(reqData?.internalResponse);
    const accountNumber = data?.profile?.accountNumber;
    const profile: any = await this.canaraUserProfile(data?.profile);

    // Update companyName & salary in profile
    if (profile && Object.keys(profile)?.length) {
      profile.companyName = reqData?.companyName;
      profile.salary = reqData?.salary;
    }

    const transactions = this.canaraTransactions(
      data?.transactions,
      accountNumber,
    );

    const callbackList = [
      {
        url: nSyncTransactions,
        method: 'POST',
        body: {
          bankCode: 'CANARA',
          webResponse: {
            profile,
            transactions,
          },
        },
      },
      {
        url: nInsertLog,
        method: 'POST',
        needWebResponse: true,
        body: {
          loanId: reqData.loanId,
          userId: reqData.userId,
          type: 1,
          subType: 1,
          status: 2,
          values: { bankCode: 'CANARA', step: 4.5 },
        },
        preTriggersToExecute: [`LITT_SKIP_JS_LOADER_PERC_${totalSteps[3]}`],
      },
    ];
    return { callbackList };
  }

  // CANARA -> Profile details
  private async canaraUserProfile(reqData) {
    const profile = {
      accountNumber: reqData?.accountNumber ?? '',
      inAppService: true,
      bankCode: 'CANARA',
      name: reqData?.customerName,
      ifscCode: '',
      ifscode: '',
      ccDetails: {},
    };

    // Get IFSC details from google and razorpay
    const branchName = reqData?.branchName;
    const ifscCode = await this.commonNetbankingService.fetchIFSCFromBranchName(
      branchName,
      'CANARA',
    );
    profile.ifscode = ifscCode;
    profile.ifscCode = ifscCode;
    return profile;
  }

  // CANARA -> Transaction details
  private canaraTransactions(transactions, accountId) {
    const finalizedList = [];
    for (const item of transactions) {
      try {
        const transData: TransactionJSON = { accountId };
        // Date
        transData.dateTime = this.dateService.anyToDateStr(
          item.transactionDate,
        );
        // Narration
        transData.description = item.description;
        // Closing balance
        transData.balanceAfterTransaction = item.balanceamt;
        // Amount and type
        const amount = item.tempAmountint;
        if (amount != 0) {
          transData.amount = amount;
          transData.type = item.transactionType == 'D' ? 'DEBIT' : 'CREDIT';
        }

        if (transData.dateTime && transData.amount)
          finalizedList.push(transData);
      } catch (error) {}
    }
    return finalizedList;
  }
}
