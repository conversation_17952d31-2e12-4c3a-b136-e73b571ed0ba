<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Your Site</title>
  </head>

  <body>
    <!-- And add the code below into the body of your HTML -->
    <div id="zoop-gateway-model">
      <div id="zoop-model-content"></div>
    </div>
    <script>
      (() => {
        const o = Object.freeze({
          POPUP: 'POPUP',
          TAB: 'TAB',
          REDIRECT: 'REDIRECT',
        });

        !(() => {
          const e = Object.freeze({
            POPUP: 'POPUP',
            TAB: 'TAB',
            REDIRECT: 'REDIRECT',
          });
          !(function () {
            var o = {
                eSignV4URL: 'https://esign.zoop.plus',
                digilockerV1URL: 'https://gateway.zoop.plus/digilocker/v1',
                livenessV1URL: 'https://gateway.zoop.plus/liveness/init',
                studentSDKV1URL:
                  'https://gateway.zoop.plus/student-verification-sdk/request_id',
                ocrSDKV1URL: 'https://gateway.zoop.plus/ocr/init',
                zoopModel: window.document.getElementById('zoop-gateway-model'),
                zoopWindow: null,
              },
              t = {
                zoopGateWayModel: {
                  display: 'none',
                  position: 'fixed',
                  'z-index': 1,
                  left: 0,
                  top: 0,
                  width: '100%',
                  height: '100%',
                  overflow: 'auto',
                  'background-color': 'rgba(0, 0, 0, 0.4)',
                },
                zoopModelContent: {
                  'border-radius': '10px',
                  'background-color': '#fefefe',
                  'margin-top': '50px',
                  'margin-bottom': 'auto',
                  'margin-left': 'auto',
                  'margin-right': 'auto',
                  padding: ' -1px',
                  width: '700px',
                  height: '675px',
                },
                iframe: {
                  'border-radius': 'inherit',
                  margin: '0px',
                  padding: '0px',
                  border: 'none',
                },
              },
              n = {
                options: o,
                styles: t,
                esignGatewayOptions: {
                  gateway_url: '',
                  transaction_id: '',
                  show_download_btn: 'Y',
                  mode: 'REDIRECT',
                  zoomLevel: 2,
                  version: 'v5',
                },
                digilockerGatewayOption: {
                  request_id: '',
                  gatewayURL: o.digilockerV1URL,
                  mode: e.TAB,
                },
                livenessGatewayOption: {
                  request_id: '',
                  gatewayURL: o.livenessV1URL,
                  mode: e.REDIRECT,
                },
                studentSDKGatewayOption: {
                  request_id: '',
                  gatewayURL: o.studentSDKV1URL,
                  mode: e.REDIRECT,
                },
                ocrSDKGatewayOption: {
                  request_id: '',
                  gatewayURL: o.ocrSDKV1URL,
                  mode: e.REDIRECT,
                },
                check: function (e, o) {
                  return (
                    !!e.hasOwnProperty(o) &&
                    (!!n.isNullUndefinedOrEmpty(e[o]) ||
                      ('undefined' !== o && null !== o && 0 !== o.length))
                  );
                },
                onError: function () {},
                onSuccess: function () {},
                isNullUndefinedOrEmpty: function (e) {
                  return null == e || 0 === e.length;
                },
                setEnvironment: function (e) {
                  switch (e) {
                    case 'production':
                      (n.options.url = n.options.production),
                        (n.options.itdURL = n.options.itdProdURL);
                      break;
                    case 'staging':
                      (n.options.url = n.options.staging),
                        (n.options.itdURL = n.options.itdStagingURL);
                  }
                },
                setStyles: function (e, o, t) {
                  let n;
                  if ('class' == t) {
                    const o = document.getElementByClass(e);
                    n = o && o.style;
                  } else if ('id' == t) {
                    const o = document.getElementById(e);
                    n = o && o.style;
                  } else n = document.getElementsByTagName(e);
                  if (n && Object.keys(o).length) for (var i in o) n[i] = o[i];
                },
                eSignGatewayInit: function (o) {
                  return (
                    (n.esignGatewayOptions.show_download_btn = n.check(
                      o,
                      'show_download_btn',
                    )
                      ? o.show_download_btn
                      : n.esignGatewayOptions.show_download_btn),
                    (n.esignGatewayOptions.mode = n.check(o, 'mode')
                      ? o.mode
                      : n.esignGatewayOptions.mode),
                    (n.esignGatewayOptions.zoomLevel = n.check(o, 'zoomLevel')
                      ? Number.parseFloat(
                          0.8 +
                            0.2 *
                              (Math.max(
                                Math.min(Number.parseInt(o.zoomLevel), 7),
                                1,
                              ) -
                                1),
                        ).toFixed(1)
                      : n.esignGatewayOptions.zoomLevel),
                    o.version && (n.esignGatewayOptions.version = o.version),
                    o.mode && o.mode.toUpperCase() === e.TAB
                      ? (n.esignGatewayOptions.mode = e.TAB)
                      : (o.mode && (o.mode.toUpperCase(), e.REDIRECT),
                        (n.esignGatewayOptions.mode = e.REDIRECT)),
                    (n.options.zoopModel =
                      window.document.getElementById('zoop-gateway-model')),
                    !0
                  );
                },
                eSignGateway: function (o) {
                  if (n.isNullUndefinedOrEmpty(o))
                    throw new Error(
                      'Gateway Transaction Id is mandatory to initiate gateway.',
                    );
                  (n.esignGatewayOptions.transaction_id = o),
                    (n.esignGatewayOptions.gateway_url =
                      n.options.eSignV4URL +
                      `/${n.esignGatewayOptions.version}/viewer`);
                  let t =
                    n.esignGatewayOptions.gateway_url +
                    '/' +
                    n.esignGatewayOptions.transaction_id +
                    '?show_download_btn=' +
                    n.esignGatewayOptions.show_download_btn +
                    '&mode=' +
                    n.esignGatewayOptions.mode +
                    '&zoom_level=' +
                    n.esignGatewayOptions.zoomLevel +
                    '&v=4.3.0';
                  n.esignGatewayOptions.mode === e.TAB
                    ? null == n.options.zoopWindow ||
                      n.options.zoopWindow.closed
                      ? (n.options.zoopWindow = window.open(
                          encodeURI(t),
                          '_blank',
                        ))
                      : n.options.zoopWindow.focus()
                    : (n.esignGatewayOptions.mode,
                      e.REDIRECT,
                      (window.location = encodeURI(t)));
                },
                initDigilockerGateway: function (o = {}) {
                  o.mode &&
                    o.mode.toUpperCase() === e.REDIRECT &&
                    (n.digilockerGatewayOption.mode = e.REDIRECT);
                },
                openDigilockerGateway: function (o) {
                  n.digilockerGatewayOption.request_id = o;
                  const t = `${n.digilockerGatewayOption.gatewayURL}/start/${n.digilockerGatewayOption.request_id}?mode=${n.digilockerGatewayOption.mode}`;
                  n.digilockerGatewayOption.mode === e.REDIRECT &&
                    (window.location = encodeURI(t)),
                    n.digilockerGatewayOption.mode === e.TAB &&
                      (null == n.options.zoopWindow ||
                      n.options.zoopWindow.closed
                        ? (n.options.zoopWindow = window.open(
                            encodeURI(t),
                            '_blank',
                          ))
                        : n.options.zoopWindow.focus());
                },
                initLivenessGateway: function (e = {}) {
                  n.livenessGatewayOption.mode = e.mode;
                },
                openLivenessGateway: function (o) {
                  n.livenessGatewayOption.request_id = o;
                  const t = `${n.livenessGatewayOption.gatewayURL}/${n.livenessGatewayOption.request_id}?mode=${n.livenessGatewayOption.mode}`;
                  if (n.livenessGatewayOption.mode !== e.REDIRECT)
                    throw new Error('only REDIRECT mode is supported');
                  window.location = encodeURI(t);
                },
                initStudentSDKGateway: function (e = {}) {
                  n.studentSDKGatewayOption.mode = e.mode;
                },
                openStudentSDKGateway: function (o) {
                  n.studentSDKGatewayOption.request_id = o;
                  const t = `${n.studentSDKGatewayOption.gatewayURL}/${n.studentSDKGatewayOption.request_id}/`;
                  if (n.studentSDKGatewayOption.mode !== e.REDIRECT)
                    throw new Error('only REDIRECT mode is supported');
                  window.location = encodeURI(t);
                },
                initOcrSDKGateway: function (e = {}) {
                  n.ocrSDKGatewayOption.mode = e.mode;
                },
                openOcrSDKGateway: function (o) {
                  n.studentSDKGatewayOption.request_id = o;
                  const t = `${n.ocrSDKGatewayOption.gatewayURL}/${n.ocrSDKGatewayOption.request_id}/`;
                  if (n.ocrSDKGatewayOption.mode !== e.REDIRECT)
                    throw new Error('only REDIRECT mode is supported');
                  window.location = encodeURI(t);
                },
              };
            const i = {
              close: () => {},
              'consent-denied': () => {},
              'otp-error': () => {},
              'gateway-error': () => {},
              'esign-result': () => {},
              'esign-success': () => {},
              'esign-error': () => {},
              'itd-error': () => {},
              'digilocker-error': () => {},
              'digilocker-success': () => {},
              'liveness-success': () => {},
              'liveness-failure': () => {},
              'liveness-timeout': () => {},
              'liveness-error': () => {},
              'liveness-internal-server-error': () => {},
              'liveness-invalid-reqid': () => {},
              'liveness-session-expired': () => {},
            };
            (n.on = function (e = '', o = () => {}) {
              if ('string' != typeof e)
                throw new Error('Event name must be a string.');
              if ('function' != typeof o)
                throw new Error('Callback must be a function.');
              Object.keys(i).includes(e)
                ? (i[e] = o)
                : console.warn(`No event found named ${e}`);
            }),
              (n.emit = function (e, o) {
                i[e](o);
              }),
              (n.dispatchEvent = function (e) {
                let o;
                if (
                  ![
                    n.options.url,
                    n.options.production,
                    n.options.staging,
                    n.options.eSignV4URL,
                    n.options.digilockerV1URL,
                    n.options.livenessV1URL,
                  ].some((o) => o.startsWith(e.origin))
                )
                  return console.log('Message is not from Zoop Gateway');
                if (e.data && ((o = e.data), o.hasOwnProperty('action')))
                  switch (o.action) {
                    case 'close':
                    case 'consent-denied':
                    case 'otp-error':
                    case 'gateway-error':
                    case 'esign-result':
                    case 'esign-success':
                    case 'esign-error':
                    case 'digilocker-error':
                    case 'digilocker-success':
                    case 'liveness-failure':
                    case 'liveness-timeout':
                    case 'liveness-error':
                    case 'liveness-internal-server-error':
                    case 'liveness-invalid-reqid':
                    case 'liveness-session-expired':
                      return (
                        (o.payload =
                          'string' == typeof o.payload
                            ? JSON.parse(o.payload)
                            : o.payload),
                        (n.options.zoopModel.style.display = 'none'),
                        (window.document.getElementById(
                          'zoop-model-content',
                        ).innerHTML = ''),
                        n.options.zoopWindow &&
                          !n.options.zoopWindow.closed &&
                          n.options.zoopWindow.close(),
                        void n.emit(o.action, o)
                      );
                    default:
                      console.warn('Unsupported event: ', o.action);
                  }
              }),
              n.setStyles('zoop-gateway-model', t.zoopGateWayModel, 'id'),
              n.setStyles('zoop-model-content', t.zoopModelContent, 'id'),
              window.addEventListener('message', n.dispatchEvent, !1),
              (window.zoop = n);
          })();
        })();
      })();
    </script>
    <script type="application/javascript">
      function openGateway() {
        zoop.eSignGatewayInit({ mode: 'REDIRECT', zoomLevel: 1 });
        zoop.eSignGateway('##ID##');
      }
      openGateway();
    </script>
  </body>
</html>
