import { Injectable } from '@nestjs/common';
import { k500Error } from 'src/constants/misc';
import { kParamMissing } from 'src/constants/responses';
import { ExperianScoreEntity } from 'src/entities/experianScore.entity';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { TypeService } from 'src/utils/type.service';

@Injectable()
export class ExperianService {
  constructor(
    // repo
    private readonly repoManager: RepositoryManager,
    private readonly typeService: TypeService,
  ) {}

  async getLatestExperianData(reqData) {
    const userIds = reqData?.userIds;
    if (!userIds.length) return kParamMissing('userId');

    const attribtues = [
      'id',
      'experianScore',
      'overdueAccounts',
      'inquiryPast30Days',
      'overdueAmount',
      'totalDelayDays',
      'PLAccounts',
      'PLOutstanding',
      'totalOutstanding',
      'experianFetchDate',
    ];
    const options = { where: { userId: userIds }, order: [['id', 'DESC']] };
    const experianData = await this.repoManager.getRowWhereData(
      ExperianScoreEntity,
      attribtues,
      options,
    );

    if (experianData == k500Error) throw new Error();
    if (!experianData) return {};
    const preparedData = this.prepareExperianData(experianData);
    return preparedData;
  }

  private prepareExperianData(data) {
    const transformedData = {
      'Experian Score': data.experianScore ?? '-',
      'Experian Refreshed Date':
        this.typeService.getDateFormated(data.experianFetchDate) ?? '-',
      'PL Account': data.PLAccounts ?? '-',
      'Overdue Account': data.overdueAccounts ?? '-',
      'Overdue Amount': data.overdueAmount ?? '-',
      'Delay Days': data.totalDelayDays ?? '-',
      'Enquiry In Past 30 Days': data.inquiryPast30Days ?? '-',
      'Total Outstanding Balance': data.totalOutstanding ?? '-',
      'PL Outstanding Balance': data.PLOutstanding ?? '-',
    };
    return transformedData;
  }
}
