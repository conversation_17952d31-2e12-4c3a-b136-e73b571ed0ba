import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { admin } from './admin.entity';

@Table({})
export class CollectionPerformanceReport extends Model<CollectionPerformanceReport> {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    allowNull: false,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: 'Date of Pool and Collection. This is Global Date(TZ format)',
  })
  dataDate: string;

  @ForeignKey(() => admin)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  adminId: number;

  @BelongsTo(() => admin, {
    foreignKey: 'adminId',
    targetKey: 'id',
    constraints: false,
  })
  adminData: admin;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  totalCasesCount: number;

  @Column({
    type: DataType.ARRAY(DataType.INTEGER),
    allowNull: true,
    defaultValue: [],
  })
  totalCasesData: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Current Unpaid EMI Amount',
  })
  totalEmiAmount: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Pool Bucket Delay Days(1-5) Wise',
    defaultValue: 0,
  })
  pool1to5: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Pool Bucket Delay Days(6-15) Wise',
    defaultValue: 0,
  })
  pool6to15: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Pool Bucket Delay Days(16-30) Wise',
    defaultValue: 0,
  })
  pool16to30: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Pool Bucket Delay Days(0-30) Wise',
    defaultValue: 0,
  })
  pool1to30: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Pool Bucket Delay Days(31-45) Wise',
    defaultValue: 0,
  })
  pool31to45: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Pool Bucket Delay Days(45-60) Wise',
    defaultValue: 0,
  })
  pool46to60: number;
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Pool Bucket Delay Days(31-60) Wise',
    defaultValue: 0,
  })
  pool31to60: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Pool Bucket Delay Days(61-90) Wise',
    defaultValue: 0,
  })
  pool61to90: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Pool Bucket Delay Days(91-120) Wise',
    defaultValue: 0,
  })
  pool91to120: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Pool Bucket Delay Days(121-150) Wise',
    defaultValue: 0,
  })
  pool121to150: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Pool Bucket Delay Days(151-180) Wise',
    defaultValue: 0,
  })
  pool151to180: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Pool Bucket Delay Days(91-180) Wise',
    defaultValue: 0,
  })
  pool91to180: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Pool Bucket Delay Days(180+) Wise',
    defaultValue: 0,
  })
  pool180Plus: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Pool of Entry Date(It is Single Day Pool When Entry is Added)',
    defaultValue: 0,
  })
  todayPool: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Collection Bucket Delay Days(1-5) Wise',
    defaultValue: 0,
  })
  collection1to5: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Collection Bucket Delay Days(6-15) Wise',
    defaultValue: 0,
  })
  collection6to15: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Collection Bucket Delay Days(16-30) Wise',
    defaultValue: 0,
  })
  collection16to30: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Collection Bucket Delay Days(0-30) Wise',
    defaultValue: 0,
  })
  collection1to30: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Collection Bucket Delay Days(31-45) Wise',
    defaultValue: 0,
  })
  collection31to45: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Collection Bucket Delay Days(45-60) Wise',
    defaultValue: 0,
  })
  collection46to60: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Collection Bucket Delay Days(30-60) Wise',
    defaultValue: 0,
  })
  collection31to60: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Collection Bucket Delay Days(61-90) Wise',
    defaultValue: 0,
  })
  collection61to90: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Collection Bucket Delay Days(91-120) Wise',
    defaultValue: 0,
  })
  collection91to120: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Collection Bucket Delay Days(121-150) Wise',
    defaultValue: 0,
  })
  collection121to150: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Collection Bucket Delay Days(151-180) Wise',
    defaultValue: 0,
  })
  collection151to180: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Collection Bucket Delay Days(91-180) Wise',
    defaultValue: 0,
  })
  collection91to180: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Collection Bucket Delay Days(180+) Wise',
    defaultValue: 0,
  })
  collection180Plus: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Collection Amount of Current Amount',
    defaultValue: 0,
  })
  todayCollection: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Month to Date Collection Amount',
    defaultValue: 0,
  })
  mtdCollection: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: 'Average of mtdCollection till Date',
    defaultValue: 0,
  })
  mtdAverage: number;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
    defaultValue: [],
  })
  todayLoanAndDPDData: any;
}
