import { Table, Column, Model, DataType } from 'sequelize-typescript';

@Table({})
export class shiftPlanEntity extends Model<shiftPlanEntity> {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    allowNull: false,
  })
  id: number;

  @Column({
    type: DataType.SMALLINT,
    allowNull: true,
  })
  roleId: number;

  @Column({
    type: DataType.TIME,
    allowNull: true,
  })
  startTime: string;

  @Column({
    type: DataType.TIME,
    allowNull: true,
  })
  endTime: string;

  @Column({
    type: DataType.ARRAY(DataType.INTEGER),
    allowNull: true,
  })
  employees: any;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  updatedBy: number;

  @Column({
    type: DataType.ENUM,
    values: ['0', '1'],
    defaultValue: '1',
    comment: '0 for Not Active 1 for Active.',
  })
  isActive: string;
}
