import { Injectable } from '@nestjs/common';
import { Op } from 'sequelize';
import { MAX_BEARING_LIMIT, MAX_LAT_LIMIT } from 'src/constants/globals';
import { k500Error } from 'src/constants/misc';
import {
  k422ErrorMessage,
  kBadRequest,
  kInternalError,
  kParamMissing,
  kSuccessData,
} from 'src/constants/responses';
import { kNoDataFound, kServiceUnavailableRoute } from 'src/constants/strings';
import { MasterEntity } from 'src/entities/master.entity';
import { RedisService } from 'src/redis/redis.service';
import { GoogleCordinatesRepository } from 'src/repositories/googleCordinates.repository';
import { LocationRepository } from 'src/repositories/location.repository';
import { MasterRepository } from 'src/repositories/master.repository';
import { UserRepository } from 'src/repositories/user.repository';
import { DefaulterSharedService } from 'src/shared/defaulter.shared.service';
import { APIService } from 'src/utils/api.service';
import { ErrorContextService } from 'src/utils/error.context.service';
import { TypeService } from 'src/utils/type.service';
import { LocationService } from 'src/admin/location/location.service';
import { NUMBERS } from 'src/constants/numbers';

@Injectable()
export class LocationServiceV4 {
  constructor(
    private readonly defaulterService: DefaulterSharedService,
    private readonly apiService: APIService,
    private readonly masterRepo: MasterRepository,
    private readonly repository: LocationRepository,
    private readonly typeService: TypeService,
    private readonly userRepo: UserRepository,
    private readonly googleCordinatesRepo: GoogleCordinatesRepository,
    private readonly errorContextService: ErrorContextService,
    private readonly redisService: RedisService,
    private readonly locationService: LocationService,
  ) {}

  async syncData(reqData) {
    try {
      // Validate user ID
      const userId = reqData?.userId;
      if (!userId) return kParamMissing('userId');

      // Extract latitude and longitude
      const latitude = reqData?.lat ?? reqData?.locationFromIp?.latitude;
      const longitude = reqData?.long ?? reqData?.locationFromIp?.longitude;
      if (!latitude || !longitude || latitude == 0 || longitude == 0)
        return kSuccessData;

      // handle duplicate locationPoint request
      const locationPointRedisKey = `${userId}_LOCATION_POINT_${latitude}_${longitude}`;
      const isNewLocationPointData =
        await this.redisService.setIfNotExistsWithNX(
          locationPointRedisKey,
          NUMBERS.TEN_MINUTES_IN_SECONDS,
        );
      if (!isNewLocationPointData) return {};

      // Fetch location data
      const locationDataResponse: any = await this.finalLatLongAddress(
        latitude,
        longitude,
      );
      if (
        !locationDataResponse ||
        locationDataResponse?.finalData?.message ||
        !locationDataResponse?.bearing
      ) {
        throw new Error('Invalid or missing location data.');
      }

      // Parse location data
      const locationData = locationDataResponse?.finalData[0]
        ? JSON.parse(locationDataResponse?.finalData[0])
        : {};

      const bearing = locationDataResponse?.bearing || 0;
      // Build location object
      const newLocationObj = this.buildLocationObject(
        userId,
        reqData,
        locationData,
        bearing,
        latitude,
        longitude,
      );

      // handle duplicate locationData request
      const locationDataRedisKey = `${userId}_LOCATION_DATA_${newLocationObj.location}`;
      const isNewLocationData = await this.redisService.setIfNotExistsWithNX(
        locationDataRedisKey,
        NUMBERS.TEN_MINUTES_IN_SECONDS,
      );
      if (!isNewLocationData) return {};

      // Set country name for IP-based location
      this.setCountryNameIfMissing(newLocationObj, reqData);

      // Handle non-India country and loan status
      if (newLocationObj?.address?.countryName !== 'India') {
        const loanStatus = await this.masterRepo.getRowWhereData(['id'], {
          where: { 'status.loan': 6, userId },
        });
        if (!loanStatus) return { continueRoute: kServiceUnavailableRoute };
      }

      // Update user's latest location
      const updateResponse: any = await this.syncLatestLocationForUserProfile(
        newLocationObj,
      );
      if (updateResponse?.message) return updateResponse;

      // Create location data row
      const createdData = await this.repository.createRowData(newLocationObj);
      if (!createdData || createdData === k500Error) {
        throw new Error('Error creating row data.');
      }

      // Clear cache
      await this.redisService.del(`${userId}_USER_BASIC_DETAILS`);

      // Handle non-India route again if applicable
      if (newLocationObj?.address?.countryName !== 'India') {
        return { continueRoute: kServiceUnavailableRoute };
      }

      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private buildLocationObject(
    userId,
    reqData,
    locationData,
    bearing,
    latitude,
    longitude,
  ) {
    const { addressLine = '', locality = '' } = locationData || {};

    return {
      userId,
      location: addressLine || '',
      city: locality || '',
      lat: latitude || '',
      long: longitude || '',
      bearing,
      address: locationData || {},
      truecallerData: reqData?.locationFromIp
        ? {
            type: 'ipwhos',
            ip: reqData?.locationFromIp?.ip || '',
            country_code:
              reqData?.locationFromIp?.country_code ||
              reqData?.locationFromIp?.country ||
              '',
            region_code: reqData?.locationFromIp?.region_code || '',
            postal: reqData?.locationFromIp?.postal || '',
            city: reqData?.locationFromIp?.city?.toLowerCase() || '',
            citylatlong: `${reqData?.locationFromIp?.latitude || ''},${
              reqData?.locationFromIp?.longitude ?? ''
            }`,
            country: reqData?.locationFromIp?.country?.toLowerCase() || '',
          }
        : {},
    };
  }

  private setCountryNameIfMissing(newLocationObj, reqData) {
    if (!newLocationObj?.address?.countryName && reqData?.locationFromIp) {
      const location = reqData?.locationFromIp;

      if (location?.country_code && location?.country) {
        newLocationObj.address.countryName =
          location?.country_code?.toLowerCase() === 'in'
            ? 'India'
            : location?.country || '';
        newLocationObj.city = location?.city || '';
      } else if (location?.country) {
        newLocationObj.address.countryName =
          location?.country.toLowerCase() === 'in'
            ? 'India'
            : location?.country || '';
      }
    }
  }

  // checks if particular location exists for today for particular user
  private async checkIfAlreadyExists(reqData) {
    try {
      const bearing = reqData.bearing;
      const minBearingLimit = (+bearing - MAX_BEARING_LIMIT).toString();
      const maxBearingLimit = (+bearing + MAX_BEARING_LIMIT).toString();
      const minLatLimit = (reqData.lat - MAX_LAT_LIMIT).toString();
      const maxLatLimit = (reqData.lat + MAX_BEARING_LIMIT).toString();

      const todayDate = this.typeService.getGlobalDate(new Date()).toJSON();
      const range = this.typeService.getUTCDateRange(todayDate, todayDate);
      const options = {
        where: {
          userId: reqData.userId,
          bearing: {
            [Op.gte]: minBearingLimit,
            [Op.lte]: maxBearingLimit,
          },
          lat: {
            [Op.gte]: minLatLimit,
            [Op.lte]: maxLatLimit,
          },
          createdAt: {
            [Op.gte]: range.fromDate,
            [Op.lte]: range.endDate,
          },
        },
      };
      const attributes = ['id'];
      const isExists: any = await this.repository.getRowWhereData(
        attributes,
        options,
      );
      if (isExists == k500Error) return kInternalError;
      if (isExists) return k422ErrorMessage('Location already exists');
      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // Add last location data in master and user data for admin panel
  async syncLatestLocationForUserProfile(reqData) {
    try {
      const masterInclude: any = { model: MasterEntity };
      masterInclude.attributes = ['miscData'];
      const include = [masterInclude];
      const attributes = ['masterId', 'stage'];
      const userId = reqData.userId;
      const options = { include, where: { id: userId } };

      const userData = await this.userRepo.getRowWhereData(attributes, options);
      if (userData == k500Error) return kInternalError;
      if (!userData) return k422ErrorMessage(kNoDataFound);
      const masterData = userData.masterData ?? {};
      const masterId = userData.masterId;
      const miscData = masterData.miscData ?? {};
      miscData.lastLocation = reqData.location;
      miscData.lastLat = reqData.lat;
      miscData.lastLong = reqData.long;
      miscData.lastLocationDateTime = new Date().getTime();
      miscData.locationStage = userData?.stage;

      // Update master data
      let updatedData: any = { miscData };
      let updateResponse: any = await this.masterRepo.updateRowData(
        updatedData,
        masterId,
      );
      if (updateResponse == k500Error) return kInternalError;
      if (updateResponse[0] == 1) {
        const key = `${userId}_USER_PROFILE`;
        await this.redisService.del(key);
      }
      // Update user data
      updatedData = {
        city: reqData.city?.toLowerCase(),
        state: reqData.state?.toLowerCase(),
      };
      updateResponse = await this.userRepo.updateRowData(updatedData, userId);
      if (updateResponse == k500Error) return kInternalError;

      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async coordinatesToAddress(lat: number, lng: number, bearing = 0) {
    try {
      const apiParams = {
        latlng: `${lat},${lng}`,
        key: process.env.GOOGLE_MAP_API_KEY,
      };
      const googleRes = await this.apiService.get(
        process.env.GEOCODE_API_URL,
        apiParams,
      );
      if (!googleRes || googleRes === k500Error) return kInternalError;
      if (!googleRes.status || googleRes.status !== 'OK')
        return k422ErrorMessage('Google Map Service Unavailable!');
      if (!googleRes.results || googleRes.results.length === 0)
        return kNoDataFound;
      const finalAddrData = [];
      for (let i = 0; i < googleRes.results.length; i++) {
        try {
          const addressItem = googleRes.results[i];
          const editedAddrData: any =
            this.typeService._convertGoogleAddress(addressItem);
          if (!editedAddrData || editedAddrData === k500Error) continue;

          const coordinatesCreateObj = {
            lat: editedAddrData.coordinates.latitude,
            lng: editedAddrData.coordinates.longitude,
            bearing: this.typeService.getBearingFromLatLong(
              editedAddrData.coordinates.latitude,
              editedAddrData.coordinates.longitude,
            ),
            googleResponse: JSON.stringify(editedAddrData),
          };
          await this.googleCordinatesRepo.create(coordinatesCreateObj);

          const preciseObject: any = { ...coordinatesCreateObj };
          preciseObject.lat = lat;
          preciseObject.lng = lng;
          preciseObject.bearing = bearing;
          await this.googleCordinatesRepo.create(preciseObject);

          finalAddrData.push(JSON.stringify(editedAddrData));
          break;
        } catch (error) {
          this.errorContextService.throwAndSetCtxErr(error);
          console.error('coordinatesToAddress1 Error in: ', error);
        }
      }
      return finalAddrData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      console.error('coordinatesToAddress Error in: ', error);
      return kInternalError;
    }
  }
  async finalLatLongAddress(latitude, longitude) {
    try {
      const bearing = this.typeService.getBearingFromLatLong(
        latitude,
        longitude,
      );

      // Check if the location already exists
      let finalData: any = await this.locationService.getClosestLatLongAddress(
        latitude,
        longitude,
        bearing,
      );

      // Handle errors from the existing data check
      if (finalData === k500Error)
        throw new Error('Location data fetch failed or returned invalid data.');

      // If no existing data, fetch from coordinates
      if (!finalData.length) {
        finalData = await this.coordinatesToAddress(
          latitude,
          longitude,
          bearing,
        );
        if (!finalData || finalData === k500Error)
          throw new Error(
            'Location data fetch failed or returned invalid data.',
          );
      }

      return { finalData, bearing };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
}
