import * as crypto from 'crypto';
// Imports
import { Injectable } from '@nestjs/common';
import { AnyObject, Collection, Connection } from 'mongoose';
import { InjectConnection } from '@nestjs/mongoose';
import { EnvConfig } from 'src/configs/env.config';
import { k500Error } from 'src/constants/misc';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { CronAPILogger } from 'src/entities/cron_api_logger.schema';
import { GetAPILogger } from 'src/entities/schemas/get_api_logs_schema';
import { APILogger } from 'src/entities/api_logger.schema';
import { LeadTracking } from 'src/entities/schemas/leadTracking.schema';

@Injectable()
export class MongoQueryService {
  constructor(
    @InjectConnection(EnvConfig.database.mongodb.mongoFramesDBname)
    private readonly connection: Connection,
    private readonly repoManager: RepositoryManager,
  ) {}
  async insertData(data: any): Promise<any> {
    const cleanData = JSON.parse(JSON.stringify(data));
    const createdAt = new Date();
    const expireAt = new Date();
    expireAt.setDate(expireAt.getDate() + 31); // Set 31 day TTL
    cleanData['expireAt'] = expireAt;
    cleanData['createdAt'] = createdAt;
    const uuid = cleanData?.userId;
    const collection = this.getCollection(uuid);
    const final = await collection.insertOne(cleanData);
    return final;
  }

  async getAggData(userId: string, options: any): Promise<any> {
    try {
      const collection = this.getCollection(userId);
      if (!Array.isArray(options)) return k500Error;
      return await collection.aggregate(options).toArray();
    } catch (error) {
      return k500Error;
    }
  }

  //#region GET COLLECTION BY UUID PARTITION
  private getCollection(uuid: string): Collection<AnyObject> {
    const collectioName = this.getCollectionName(uuid);
    return this.connection.collection(collectioName);
  }

  getCollectionName(uuid: string) {
    const hash = crypto.createHash('sha256').update(uuid).digest();
    const partition = hash.readUInt32BE(0) % 10;
    return `frames_${partition}`;
  }
  //#endregion

  async udpdateElaspedTimeInMongo(mongoLoggerObj: any, elapsedTime: number) {
    try {
      const updateData = {
        elapsedTime,
        statusCode: mongoLoggerObj?.statusCode,
        resMessage: mongoLoggerObj?.resMessage,
      };
      await this.repoManager.createOrUpdateRowData(
        mongoLoggerObj.repositoryInc,
        updateData,
        mongoLoggerObj.logId,
      );
    } catch (error) {
      console.log(error);
    }
  }

  deserializeClassName(parsedObj: any) {
    let deserializedClass = null;
    const match = parsedObj.repositoryInc.match(/\[class (\w+)\]/);
    if (match) {
      const className = match[1];
      const classMap = {
        CronAPILogger,
        GetAPILogger,
        APILogger,
        LeadTracking,
      };
      deserializedClass = classMap[className];
    }
    return deserializedClass;
  }
}
