import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { k422ErrorMessage, kInternalError } from 'src/constants/responses';
import { TrackErrorMetricsRepository } from 'src/repositories/trackErrorMetrics.repository';
import { TrackStepCategoryRepository } from 'src/repositories/trackStepCategory.respository';
import { APIService } from 'src/utils/api.service';
import { steps } from './trackUtils';
import { k500Error } from 'src/constants/misc';
import { TrackStepMetricsRepository } from 'src/repositories/trackStepMetrics.repository';
import { DateService } from 'src/utils/date.service';
import { Op } from 'sequelize';
import { TrackErrorMetrics } from 'src/entities/trackErrorsMetrics.entity';
import { kNoDataFound } from 'src/constants/strings';
import { TrackStepCategory } from 'src/entities/trackStepCategory.entity';
import { TrackUserAttemptsRepository } from 'src/repositories/trackAttemptUser.repository';
import { TrackUserAttempts } from 'src/entities/trackUserAttempts.entity';

@Injectable()
export class TrackUserMetricsService {
  constructor(
    private readonly dateServices: DateService,
    private readonly trackErrorMetrics: TrackErrorMetricsRepository,
    private readonly trackStepCategory: TrackStepCategoryRepository,
    private readonly trackStepMetricsRepository: TrackStepMetricsRepository,
    private readonly trackUserAttempts: TrackUserAttemptsRepository,
  ) {}

  async updateStepMetrics() {
    const startObj = this.dateServices.getStartOfDay(new Date());
    const endObj = this.dateServices.getEndOfDay(new Date());

    const startDate = startObj.startOfDay;
    const endDate = endObj.endOfDay;

    const trackErrorInclude = {
      model: TrackErrorMetrics,
      attributes: [
        'id',
        'apiEndPoint',
        'errorMessage',
        'errorStatusCode',
        'stepId',
        'errorCount',
        'createdAt',
        'userId',
      ],
      where: {
        createdAt: {
          [Op.and]: {
            [Op.gte]: startDate,
            [Op.lt]: endDate,
          },
        },
      },
      required: false,
      order: [['id', 'DESC']],
    };

    const trackAttemptUserInclude = {
      model: TrackUserAttempts,
      attributes: ['id', 'apiEndPoint', 'stepId', 'attemptCount', 'userId'],
      where: {
        createdAt: {
          [Op.and]: {
            [Op.gte]: startDate,
            [Op.lt]: endDate,
          },
        },
      },
      required: false,
      order: [['id', 'DESC']],
    };

    const categoriesAttr = ['id', 'stepName', 'status', 'createdAt'];
    const categoriesOption = {
      where: {
        status: true,
      },
      include: [trackErrorInclude, trackAttemptUserInclude],
    };
    const todayCategories = await this.trackStepCategory.getTableWhereData(
      categoriesAttr,
      categoriesOption,
    );

    if (todayCategories == k500Error) return kInternalError;

    if (!todayCategories || todayCategories?.length === 0)
      return k422ErrorMessage(kNoDataFound);

    const categoriesErrorsCount = new Map(); // count for error
    const categoriesAttemptCount = new Map(); // count for user attempt

    todayCategories.forEach((categoriesRow) => {
      const errors = categoriesRow?.trackErrorData || [];
      const userAttempts = categoriesRow?.trackAttemptData || [];

      errors.forEach((row) => {
        if (!categoriesErrorsCount.has(row.stepId)) {
          categoriesErrorsCount.set(row.stepId, 0);
        }
        const count = categoriesErrorsCount.get(row.stepId);
        categoriesErrorsCount.set(row.stepId, count + row.errorCount);
      });

      userAttempts.forEach((row) => {
        if (!categoriesAttemptCount.has(row.stepId)) {
          categoriesAttemptCount.set(row.stepId, 0);
        }
        const count = categoriesAttemptCount.get(row.stepId);
        categoriesAttemptCount.set(row.stepId, count + row.attemptCount);
      });
    });

    for (const item of todayCategories) {
      const totalErrors = categoriesErrorsCount.get(item?.id) ?? 0;
      const totalAttempts = categoriesAttemptCount.get(item?.id) ?? 0;

      const payload = {
        stepId: item?.id,
        stepName: item?.stepName,
        totalErrors,
        totalAttempts,
      };

      await this.trackStepMetricsRepository.createRowData(payload);
    }
  }

  async errorMetricsData(reqData) {
    const { error, body, url, userId } = reqData;

    if (url.includes('admin/')) return false;
    let payload = {
      apiEndPoint: url,
      errorMessage: error?.response?.statusText || null,
      errorStatusCode: error?.response?.status || null,
      stepId: null,
      errorCount: 1,
      userId,
    };
    if (body) {
      payload.errorMessage = body?.message;
      payload.errorStatusCode = body?.statusCode;
    }

    const stepId: any = await this.checkStep(url, 'TRACK_ERROR');
    if (!stepId || stepId == k500Error) return kInternalError;
    payload.stepId = stepId;

    const startObj = this.dateServices.getStartOfDay(new Date());
    const endObj = this.dateServices.getEndOfDay(new Date());

    const startDate = startObj.startOfDay;
    const endDate = endObj.endOfDay;

    // check if error is already exist
    const attr = [
      'id',
      'apiEndPoint',
      'errorStatusCode',
      'stepId',
      'errorCount',
    ];
    let option: any = {
      where: {
        apiEndPoint: url,
        errorStatusCode: payload.errorStatusCode,
        stepId,
        errorMessage: payload.errorMessage,
        createdAt: {
          [Op.and]: {
            [Op.gte]: startDate,
            [Op.lt]: endDate,
          },
        },
      },
      order: [['id', 'DESC']],
    };

    if (userId) option.where.userId = userId;

    const isErrorExist = await this.trackErrorMetrics.getRowWhereData(
      attr,
      option,
    );

    if (isErrorExist == k500Error) return kInternalError;

    if (isErrorExist) {
      const updateData = { errorCount: isErrorExist?.errorCount + 1 };
      await this.trackErrorMetrics.updateRowData(updateData, isErrorExist?.id);
      return true;
    }

    await this.trackErrorMetrics.createRowData(payload);

    return true;
  }

  async checkStep(url, isFuncCallFor) {
    try {
      if (!url) return false;
      const urlStep = this.getStepCategory(url, isFuncCallFor);
      if (!urlStep) return false;

      const attr = ['id', 'status', 'stepName'];
      const option = { where: { stepName: urlStep, status: true } };
      const stepCategories = await this.trackStepCategory.getRowWhereData(
        attr,
        option,
      );

      if (!stepCategories || stepCategories == k500Error) return k500Error;

      return stepCategories?.id;
    } catch (err) {
      return k500Error;
    }
  }

  getStepCategory(url, isFuncCallFor) {
    let step;
    if (!url) return false;
    for (const key in steps) {
      if (isFuncCallFor === 'TRACK_ATTEMPT' && key === 'OTHER') continue;
      if (steps[key].includes(url)) {
        step = key;
      }
    }
    return step;
  }

  async getUserMetrics(body) {
    const {
      startDate = new Date(),
      endDate = new Date(),
      userId,
      stepId,
      limit = 20,
      isApiCallFor = 'TRACK_ERROR',
    } = body;

    const startObj = this.dateServices.getStartOfDay(new Date(startDate));
    const endObj = this.dateServices.getEndOfDay(new Date(endDate));

    const startDateTime = startObj.startOfDay;
    const endDateTime = endObj.endOfDay;

    const categoryInclude = {
      model: TrackStepCategory,
      attributes: ['id', 'stepName'],
      where: {
        status: true,
      },
    };

    if (isApiCallFor === 'TRACK_ERROR') {
      const trackErrorAttr = [
        'id',
        'apiEndPoint',
        'errorMessage',
        'errorStatusCode',
        'stepId',
        'errorCount',
        'createdAt',
        'userId',
      ];
      let trackErrorOption: any = {
        where: {
          createdAt: {
            [Op.and]: {
              [Op.gte]: startDateTime,
              [Op.lt]: endDateTime,
            },
          },
        },
        include: [categoryInclude],
        order: [['id', 'DESC']],
        limit,
      };

      if (userId) trackErrorOption.where.userId = userId;
      if (stepId) trackErrorOption.where.stepId = stepId;

      const trackErrors = await this.trackErrorMetrics.getTableWhereData(
        trackErrorAttr,
        trackErrorOption,
      );

      if (!trackErrors || trackErrors?.length === 0)
        return k422ErrorMessage(kNoDataFound);

      return trackErrors;
    } else {
      const trackAttemptAttr = [
        'id',
        'apiEndPoint',
        'reqBody',
        'stepId',
        'attemptCount',
        'createdAt',
        'userId',
      ];
      let trackAttemptOption: any = {
        where: {
          createdAt: {
            [Op.and]: {
              [Op.gte]: startDateTime,
              [Op.lt]: endDateTime,
            },
          },
        },
        include: [categoryInclude],
        order: [['id', 'DESC']],
        limit,
      };

      if (userId) trackAttemptOption.where.userId = userId;
      if (stepId) trackAttemptOption.where.stepId = stepId;

      const trackAttempt = await this.trackUserAttempts.getTableWhereData(
        trackAttemptAttr,
        trackAttemptOption,
      );

      if (!trackAttempt || trackAttempt?.length === 0)
        return k422ErrorMessage(kNoDataFound);

      return trackAttempt;
    }
  }

  async getCategoryMetrics(query) {
    const { startDate = new Date(), endDate = new Date() } = query;

    const startObj = this.dateServices.getStartOfDay(new Date(startDate));
    const endObj = this.dateServices.getEndOfDay(new Date(endDate));

    const startDateTime = startObj.startOfDay;
    const endDateTime = endObj.endOfDay;

    const trackErrorAttr = [
      'id',
      'stepId',
      'totalErrors',
      'totalAttempts',
      'createdAt',
    ];
    let trackErrorOption: any = {
      where: {
        createdAt: {
          [Op.and]: {
            [Op.gte]: startDateTime,
            [Op.lt]: endDateTime,
          },
        },
      },
      order: [['id', 'DESC']],
    };

    const trackErrors = await this.trackStepMetricsRepository.getTableWhereData(
      trackErrorAttr,
      trackErrorOption,
    );

    if (!trackErrors || trackErrors?.length === 0)
      return k422ErrorMessage(kNoDataFound);

    return trackErrors;
  }

  async userAttemptMetrics(reqData) {
    const { body, url, userId } = reqData;

    if (url.includes('admin/')) return false;

    if (url.includes('/v4/user/checknewusereligibleornot')) {
      const isExistSalariedkey = 'isSalaried' in body;
      const isExistSalaryModekey = 'salaryMode' in body;

      if (
        !isExistSalariedkey ||
        !isExistSalaryModekey ||
        !body.salary ||
        !body.userId
      )
        return false;
    }

    let payload = {
      apiEndPoint: url,
      reqBody: body || null,
      stepId: null,
      attemptCount: 1,
      userId,
    };
    if (body) payload.reqBody = body;

    const stepId: any = await this.checkStep(url, 'TRACK_ATTEMPT');
    if (!stepId || stepId == k500Error) return kInternalError;
    payload.stepId = stepId;

    const startObj = this.dateServices.getStartOfDay(new Date());
    const endObj = this.dateServices.getEndOfDay(new Date());

    const startDate = startObj.startOfDay;
    const endDate = endObj.endOfDay;

    // check if error is already exist
    const attr = ['id', 'apiEndPoint', 'stepId', 'attemptCount'];
    let option: any = {
      where: {
        apiEndPoint: url,
        stepId,
        createdAt: {
          [Op.and]: {
            [Op.gte]: startDate,
            [Op.lt]: endDate,
          },
        },
      },
      order: [['id', 'DESC']],
    };

    if (userId) option.where.userId = userId;

    const isAttemptExist = await this.trackUserAttempts.getRowWhereData(
      attr,
      option,
    );

    if (isAttemptExist == k500Error) return kInternalError;

    if (isAttemptExist) {
      let updateData: any = { attemptCount: isAttemptExist?.attemptCount + 1 };
      if (body) updateData.reqBody = body;

      await this.trackUserAttempts.updateRowData(
        updateData,
        isAttemptExist?.id,
      );
      return true;
    }

    await this.trackUserAttempts.createRowData(payload);

    return true;
  }
}
