// Imports
import { Table, Model, Column, DataType } from 'sequelize-typescript';

@Table({})
export class ProteanEntity extends Model<ProteanEntity> {
  @Column({
    type: DataType.STRING,
    allowNull: false,
    primaryKey: true,
  })
  hashPhone: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  phone: string;

  @Column({
    type: DataType.STRING(32),
    allowNull: true,
  })
  type: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
    defaultValue: {},
  })
  data: any;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  response: string;
}
