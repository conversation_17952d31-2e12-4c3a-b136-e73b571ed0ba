<html>

<head>
  <style type="text/css">
    html,
    body {
      margin: 0;
      padding: 0;
      font-family: Work Sans, sans-serif;
    }

    table {
      border-collapse: collapse;
      width: 800px;
      margin: auto;
    }

    th,
    td {
      padding: 10px 15px;
      border-collapse: separate;
      font-size: 12px;
    }

    .border-table th,
    .border-table td {
      border: 1px solid #e6e6e6;
      text-align: center;
    }

    .table-head {
      margin: 0 auto;
      text-align: center;
      font-weight: bold;
    }

    .cell-odd {
      background-color: #EAEAEA;
      color: #2F1518;
    }

    .cell-even {
      background-color: #F3F3F3;
      color: #2F1518;
    }

    .cell-end {
      background-color: #ECECEC;
    }
  </style>
</head>

<body>
  <div style=" margin: auto">
    <table class="master">
      <tr>
        <td style="
              text-align: center;
              padding: 10px 0 15px;
              height: 40px;
            ">
          <img width="125px"
            src={{nbfcLogo}}
            class="CToWUd" data-bit="iit" />
        </td>
      </tr>
       <tr>
        <td><div style='background-color: #2F1518; height: 2px;'></div></td>
      </tr>
      <tr>
        <td style="text-align: center; padding: 15px 0; justify-content: center">
          <div style="
                margin: 0 auto;
                text-align: center;
                border: 1px solid #cdcdcd;
                padding: 10px;
                border-radius: 8px;
                background-image: linear-gradient(to top, #f2f2f2, #ffffff);
                width: fit-content;
                font-weight: bold;
                font-size: 14px;
              ">
            Collection Summary : {{reportDate}}
          </div>
        </td>
      </tr>

      <!-- Daywise collection Summary table 1-->
      <tr>
        <td>
          <div class="table-head">DAYWISE COLLECTION SUMMARY</div>
        </td>
      </tr>

      <tr>
        <td style="padding-bottom: 0">
          <table style="width: 100%"> 
            <thead>
              <tr style="position: sticky; top: 0">
                {{#each dayWiseFields as |dayWiseField index|}}
                <th style="background-color: {{#cIf @index '%' 2}} #62393E {{else}} #2F1518 {{/cIf}};
                color: #fff; font-weight: bold; font-size: 14px;
                {{#cIf @first}} border: none;{{else}}border: none !important;{{/cIf}}
                {{#cIf @first}} width: 10%;{{else}}width: 15%;{{/cIf}}{{#cIf @first}}border-top-left-radius: 10px;{{/cIf}}
                  {{#cIf @last}}border-top-right-radius: 10px;{{/cIf}}">
                  {{dayWiseField}}
                </th>
                {{/each}}
              </tr>
            </thead>
          </table>
        </td>
      </tr>

      <tr>
        <td style="padding-top: 0">
          <div style="overflow-y: auto; max-height: 300px ;width: calc(100% - -12px);">
            <table class="border-table" style="width: 100%">
              <tbody>
                {{#each day_wise as |daySummary index|}}
                <tr {{#cIf @last}} class="cell-end" {{/cIf}}>
                  <td style="width: 10%" class="{{#cIf @index '%' 2}}cell-even{{else}}cell-odd{{/cIf}}">
                    {{daySummary.key}}
                  </td>
                  <td style="width: 15%">{{daySummary.[1-15]}}</td>
                  <td style="width: 15%">{{daySummary.[16-30]}}</td>
                  <td style="width: 15%">{{daySummary.[31-60]}}</td>
                  <td style="width: 15%">{{daySummary.[61-90]}}</td>
                  <td style="width: 15%">{{daySummary.[91+]}}</td>
                  <td class="cell-end" style="width: 15%">{{daySummary.Total}}</td>
                </tr>
                {{/each}}
              </tbody>
            </table>
          </div>
        </td>
      </tr>


      <!-- Agentwise collection Summary table (Total due Vs Recover)-->
      <tr>
        <td>
          <div class="table-head">
            AGENTWISE COLLECTION SUMMARY
            <span style="color: #7f7f7f; font-weight: normal">(Total due Vs Recover)</span>
          </div>
        </td>
      </tr>

      <tr>
        <td style="padding-top: 0">
          <div style="overflow: auto">
            <table class="border-table" style="width: 100%">
              <tr "position: sticky; top: 0">
                {{#each dayWiseFields as |dayWiseField index|}}
                <th style="background-color: {{#cIf @index '%' 2}} #62393E {{else}} #2F1518 {{/cIf}};
                color: #fff; font-weight: bold; font-size: 14px;{{#cIf @first}}height: 40px;{{/cIf}}{{#cIf @first}} border: none;{{else}}border: none !important;{{/cIf}}{{#cIf @first}}border-top-left-radius: 10px;{{/cIf}}
                  {{#cIf @last}}border-top-right-radius: 10px;{{/cIf}}" {{#cIf @index '>' 0}}colspan="2" {{/cIf}}>
                  {{dayWiseField}}
                </th>
                {{/each}}
              </tr>
              <!-- Name and Overdue/Recovered row -->
              <tr style="border-collapse: collapse; border: 1px solid #e6e6e6">
                <td class="cell-odd" style="text-align: center">Name</td>
                {{#each dayWiseFields as |dayWiseField index|}}
                {{#cIf @index '>' 0}}
                <td class="cell-even">Overdue</td>
                <td style="border-right: 1px solid #e6e6e6" class="cell-odd">Recovered</td>
                {{/cIf}}
                {{/each}}
              </tr>

              <tbody>
                {{#each day_agent_wise as |agentSummary|}}
                <tr {{#cIf @last}} class="cell-end" {{/cIf}}>
                  <td class="{{#cIf @index '%' 2}}cell-odd{{else}}cell-even{{/cIf}}">{{agentSummary.key}}</td>
                  {{#each agentSummary.[1-15] as |bucket|}}
                  <td>{{bucket}}</td>
                  {{/each}}
                  {{#each agentSummary.[16-30] as |bucket|}}
                  <td>{{bucket}}</td>
                  {{/each}}
                  {{#each agentSummary.[31-60] as |bucket|}}
                  <td>{{bucket}}</td>
                  {{/each}}
                  {{#each agentSummary.[61-90] as |bucket|}}
                  <td>{{bucket}}</td>
                  {{/each}}
                  {{#each agentSummary.[91+] as |bucket|}}
                  <td>{{bucket}}</td>
                  {{/each}}
                  {{#each agentSummary.Total as |total|}}
                  <td class="cell-end">{{total}}</td>
                  {{/each}}
                </tr>
                {{/each}}
              </tbody>
            </table>
          </div>
        </td>
      </tr>

      <!--Daywise Agent collection Summary table 2-->
      <tr>
        <td>
          <div class="table-head">DAYWISE AGENT COLLECTION SUMMARY</div>
        </td>
      </tr>

      <tr>
        <td style="padding-bottom: 0">
          <div style="overflow-y: auto;max-height:300px">
          <table class="border-table" style="width: 100%;">
            <thead>
              <tr style="top: 0;">
                {{#each agentNameFields as |agentNameFields index|}}
                <th style="background-color: {{#cIf index '%' 2}} #62393E {{else}} #2F1518 {{/cIf}};
          color: #fff; font-weight: bold; font-size: 14px; border: none; 
          {{#cIf @first}}height: 40px;{{/cIf}}
         width: 15%;{{#cIf @first}}border-top-left-radius: 10px;{{/cIf}}
                  {{#cIf @last}}border-top-right-radius: 10px;{{/cIf}}">
                  {{agentNameFields}}
                </th>
                {{/each}}
              </tr>
            </thead>

            <tbody>
              {{#each agent_wise.agent_wise_details as |data index|}}
              <tr{{#cIf @last}} class="cell-end" {{/cIf}}>
                {{#each data as |value|}}
                <td {{#cIf @first}}class="{{#cIf index '%' 2}}cell-even{{else}}cell-odd{{/cIf}}" {{/cIf}}{{#cIf
                  @last}}class="cell-end" {{/cIf}}>{{value}}</td>
                {{/each}}
      </tr>
      {{/each}}
      </tbody>
     </table>
    </div>
  </td>
  </tr>


  <!-- Footer -->
  <tr>
    <td>
  <tr style="text-align: center">
    <td style="
                    background-color: #2F1518;
                    color: #ffffff;padding: 10px 0 0 0;
                    
                  ">
      Partnered NBFC :
      <span style="font-weight: bold">
         {{nbfcName}}
      </span>
      / NBFC registration number :
      <span style="font-weight: bold"> {{nbfcRegisterationNumber}} </span>
    </td>
  </tr>
  <tr style="text-align: center">
    <td style="
                    background-color: #2F1518;
                    color: #ffffff;
                    padding:5px 0 10px 0;
                  ">

         {{nbfcAddress}}
    </td>
  </tr>
  </td>
  </tr>
  </table>
  </div>
</body>

</html>