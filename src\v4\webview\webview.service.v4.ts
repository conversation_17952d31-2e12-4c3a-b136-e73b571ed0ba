import { Injectable } from '@nestjs/common';
import { kInternalError } from 'src/constants/responses';
import { ResidenceSharedService } from 'src/shared/residence.service';
import { getCAMSBankStr, kGetOTPTriggers } from 'src/constants/objects';

@Injectable()
export class WebviewServiceV4 {
  constructor(
    private readonly residenceSharedService: ResidenceSharedService,
  ) {}

  async validateResponse(data: any) {
    try {
      const type = data.type ?? data.source?.type ?? '';
      if (type == 'cams')
        return await this.residenceSharedService.handleCamsFlow(data);
      if (type == 'SUBSCRIPTION')
        return await this.residenceSharedService.handleSubscriptionFlow(data);
    } catch (error) {
      console.error('Error in: ', error);
      return kInternalError;
    }
  }

  async getOTPTriggers(queryData: any) {
    try {
      const rawData = queryData?.data ?? {};
      const otp = queryData?.otp ?? '';
      const type = queryData?.type ?? '';

      const jsTriggers = kGetOTPTriggers(otp, type);
      let data: any = [];
      if (jsTriggers) data.push(jsTriggers);
      if (type == 'cams') {
        data.push(
          'checkOTPValidation=document.getElementsByTagName("p")[5].innerText#Invalid OTP! Try again.#Invalid OTP! Try again.',
        );
        data.push('defaultSeconds=15');
        data.push('isProcessing=false');
        data.push(
          `Delayed=>5000, for(let index = 0; index < document.getElementsByTagName("tr").length; index++) { if (document.getElementsByTagName("tr")[index].innerText.includes("${getCAMSBankStr(
            rawData.bankCode,
          )}")) { document.getElementsByTagName("tr")[index].click(); } }`,
        );
        data.push(
          `Delayed=>5500, document.getElementsByTagName("button")[0].click();`,
        );
        data.push(
          "Delayed=>12000, document.getElementsByTagName('button')[0].innerText = 'Continue'; document.getElementsByTagName('button')[1].remove(); targetElement = document.getElementsByTagName('button')[0]; document.getElementsByTagName('section')[0].append(targetElement);",
        );
      }

      return data;
    } catch (error) {
      console.error('Error in: ', error);
      return kInternalError;
    }
  }

  delay = (ms) => new Promise((res) => setTimeout(res, ms));
}
