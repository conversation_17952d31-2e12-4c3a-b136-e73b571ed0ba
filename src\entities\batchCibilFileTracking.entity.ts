// Imports
import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { admin } from './admin.entity';

@Table({})
export class BatchCibilFileTrackingEntity extends Model<BatchCibilFileTrackingEntity> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({ type: DataType.STRING, unique: true, allowNull: false })
  fileName: string;

  @Column({
    type: DataType.ENUM,
    values: ['3', '5'],
    allowNull: false,
  })
  tokenType: string;

  @Column({ type: DataType.INTEGER, allowNull: false })
  numberOfUser: number;

  @Column({ type: DataType.TEXT, allowNull: false })
  url: string;

  @Column({ type: DataType.DATE, allowNull: false })
  submissionDate: Date;

  @Column({ type: DataType.DATE, allowNull: false })
  batchOutputDate: Date;

  @ForeignKey(() => admin)
  @Column({ type: DataType.INTEGER, allowNull: false })
  uploadedBy: number;

  @BelongsTo(() => admin)
  adminData: admin;
}
