# Logics .

-> IDV - Identity eligibility
IDV 1.0.0 - For android users email will get auto verified as in android we are shwoing google popup of already logged in email addresses

-> AE - Amount eligibility
AE 1.0.0 - For new user minimum salary should be 27000
AE 1.0.1 - For repeater user minimum salary should be 13500

-> EC - Emi calculation
EC 1.0.0 - Reducing principal
EC 2.0.0 - Deviding equally ( Current )

-> CSC - Cibil score calculation
CSC 1.0.0 - Guarantor account should be in exception for all scenarios
CSC 1.1.0 - PL score all logic should be in exception for repeater with last minimum 3 consecutive loans

-> OCC - Overdue charges calculation
OCC 1.0.0 - For the emi which is past due and unpaid then ECS bounce will get charged

-> RPC -> Refund process calculation
RPC 1.0.0 - Cashfree autodebit refund not possible due to cashfree's policy
RPC 1.1.0 - ICICI UPI refund should be last priority for refund for settlement profit

# Jenkins testin
*Commit massege print on slack
Testing 

