// Imports
import { LeadService } from './lead.service';
import { Body, Controller, Get, Post, Query, Res } from '@nestjs/common';
import { kInternalError, kSuccessData } from 'src/constants/responses';

@Controller('admin/lead')
export class LeadController {
  constructor(private readonly service: LeadService) {}

  @Get('qualityLead')
  async funQualityLead(@Query() query, @Res() res) {
    try {
      const data = await this.service.qualityLead(query);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      return res.send(kInternalError);
    }
  }
}
