// Imports
import { UUID } from 'crypto';
import { Table, Column, Model, DataType } from 'sequelize-typescript';

@Table({})
export class PeriodicEntity extends Model<PeriodicEntity> {
  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    primaryKey: true,
    comment: 'sessionId - finvu, txnId - cams',
  })
  sessionId: string;

  @Column({
    allowNull: false,
    comment:
      '0 -> Awaiting response, 1 -> Response received, 2 -> Response rejected, 3 -> Validating response, 4 -> Journey completed',
    defaultValue: '0',
    type: DataType.SMALLINT,
  })
  status: number;

  @Column({
    allowNull: false,
    comment: '1 -> FINVU, 2 -> CAMS',
    type: DataType.SMALLINT,
  })
  source: string;

  @Column({
    allowNull: false,
    comment:
      '1 -> LEAD CONVERSATION, 2 -> Post Payment, 3 -> Salary Verification, 7 -> <PERSON>fa<PERSON> On Demand Balance check, 6 -> For Financial Summary',
    type: DataType.SMALLINT,
  })
  type: boolean;

  @Column({
    allowNull: true,
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    allowNull: true,
    defaultValue: {},
    type: DataType.JSONB,
  })
  data: any;

  @Column({ type: DataType.UUID, allowNull: true })
  consentId: UUID;
}
