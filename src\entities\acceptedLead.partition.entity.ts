// Imports
import { Model, Table } from 'sequelize-typescript';
import { Column, DataType } from 'sequelize-typescript';

@Table({})
export class AcceptedLead_0 extends Model<AcceptedLead_0> {
  @Column({
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
    type: DataType.UUID,
  })
  id: string;

  @Column({
    type: DataType.SMALLINT,
    comment:
      '0: Accepted, 1: In Process, 2: Rejected, 3: Disbursed, 4: Existing',
  })
  leadStatus: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0: <PERSON> Buddha, 1: <PERSON><PERSON><PERSON><PERSON>, 2: Switch<PERSON>yLoan, 3: LoanTap',
  })
  leadSource: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Salaried, 1: Self employed' })
  jobType: number;

  @Column({ type: DataType.SMALLINT })
  cibilScore: number;

  @Column({ type: DataType.SMALLINT })
  age: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Male, 1: Female' })
  gender: number;

  @Column({ type: DataType.DATEONLY })
  dob: string;

  @Column({ type: DataType.DATE })
  disbursementDate: Date;

  @Column({ type: DataType.INTEGER, allowNull: false })
  salary: number;

  @Column({ type: DataType.INTEGER })
  pincode: number;

  @Column({ type: DataType.DOUBLE, allowNull: true })
  disbursement_amount: number;

  @Column({ type: DataType.TEXT })
  fullName: string;

  @Column({ type: DataType.TEXT })
  email: string;

  @Column({ type: DataType.TEXT })
  state: string;

  @Column({ type: DataType.TEXT })
  phone: string;

  @Column({ type: DataType.TEXT })
  hashPhone: string;

  @Column({ type: DataType.TEXT })
  pan: string;

  @Column({ type: DataType.TEXT })
  hashPAN: string;

  @Column({ type: DataType.DATE })
  registration_date: Date;
}

@Table({})
export class AcceptedLead_1 extends Model<AcceptedLead_1> {
  @Column({
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
    type: DataType.UUID,
  })
  id: string;

  @Column({
    type: DataType.SMALLINT,
    comment:
      '0: Accepted, 1: In Process, 2: Rejected, 3: Disbursed, 4: Existing',
  })
  leadStatus: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0: Finance Buddha, 1: FintiFi, 2: SwitchMyLoan, 3: LoanTap',
  })
  leadSource: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Salaried, 1: Self employed' })
  jobType: number;

  @Column({ type: DataType.SMALLINT })
  cibilScore: number;

  @Column({ type: DataType.SMALLINT })
  age: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Male, 1: Female' })
  gender: number;

  @Column({ type: DataType.DATEONLY })
  dob: string;

  @Column({ type: DataType.DATE })
  disbursementDate: Date;

  @Column({ type: DataType.INTEGER, allowNull: false })
  salary: number;

  @Column({ type: DataType.INTEGER })
  pincode: number;

  @Column({ type: DataType.DOUBLE, allowNull: true })
  disbursement_amount: number;

  @Column({ type: DataType.TEXT })
  fullName: string;

  @Column({ type: DataType.TEXT })
  email: string;

  @Column({ type: DataType.TEXT })
  state: string;

  @Column({ type: DataType.TEXT })
  phone: string;

  @Column({ type: DataType.TEXT })
  hashPhone: string;

  @Column({ type: DataType.TEXT })
  pan: string;

  @Column({ type: DataType.TEXT })
  hashPAN: string;

  @Column({ type: DataType.DATE })
  registration_date: Date;
}

@Table({})
export class AcceptedLead_2 extends Model<AcceptedLead_2> {
  @Column({
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
    type: DataType.UUID,
  })
  id: string;

  @Column({
    type: DataType.SMALLINT,
    comment:
      '0: Accepted, 1: In Process, 2: Rejected, 3: Disbursed, 4: Existing',
  })
  leadStatus: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0: Finance Buddha, 1: FintiFi, 2: SwitchMyLoan, 3: LoanTap',
  })
  leadSource: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Salaried, 1: Self employed' })
  jobType: number;

  @Column({ type: DataType.SMALLINT })
  cibilScore: number;

  @Column({ type: DataType.SMALLINT })
  age: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Male, 1: Female' })
  gender: number;

  @Column({ type: DataType.DATEONLY })
  dob: string;

  @Column({ type: DataType.DATE })
  disbursementDate: Date;

  @Column({ type: DataType.INTEGER, allowNull: false })
  salary: number;

  @Column({ type: DataType.INTEGER })
  pincode: number;

  @Column({ type: DataType.DOUBLE, allowNull: true })
  disbursement_amount: number;

  @Column({ type: DataType.TEXT })
  fullName: string;

  @Column({ type: DataType.TEXT })
  email: string;

  @Column({ type: DataType.TEXT })
  state: string;

  @Column({ type: DataType.TEXT })
  phone: string;

  @Column({ type: DataType.TEXT })
  hashPhone: string;

  @Column({ type: DataType.TEXT })
  pan: string;

  @Column({ type: DataType.TEXT })
  hashPAN: string;

  @Column({ type: DataType.DATE })
  registration_date: Date;
}

@Table({})
export class AcceptedLead_3 extends Model<AcceptedLead_3> {
  @Column({
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
    type: DataType.UUID,
  })
  id: string;

  @Column({
    type: DataType.SMALLINT,
    comment:
      '0: Accepted, 1: In Process, 2: Rejected, 3: Disbursed, 4: Existing',
  })
  leadStatus: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0: Finance Buddha, 1: FintiFi, 2: SwitchMyLoan, 3: LoanTap',
  })
  leadSource: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Salaried, 1: Self employed' })
  jobType: number;

  @Column({ type: DataType.SMALLINT })
  cibilScore: number;

  @Column({ type: DataType.SMALLINT })
  age: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Male, 1: Female' })
  gender: number;

  @Column({ type: DataType.DATEONLY })
  dob: string;

  @Column({ type: DataType.DATE })
  disbursementDate: Date;

  @Column({ type: DataType.INTEGER, allowNull: false })
  salary: number;

  @Column({ type: DataType.INTEGER })
  pincode: number;

  @Column({ type: DataType.DOUBLE, allowNull: true })
  disbursement_amount: number;

  @Column({ type: DataType.TEXT })
  fullName: string;

  @Column({ type: DataType.TEXT })
  email: string;

  @Column({ type: DataType.TEXT })
  state: string;

  @Column({ type: DataType.TEXT })
  phone: string;

  @Column({ type: DataType.TEXT })
  hashPhone: string;

  @Column({ type: DataType.TEXT })
  pan: string;

  @Column({ type: DataType.TEXT })
  hashPAN: string;

  @Column({ type: DataType.DATE })
  registration_date: Date;
}

@Table({})
export class AcceptedLead_4 extends Model<AcceptedLead_4> {
  @Column({
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
    type: DataType.UUID,
  })
  id: string;

  @Column({
    type: DataType.SMALLINT,
    comment:
      '0: Accepted, 1: In Process, 2: Rejected, 3: Disbursed, 4: Existing',
  })
  leadStatus: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0: Finance Buddha, 1: FintiFi, 2: SwitchMyLoan, 3: LoanTap',
  })
  leadSource: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Salaried, 1: Self employed' })
  jobType: number;

  @Column({ type: DataType.SMALLINT })
  cibilScore: number;

  @Column({ type: DataType.SMALLINT })
  age: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Male, 1: Female' })
  gender: number;

  @Column({ type: DataType.DATEONLY })
  dob: string;

  @Column({ type: DataType.DATE })
  disbursementDate: Date;

  @Column({ type: DataType.INTEGER, allowNull: false })
  salary: number;

  @Column({ type: DataType.INTEGER })
  pincode: number;

  @Column({ type: DataType.DOUBLE, allowNull: true })
  disbursement_amount: number;

  @Column({ type: DataType.TEXT })
  fullName: string;

  @Column({ type: DataType.TEXT })
  email: string;

  @Column({ type: DataType.TEXT })
  state: string;

  @Column({ type: DataType.TEXT })
  phone: string;

  @Column({ type: DataType.TEXT })
  hashPhone: string;

  @Column({ type: DataType.TEXT })
  pan: string;

  @Column({ type: DataType.TEXT })
  hashPAN: string;

  @Column({ type: DataType.DATE })
  registration_date: Date;
}

@Table({})
export class AcceptedLead_5 extends Model<AcceptedLead_5> {
  @Column({
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
    type: DataType.UUID,
  })
  id: string;

  @Column({
    type: DataType.SMALLINT,
    comment:
      '0: Accepted, 1: In Process, 2: Rejected, 3: Disbursed, 4: Existing',
  })
  leadStatus: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0: Finance Buddha, 1: FintiFi, 2: SwitchMyLoan, 3: LoanTap',
  })
  leadSource: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Salaried, 1: Self employed' })
  jobType: number;

  @Column({ type: DataType.SMALLINT })
  cibilScore: number;

  @Column({ type: DataType.SMALLINT })
  age: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Male, 1: Female' })
  gender: number;

  @Column({ type: DataType.DATEONLY })
  dob: string;

  @Column({ type: DataType.DATE })
  disbursementDate: Date;

  @Column({ type: DataType.INTEGER, allowNull: false })
  salary: number;

  @Column({ type: DataType.INTEGER })
  pincode: number;

  @Column({ type: DataType.DOUBLE, allowNull: true })
  disbursement_amount: number;

  @Column({ type: DataType.TEXT })
  fullName: string;

  @Column({ type: DataType.TEXT })
  email: string;

  @Column({ type: DataType.TEXT })
  state: string;

  @Column({ type: DataType.TEXT })
  phone: string;

  @Column({ type: DataType.TEXT })
  hashPhone: string;

  @Column({ type: DataType.TEXT })
  pan: string;

  @Column({ type: DataType.TEXT })
  hashPAN: string;

  @Column({ type: DataType.DATE })
  registration_date: Date;
}

@Table({})
export class AcceptedLead_6 extends Model<AcceptedLead_6> {
  @Column({
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
    type: DataType.UUID,
  })
  id: string;

  @Column({
    type: DataType.SMALLINT,
    comment:
      '0: Accepted, 1: In Process, 2: Rejected, 3: Disbursed, 4: Existing',
  })
  leadStatus: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0: Finance Buddha, 1: FintiFi, 2: SwitchMyLoan, 3: LoanTap',
  })
  leadSource: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Salaried, 1: Self employed' })
  jobType: number;

  @Column({ type: DataType.SMALLINT })
  cibilScore: number;

  @Column({ type: DataType.SMALLINT })
  age: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Male, 1: Female' })
  gender: number;

  @Column({ type: DataType.DATEONLY })
  dob: string;

  @Column({ type: DataType.DATE })
  disbursementDate: Date;

  @Column({ type: DataType.INTEGER, allowNull: false })
  salary: number;

  @Column({ type: DataType.INTEGER })
  pincode: number;

  @Column({ type: DataType.DOUBLE, allowNull: true })
  disbursement_amount: number;

  @Column({ type: DataType.TEXT })
  fullName: string;

  @Column({ type: DataType.TEXT })
  email: string;

  @Column({ type: DataType.TEXT })
  state: string;

  @Column({ type: DataType.TEXT })
  phone: string;

  @Column({ type: DataType.TEXT })
  hashPhone: string;

  @Column({ type: DataType.TEXT })
  pan: string;

  @Column({ type: DataType.TEXT })
  hashPAN: string;

  @Column({ type: DataType.DATE })
  registration_date: Date;
}

@Table({})
export class AcceptedLead_7 extends Model<AcceptedLead_7> {
  @Column({
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
    type: DataType.UUID,
  })
  id: string;

  @Column({
    type: DataType.SMALLINT,
    comment:
      '0: Accepted, 1: In Process, 2: Rejected, 3: Disbursed, 4: Existing',
  })
  leadStatus: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0: Finance Buddha, 1: FintiFi, 2: SwitchMyLoan, 3: LoanTap',
  })
  leadSource: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Salaried, 1: Self employed' })
  jobType: number;

  @Column({ type: DataType.SMALLINT })
  cibilScore: number;

  @Column({ type: DataType.SMALLINT })
  age: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Male, 1: Female' })
  gender: number;

  @Column({ type: DataType.DATEONLY })
  dob: string;

  @Column({ type: DataType.DATE })
  disbursementDate: Date;

  @Column({ type: DataType.INTEGER, allowNull: false })
  salary: number;

  @Column({ type: DataType.INTEGER })
  pincode: number;

  @Column({ type: DataType.DOUBLE, allowNull: true })
  disbursement_amount: number;

  @Column({ type: DataType.TEXT })
  fullName: string;

  @Column({ type: DataType.TEXT })
  email: string;

  @Column({ type: DataType.TEXT })
  state: string;

  @Column({ type: DataType.TEXT })
  phone: string;

  @Column({ type: DataType.TEXT })
  hashPhone: string;

  @Column({ type: DataType.TEXT })
  pan: string;

  @Column({ type: DataType.TEXT })
  hashPAN: string;

  @Column({ type: DataType.DATE })
  registration_date: Date;
}

@Table({})
export class AcceptedLead_8 extends Model<AcceptedLead_8> {
  @Column({
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
    type: DataType.UUID,
  })
  id: string;

  @Column({
    type: DataType.SMALLINT,
    comment:
      '0: Accepted, 1: In Process, 2: Rejected, 3: Disbursed, 4: Existing',
  })
  leadStatus: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0: Finance Buddha, 1: FintiFi, 2: SwitchMyLoan, 3: LoanTap',
  })
  leadSource: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Salaried, 1: Self employed' })
  jobType: number;

  @Column({ type: DataType.SMALLINT })
  cibilScore: number;

  @Column({ type: DataType.SMALLINT })
  age: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Male, 1: Female' })
  gender: number;

  @Column({ type: DataType.DATEONLY })
  dob: string;

  @Column({ type: DataType.DATE })
  disbursementDate: Date;

  @Column({ type: DataType.INTEGER, allowNull: false })
  salary: number;

  @Column({ type: DataType.INTEGER })
  pincode: number;

  @Column({ type: DataType.DOUBLE, allowNull: true })
  disbursement_amount: number;

  @Column({ type: DataType.TEXT })
  fullName: string;

  @Column({ type: DataType.TEXT })
  email: string;

  @Column({ type: DataType.TEXT })
  state: string;

  @Column({ type: DataType.TEXT })
  phone: string;

  @Column({ type: DataType.TEXT })
  hashPhone: string;

  @Column({ type: DataType.TEXT })
  pan: string;

  @Column({ type: DataType.TEXT })
  hashPAN: string;

  @Column({ type: DataType.DATE })
  registration_date: Date;
}

@Table({})
export class AcceptedLead_9 extends Model<AcceptedLead_9> {
  @Column({
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
    type: DataType.UUID,
  })
  id: string;

  @Column({
    type: DataType.SMALLINT,
    comment:
      '0: Accepted, 1: In Process, 2: Rejected, 3: Disbursed, 4: Existing',
  })
  leadStatus: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0: Finance Buddha, 1: FintiFi, 2: SwitchMyLoan, 3: LoanTap',
  })
  leadSource: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Salaried, 1: Self employed' })
  jobType: number;

  @Column({ type: DataType.SMALLINT })
  cibilScore: number;

  @Column({ type: DataType.SMALLINT })
  age: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Male, 1: Female' })
  gender: number;

  @Column({ type: DataType.DATEONLY })
  dob: string;

  @Column({ type: DataType.DATE })
  disbursementDate: Date;

  @Column({ type: DataType.INTEGER, allowNull: false })
  salary: number;

  @Column({ type: DataType.INTEGER })
  pincode: number;

  @Column({ type: DataType.DOUBLE, allowNull: true })
  disbursement_amount: number;

  @Column({ type: DataType.TEXT })
  fullName: string;

  @Column({ type: DataType.TEXT })
  email: string;

  @Column({ type: DataType.TEXT })
  state: string;

  @Column({ type: DataType.TEXT })
  phone: string;

  @Column({ type: DataType.TEXT })
  hashPhone: string;

  @Column({ type: DataType.TEXT })
  pan: string;

  @Column({ type: DataType.TEXT })
  hashPAN: string;

  @Column({ type: DataType.DATE })
  registration_date: Date;
}
