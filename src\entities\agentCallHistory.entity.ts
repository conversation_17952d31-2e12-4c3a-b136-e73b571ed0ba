import { Model, Table } from 'sequelize-typescript';
import { Column, DataType } from 'sequelize-typescript';

@Table({})
export class AgentCallHistoryEntity extends Model<AgentCallHistoryEntity> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  })
  isCallFromAgent: boolean;

  @Column({
    type: DataType.SMALLINT,
    allowNull: false,
    defaultValue: -1,
    comment:
      '-1 for not answered call, 0 for miss call, 1 for answered call, 2 for miss call due to busy with other call',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    allowNull: true,
    comment: 'actual talk time',
  })
  duration: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  adminId: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  callStartTime: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  callEndTime: Date;

  @Column({
    type: DataType.UUID,
    allowNull: true,
  })
  userId: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  customerFullName: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  agentHashPhone: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  customerHashPhone: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  agentPhone: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  customerPhone: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  recordingURL: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  })
  isAgentOnShift: boolean;
}
