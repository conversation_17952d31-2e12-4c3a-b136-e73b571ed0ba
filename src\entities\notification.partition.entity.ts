// Imports
import { Model, Table } from 'sequelize-typescript';
import { Column, DataType } from 'sequelize-typescript';

@Table({})
export class Notification_0 extends Model<Notification_0> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;
}

@Table({})
export class Notification_1 extends Model<Notification_1> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;
}

@Table({})
export class Notification_2 extends Model<Notification_2> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;
}

@Table({})
export class Notification_3 extends Model<Notification_3> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;
}

@Table({})
export class Notification_4 extends Model<Notification_4> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;
}

@Table({})
export class Notification_5 extends Model<Notification_5> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;
}

@Table({})
export class Notification_6 extends Model<Notification_6> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;
}

@Table({})
export class Notification_7 extends Model<Notification_7> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;
}

@Table({})
export class Notification_8 extends Model<Notification_8> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;
}

@Table({})
export class Notification_9 extends Model<Notification_9> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.UUID,
  })
  userId: string;

  @Column({
    type: DataType.SMALLINT,
    defaultValue: 0,
    comment: '0=Sent, 2=Done, 3=Process, 4=Reject, 5=Received',
  })
  status: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0=unread 1=read',
    defaultValue: 0,
  })
  notificationFlag: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @Column({
    type: DataType.TEXT,
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  refrenceId: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    defaultValue: 'system',
  })
  sentBy: string;
}
