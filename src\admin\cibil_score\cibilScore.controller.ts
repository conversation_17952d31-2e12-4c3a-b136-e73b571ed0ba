import {
  Controller,
  Get,
  Post,
  Query,
  Body,
  Res,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { kInternalError, kSuccessData } from 'src/constants/responses';
import { CIBILScoreService } from './cibilScore.service';
import { CIBILTxtToObjectService } from './cibilTxtToObject.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { kUploadFileObj } from 'src/constants/objects';
import { MigrationSharedService } from 'src/shared/migration.service';
import { CibilService } from 'src/shared/cibil.service';
import { ErrorContextService } from 'src/utils/error.context.service';

@Controller('admin/cibilScore')
export class CIBILScoreController {
  constructor(
    private readonly service: CIBILScoreService,
    private readonly txtToObjService: CIBILTxtToObjectService,
    private readonly migrationSharedService: MigrationSharedService,
    private readonly cibilService: CibilService,
    private readonly errorContextService: ErrorContextService,
  ) {}

  @Get('/getCIBILScore')
  async getCIBILScore(@Query() query, @Res() res) {
    try {
      const result = await this.service.getCIBILScore(query);
      if (result.message) return res.json(result);
      return res.json({ ...kSuccessData, data: result });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Get('/txtToExcel')
  async getTxtToExcel(@Query() query, @Res() res) {
    try {
      const result: any = await this.txtToObjService.getTxtToExcel(query);
      if (result?.message) return res.json(result);
      return res.json({ ...kSuccessData, data: result });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  // Add CIBIL Trigger Data from Excel
  @Post('addCIBILTriggerData')
  @UseInterceptors(FileInterceptor('file', kUploadFileObj()))
  async addCIBILTriggerData(@UploadedFile() file, @Body() body, @Res() res) {
    try {
      body.file = file;
      const data: any = await this.service.addCIBILTriggerData(body);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }

  // Get CIBIL Trigger Data
  @Get('getCIBILTriggerData')
  async getCIBILTriggerData(@Query() query, @Res() res) {
    try {
      const result: any = await this.service.getCIBILTriggerData(query);
      if (result.message) return res.json(result);
      return res.json({ ...kSuccessData, data: result });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Post('cibilFetchDateMigrate')
  async funCibilFetchDateMigrate(@Body() body, @Res() res) {
    try {
      const result: any =
        await this.migrationSharedService.funCibilFetchDateMigrate(body);
      if (result.message) return res.json(result);
      return res.json({ ...kSuccessData, data: result });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Get('getLatestCibilData')
  async getLatestCibilData(@Query() query, @Res() res) {
    try {
      const data: any = await this.cibilService.getLatestCibilData(query);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Get('getAllCibilDataUserWise')
  async getAllCibilDataUserWise(@Query() query, @Res() res) {
    try {
      const data: any = await this.cibilService.getAllCibilDataUserWise(query);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Get('getCibilDataIdWise')
  async getCibilDataIdWise(@Query() query, @Res() res) {
    try {
      const data: any = await this.cibilService.getCibilDataIdWise(query);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }
  @Get('migrateOldestOpenandEnquiryDate')
  async funMigrateOldestOpenandEnquiryDate(@Query() query, @Res() res) {
    try {
      const data: any =
        await this.cibilService.migrateOldestOpenandEnquiryDate();
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  // @Post("getEligibleCibilData")
  // async funcGetElogibleCibilData(@Body() body, @Res() res) {
  //   try {
  //     const data: any = await this.service.getCibilEligibleData(body)
  //     if (data?.message) return res.json(data);
  //     return res.json({ ...kSuccessData, data });
  //   } catch (error) {

  //
  //     return res.json(kInternalError);
  //   }
  // }

  @Post('updateInputCibilUser')
  async updateInputCibilUser(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.funUpdateCibilInputUser(body);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Get('migrateExistingInput')
  async migrateExistingInput(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.migrateExistingData();
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }
}
