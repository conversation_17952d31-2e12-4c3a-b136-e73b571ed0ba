// Imports
import { Injectable } from '@nestjs/common';
import { Op, Sequelize } from 'sequelize';
import { k500Error } from 'src/constants/misc';
import {
  k422ErrorMessage,
  kInternalError,
  kParamMissing,
  kParamsMissing,
} from 'src/constants/responses';
import {
  UAT_PHONE_NUMBER,
  gIsPROD,
  SYSTEM_ADMIN_ID,
  GLOBAL_RANGES,
  LSP_APP_LINK,
  NBFC_APP_LINK,
  GLOBAL_FLOW,
} from 'src/constants/globals';
import { DownloaAppTrackRepo } from 'src/repositories/downloadTrack.repository';
import { UserRepository } from 'src/repositories/user.repository';
import { TypeService } from 'src/utils/type.service';
import { CryptService } from 'src/utils/crypt.service';
import { KYCEntity } from 'src/entities/kyc.entity';
import { loanTransaction } from 'src/entities/loan.entity';
import { MasterEntity } from 'src/entities/master.entity';
import { employmentDetails } from 'src/entities/employment.entity';
import { BankingEntity } from 'src/entities/banking.entity';
import { SubScriptionEntity } from 'src/entities/subscription.entity';
import { esignEntity } from 'src/entities/esign.entity';
import { PAGE_LIMIT } from 'src/constants/globals';
import { CrmRepository } from 'src/repositories/crm.repository';
import {
  kNoDataFound,
  kPending,
  kUnder_Verification,
} from 'src/constants/strings';
import { SharedNotificationService } from 'src/shared/services/notification.service';
import { UserStage, kMonths } from 'src/constants/objects';
import { LoanRepository } from 'src/repositories/loan.repository';
import { TransactionRepository } from 'src/repositories/transaction.repository';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { SequelOptions } from 'src/interfaces/include.options';
import { WhatsAppService } from 'src/thirdParty/whatsApp/whatsApp.service';
import { RequestSupportRepository } from 'src/repositories/request_support.repository';
import { CallingService } from '../calling/calling.service';
import { TemplateRepository } from 'src/repositories/template.repository';
import { registeredUsers } from 'src/entities/user.entity';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { FileService } from 'src/utils/file.service';
import { CibilScoreEntity } from 'src/entities/cibil.score.entity';
import { EnvConfig } from 'src/configs/env.config';
import { DateService } from 'src/utils/date.service';
import { BatchCibilDataEntity } from 'src/entities/batchCibilData.entity';
import { BatchCibilFileTrackingEntity } from 'src/entities/batchCibilFileTracking.entity';
import { ErrorContextService } from 'src/utils/error.context.service';
import { ExperianScoreEntity } from 'src/entities/experianScore.entity';
import { ExperianSharedService } from 'src/shared/experian.service';
import { nDataCodes } from 'src/constants/network';
import { APIService } from 'src/utils/api.service';
@Injectable()
export class AnalysisService {
  constructor(
    private readonly downloadAppTrackRepo: DownloaAppTrackRepo,
    private readonly userRepo: UserRepository,
    private readonly requestSupportRepo: RequestSupportRepository,
    private readonly typeService: TypeService,
    private readonly cryptService: CryptService,
    private readonly callingService: CallingService,
    private readonly crmRepo: CrmRepository,
    private readonly sharedNotification: SharedNotificationService,
    private readonly loanRepository: LoanRepository,
    private readonly CommonSharedService: CommonSharedService,
    private readonly dateService: DateService,
    private readonly transactionRepository: TransactionRepository,
    private readonly whatsappService: WhatsAppService,
    private readonly templateRepo: TemplateRepository,
    private readonly repoManager: RepositoryManager,
    private readonly fileService: FileService,
    private readonly repo: RepositoryManager,
    private readonly errorContextService: ErrorContextService,
    private readonly experianSharedService: ExperianSharedService,
    private readonly api: APIService,
  ) {}

  async getUserAppDownloadReport(query) {
    try {
      let startDate = query?.startDate;
      let endDate = query?.endDate;
      if (!startDate || !endDate) return kParamsMissing;
      startDate = this.typeService.getGlobalDate(startDate);
      endDate = this.typeService.getGlobalDate(endDate);
      const downloadAtt: any = ['typeOfDevice', 'isRegistered', 'isExits'];
      const options = {
        where: {
          registeredDate: {
            [Op.gte]: startDate.toJSON(),
            [Op.lte]: endDate.toJSON(),
          },
        },
      };
      const userData: any = await this.downloadAppTrackRepo.getTableWhereData(
        downloadAtt,
        options,
      );
      if (userData == k500Error) return kInternalError;
      const preparedData = this.getFormateData(userData);
      if (preparedData.message) return preparedData;
      let finalData: any = [];
      for (let key in preparedData) {
        try {
          finalData.push({ typeOfDevice: key, ...preparedData[key] });
        } catch (error) {}
      }
      return finalData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  private getFormateData(data) {
    try {
      const formatedData: any = {};
      for (let i = 0; i < data.length; i++) {
        try {
          const user = data[i];
          if (formatedData[user.typeOfDevice]) {
            formatedData[user.typeOfDevice].totalUser += 1;
            if (user?.isExits)
              formatedData[user.typeOfDevice].alreadyRegistered += 1;
          } else {
            formatedData[user.typeOfDevice] = {
              totalUser: 1,
              alreadyRegistered: 0,
            };
            if (user?.isExits)
              formatedData[user.typeOfDevice].alreadyRegistered = 1;
          }
        } catch (error) {}
      }
      return formatedData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region getting users count stucked at stages
  async funGetUserStuckDataCounts(query) {
    try {
      ///Date filter
      const startDate = query?.startDate ?? new Date();
      const endDate = query?.endDate ?? new Date();
      let dateRange: any = this.typeService.getUTCDateRange(startDate, endDate);
      dateRange = { [Op.gte]: dateRange.fromDate, [Op.lte]: dateRange.endDate };
      query.dateRange = dateRange;
      query.counts = 'true';

      const inProCount = await this.allInProgressUserStuckData(query);
      if (inProCount.message) return inProCount;
      const regisCount = await this.getRegistrationStuckedData(query);
      if (regisCount?.message) return regisCount;
      const aadhaarCount = await this.getAadhaarStuckData(query);
      if (aadhaarCount?.message) return aadhaarCount;
      const missingBankStatement = await this.missingBankSatement(query);
      if (missingBankStatement?.message) return missingBankStatement;
      const qualityUserCount = await this.getQualityUserData(query);
      if (qualityUserCount?.message) return qualityUserCount;

      return {
        IN_PROGRESS: inProCount,
        REGISTRATION: regisCount,
        AADHAAR: aadhaarCount,
        MISSING_STATEMENT: missingBankStatement,
        QUALITY_USER: qualityUserCount,
      };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //common user options for userStuck count
  private commonUserOptionsForCounts(query, masterWhere) {
    try {
      masterWhere.updatedAt = query.dateRange;
      const masterInclude: any = {
        model: MasterEntity,
        attributes: ['id'],
        where: masterWhere,
        required: true,
      };
      const toDay = this.typeService.getGlobalDate(new Date());
      const userOptions: any = {
        where: {
          isBlacklist: { [Op.ne]: '1' },
          NextDateForApply: {
            [Op.or]: [{ [Op.lte]: toDay.toJSON() }, { [Op.eq]: null }],
          },
        },
        include: [masterInclude],
      };
      return userOptions;
    } catch (error) {}
  }

  //#region getting user stuck data
  async funGetUserStuckData(query) {
    const stage = query?.stage;
    if (!stage) return kParamMissing('stage');
    const startDate = query?.startDate ?? new Date();
    const endDate = query?.endDate ?? new Date();
    let dateRange: any = this.typeService.getUTCDateRange(startDate, endDate);
    dateRange = { [Op.gte]: dateRange.fromDate, [Op.lte]: dateRange.endDate };
    query.dateRange = dateRange;
    if (stage == 'REGISTRATION')
      return await this.getRegistrationStuckedData(query);
    else if (stage == 'AADHAAR') return await this.getAadhaarStuckData(query);
    else if (stage == 'MISSING_BANK')
      return await this.missingBankSatement(query);
    else if (stage == 'QUALITY') return await this.getQualityUserData(query);
    else if (stage == 'PREMIUM_USERS') return await this.getPremiumUsers(query);
    else return await this.getUsersInProgressData(query);
  }
  //#endregion

  async selfieStuckWhatsappMsg(query) {
    try {
      const endDate: any = new Date();
      const startDate: any = new Date(endDate);
      startDate.setDate(endDate.getDate() - 7);
      let dateRange: any = this.typeService.getUTCDateRange(startDate, endDate);
      dateRange = { [Op.gte]: dateRange.fromDate, [Op.lte]: dateRange.endDate };
      const queryData = {
        stage: 'IN_PROGRESS',
        subKey: 'SELFIE',
        key: 'PENDING',
        dateRange,
      };
      const data: any = await this.getSelfieStuckData(queryData);

      if (query?.list == 'true') return data;

      for (let i = 0; i < data.rows.length; i++) {
        try {
          const ele = data.rows[i];
          const userId = ele?.userId;
          const customerName = ele?.Name ?? 'User';
          const loanId = ele['Loan id'];
          const email = ele?.Email;
          let number = ele['Mobile number'];
          if (!gIsPROD) number = UAT_PHONE_NUMBER[i];
          const whatsappOption: any = {
            userId,
            customerName,
            loanId,
            email,
            number,
            title: 'Selfie stuck',
            requestData: 'Selfie_stuck',
            appLink: ele?.appType == '0' ? LSP_APP_LINK : NBFC_APP_LINK,
          };
          // await this.whatsappService.sendWhatsAppMessage(whatsappOption);
          this.whatsappService.sendWhatsAppMessageMicroService(whatsappOption);
        } catch (error) {
          this.errorContextService.throwAndSetCtxErr(error);
          return kInternalError;
        }
      }
      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async kycStuckWhatsappMsg(query) {
    try {
      const endDate: any = new Date();
      const startDate: any = new Date(endDate);
      startDate.setDate(endDate.getDate() - 7);
      let dateRange: any = this.typeService.getUTCDateRange(startDate, endDate);
      dateRange = { [Op.gte]: dateRange.fromDate, [Op.lte]: dateRange.endDate };
      const attributes = ['fullName', 'id', 'email', 'phone', 'appType'];
      const masterInclude: any = {
        model: MasterEntity,
        where: {
          [Op.and]: {
            'status.aadhaar': {
              [Op.or]: ['-1', '0', '2'],
            },
            'status.permission': '1',
            'status.phone': '1',
            'status.basic': '1',
            'status.selfie': '5',
            'status.pan': '5',
            'status.pin': '1',
            'status.email': '1',
            'status.personal': '1',
            'status.professional': '1',
            updatedAt: dateRange,
            'coolOffData.count': 0,
          },
        },
        attributes: ['loanId'],
      };
      const options: any = {
        include: masterInclude,
        where: {
          isBlacklist: '0',
        },
      };
      if (!gIsPROD) options.limit = 3;
      const data = await this.userRepo.getTableWhereDataWithCounts(
        attributes,
        options,
      );
      if (data === k500Error) return kInternalError;

      if (query?.list == 'true') return data;
      for (let i = 0; i < data.rows.length; i++) {
        try {
          const ele = data.rows[i];
          const userId = ele?.id;
          const customerName = ele?.fullName ?? 'User';
          const loanId = ele?.masterData?.loanId;
          const email = ele?.email;
          let number = this.cryptService.decryptPhone(ele?.phone);
          if (!gIsPROD) number = UAT_PHONE_NUMBER[i];
          const whatsappOption: any = {
            userId,
            customerName,
            loanId,
            email,
            number,
            title: 'Kyc stuck',
            requestData: 'Kyc_stuck',
            appLink: ele?.appType == '0' ? LSP_APP_LINK : NBFC_APP_LINK,
          };
          // await this.whatsappService.sendWhatsAppMessage(whatsappOption);
          this.whatsappService.sendWhatsAppMessageMicroService(whatsappOption);
        } catch (error) {
          console.log(error, 'check error>>>>>>>.');
          return kInternalError;
        }
      }
      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region get all in progress loan data
  async getUsersInProgressData(query) {
    const stage = query?.subKey ?? 'ALL';
    if (stage == 'EMPLOYMENT') return await this.getEmploymentStuckData(query);
    else if (stage == 'EXPRESSREAPPLY')
      return await this.getExpressReapplyStuckData(query);
    else if (stage == 'BANKSTATEMENT')
      return await this.getBankStatementStuckData(query);
    else if (stage == 'SELFIE') return await this.getSelfieStuckData(query);
    else if (stage == 'LOAN_ACCEPT')
      return await this.getLoanAcceptStuckData(query);
    else if (stage == 'PAN') return await this.getPanStuckData(query);
    else if (stage == 'ADD_REFERENCE')
      return await this.getReferenceStuckData(query);
    else if (stage == 'EMANDATE') return await this.getMandateStuckData(query);
    else if (stage == 'ESIGN') return await this.getEsignStuckData(query);
    else return await this.allInProgressUserStuckData(query);
  }
  //#endregion

  //get all inprogress userStuck
  async allInProgressUserStuckData(query) {
    try {
      const approved = [1, 3];
      const masterWhere: any = {
        updatedAt: query.dateRange,
        status: {
          // We need users who's loan is started but not approved yet
          loan: { [Op.notIn]: [-2, 2, 6, 7] },
          phone: { [Op.or]: approved },
          email: { [Op.or]: approved },
          permission: { [Op.or]: approved },
          basic: { [Op.or]: approved },
          personal: { [Op.or]: approved },
          professional: { [Op.or]: approved },
          selfie: { [Op.or]: [0, 1, 2, 3, 5] },
          disbursement: -1,
        },
      };
      if (query?.counts == 'true') {
        const opts = this.commonUserOptionsForCounts(query, masterWhere);
        const counts = await this.userRepo.getCountsWhere(opts);
        if (counts === k500Error) return kInternalError;
        return counts;
      }
      query.download = 'true';
      const options = this.commonUserOptions(query);
      return await this.getAllUserStuckDataQuery(options, 'inProgress', query);
    } catch (error) {}
  }

  //#region preparing user stuck data
  async prepareUserStuckData(users, stage?, reqDetails: any = {}, reqList?) {
    try {
      let remarkFlag: boolean;
      if (reqDetails?.CRMRemark == 'true' || reqDetails?.CRMRemark == 'false') {
        remarkFlag = true;
      }

      let crmList: any;
      if (!remarkFlag) crmList = await this.getLastCrmList(users);
      if (!crmList || crmList.message) crmList = [];

      const userIds = users.map((el) => el.id);

      const batchCibilList = await this.repoManager.getTableWhereData(
        BatchCibilDataEntity,
        ['id', 'userId', 'cibilScore', 'plScore', 'fileId'],
        { where: { userId: userIds }, order: [['id', 'DESC']] },
      );
      if (batchCibilList == k500Error) throw new Error();

      const fileIds = [...new Set(batchCibilList.map((el) => el.fileId))];
      const batchCibilFileList = await this.repoManager.getTableWhereData(
        BatchCibilFileTrackingEntity,
        ['id', 'batchOutputDate'],
        { where: { id: fileIds } },
      );
      if (batchCibilFileList == k500Error) throw new Error();

      users = await this.experianSharedService.getExperianData(users, userIds);

      let userLeadScore: any = [];
      if (users?.length > 0) {
        const userIdsForLead = users.map((user) => `'${user.id}'`).join(', ');
        userLeadScore = await this.performAnotherCurlAction([
          userIdsForLead,
        ]).catch((_) => {});
      }

      const finalData = [];
      for (let index = 0; index < users.length; index++) {
        try {
          const user = users[index];
          const appType = user?.masterData?.loanData?.appType ?? user?.appType;
          const loanCreatedAt = user?.masterData?.loanData?.createdAt
            ? this.typeService.dateTimeFormate(
                user?.masterData?.loanData?.createdAt,
              )
            : '-';
          const temp: any = {};
          const reqData = reqList.find((ele) => ele?.userId == user?.id);
          const activeResponse = this.typeService.getLastActiveUserTime(
            user?.lastOnlineTime,
          );
          const loan = user?.masterData?.loanData;
          const bankData = loan?.bankingData;
          let missingMonth = bankData?.dataOfMonth;
          if (missingMonth) {
            missingMonth = JSON.parse(missingMonth);
            missingMonth = Object.keys(missingMonth).find((ele) => {
              return missingMonth[ele] == true;
            });
          }
          let lastCrm;
          if (!remarkFlag) lastCrm = crmList.find((f) => f?.userId == user?.id);
          temp['userId'] = user?.id ?? '-';
          temp['Name'] = user?.fullName ?? '-';
          temp['Loan id'] = user?.masterData?.loanId ?? '-';
          temp['Mobile number'] =
            this.cryptService.decryptPhone(user?.phone) ?? '-';
          temp['Email'] = user?.email ?? '-';
          temp['Applied amount'] = +(loan?.netApprovedAmount ?? '0');
          temp['Completed loans'] = user?.completedLoans ?? 0;
          temp['Salary'] = user?.masterData?.otherInfo?.salaryInfo ?? '-';
          temp['Lead Score'] =
            userLeadScore?.output?.find((score) => score?.user_id == user.id)
              ?.lead_score || 0;
          temp['Assigned CSE'] =
            (
              await this.CommonSharedService.getAdminData(
                user?.masterData?.assignedCSE,
              )
            )?.fullName ?? '-';
          // Response pending from account aggregator
          if (reqDetails?.subKey == 'AA_RESPONSE') {
            temp['Attempts'] = bankData?.attempts ?? '1';
            temp['Action track'] = 'BANK VERIFICATION';
          } else if (bankData?.aaDataStatus == 1)
            temp['Action track'] = 'BANK VERIFICATION';
          else temp['Action track'] = this.getActionTrack(user.stage);

          const batchCibilData = batchCibilList.find(
            (el) => el.userId == user.id,
          );
          temp['Batch CIBIL'] = batchCibilData?.cibilScore ?? '-';
          temp['Batch PL'] = batchCibilData?.plScore ?? '-';
          const batchCibilFileData = batchCibilFileList.find(
            (el) => el.id == batchCibilData?.fileId,
          );
          const batchOutputDate = batchCibilFileData?.batchOutputDate;
          temp['Batch Date'] = batchOutputDate
            ? this.typeService.getDateFormated(batchOutputDate)
            : '-';
          temp['Experian Score'] = user?.experian?.experianScore ?? '-';
          temp['E-Overdue Accounts'] = user?.experian?.overdueAccounts ?? '-';
          temp['E-Overdue Amount'] = user?.experian?.overdueAmount ?? '-';
          temp['E-Inquiries(30D)'] = user?.experian?.inquiryPast30Days ?? '-';
          temp['Reg. Date'] =
            this.typeService.getDateFormatted(user?.createdAt) ?? '-';
          temp['Loan Created Date'] = loanCreatedAt;
          temp['AA Response'] = bankData?.aaDataStatus == 1 ? true : false;
          temp['Employment information'] =
            user?.masterData?.otherInfo?.employmentInfo === ''
              ? '-'
              : user?.masterData?.otherInfo?.employmentInfo ?? '-';
          temp['Error Meassage'] = reqData?.reason ?? '-';
          temp['Connect With'] = reqData?.connectVia ?? '-';
          temp['Platform'] = user?.typeOfDevice == '0' ? 'Android' : 'IOS';
          temp['Company name'] = user?.masterData?.empData?.companyName ?? '-';
          temp['Pan'] = user?.kycData?.panCardNumber ?? '-';
          temp['City'] = user?.city ?? '-';
          temp['Name as per bank'] = bankData?.name ?? '-';
          temp['Account number'] = bankData?.accountNumber ?? '-';
          temp['IFSC code'] = bankData?.ifsCode ?? '-';
          temp['Mandate Bank'] = bankData?.mandateBank ?? '-';
          temp['Emandate via'] = loan?.subscriptionData?.mode ?? '-';
          temp['Emandate type'] = loan?.subscriptionData?.subType ?? '-';
          temp['E-sign via'] = loan?.eSignData?.esign_mode ?? '-';
          temp['CRM date'] =
            remarkFlag && user?.lastCrm?.createdAt
              ? this.typeService.getDateFormatted(user?.lastCrm?.createdAt)
              : lastCrm?.createdAt
              ? this.typeService.getDateFormatted(lastCrm?.createdAt)
              : '-';
          temp['CRM created by'] =
            remarkFlag && user?.lastCrm?.adminName
              ? user?.lastCrm?.adminName
              : (await this.CommonSharedService.getAdminData(lastCrm?.adminId))
                  ?.fullName ?? '-';
          temp['CRM remark'] =
            remarkFlag && user?.lastCrm?.remark
              ? user?.lastCrm?.remark
              : lastCrm?.remark ?? '-';

          const statusObj = user?.masterData?.status;
          const rejection = user?.masterData?.rejection;
          const check =
            stage == 'inProgress'
              ? this.getInProgressStatusReason(statusObj, rejection)
              : this.checkStatusAndReason(stage, statusObj, rejection);
          temp['Reject reason'] = check?.reason ?? '-';

          temp['isOnline'] =
            activeResponse?.lastActiveAgoMinutes < 5 &&
            activeResponse?.lastActiveAgo != '';
          temp['Last Active ago'] =
            activeResponse?.lastActiveAgo == ''
              ? null
              : activeResponse?.lastActiveAgo;
          temp.appType = appType;
          temp['Verified salary'] =
            bankData?.salary ?? bankData?.otherDetails?.salary?.average ?? '-';
          temp['Status'] =
            stage == 'missing_bank' ? kPending : check?.status ?? '-';
          finalData.push(temp);
        } catch (error) {
          console.log({ error });
          this.errorContextService.throwAndSetCtxErr(error);
        }
      }
      finalData.sort((a, b) => b['Lead Score'] - a['Lead Score']);
      return finalData;
    } catch (error) {}
  }
  //#endregion

  private getActionTrack(stage) {
    try {
      switch (stage) {
        case UserStage.PHONE_VERIFICATION:
          return 'PHONE_VERIFICATION';

        case UserStage.BASIC_DETAILS:
          return 'BASIC DETAILS';

        case UserStage.SELFIE:
          return 'SELFIE';

        case UserStage.NOT_ELIGIBLE:
          return 'NOT_ELIGIBLE';

        case UserStage.PIN:
          return 'PIN';

        case UserStage.AADHAAR:
          return 'AADHAAR';

        case UserStage.EMPLOYMENT:
          return 'EMPLOYMENT DETAILS';

        case UserStage.BANKING:
          return 'BANK VERIFICATION';

        case UserStage.RESIDENCE:
          return 'RESIDENCE';

        case UserStage.LOAN_ACCEPT:
          return 'LOAN ACCEPT';

        case UserStage.CONTACT:
          return 'CONTACT VERIFICATION';

        case UserStage.PAN:
          return 'PAN VERIFICATION';

        case UserStage.FINAL_VERIFICATION:
          return 'FINAL_VERIFICATION';

        case UserStage.MANDATE:
          return 'EMANDATE';

        case UserStage.ESIGN:
          return 'ESIGN';

        case UserStage.DISBURSEMENT:
          return 'DISBURSEMENT';

        case UserStage.REPAYMENT:
          return 'REPAYMENT';

        case UserStage.DEFAULTER:
          return 'DEFAULTER';

        case UserStage.EXPRESS_REAPPLY:
          return 'EXPRESS REAPPLY';

        case UserStage.REAPPLY:
          return 'REAPPLY';

        default:
          return '-';
      }
    } catch (error) {
      return '-';
    }
  }

  checkStatusAndReason(stage, statusObj, rejection) {
    try {
      let arr = [stage];
      let pending = [-1, 2, 5];
      let underVerification = [0];
      if (stage == 'registration')
        arr = [
          'phone',
          'email',
          'permission',
          'basic',
          'personal',
          'professional',
          'selfie',
        ];
      else if (stage == 'employment')
        arr = ['company', 'workMail', 'salarySlip'];
      else if (stage == 'bank') pending = [-1, 2, 4];
      else if (stage == 'selfie') {
        pending = [-1, 2];
        underVerification = [0, 5];
      } else if (stage == 'pan') pending = [-1, 2, 5, 6];

      let status = '-';
      let reason = '-';
      for (let index = 0; index < arr.length; index++) {
        try {
          let el = arr[index];
          if (status != '-') break;
          if (underVerification.includes(statusObj[el])) {
            status = kUnder_Verification;
            const empS = [0, 1, 3];
            if (stage == 'employment' && !empS.includes(statusObj['bank']))
              status = 'In Process';
            if (stage == 'eSign') status = 'Initiated';
          } else if (pending.includes(statusObj[el])) {
            status = kPending;
            if (stage == 'eSign') status = 'Not Initiated';
          }

          if (reason == '-') {
            if (el == 'bank') el = 'banking';
            reason = rejection?.el ?? rejection?.eligibility ?? '-';
            if (reason == '') reason = '-';
          }
        } catch (error) {}
      }
      return { status, reason };
    } catch (error) {}
  }

  getInProgressStatusReason(statusObj, rejection) {
    try {
      const allStage = [
        'registration',
        'aadhaar',
        'employment',
        'bank',
        'residence',
        'selfie',
        'pan',
        'loan',
        'reference',
        'eMandate',
        'eSign',
      ];
      let status = '-';
      let reason = '-';
      for (let index = 0; index < allStage.length; index++) {
        try {
          const stage = allStage[index];
          const check = this.checkStatusAndReason(stage, statusObj, rejection);
          status = check?.status ?? '-';
          reason == '-' ? check?.reason : '-';
          if (status != '-') break;
        } catch (error) {}
      }
      return { status, reason };
    } catch (error) {}
  }

  //for filter
  getStageWhere(stage, type) {
    try {
      const where: any = {};
      const pending = { [Op.or]: [-1, 2] };
      if (stage == 'EMPLOYMENT') {
        if (type == 'PENDING')
          where.status = {
            [Op.or]: [
              { company: pending },
              { workMail: pending },
              { salarySlip: pending },
            ],
          };
        else if (type == 'UNDERVERIFICATION')
          where.status = {
            [Op.or]: [{ company: 0 }, { workMail: 0 }, { salarySlip: 0 }],
          };
      } else if (stage == 'BANKSTATEMENT') {
        if (type == 'PENDING') where.status = { bank: { [Op.or]: [-1, 2, 4] } };
        else if (type == 'UNDERVERIFICATION') where.status = { bank: 0 };
      } else if (stage == 'SELFIE') {
        if (type == 'PENDING') where.status = { selfie: pending };
        else if (type == 'UNDERVERIFICATION')
          where.status = { selfie: { [Op.or]: [0, 5] } };
      } else if (stage == 'LOAN_ACCEPT') {
        if (type == 'PENDING') where.status = { loan: -1 };
        else if (type == 'UNDERVERIFICATION') where.status = { loan: 0 };
      } else if (stage == 'ADD_REFERENCE') {
        if (type == 'PENDING') where.status = { reference: pending };
        else if (type == 'UNDERVERIFICATION') where.status = { reference: 0 };
      } else if (stage == 'PAN') {
        if (type == 'PENDING')
          where.status = { pan: { [Op.or]: [-1, 2, 5, 6] } };
        else if (type == 'UNDERVERIFICATION') where.status = { pan: 0 };
      } else if (stage == 'EMANDATE') {
        if (type == 'PENDING') where.status = { eMandate: -1 };
        else if (type == 'UNDERVERIFICATION') where.status = { eMandate: 0 };
      } else if (stage == 'ESIGN') {
        if (type == 'PENDING') where.status = { eSign: -1 };
        else if (type == 'UNDERVERIFICATION') where.status = { eSign: 0 };
      }
      return where;
    } catch (error) {
      return {};
    }
  }

  //common user options for stuck
  private commonUserOptions(query: any = {}) {
    const mainStage = query?.stage;
    let stage = query?.subKey ?? '';
    let searchText = query?.searchText;
    const download = query?.download ?? 'false';
    const page = query?.page ?? 1;
    let type = query?.key ?? 'ALL';
    const userWhere: any = {};
    let masterWhere: any = {};
    let bankWhere: any = {};
    let loanWhere: any = {};
    const cseAdmin =
      query?.cseAdmin && query?.cseAdmin != '-1'
        ? parseInt(query?.cseAdmin)
        : 0;
    let CRMRemark = query?.CRMRemark;

    const isJoinRequired =
      mainStage == 'MISSING_BANK' || stage == 'AA_RESPONSE' ? true : false;

    userWhere.stageTime = query?.dateRange;
    if (searchText) {
      const firstTwoLetters = searchText.substring(0, 2).toLowerCase();
      if (firstTwoLetters == 'l-') {
        const restOfString = searchText.substring(2);
        masterWhere.loanId = +restOfString;
      } else {
        if (!isNaN(searchText)) {
          searchText = this.cryptService.encryptPhone(searchText);
          searchText = searchText.split('===')[1];
          userWhere.phone = { [Op.iRegexp]: searchText };
        } else
          userWhere[Op.or] = [
            { fullName: { [Op.iRegexp]: searchText } },
            { email: { [Op.iRegexp]: searchText } },
          ];
      }
    }
    //Quality User(good cibil)
    if (mainStage == 'QUALITY') {
      userWhere.maybeGoodCibil = 1;
      userWhere.stage = { [Op.lte]: 15, [Op.ne]: 4 };
    }
    // Missing bank statement
    if (mainStage == 'MISSING_BANK') {
      bankWhere = {
        salaryVerification: '4',
        dataOfMonth: { [Op.ne]: null },
      };
    }
    // AA Response pending from aa side
    else if (stage == 'AA_RESPONSE') {
      bankWhere = {
        salaryVerification: '4',
        consentId: { [Op.ne]: null },
      };
      loanWhere = { loanStatus: 'InProcess' };
    }
    if (stage) {
      stage = this.getStageNumber(stage);
      if (stage != -1) stage = { stage: { [Op.in]: stage } };
      else stage = '';
    } else {
      stage = this.getStageNumber(mainStage);
      if (stage != -1) stage = { stage: { [Op.in]: stage } };
      else stage = '';
    }

    const allInProgressStage = [3, 7, 8, 9, 10, 11, 12, 14, 15, 20];
    if (type && type != 'ALL') {
      if (type == 'PENDING') type = { stageStatus: { [Op.in]: [-1] } };
      else type = { stageStatus: { [Op.in]: [0] } };
    } else if (mainStage == 'IN_PROGRESS' && (stage ?? '') == '') {
      bankWhere = { aaDataStatus: 1, salaryVerification: '4' };
      type = {};
      stage = {
        stage: {
          [Op.in]: allInProgressStage,
        },
      };
      // In process user has started the loan process
      if (!masterWhere?.loanId) masterWhere.loanId = { [Op.ne]: null };
    } else type = {};
    if (cseAdmin) {
      const assignedCseFilter = [
        { '$masterData.assignedCSE$': null },
        { '$masterData.assignedCSE$': { [Op.in]: [SYSTEM_ADMIN_ID, cseAdmin] } },
      ];
      if (!userWhere[Op.or]) 
        userWhere[Op.or] = assignedCseFilter;
       else 
        userWhere[Op.and] = { [Op.or]: assignedCseFilter };
    }
    const kycInclude = {
      model: KYCEntity,
      attributes: ['id', 'panCardNumber'],
      required: isJoinRequired,
    };
    const empInclude = {
      model: employmentDetails,
      attributes: ['id', 'companyName'],
      required: isJoinRequired,
    };
    const bankInclude = {
      model: BankingEntity,
      where: bankWhere,
      attributes: [
        'attempts',
        'id',
        'accountNumber',
        'ifsCode',
        'mandateBank',
        'isNeedTagSalary',
        'dataOfMonth',
        'aaDataStatus',
        'otherDetails',
        'salary',
        'name',
      ],
      required: isJoinRequired,
    };
    const subscInclude = {
      model: SubScriptionEntity,
      attributes: ['id', 'mode', 'subType'],
      required: false,
    };
    const eSignInclude = {
      model: esignEntity,
      attributes: ['id', 'esign_mode'],
      required: false,
    };

    const loanInclude: SequelOptions = {
      model: loanTransaction,
      attributes: ['id', 'netApprovedAmount', 'createdAt'],
      include: [bankInclude, subscInclude, eSignInclude],
      required: isJoinRequired,
      where: loanWhere,
    };
    const masterInclude: SequelOptions = {
      where: masterWhere,
      model: MasterEntity,
      attributes: [
        'id',
        'loanId',
        'status',
        'otherInfo',
        'rejection',
        'assignedCSE',
      ],
      include: [empInclude, loanInclude],
      required: isJoinRequired || masterWhere?.loanId ? true : false,
    };

    if (CRMRemark == 'true') userWhere.lastCrm = { [Op.not]: null };
    if (CRMRemark == 'false') userWhere.lastCrm = { [Op.is]: null };

    // Selfie fixes
    if (mainStage == 'IN_PROGRESS') {
      if (!masterWhere?.loanId) {
        masterWhere.loanId = { [Op.ne]: null };
        masterInclude.required = true;
      }
    }

    let cseWhere: any = {};
    if (GLOBAL_FLOW.IS_CSE_REJECTION_FLOW) {
      cseWhere['$masterData.loanData.cseRejectBy$'] = {
        [Op.is]: null,
      };
    }

    const userOptions: any = {
      where: {
        ...stage,
        ...type,
        ...cseWhere,
        ...userWhere,
      },
      order: [['stageTime', 'DESC']],
      include: [masterInclude, kycInclude],
    };
    if (download != 'true') {
      userOptions.offset = +page * PAGE_LIMIT - PAGE_LIMIT;
      userOptions.limit = PAGE_LIMIT;
    }
    return userOptions;
  }

  private getStageNumber(stage) {
    try {
      switch (stage) {
        case 'REGISTRATION':
          return [
            UserStage.BASIC_DETAILS,
            UserStage.PHONE_VERIFICATION,
            UserStage.SELFIE,
          ];
        case 'SELFIE':
          return [UserStage.SELFIE];
        case 'PAN':
          return [UserStage.PAN];
        case 'EMPLOYMENT':
          return [UserStage.EMPLOYMENT];
        case 'BANKSTATEMENT':
          return [UserStage.BANKING];
        case 'MISSING_BANK':
          return [UserStage.BANKING];
        case 'AA_RESPONSE':
          return [UserStage.NO_ROUTE];
        case 'RESIDENCE':
          return [UserStage.RESIDENCE];
        case 'REFERENCE':
          return [UserStage.CONTACT];
        case 'AADHAAR':
          return [UserStage.AADHAAR];
        case 'FINALBUCKET':
          return [UserStage.FINAL_VERIFICATION];
        case 'EMANDATE':
          return [UserStage.MANDATE];
        case 'ESIGN':
          return [UserStage.ESIGN];
        case 'LOAN_ACCEPT':
          return [UserStage.LOAN_ACCEPT];
        default:
          return -1;
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }

  async getAllUserStuckDataQuery(options, stage?, reqData: any = {}) {
    const attributes = [
      'id',
      'fullName',
      'phone',
      'email',
      'typeOfDevice',
      'completedLoans',
      'lastOnlineTime',
      'city',
      'createdAt',
      'stage',
      'appType',
    ];

    if (reqData?.CRMRemark == 'true' || reqData?.CRMRemark == 'false') {
      attributes.push('lastCrm');
    }

    const userData = await this.userRepo.getTableWhereDataWithCounts(
      attributes,
      { ...options },
    );
    if (userData == k500Error) throw new Error();

    const userIds = userData.rows.map((d) => d.id);

    // get data from requestSupportRepo Entity
    const reqOpts: any = {
      where: { userId: userIds },
      order: [['id', 'DESC']],
    };
    if (stage != 'inProgress') reqOpts.where.lastStep = stage;
    const reqList = await this.requestSupportRepo.getTableWhereData(
      ['userId', 'reason', 'connectVia'],
      reqOpts,
    );

    const finalData = await this.prepareUserStuckData(
      userData?.rows,
      stage,
      reqData,
      reqList,
    );
    let totalAmount = 0;
    for (let i = 0; i < finalData.length; i++) {
      totalAmount += finalData[i]?.['Applied amount'] ?? 0;
    }
    return { count: userData?.count, rows: finalData, totalAmount };
  }

  //get user last crm data
  async getLastCrmList(userList: any = []) {
    try {
      const userIds = userList.map((el) => el?.id);

      const crmAttr = ['id', 'userId', 'createdAt', 'remark', 'adminId'];
      const crmOps: any = {
        where: { userId: userIds },
        order: [['id', 'DESC']],
      };

      const crmList = await this.crmRepo.getTableWhereData(crmAttr, crmOps);
      if (crmList === k500Error) return kInternalError;
      return crmList;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#region get registration stucked user data
  async getRegistrationStuckedData(query) {
    try {
      const masterWhere = {
        [Op.and]: [
          {
            status: {
              [Op.or]: [
                { phone: { [Op.or]: [-1, 5] } },
                { email: { [Op.or]: [-1, 5] } },
                { permission: -1 },
                { basic: -1 },
                { personal: -1 },
                { professional: -1 },
                { selfie: -1 },
              ],
            },
          },
          { status: { loan: { [Op.ne]: 6 } } },
        ],
      };
      if (query?.counts == 'true') {
        const opts = this.commonUserOptionsForCounts(query, masterWhere);
        const counts = await this.userRepo.getCountsWhere(opts);
        if (counts === k500Error) return kInternalError;
        return counts;
      }
      query.download = 'true';
      const options = this.commonUserOptions(query);
      return await this.getAllUserStuckDataQuery(
        options,
        'registration',
        query,
      );
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region get aadhaar stucked user data
  async getAadhaarStuckData(query) {
    try {
      const approvedStatus = [1, 3];
      const previousApproved = {
        phone: { [Op.or]: approvedStatus },
        email: { [Op.or]: approvedStatus },
        permission: { [Op.or]: approvedStatus },
        basic: { [Op.or]: approvedStatus },
        personal: { [Op.or]: approvedStatus },
        professional: { [Op.or]: approvedStatus },
        selfie: { [Op.or]: [1, 3, 5] },
      };
      const masterWhere = {
        status: { ...previousApproved, aadhaar: -1 },
      };
      if (query?.counts == 'true') {
        const opts = this.commonUserOptionsForCounts(query, masterWhere);
        const counts = await this.userRepo.getCountsWhere(opts);
        if (counts === k500Error) return kInternalError;
        return counts;
      }
      query.download = 'true';
      const options = this.commonUserOptions(query);
      const isWhatsapp = query?.isWhatsapp;
      return await this.getAllUserStuckDataQuery(options, 'aadhaar', {
        isWhatsapp,
      });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  async missingBankSatement(query) {
    try {
      const approvedStatus = [1, 3];
      const previousApproved = {
        aadhaar: { [Op.or]: approvedStatus },
        company: { [Op.or]: [0, 1, 3] },
        workMail: { [Op.or]: [0, 1, 3, 4] },
        salarySlip: { [Op.or]: [0, 1, 3] },
        loan: { [Op.or]: [-2, -1] },
      };
      const masterWhere = {
        status: { ...previousApproved, bank: 4 },
      };
      if (query?.counts == 'true') {
        const opts = this.commonUserOptionsForCounts(query, masterWhere);
        const counts = await this.userRepo.getCountsWhere(opts);
        if (counts === k500Error) return kInternalError;
        return counts;
      }

      const options = this.commonUserOptions(query);
      const isWhatsapp = query?.isWhatsapp;
      return await this.getAllUserStuckDataQuery(options, 'missing_bank', {
        isWhatsapp,
      });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region user stuck
  async getQualityUserData(query) {
    try {
      const approvedStatus = [1, 3];
      const previousApproved = {
        aadhaar: { [Op.or]: approvedStatus },
        esign: { [Op.ne]: 1 },
      };
      const masterWhere = {
        status: { ...previousApproved },
      };
      if (query?.counts == 'true') {
        const opts = this.commonUserOptionsForCounts(query, masterWhere);
        const counts = await this.userRepo.getCountsWhere(opts);
        if (counts === k500Error) return kInternalError;
        return counts;
      }
      const options = this.commonUserOptions(query);
      const isWhatsapp = query?.isWhatsapp;
      return await this.getAllUserStuckDataQuery(options, 'inProgress', {
        isWhatsapp,
      });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region get employment stucked user data
  async getEmploymentStuckData(query) {
    try {
      const options = this.commonUserOptions(query);
      const isWhatsapp = query?.isWhatsapp;
      return await this.getAllUserStuckDataQuery(options, 'employment', {
        isWhatsapp,
      });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region get Bank statement stucked user data
  async getBankStatementStuckData(query) {
    try {
      const options = this.commonUserOptions(query);
      const isWhatsapp = query?.isWhatsapp;
      return await this.getAllUserStuckDataQuery(options, 'bank', {
        isWhatsapp,
      });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region get Bank statement stucked user data
  async getExpressReapplyStuckData(query) {
    try {
      const options = this.commonUserOptions(query);
      return await this.getAllUserStuckDataQuery(options, 'expressReapply');
    } catch (error) {
      return kInternalError;
    }
  }
  //#endregion

  //#region get selfie stucked user data
  async getSelfieStuckData(query) {
    const options = this.commonUserOptions(query);
    const isWhatsapp = query?.isWhatsapp;
    return await this.getAllUserStuckDataQuery(options, 'selfie', {
      isWhatsapp,
    });
  }

  //#region get loan accept stucked user data
  async getLoanAcceptStuckData(query) {
    try {
      const options = this.commonUserOptions(query);
      const isWhatsapp = query?.isWhatsapp;

      const data: any = await this.getAllUserStuckDataQuery(options, 'loan', {
        isWhatsapp,
      });

      query.download = 'true';
      const optionsNew: any = this.commonUserOptions(query);
      const dataNew: any = await this.getAllUserStuckDataQuery(
        optionsNew,
        'loan',
        {
          isWhatsapp,
        },
      );

      data.totalAmount = dataNew.totalAmount;
      return data;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region get reference stucked user data
  async getReferenceStuckData(query) {
    try {
      const options = this.commonUserOptions(query);
      const isWhatsapp = query?.isWhatsapp;
      return await this.getAllUserStuckDataQuery(options, 'reference', {
        isWhatsapp,
      });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region get pan stucked user data
  async getPanStuckData(query) {
    try {
      const options = this.commonUserOptions(query);
      const isWhatsapp = query?.isWhatsapp;
      return await this.getAllUserStuckDataQuery(options, 'pan', {
        isWhatsapp,
      });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region get Mandate stucked user data
  async getMandateStuckData(query) {
    try {
      const approvedStatus = [1, 3];
      const previousApproved = { loan: { [Op.or]: approvedStatus } };
      const masterWhere = {
        status: { ...previousApproved, eMandate: { [Op.or]: [-1, 0, 2] } },
      };
      const options = this.commonUserOptions(query);
      const isWhatsapp = query?.isWhatsapp;
      return await this.getAllUserStuckDataQuery(options, 'eMandate', {
        isWhatsapp,
      });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region get Esign stucked user data
  async getEsignStuckData(query) {
    try {
      const options = this.commonUserOptions(query);
      const isWhatsapp = query?.isWhatsapp;
      return await this.getAllUserStuckDataQuery(options, 'eSign', {
        isWhatsapp,
      });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region calling stucked user
  async callStuckUser(query) {
    try {
      const adminId = query?.adminId;
      if (!adminId) return kParamMissing('adminId');
      const stage = query?.stage == 'ALL' ? 'IN_PROGRESS' : query?.stage;
      if (!stage) return kParamMissing(stage);

      const stuckData = await this.funGetUserStuckData(query);
      if (stuckData?.message) return stuckData;
      const targetList = [];
      stuckData.rows.forEach((ele) => {
        try {
          const userObj = {
            userId: ele?.userId,
            phone: ele['Mobile number'] == '-' ? null : ele['Mobile number'],
            loanId: ele['Loan id'] == '-' ? null : ele['Loan id'],
          };
          targetList.push(userObj);
        } catch (error) {}
      });
      const subKey = query?.subKey ?? null;
      const key =
        stage == 'IN_PROGRESS' && subKey != 'ALL'
          ? query?.subKey ?? stage
          : stage;
      const category =
        key.substr(key.length - 5) != 'STUCK' ? `${key}_STUCK` : key;
      const callData = { category, adminId, targetList };
      const placeCall = await this.callingService.placeCall(callData);
      if (placeCall.message) return placeCall;
      return targetList;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region getting keys and subkeys for users in progress
  async funGetInProcessLoanUserStuckList() {
    try {
      const keyList = [
        { key: 'ALL', value: 'ALL' },
        { key: 'Pending', value: 'PENDING' },
        { key: 'Under Verification', value: 'UNDERVERIFICATION' },
      ];
      const subKeyList = [
        { key: 'ALL', value: 'ALL' },
        { key: 'Employment', value: 'EMPLOYMENT' },
        { key: 'Bank Statement', value: 'BANKSTATEMENT' },
        { key: 'AA Response', value: 'AA_RESPONSE' },
        { key: 'Selfie', value: 'SELFIE' },
        { key: 'Loan Accept', value: 'LOAN_ACCEPT' },
        { key: 'Pan', value: 'PAN' },
        { key: 'E-Mandate', value: 'EMANDATE' },
        { key: 'E-Sign', value: 'ESIGN' },
      ];
      return { keyList, subKeyList };
    } catch (error) {}
  }
  //#endregion

  async funSentNotificationInProcess() {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 7);

      const data: any = await this.funGetUserStuckData({
        stage: 'IN_PROGRESS',
        startDate,
        endDate: new Date(),
        download: 'true',
        isWhatsapp: true,
      });
      data.rows = data?.rows.filter((el) => el?.Status != 'Under Verification');

      const tempOpt = { where: { title: 'Complete your process' } };
      const template = await this.templateRepo.getRowWhereData(['id'], tempOpt);
      if (template === k500Error) return kInternalError;

      await this.sharedNotification.sendNotificationToUser({
        userData: data.rows,
        isMsgSent: gIsPROD ? true : false,
        id: template?.id,
      });
      //user stuck whatsapp message stop temp
      // await this.sendUserStuckWhatsappMsg(data?.rows);

      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region get query for month wise loan analytics
  getLoanAnalyticsMonthWiseQuery(fromMonth, attributes) {
    try {
      fromMonth.setFullYear(fromMonth.getFullYear() - 1);
      fromMonth.setMonth(fromMonth.getMonth() - 11);
      const disDate = 'loan_disbursement_date';
      attributes.push(
        this.typeService.manageDateAttr('year', '', disDate),
        this.typeService.manageDateAttr('month', '', disDate),
        'completedLoan',
      );
      const group = [
        this.typeService.manageDateAttr('year', '', disDate, false),
        this.typeService.manageDateAttr('month', '', disDate, false),
        'completedLoan',
      ];
      return { attributes, group };
    } catch (error) {
      k500Error;
    }
  }
  //#endregion

  //#region get query for day wise loan analytics
  getLoanAnalyticsDayWiseQuery(fromMonth, attributes) {
    try {
      fromMonth.setMonth(fromMonth.getMonth() - 1);
      fromMonth.setDate(1);
      const disDate = 'loan_disbursement_date';
      attributes.push(
        this.typeService.manageDateAttr('year', '', disDate),
        this.typeService.manageDateAttr('month', '', disDate),
        this.typeService.manageDateAttr('day', '', disDate),
        'completedLoan',
      );
      const group = [
        this.typeService.manageDateAttr('year', '', disDate, false),
        this.typeService.manageDateAttr('month', '', disDate, false),
        this.typeService.manageDateAttr('day', '', disDate, false),
        'completedLoan',
      ];
      return { attributes, group };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }
  //#endregion

  //#region get loan anaytics type wise
  async funGetLoanAnalyticsTypeWise(query) {
    try {
      const type = query?.type ?? 'MONTH';
      /// find row Data
      const loanData = await this.getRowDataAnalytics(type);
      if (loanData?.message) return loanData;

      let finalData;
      let previousMonthData;
      let currentMonthData;
      let _others;

      if (type == 'MONTH')
        finalData = { previousYearData: [], currentYearData: [] };
      else if (type == 'DAY') {
        finalData = { previousMonthData: [], currentMonthData: [] };
        const monthData = [
          ...new Set(loanData.map((item) => item.loan_disbursement_datemonth)),
        ].sort((a: any, b: any) => a - b);
        [previousMonthData, currentMonthData, ..._others] = monthData;
      }
      /// pre pare Data
      for (let i = 0; i < loanData.length; i++) {
        try {
          const loan = loanData[i];
          const loanYear = loan.loan_disbursement_dateyear;
          const loanMonth = loan.loan_disbursement_datemonth;
          let date = loanMonth + '-01-' + loanYear;
          const loanDisDate = new Date(date);
          let loanDay = loan.loan_disbursement_dateday;
          if (type != 'DAY') loanDay = 1;
          date =
            loanYear +
            (loanMonth < 10 ? '-0' + loanMonth : '-' + loanMonth) +
            (loanDay < 10 ? '-0' + loanDay : '-' + loanDay);

          const currDate = new Date();
          currDate.setFullYear(currDate.getFullYear() - 1);
          const key =
            type == 'DAY'
              ? previousMonthData == loanMonth
                ? 'previousMonthData'
                : 'currentMonthData'
              : currDate > loanDisDate
              ? 'previousYearData'
              : 'currentYearData';

          const loanCounts = +(loan?.totalLoan ?? 0);
          const newUsersCount = loan?.completedLoan === 0 ? loanCounts : 0;
          const repeatedUsersCount = loan?.completedLoan > 0 ? loanCounts : 0;
          const year = loanDisDate.getFullYear();
          const month = loanDisDate
            .toLocaleDateString('default', { month: 'short' })
            .toUpperCase();

          const temp = {
            date,
            year,
            month,
            loanCounts,
            newUsersCount,
            repeatedUsersCount,
          };
          if (type == 'DAY') {
            delete temp.month;
            delete temp.year;
          }
          const index = finalData[key].findIndex((item) => item?.date === date);
          if (index != -1) {
            const find = finalData[key][index];
            temp.loanCounts += find.loanCounts;
            temp.newUsersCount += find.newUsersCount;
            temp.repeatedUsersCount += find.repeatedUsersCount;
            finalData[key][index] = temp;
          } else finalData[key].push(temp);
        } catch (error) {
          this.errorContextService.throwAndSetCtxErr(error);
          return kInternalError;
        }
        try {
          if (type == 'MONTH') {
            finalData.currentYearData.sort((b: any, a: any) => {
              return new Date(a?.date).getTime() - new Date(b?.date).getTime();
            });
            finalData.previousYearData.sort((b: any, a: any) => {
              return new Date(a?.date).getTime() - new Date(b?.date).getTime();
            });
          }
        } catch (error) {}
      }
      return finalData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region get row Data
  private async getRowDataAnalytics(type) {
    try {
      const fromMonth = new Date();
      const currentMonth = new Date();
      /// attributes
      let attributes: any = [
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'totalLoan'],
      ];
      /// options
      let options;
      if (type == 'MONTH')
        options = this.getLoanAnalyticsMonthWiseQuery(fromMonth, attributes);
      else if (type == 'DAY')
        options = this.getLoanAnalyticsDayWiseQuery(fromMonth, attributes);
      if (options == k500Error) return kInternalError;
      /// where
      const where = {
        loan_disbursement_date: { [Op.gte]: fromMonth, [Op.lte]: currentMonth },
        loanStatus: { [Op.or]: ['Active', 'Complete'] },
      };

      const result = await this.loanRepository.getTableWhereData(
        options?.attributes,
        { where, group: options?.group },
      );
      if (result == k500Error) return kInternalError;
      return result;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region get query for day wise loan repayment analytics
  getLoanRepaymentAnalyticsDayWiseQuery(attributes) {
    try {
      attributes.push(
        this.typeService.manageDateAttr('year', '', 'completionDate'),
        this.typeService.manageDateAttr('month', '', 'completionDate'),
        this.typeService.manageDateAttr('day', '', 'completionDate'),
      );
      const group = [
        this.typeService.manageDateAttr('year', '', 'completionDate', false),
        this.typeService.manageDateAttr('month', '', 'completionDate', false),
        this.typeService.manageDateAttr('day', '', 'completionDate', false),
      ];
      return { attributes, group };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }
  //#endregion

  //#region get query for month wise loan repayment analytics
  getLoanRepaymentAnalyticsMonthWiseQuery(from, attributes) {
    try {
      from.setMonth(from.getMonth() - 23);
      from.setDate(1);
      attributes.push(
        this.typeService.manageDateAttr('year', '', 'completionDate'),
        this.typeService.manageDateAttr('month', '', 'completionDate'),
      );
      const group = [
        this.typeService.manageDateAttr('year', '', 'completionDate', false),
        this.typeService.manageDateAttr('month', '', 'completionDate', false),
      ];
      return { attributes, group };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }
  //#endregion

  //#region get loan repayment analytics
  async funGetLoanRepaymentAnalyticsTypeWise(query) {
    try {
      const type: 'MONTH' | 'DAY' = query?.type ?? 'MONTH';
      const loanData = await this.getLoanRepaymentRowData(type);
      if (loanData == k500Error) return kInternalError;
      let finalData;
      let currentDate: any = this.typeService.getGlobalDate(new Date());

      if (type == 'DAY') {
        currentDate.setDate(1);
        finalData = { currMonthData: [], lastMonthData: [] };
      } else if (type == 'MONTH') {
        currentDate.setMonth(currentDate.getMonth() - 11);
        currentDate.setDate(1);
        finalData = { currYearData: [], lastYearData: [] };
      }
      currentDate = this.typeService.getGlobalDate(currentDate.toString());
      loanData.forEach((curr) => {
        try {
          const currMonth =
            +curr.completionDatemonth < 10
              ? '0' + curr.completionDatemonth
              : curr.completionDatemonth;
          const currDay =
            type == 'DAY'
              ? +curr.completionDateday < 10
                ? '0' + curr.completionDateday
                : curr.completionDateday
              : '01';
          const currYear = curr.completionDateyear;
          let date: any = currMonth + '-' + currDay + '-' + currYear;
          let completionDate: any = this.typeService.getGlobalDate(date);
          const key =
            type == 'DAY'
              ? completionDate < currentDate
                ? 'lastMonthData'
                : 'currMonthData'
              : completionDate < currentDate
              ? 'lastYearData'
              : 'currYearData';
          completionDate = this.typeService.getGlobalDate(new Date(date));
          date = currYear + '-' + currMonth + '-' + currDay;
          const tempObj: any = {
            date,
            totalRepaymentAmount: curr?.totalRepayAmount,
            totalRepaymentCounts: +(curr?.count ?? 0),
          };
          if (type == 'MONTH') {
            tempObj.year = completionDate.getFullYear();
            tempObj.month = kMonths[+currMonth - 1]
              .substring(0, 3)
              .toUpperCase();
          }
          const existingDate = finalData[key].findIndex((item) => {
            return item.date == date;
          });

          if (existingDate != -1) {
            const find = finalData[key][existingDate];
            tempObj.totalRepaymentAmount += find.totalRepaymentAmount;
            tempObj.totalRepaymentCounts += +(curr?.count ?? 0);
            finalData[key][existingDate] = tempObj;
          } else finalData[key].push(tempObj);
        } catch (error) {
          this.errorContextService.throwAndSetCtxErr(error);
          return k500Error;
        }
      });
      try {
        const keys =
          type == 'MONTH'
            ? ['currYearData', 'lastYearData']
            : ['currMonthData', 'lastMonthData'];
        keys.forEach((key) => {
          finalData[key] = finalData[key].map((item) => {
            item.totalRepaymentCounts = +item.totalRepaymentCounts;
            item.totalRepaymentAmount = +item.totalRepaymentAmount.toFixed();
            return item;
          });
          if (type == 'MONTH')
            finalData[key].sort(
              (b: any, a: any) =>
                new Date(a?.date).getTime() - new Date(b?.date).getTime(),
            );
        });
      } catch (error) {}
      return finalData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region get rowData for loan repayment analytics
  private async getLoanRepaymentRowData(type) {
    try {
      const from = this.typeService.getGlobalDate(new Date());
      const to = this.typeService.getGlobalDate(new Date());
      const attributes = [
        [Sequelize.fn('count', Sequelize.col('id')), 'count'],
        [Sequelize.fn('SUM', Sequelize.col('paidAmount')), 'totalRepayAmount'],
      ];

      let loanRepaymentAnalyticsQuery;
      if (type == 'DAY') {
        from.setMonth(from.getMonth() - 1);
        from.setDate(1);
        // get day wise loan repayment analytics query
        loanRepaymentAnalyticsQuery =
          this.getLoanRepaymentAnalyticsDayWiseQuery(attributes);
      } else if (type == 'MONTH') {
        // get month wise loan repayment
        loanRepaymentAnalyticsQuery =
          this.getLoanRepaymentAnalyticsMonthWiseQuery(from, attributes);
      }

      if (loanRepaymentAnalyticsQuery == k500Error) return kInternalError;
      const loanData = await this.transactionRepository.getTableWhereData(
        attributes,
        {
          where: {
            completionDate: {
              [Op.gte]: from.toJSON(),
              [Op.lte]: to.toJSON(),
            },
            status: 'COMPLETED',
            type: { [Op.ne]: 'REFUND' },
          },
          group: [...loanRepaymentAnalyticsQuery?.group],
        },
      );
      return loanData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // #start region send whatsapp message to user stuck

  async sendUserStuckWhatsappMsg(userData) {
    try {
      const dataLength = !gIsPROD ? UAT_PHONE_NUMBER.length : userData.length;
      const adminId = userData?.adminId ?? SYSTEM_ADMIN_ID;
      const maxLoanAmount = this.typeService.amountNumberWithCommas(
        GLOBAL_RANGES.MAX_LOAN_AMOUNT,
      );
      for (let i = 0; i < dataLength; i++) {
        try {
          const ele = userData[i];
          const userId = ele?.userId;
          const customerName = ele?.Name;
          const loanId = ele?.['Loan id'];
          const email = ele?.Email;
          const appType = ele?.appType;
          let number = ele?.['Mobile number'];
          if (!gIsPROD) number = UAT_PHONE_NUMBER[i];

          const whatsappOption: any = {
            userId,
            customerName,
            loanId,
            email,
            number,
            adminId,
            loanAmount: maxLoanAmount,
            title: 'User stuck',
            requestData: 'user_stuck',
            appType,
          };
          // await this.whatsappService.sendWhatsAppMessage(whatsappOption);
          this.whatsappService.sendWhatsAppMessageMicroService(whatsappOption);
        } catch (error) {}
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region get premium users data

  getUserAdminStageStatusValue(
    user_admin_stage_status: string,
  ): string | undefined {
    let userAdminStageStatus;
    if (user_admin_stage_status === 'PENDING') {
      userAdminStageStatus = '-1';
    } else if (user_admin_stage_status === 'UNDERVERIFICATION') {
      userAdminStageStatus = '0';
    } else if (user_admin_stage_status === 'NOT_ELIGIBLE') {
      userAdminStageStatus = '2';
    }
    return userAdminStageStatus;
  }

  getStageStatusString(stageStatus: number): string | undefined {
    try {
      if (stageStatus === -1) {
        return 'Pending';
      } else if (stageStatus === 0) {
        return 'Under Verification';
      } else if (stageStatus === 2) {
        return 'Not Eligible';
      }
      return undefined;
    } catch (error) {}
  }

  funPremiumUserQuery(
    STAGE,
    STAGE_STATUS,
    searchText,
    fullName,
    phone,
    loanId,
    query,
    fromDate,
    toDate,
    download,
    LIMIT,
    OFFSET,
    applyLimitOffset,
  ) {
    const premiumCompanies =
      EnvConfig.nbfcType == '0'
        ? [
            1474681, 1474715, 1474801, 1474986, 1475079, 1475375, 1475497,
            1475509, 1476157, 1476635, 1476905, 1477020, 1477151, 1478095,
            1478107, 1478918, 1480504, 1481120, 1483407, 1483732, 1483844,
            1485625, 1486433, 1493253, 1494780, 1499151, 1521462, 1525066,
            1537809, 1571730, 1573901, 1586137, 1679174, 2008110, 2027698,
          ]
        : [
            1474801, 1679174, 1481120, 1474986, 1521462, 1485625, 1478095,
            1480504, 1494780, 1483732, 1475497, 1476157, 1483844, 1477020,
            1478918, 1476635, 1537809, 1478107, 1475375, 1525066, 1573901,
            1493253, 1499151, 1483407, 1474681, 1586137, 1475509, 2008110,
            1475079, 1477151, 2027698, 1476905, 1474715, 1571730, 1486433,
          ];
    let premiumUsersQuery = `
    SELECT 
	    "registeredUsers"."id",
      "registeredUsers"."phone",
      "registeredUsers"."fullName",
      "registeredUsers"."email",
      ("MasterEntities"."otherInfo"#>>'{salaryInfo}') AS "masterData.salaryInfo",
      ("MasterEntities"."otherInfo"#>>'{netPaySalary}') AS "masterData.netPaySalary",
      ("MasterEntities"."assignedCSE") AS "masterData.assignedCSE",
      "registeredUsers"."gender",
      "registeredUsers"."city",
      "registeredUsers"."createdAt",
      "registeredUsers"."lastLoanId",
      "loanTransactions"."loanStatus",
      "loanTransactions"."companyId",
      "registeredUsers"."stage",
      "registeredUsers"."stageStatus",
      "registeredUsers"."isBlacklist",
      "registeredUsers"."lastCrm",
      "CibilScoreEntities"."cibilScore", 
      "CibilScoreEntities"."plScore", 
      "CibilScoreEntities"."PLAccounts",
      "CibilScoreEntities"."inquiryPast30Days"
        FROM "registeredUsers"
        INNER JOIN "MasterEntities"
          ON "registeredUsers"."masterId" = "MasterEntities"."id"
        LEFT JOIN "loanTransactions"
          ON "MasterEntities"."loanId" = "loanTransactions"."id"
        JOIN "CibilScoreEntities"
          ON "loanTransactions"."id" = "CibilScoreEntities"."loanId"
        WHERE 
        "loanTransactions"."loanStatus" NOT IN ('Rejected', 'Active')
        AND "registeredUsers"."isBlacklist" != '1'
        AND "registeredUsers"."stage" NOT IN ('4')
        AND "CibilScoreEntities"."PLAccounts" > 10
        AND "CibilScoreEntities"."overdueBalance" = 0
        AND "CibilScoreEntities"."plScore" >= 700
        AND "CibilScoreEntities"."cibilScore" >= 700
        AND
        (
          ("loanTransactions"."companyId" IN (${premiumCompanies}))
          OR
          ("CibilScoreEntities"."inquiryPast30Days" <= 4 OR "CibilScoreEntities"."PLOutstanding" >= 2000000 OR "CibilScoreEntities"."plScore" >= 810)
        )
    `;

    if (STAGE !== null && STAGE != undefined) {
      premiumUsersQuery += ` AND "registeredUsers"."stage" = '${STAGE}' `;
    }
    if (STAGE_STATUS !== null && STAGE_STATUS != undefined) {
      premiumUsersQuery += ` AND "registeredUsers"."stageStatus" = '${STAGE_STATUS}' `;
    }
    if (searchText !== '') {
      premiumUsersQuery += `
      AND (`;
      if (fullName !== '' && fullName != undefined) {
        premiumUsersQuery += ` "registeredUsers"."fullName" ILIKE '%${fullName}%'`;
      }
      if (phone !== '' && phone != undefined) {
        premiumUsersQuery += `
        "registeredUsers"."phone" ILIKE '%${phone}%'`;
      }

      if (loanId !== '' && loanId != undefined) {
        premiumUsersQuery += `
        "registeredUsers"."lastLoanId" = '${loanId}'`;
      }
      premiumUsersQuery += `)`;
    }
    if (query?.startDate && query?.endDate) {
      premiumUsersQuery += `
            AND ("registeredUsers"."createdAt" >= '${fromDate}' AND "registeredUsers"."createdAt" <= '${toDate}')
      `;
    }
    const cseAdmin =
      query?.cseAdmin && query?.cseAdmin != '-1'
        ? parseInt(query?.cseAdmin)
        : 0;
    if (cseAdmin) {
      premiumUsersQuery += `
          AND ("MasterEntities"."assignedCSE" IS NULL OR "MasterEntities"."assignedCSE" IN ('${SYSTEM_ADMIN_ID}','${cseAdmin}'))
      `;
    }
    if (download === false) {
      if (applyLimitOffset) {
        if (LIMIT !== null) {
          premiumUsersQuery += ` LIMIT ${LIMIT} `;
        }
        if (OFFSET !== null) {
          premiumUsersQuery += ` OFFSET ${OFFSET} `;
        }
      }
    }
    premiumUsersQuery += `;`;
    return premiumUsersQuery;
  }

  async getPremiumUsers(query) {
    try {
      const page = query?.page ?? 1;
      const pageSize = query?.pageSize ?? PAGE_LIMIT;
      const offset = pageSize * (page - 1);
      let download = query?.download == 'true' ? true : false;
      const LIMIT = parseInt(pageSize) || 10;
      const OFFSET = offset || 0;
      const STAGE =
        query?.userStage === 'ALL'
          ? undefined
          : query?.userStage &&
            Number.isInteger(+this.getStageNumber(query?.userStage)) &&
            +this.getStageNumber(query?.userStage);
      const STAGE_STATUS =
        query?.stageStatus === 'ALL'
          ? undefined
          : query?.stageStatus &&
            Number.isInteger(
              +this.getUserAdminStageStatusValue(query.stageStatus),
            ) &&
            +this.getUserAdminStageStatusValue(query.stageStatus);
      const startDate = query?.startDate ?? new Date();
      const endDate = query?.endDate ?? new Date();
      let dateRange = await this.typeService.getUTCDateRange(
        startDate,
        endDate,
      );
      let fromDate = dateRange.fromDate;
      let toDate = dateRange.endDate;
      const searchText = query?.searchText ?? '';
      let encryptedData;
      if (searchText) {
        if (!isNaN(searchText)) {
          encryptedData = await this.cryptService.encryptPhone(searchText);
          encryptedData = encryptedData.split('===')[1];
        }
        var phone = '';
        var fullName = '';
        var loanId;

        const firstTwoLetters = searchText.substring(0, 2).toLowerCase();
        const restOfString: any = searchText.substring(2);

        if (encryptedData) {
          phone = `${encryptedData}`;
        } else if (firstTwoLetters == 'l-' || firstTwoLetters == 'L-') {
          loanId = +restOfString;
        } else {
          fullName = `${searchText}`;
        }
      }

      let limitedPremiumUsers = this.funPremiumUserQuery(
        STAGE,
        STAGE_STATUS,
        searchText,
        fullName,
        phone,
        loanId,
        query,
        fromDate,
        toDate,
        download,
        LIMIT,
        OFFSET,
        true,
      );
      let allPremiumUsersDataRes = await this.repoManager.injectRawQuery(
        registeredUsers,
        limitedPremiumUsers,
      );
      let allPremiumUsers = this.funPremiumUserQuery(
        STAGE,
        STAGE_STATUS,
        searchText,
        fullName,
        phone,
        loanId,
        query,
        fromDate,
        toDate,
        download,
        LIMIT,
        OFFSET,
        false,
      );
      const allPremiumUsersDataResForCount =
        await this.repoManager.injectRawQuery(registeredUsers, allPremiumUsers);
      if (allPremiumUsersDataRes == k500Error) return kInternalError;
      if (!allPremiumUsersDataRes || allPremiumUsersDataRes?.length == 0)
        return k422ErrorMessage(kNoDataFound);

      const userIds = allPremiumUsersDataRes.map((el) => el.id);
      allPremiumUsersDataRes = await this.experianSharedService.getExperianData(
        allPremiumUsersDataRes,
        userIds,
      );
      const permiumUsersList = [];
      for (let index = 0; index < allPremiumUsersDataRes.length; index++) {
        const permiumUser = allPremiumUsersDataRes[index];
        const salaryAmmount = this.typeService.amountNumberWithCommas(
          permiumUser.masterData?.salaryInfo ??
            permiumUser.masterData?.netPaySalary ??
            0,
        );
        const userStage =
          this.getActionTrack(permiumUser?.stage).replace(/_/g, ' ') ?? '-';

        const userAdminStageStatus = this.getStageStatusString(
          permiumUser?.stageStatus,
        );

        const obj = {
          Name: permiumUser?.fullName ?? '-',
          'Loan id': permiumUser?.lastLoanId ?? '-',
          Phone: this.cryptService.decryptPhone(permiumUser?.phone) ?? '-',
          Salary: salaryAmmount,
          'Cibil Score': permiumUser?.cibilScore ?? '-',
          'PL Score': permiumUser?.plScore ?? '-',
          'PL Accounts': permiumUser?.PLAccounts ?? '-',
          'Experian Score': permiumUser?.experian?.experianScore ?? '-',
          'E-Overdue Accounts': permiumUser?.experian?.overdueAccounts ?? '-',
          'E-Overdue Amount': permiumUser?.experian?.overdueAmount ?? '-',
          'E-Inquiries(30D)': permiumUser?.experian?.inquiryPast30Days ?? '-',
          'User id': permiumUser?.id ?? '-',
          'Created at':
            this.typeService.dateToJsonStr(permiumUser?.createdAt) ?? '-',
          'Action track': userStage,
          'Assigned CSE':
            (
              await this.CommonSharedService.getAdminData(
                permiumUser.masterData?.assignedCSE,
              )
            )?.fullName ?? '-',
          'Last CRM by': permiumUser?.lastCrm?.adminName ?? '-',
          'Last CRM remark': permiumUser?.lastCrm?.remark ?? '-',
          Status: userAdminStageStatus,
        };

        permiumUsersList.push(obj);
      }

      // Download -> Report in excel
      if (download && allPremiumUsersDataRes.length > 0) {
        const rawExcelData = {
          sheets: ['local-reports'],
          data: [permiumUsersList],
          sheetName: 'All Premium Users List.xlsx',
          needFindTuneKey: false,
        };
        const url: any = await this.fileService.objectToExcelURL(rawExcelData);
        if (url?.message) return url;
        return { fileUrl: url };
      }

      let allPremiumUsersData: any = {};
      allPremiumUsersData.count = allPremiumUsersDataResForCount?.length || 0;
      allPremiumUsersData.rows = permiumUsersList;
      return allPremiumUsersData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#endregion

  private async performAnotherCurlAction(userIds) {
    if (!gIsPROD) return {};
    if (EnvConfig.nbfc.nbfcType != '0') return {};

    const token = Buffer.from(
      `${EnvConfig.neighbour.dataCodesUserName}:${EnvConfig.neighbour.dataCodesPassword}`,
      'utf8',
    ).toString('base64');
    const headers = { Authorization: `Basic ${token}` };

    const query = `SELECT user_id, lead_score FROM user_details WHERE user_id IN (${userIds})`;

    const payload = {
      db_type: 'clickHouse',
      query: query,
    };
    return await this.api.post(nDataCodes.user.dbGetUrl, payload, null, null, {
      headers,
    });
  }
}
