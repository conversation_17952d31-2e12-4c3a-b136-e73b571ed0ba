// Imports
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { Op } from 'sequelize';
import { EXPERIAN_CONFIG } from 'src/constants/globals';
import { k500Error } from 'src/constants/misc';
import { k422ErrorMessage, kNoDataFound } from 'src/constants/responses';
import { ExperianScoreEntity } from 'src/entities/experianScore.entity';
import { KYCEntity } from 'src/entities/kyc.entity';
import { registeredUsers } from 'src/entities/user.entity';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { ExperianThirdParty } from 'src/thirdParty/experian/experian.service';
import { CryptService } from 'src/utils/crypt.service';
import { FileService } from 'src/utils/file.service';
import { TypeService } from 'src/utils/type.service';
import { NSModel } from 'src/admin/cibil_score/model/ns.tudf.model';
import { RedisService } from 'src/redis/redis.service';
import { RedisKeys } from 'src/constants/objects';
import { EnvConfig } from 'src/configs/env.config';
import {
  experianAccountHoldertypeCode,
  experianAccountType,
  experianAddressType,
  experianCollateralType,
  experianCreditFacilityStatus,
  experianEnquiryReason,
  experianFrequencyOfPayment,
  experianGenderCode,
  experianPaymentHistory,
  experianStateCode,
  experianSuitFiled,
  experianTelephoneType,
  kExperianMockResponse,
} from 'src/constants/experian';
import { BureauService } from 'src/admin/bureau/bureau.service';
import { SequelOptions } from 'src/interfaces/include.options';
import { MasterEntity } from 'src/entities/master.entity';
import { APIService } from 'src/utils/api.service';
import { nPredictSoftEligibility } from 'src/constants/network';

@Injectable()
export class ExperianSharedService {
  constructor(
    //third party
    @Inject(forwardRef(() => ExperianThirdParty))
    private readonly experianThirdParty: ExperianThirdParty,
    //utils
    private readonly fileService: FileService,
    private readonly typeService: TypeService,
    private readonly NSModel: NSModel,
    // Database
    private readonly cryptService: CryptService,
    // repo
    private readonly repoManager: RepositoryManager,
    private readonly redisService: RedisService,
    @Inject(forwardRef(() => BureauService))
    private readonly bureauService: BureauService,
    private readonly api: APIService,
  ) {}

  async getExperianDetails(reqData) {
    const {
      userId,
      mobileNo,
      panNumber,
      firstName,
      middleName,
      surName,
      email,
      message,
    }: any = await this.validateAndFetchData(reqData);
    if (message) {
      return message;
    }

    const experianReqData = new URLSearchParams({
      clientName: EXPERIAN_CONFIG.EXPERIAN_CLIENT_NAME,
      allowInput: '1',
      allowEdit: '1',
      allowCaptcha: '1',
      allowConsent: '1',
      allowEmailVerify: '1',
      allowVoucher: '1',
      voucherCode: EXPERIAN_CONFIG.EXPERIAN_VOUCHER_CODE,
      firstName,
      middleName,
      surName,
      mobileNo,
      reason: 'Find out my credit score',
      noValidationByPass: '0',
      emailConditionalByPass: '1',
    });
    let existingData;

    await this.bureauService.deleteRedisDataForMultipleUserId(userId);
    await this.bureauService.deleteRedisDataForFV(userId);

    /* Manipulate the cibil response with the help of redis 
    as per testing requirements, Works in DEV, UAT, PROD all environment modes*/
    const mockResponse = await this.getMockResponse(panNumber);
    if (mockResponse != null) existingData = mockResponse;
    else {
      /* Manipulate the experian response with the help of postgres
            as per testing requirements, Works in DEV, UAT, PROD all environment modes*/
      const dbResponse = await this.getDBResponse(userId);
      if (dbResponse != null) existingData = dbResponse;

      if (
        email &&
        (email.includes(EnvConfig.emailDomain.companyEmailDomain1) ||
          email.includes(EnvConfig.emailDomain.companyEmailDomain2))
      ) {
        const reportDate = new Date();
        const formattedDate = [
          reportDate.getFullYear(),
          String(reportDate.getMonth() + 1).padStart(2, '0'),
          String(reportDate.getDate()).padStart(2, '0'),
        ].join('');
        existingData = kExperianMockResponse;
        existingData.INProfileResponse.Current_Application.Current_Application_Details.Current_Applicant_Details.First_Name =
          firstName;
        existingData.INProfileResponse.CreditProfileHeader.ReportDate =
          formattedDate;
      }
    }

    const rData = {
      userId,
      requestData: Object.fromEntries(experianReqData),
    };
    const experianData = await this.repoManager.createRowData(
      ExperianScoreEntity,
      rData,
    );
    if (experianData == k500Error) throw new Error();
    if (!experianData) return kNoDataFound;

    let result =
      existingData != null
        ? existingData
        : await this.experianThirdParty.getEnhancedMatchData(experianReqData);

    if (
      result?.errorString == null ||
      result?.errorString == 'consumer record not found'
    ) {
      const updatedData: any = {};
      updatedData.responseData = result;
      if (result?.internal_source) {
        const key = result.internal_source.toUpperCase();
        if (key === 'POSTGRES') updatedData.internal_source = 1;
        else if (key === 'REDIS') updatedData.internal_source = 2;
      }
      if (result.showHtmlReportForCreditReport)
        result = await this.fileService.xmlToJson(
          result.showHtmlReportForCreditReport,
        );
      const data = result?.INProfileResponse;
      updatedData.formattedResponse = result;

      if (result?.errorString === 'consumer record not found') {
        updatedData.experianScore = -1;
      } else {
        updatedData.experianScore = +(data?.SCORE?.BureauScore ?? 0);
      }
      updatedData.inquiryPast30Days = +(
        data?.TotalCAPS_Summary?.TotalCAPSLast30Days ?? 0
      );

      if (result?.errorString === 'consumer record not found') {
        updatedData.experianFetchDate = new Date();
      } else {
        updatedData.experianFetchDate = this.typeService.experianDateToDBDate(
          data?.CreditProfileHeader?.ReportDate,
        );
      }

      const experianAccounts = data?.CAIS_Account;

      let PLAccounts = 0;
      let ourOverdueAmount = 0;
      let totalDelayDays = 0;
      let ourOverdueAccounts = 0;
      let PLOutstanding = 0;

      for (
        let index = 0;
        index < experianAccounts?.CAIS_Account_DETAILS?.length;
        index++
      ) {
        const element = experianAccounts?.CAIS_Account_DETAILS[index];

        const PLAccountTypeIds = [
          '00',
          '05',
          '06',
          '09',
          '24',
          '40',
          '41',
          '43',
          '45',
          '51',
          '52',
          '53',
          '54',
          '55',
          '56',
          '57',
          '58',
          '59',
          '60',
          '61',
          '69',
          '71',
        ];
        if (PLAccountTypeIds.includes(element.Account_Type)) {
          PLOutstanding += +(element?.Current_Balance ?? 0);
          PLAccounts++;
        }

        if (element?.Date_Closed == '') {
          ourOverdueAmount += +element.Amount_Past_Due || 0;
          ourOverdueAccounts += +element.Amount_Past_Due ? 1 : 0;

          const delayHistory = element?.CAIS_Account_History
            ? Array.isArray(element.CAIS_Account_History)
              ? element.CAIS_Account_History
              : [element.CAIS_Account_History]
            : [];

          const latestEntry = delayHistory.length ? delayHistory[0] : null;

          const delayDays = Number(latestEntry?.Days_Past_Due) || 0;

          totalDelayDays += delayDays;
        }
      }
      updatedData.PLOutstanding = PLOutstanding;
      updatedData.PLAccounts = PLAccounts ?? 0;
      updatedData.totalOutstanding = +(
        experianAccounts?.CAIS_Summary?.Total_Outstanding_Balance
          ?.Outstanding_Balance_All ?? 0
      );
      updatedData.overdueAccounts = ourOverdueAccounts;
      updatedData.overdueAmount = ourOverdueAmount;
      updatedData.totalDelayDays = totalDelayDays;

      await this.repoManager.updateRowData(
        ExperianScoreEntity,
        updatedData,
        experianData.id,
      );
      result.success = true;

      try {
        console.log(
          'reqData.parallelHit',
          reqData.parallelHit,
          nPredictSoftEligibility,
        );
        if (reqData.parallelHit) {
          await this.api.post(nPredictSoftEligibility, { userId });
        }
      } catch (error) {}

      return result;
    } else {
      const updatedData: any = {};
      updatedData.responseData = result;
      await this.repoManager.updateRowData(
        ExperianScoreEntity,
        updatedData,
        experianData.id,
      );
      result.success = false;

      return result;
    }
  }

  async validateAndFetchData(reqData) {
    const customerId = reqData['Member Reference Number']?.substring(2);
    let userId = reqData?.userId;
    if (!customerId && !userId) return k422ErrorMessage('Invalid Details');

    // Fetch user data only if needed
    const masterInc: SequelOptions = {
      attributes: ['otherInfo'],
      model: MasterEntity,
    };
    const userData = await this.repoManager.getRowWhereData(
      registeredUsers,
      ['completedLoans', 'id', 'fullName', 'phone', 'email'],
      {
        include: [masterInc],
        where: userId ? { id: userId } : { uniqueId: customerId },
      },
    );
    if (userData === k500Error) throw new Error();
    if (!userData) return kNoDataFound;
    // For parallel hit
    if (reqData.parallelHit) {
      const otherInfo = userData?.masterData?.otherInfo ?? {};
      let userEnteredSalary =
        otherInfo?.salaryInfo ?? otherInfo?.netPaySalary ?? 0;
      userEnteredSalary = +userEnteredSalary;
      if (
        isNaN(userEnteredSalary) ||
        userEnteredSalary < 50000 ||
        userData.completedLoans > 0
      ) {
        return k422ErrorMessage('user is not allowed to hit experian');
      }
    }

    userId = userId || userData?.id;

    // Fetch PAN number if not provided
    let panNumber = reqData['Income Tax ID Number'];
    if (!panNumber) {
      const kycData = await this.repoManager.getRowWhereData(
        KYCEntity,
        ['panCardNumber'],
        {
          where: { userId },
          order: [['id', 'DESC']],
        },
      );
      if (kycData === k500Error) throw new Error();
      panNumber = kycData?.panCardNumber;
    }

    const fullName = reqData['Consumer Name'] ?? userData?.fullName ?? '';
    const nameLine = this.NSModel.getAddressNameLines(fullName, 26);

    const phone = await this.cryptService.decryptPhone(userData?.phone);
    const mobileNo = reqData['Telephone Number 1 (Mobile)'] ?? phone;
    const email = userData?.email;

    return {
      customerId,
      userId,
      userData,
      panNumber,
      firstName: nameLine[0] ?? '',
      middleName: nameLine[2] ?? '',
      surName: nameLine[1] ?? '',
      mobileNo,
      email,
    };
  }

  /* Manipulate the experian response with the help of redis 
          as per testing requirements, Works in DEV, UAT, PROD all environment modes*/
  private async getMockResponse(panNumber) {
    // Get mock data
    let mockResponse =
      (await this.redisService.getKeyDetails(RedisKeys.MOCK_EXPERIAN_DATA)) ??
      {};
    if (typeof mockResponse == 'string')
      mockResponse = JSON.parse(mockResponse);
    if (typeof mockResponse == 'string')
      mockResponse = JSON.parse(mockResponse);

    if (mockResponse[panNumber])
      return { ...mockResponse[panNumber], internal_source: 'REDIS' };
    else return;
  }

  /* Manipulate the experian response with the help of postgres
                as per testing requirements, Works in DEV, UAT, PROD all environment modes*/
  private async getDBResponse(userId) {
    const today = new Date();
    // New experian should not get fetched within 30 days of previous fetched date
    today.setDate(today.getDate() - 30);
    const attributes = ['id', 'internal_source', 'responseData'];
    const options = {
      order: [['id', 'DESC']],
      where: {
        userId,
        createdAt: { [Op.gte]: today },
        experianFetchDate: { [Op.ne]: null },
        internal_source: null,
      },
    };
    let existingData: any;

    existingData = await this.repoManager.getRowWhereData(
      ExperianScoreEntity,
      attributes,
      options,
    );
    if (existingData == k500Error) return;
    if (!existingData) return;

    if (!existingData?.internal_source) {
      return { ...existingData.responseData, internal_source: 'POSTGRES' };
    }
  }

  async getExperianData(userArr, userIds) {
    try {
      if (userIds.length > 0) {
        const experianAttrs = [
          'id',
          'userId',
          'experianScore',
          'overdueAccounts',
          'overdueAmount',
          'inquiryPast30Days',
        ];
        const experianOpts = {
          where: { userId: userIds },
          order: [['id', 'DESC']],
        };
        const experianData = await this.repoManager.getTableWhereData(
          ExperianScoreEntity,
          experianAttrs,
          experianOpts,
        );
        if (experianData === k500Error) return userArr;

        for (let i = 0; i < userArr?.length; i++) {
          const find = experianData.find(
            (data) => data.userId === userArr[i].id,
          );
          if (find) userArr[i].experian = find;
        }
      }
    } catch (e) {}
    return userArr;
  }

  async getExperianDataIdWise(reqData) {
    const id = reqData?.id;

    // Fetch experian data
    const experianAttributes = ['id', 'formattedResponse', 'experianFetchDate'];
    const experianRawQuery = `SELECT "${experianAttributes.join('","')}"
       FROM "ExperianScoreEntities"
       WHERE "id" = '${id}';`;

    const experianResponse = await this.repoManager.injectRawQuery(
      ExperianScoreEntity,
      experianRawQuery,
      { useMaster: false },
    );
    if (experianResponse === k500Error) throw new Error();
    if (!experianResponse.length) return {};

    // prepare experian response
    const result = this.prepareExperianData(
      experianResponse[0],
      'consumerCreditDetails',
    );
    return result;
  }

  prepareExperianData(scoreData, type?) {
    // Prepare Cibil Data
    let experianDetails: any = {};
    const resData = scoreData?.formattedResponse?.INProfileResponse;
    if (!resData) return;
    if (type == 'consumerCreditDetails') {
      let accountDetails: any = {};
      const caisAccounts = resData?.CAIS_Account?.CAIS_Account_DETAILS ?? [];
      const caisAcc = Array.isArray(caisAccounts)
        ? caisAccounts
        : [caisAccounts];
      const capsDetails = resData?.CAPS?.CAPS_Application_Details ?? [];
      const caps = Array.isArray(capsDetails) ? capsDetails : [capsDetails];

      const names = [];
      const ids = [];
      const telephones = [];
      const emails = [];
      const addresses = [];
      const accounts = [];
      const enquiries = [];
      let uniqueEntries = new Set();

      for (let i = 0; i < caisAcc.length; i++) {
        const el = caisAcc[i];
        const acc = el?.CAIS_Holder_Details;
        const phoneDetails = el?.CAIS_Holder_Phone_Details;
        const accHolderId = el?.CAIS_Holder_ID_Details;
        const address = el?.CAIS_Holder_Address_Details;

        this.extractName(acc, names, uniqueEntries);
        this.extractIds(acc, accHolderId, ids, uniqueEntries);
        this.extractPhone(phoneDetails, telephones, uniqueEntries);
        this.extractEmail(phoneDetails, accHolderId, emails, uniqueEntries);
        this.extractAddress(address, addresses, el, uniqueEntries);
        this.extractAccounts(el, accounts);
      }

      accounts.sort((a, b) => {
        try {
          if (!a.dateOpened || !b.dateOpened) return 0;
          const [ddA, mmA, yyyyA] = a.dateOpened.split('/').map(Number);
          const [ddB, mmB, yyyyB] = b.dateOpened.split('/').map(Number);
          const dateA = new Date(yyyyA, mmA - 1, ddA).getTime();
          const dateB = new Date(yyyyB, mmB - 1, ddB).getTime();
          return dateA - dateB;
        } catch (error) {
          return 0;
        }
      });

      if (caps.length) {
        for (let i = 0; i < caps.length; i++) {
          const ele = caps[i];
          this.extractEnquiries(ele, enquiries);
        }
      }
      accountDetails.names = names;
      accountDetails.ids = ids;
      accountDetails.telephones = telephones;
      accountDetails.emails = emails;
      accountDetails.addresses = addresses;
      accountDetails.accounts = accounts;
      accountDetails.enquiries = enquiries;
      return accountDetails;
    }

    const caisAccount = resData?.CAIS_Account;
    experianDetails.bureauScore = scoreData?.experianScore ?? '-';
    experianDetails.plScore = '-';
    experianDetails.totalAccounts =
      Number(caisAccount?.CAIS_Summary?.Credit_Account?.CreditAccountTotal) ??
      '-';
    experianDetails.overdueAccounts = scoreData?.overdueAccounts ?? '-';

    const caisAcc = caisAccount?.CAIS_Account_DETAILS
      ? Array.isArray(caisAccount?.CAIS_Account_DETAILS)
        ? caisAccount?.CAIS_Account_DETAILS
        : [caisAccount?.CAIS_Account_DETAILS]
      : [];

    const aggregatedData = caisAcc?.reduce(
      (acc, el) => {
        const highestCredit =
          Number(el?.Highest_Credit_or_Original_Loan_Amount) || 0;
        const currentBalance = Number(el?.Current_Balance) || 0;
        const reportedDateStr = el?.Date_Reported;

        if (reportedDateStr) {
          const formattedDate =
            this.typeService.experianDateToDBDate(reportedDateStr); // Convert YYYYMMDD → YYYY-MM-DD
          const actualDate = new Date(formattedDate);

          if (!acc?.recentDateOpened || actualDate > acc?.recentDateOpened) {
            acc.recentDateOpened = actualDate;
          }
          if (!acc?.oldestDateOpened || actualDate < acc?.oldestDateOpened) {
            acc.oldestDateOpened = actualDate;
          }
        }

        acc.zeroBalanceAccounts +=
          el?.Date_Closed && currentBalance === 0 ? 1 : 0;
        acc.highCreditAmount = Math.max(acc.highCreditAmount, highestCredit);
        acc.currentBalance += currentBalance;
        return acc;
      },
      {
        zeroBalanceAccounts: 0,
        highCreditAmount: 0,
        currentBalance: 0,
        recentDateOpened: null,
        oldestDateOpened: null,
      },
    );

    experianDetails.zeroBalanceAccounts =
      aggregatedData?.zeroBalanceAccounts ?? '-';
    experianDetails.highCreditAmount = aggregatedData?.highCreditAmount ?? '-';
    experianDetails.currentBalance = aggregatedData?.currentBalance ?? '-';

    experianDetails.recentDateOpened = aggregatedData?.recentDateOpened
      ? this.typeService.dateToJsonStr(
          aggregatedData.recentDateOpened,
          'DD/MM/YYYY',
        )
      : '-';
    experianDetails.oldestDateOpened = aggregatedData?.oldestDateOpened
      ? this.typeService.dateToJsonStr(
          aggregatedData.oldestDateOpened,
          'DD/MM/YYYY',
        )
      : '-';

    experianDetails.overdueBalance = scoreData?.overdueAmount ?? '-';
    experianDetails.totalOverdueDays = scoreData?.totalDelayDays ?? '-';
    experianDetails.PLOutstanding = scoreData?.PLOutstanding ?? '-';
    experianDetails.totalOutstanding = scoreData?.totalOutstanding ?? '-';
    experianDetails.monthlyIncome = '-';
    const totalInquiry =
      resData?.CAPS?.CAPS_Application_Details?.length +
        +resData?.NonCreditCAPS?.NonCreditCAPS_Summary
          ?.NonCreditCAPSLast180Days || 0;
    experianDetails.totalInquiry = totalInquiry ?? '-';
    experianDetails.inquiryPast30Days = scoreData?.inquiryPast30Days ?? '-';
    experianDetails.inquiryPast12Months = '-';
    experianDetails.inquiryPast24Months = '-';
    experianDetails.PLAccounts = scoreData?.PLAccounts ?? '-';

    const capsDetails = resData?.CAPS?.CAPS_Application_Details ?? [];
    const CAPS = Array.isArray(capsDetails) ? capsDetails : [capsDetails];
    const inquiryData = CAPS?.reduce(
      (acc, el) => {
        const requestDateStr = el?.Date_of_Request;

        if (requestDateStr) {
          const formattedDate =
            this.typeService.experianDateToDBDate(requestDateStr); // Convert YYYYMMDD → YYYY-MM-DD
          const actualDate = new Date(formattedDate);

          if (!acc?.recentInquiryDate || actualDate > acc?.recentInquiryDate) {
            acc.recentInquiryDate = actualDate;
          }
          if (!acc?.oldestInquiryDate || actualDate < acc?.oldestInquiryDate) {
            acc.oldestInquiryDate = actualDate;
          }
        }
        return acc;
      },
      { recentInquiryDate: null, oldestInquiryDate: null },
    );

    experianDetails.recentInquiryDate = inquiryData?.recentInquiryDate
      ? this.typeService.dateToJsonStr(
          inquiryData.recentInquiryDate,
          'DD/MM/YYYY',
        )
      : '-';
    experianDetails.oldestInquiryDate = inquiryData?.oldestInquiryDate
      ? this.typeService.dateToJsonStr(
          inquiryData.oldestInquiryDate,
          'DD/MM/YYYY',
        )
      : '-';
    return experianDetails;
  }

  extractName(holder, namesList, uniqueEntries) {
    const first = (holder?.First_Name_Non_Normalized || '').trim();
    const last = (holder?.Surname_Non_Normalized || '').trim();
    if (!first || !last) return;

    const name = `${first} ${last}`.toUpperCase().replace(/\s+/g, ' ');
    const birthDate = holder?.Date_of_birth
      ? this.typeService.dateToJsonStr(
          this.typeService.experianDateToDBDate(holder?.Date_of_birth),
          'DD/MM/YYYY',
        )
      : '';

    const gender = experianGenderCode[holder?.Gender_Code] || '';

    const key =
      [first, last].sort().join('|').toLowerCase() + `|${birthDate}|${gender}`;

    if (uniqueEntries.has(key)) return;
    uniqueEntries.add(key);
    namesList.push({ name, birthDate, ...(gender && { gender }) });
  }

  extractIds(holder, idInfo, ids, uniqueEntries) {
    const idData = {
      'Income Tax ID Number (PAN)':
        holder?.Income_TAX_PAN ?? idInfo?.Income_TAX_PAN,
      'Passport Number': idInfo?.Passport_Number,
      'Voter ID Number': idInfo?.Voter_ID_Number,
      'Ration Card Number': idInfo?.Ration_Card_Number,
      'Universal ID Number (UID)': idInfo?.Universal_ID_Number,
      'Driver’s License Number': idInfo?.Driver_License_Number,
    };

    const validIds = Object.entries(idData).filter(([_, v]) => v);
    if (!validIds.length) return;

    const key = validIds.map(([_, v]) => v).join('|');
    if (uniqueEntries.has(key)) return;

    uniqueEntries.add(key);

    for (const [type, number] of validIds) {
      ids.push({ idType: type, idNumber: number });
    }
  }

  extractPhone(phoneInfo, telephones, uniqueEntries) {
    const phones = Array.isArray(phoneInfo) ? phoneInfo : [phoneInfo];

    for (const phone of phones) {
      const telephoneNumber =
        phone?.Mobile_Telephone_Number || phone?.Telephone_Number;
      const telephoneType = experianTelephoneType[phone?.Telephone_Type];
      const idKey = `${telephoneNumber}|${telephoneType}`;

      if (telephoneNumber && !uniqueEntries.has(idKey)) {
        uniqueEntries.add(idKey);
        telephones.push({ telephoneType, telephoneNumber });
      }
    }
  }

  extractEmail(phoneInfo, idInfo, emails, uniqueEntries) {
    const emailIds = [
      ...(Array.isArray(phoneInfo)
        ? phoneInfo.map((p) => p?.EMailId)
        : [phoneInfo?.EMailId]),
      idInfo?.EMailId,
    ].filter(Boolean);

    for (const email of emailIds) {
      const lowerEmail = email.toLowerCase();
      if (!uniqueEntries.has(lowerEmail)) {
        uniqueEntries.add(lowerEmail);
        emails.push({ emailID: lowerEmail });
      }
    }
  }

  extractAddress(addressInfo, addresses, el, uniqueEntries) {
    const addressList = Array.isArray(addressInfo)
      ? addressInfo
      : [addressInfo];

    for (const addr of addressList) {
      if (!addr) continue;

      const rawAddress = [
        addr?.First_Line_Of_Address_non_normalized,
        addr?.Second_Line_Of_Address_non_normalized,
        addr?.Third_Line_Of_Address_non_normalized,
        addr?.Fifth_Line_Of_Address_non_normalized,
      ]
        .filter(Boolean)
        .join(' ')
        .replace(/[^a-zA-Z0-9 ]/g, '')
        .replace(/\s+/g, ' ')
        .trim()
        .toLowerCase();

      const pinCode = (addr?.ZIP_Postal_Code_non_normalized || '')
        .replace(/\s+/g, '')
        .trim();
      const addressKey = `${rawAddress}|${pinCode}`;

      if (!rawAddress || uniqueEntries.has(addressKey)) continue;

      uniqueEntries.add(addressKey);

      addresses.push({
        address: rawAddress.toUpperCase(),
        pinCode,
        stateCode: experianStateCode[addr?.State_non_normalized] || '',
        dateReported: el?.Date_Reported
          ? this.typeService.dateToJsonStr(
              this.typeService.experianDateToDBDate(el?.Date_Reported),
              'DD/MM/YYYY',
            )
          : '',
        addressCategory:
          experianAddressType[addr?.Address_indicator_non_normalized] || '',
      });
    }
  }

  extractAccounts(el, accounts) {
    const delayHistory = el?.CAIS_Account_History
      ? Array.isArray(el.CAIS_Account_History)
        ? el.CAIS_Account_History
        : [el.CAIS_Account_History]
      : [];
    const latestEntry = delayHistory[0] || {};

    let today = new Date();
    today.setMonth(today.getMonth() - 6);

    const dateReported = new Date(
      this.typeService.experianDateToDBDate(el?.Date_Reported),
    );

    let past6MonDelayDays = 0;
    if (dateReported >= today) {
      const monthDiff = this.typeService.dateDifference(
        today,
        dateReported,
        'Month',
      );
      const historyArray =
        monthDiff === 0 ? [delayHistory[0]] : delayHistory.slice(0, monthDiff);
      past6MonDelayDays = historyArray.reduce((max, entry) => {
        const delay = parseInt(entry.Days_Past_Due, 10);
        return isNaN(delay) ? max : Math.max(max, delay);
      }, 0);
    }

    const hasValidDaysPastDue = delayHistory.some(
      (entry) => entry.Days_Past_Due,
    );

    const paymentHistory = hasValidDaysPastDue
      ? delayHistory
          .filter((entry) => entry.Days_Past_Due)
          .map((entry) => entry.Days_Past_Due.toString().padStart(3, '0'))
          .join('')
      : el?.Payment_History_Profile
      ? el.Payment_History_Profile.split('')
          .map((char) =>
            experianPaymentHistory.hasOwnProperty(char)
              ? experianPaymentHistory[char].padStart(3, '0')
              : char.repeat(3),
          )
          .join('')
      : '-';

    const Obj = {
      emiAmount: el?.Scheduled_Monthly_Payment_Amount,
      dateOpened: el?.Open_Date
        ? this.typeService.dateToJsonStr(
            this.typeService.experianDateToDBDate(el.Open_Date),
            'DD/MM/YYYY',
          )
        : '',
      accountType: el?.Account_Type ? experianAccountType[el.Account_Type] : '',
      dateReported: el?.Date_Reported
        ? this.typeService.dateToJsonStr(
            this.typeService.experianDateToDBDate(el.Date_Reported),
            'DD/MM/YYYY',
          )
        : '',
      accountNumber: el?.Account_Number ?? '',
      dateClosed: el?.Date_Closed
        ? this.typeService.dateToJsonStr(
            this.typeService.experianDateToDBDate(el.Date_Closed),
            'DD/MM/YYYY',
          )
        : '',
      amountOverdue: +el?.Amount_Past_Due || 0,
      lastDelayDays: +latestEntry?.Days_Past_Due || 0,
      paymentTenure: +el?.Repayment_Tenure || 0,
      currentBalance: +el?.Current_Balance || 0,
      memberShortName: el?.Subscriber_Name ?? '',
      highCreditAmount: +el?.Highest_Credit_or_Original_Loan_Amount || 0,
      paymentFrequency: el?.Terms_Frequency
        ? experianFrequencyOfPayment[el.Terms_Frequency]
        : '',
      ownershipIndicator: el?.AccountHoldertypeCode
        ? experianAccountHoldertypeCode[el.AccountHoldertypeCode]
        : '',
      collateralType: el?.Type_of_Collateral
        ? experianCollateralType[el.Type_of_Collateral]
        : '',
      creditLimit: el?.Credit_Limit_Amount ?? '',
      actualPaymentAmount: +el?.Original_Charge_Off_Amount || 0,
      suitFiled: el?.SuitFiled_WilfulDefault
        ? experianSuitFiled[el.SuitFiled_WilfulDefault]
        : '',
      creditFacilityStatus: el?.Written_off_Settled_Status
        ? experianCreditFacilityStatus[el.Written_off_Settled_Status]
        : '',
      collateralValue: +el?.Value_of_Collateral || 0,
      interestRate: el?.Rate_of_Interest ?? 0,
      woAmountTotal: +el?.Written_Off_Amt_Total || 0,
      woAmountPrincipal: +el?.Written_Off_Amt_Principal || 0,
      settlementAmount: +el?.Settlement_Amount || 0,
      lastPaymentDate: el?.Date_of_Last_Payment
        ? this.typeService.dateToJsonStr(
            this.typeService.experianDateToDBDate(el.Date_of_Last_Payment),
            'DD/MM/YYYY',
          )
        : '',
      past6MonDelayDays,
      paymentHistory,
    };

    accounts.push(Obj);
  }

  extractEnquiries(ele, enquiries) {
    const Obj = {
      enquiryDate: ele?.Date_of_Request
        ? this.typeService.dateToJsonStr(
            this.typeService.experianDateToDBDate(ele.Date_of_Request),
            'DD/MM/YYYY',
          )
        : '',
      enquiryAmount: +ele?.Amount_Financed || 0,
      enquiryPurpose: ele?.Enquiry_Reason
        ? experianEnquiryReason[ele.Enquiry_Reason]
        : '',
      memberShortName: ele?.Subscriber_Name ?? '',
    };

    enquiries.push(Obj);
  }

  // start region Get User experian Data
  async funGetUserExperianScoreData(reqData) {
    const userId = reqData?.userId;
    // Fetch experian data
    const experianAttributes = [
      'id',
      'experianScore',
      'overdueAccounts',
      'inquiryPast30Days',
      'PLAccounts',
      'overdueAmount',
      'totalDelayDays',
      'PLOutstanding',
      'totalOutstanding',
      'experianFetchDate',
      'formattedResponse',
    ];

    const experianRawQuery = `SELECT "${experianAttributes.join('","')}"
      FROM "ExperianScoreEntities"
      WHERE ("internal_source" IS NULL OR "internal_source" != 1) AND "userId" = '${userId}'
      ORDER BY "id" DESC LIMIT 1;`;

    const experianResponse = await this.repoManager.injectRawQuery(
      ExperianScoreEntity,
      experianRawQuery,
      { useMaster: false },
    );
    if (experianResponse === k500Error) throw new Error();

    const result = this.prepareExperianData(experianResponse[0]);
    return result;
  }
}
