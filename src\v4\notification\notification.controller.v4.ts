import { <PERSON>, Get, Headers, Query, Res } from '@nestjs/common';
import { kInternalError, kSuccessData } from 'src/constants/responses';
import { NotificationServiceV4 } from './notification.service.v4';
import { ErrorContextService } from 'src/utils/error.context.service';

@Controller('notification')
export class NotificationControllerV4 {
  constructor(
    private readonly service: NotificationServiceV4,
    private readonly errorContextService: ErrorContextService,
  ) {}

  @Get('countList')
  async funCountList(@Query() query, @Headers() headers, @Res() res) {
    try {
      const data: any = await this.service.countList(query, headers);
      if (data?.message) return res.send(data);
      return res.send({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.send(kInternalError);
    }
  }
}
