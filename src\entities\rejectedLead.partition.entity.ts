// Imports
import { Model, Table } from 'sequelize-typescript';
import { Column, DataType } from 'sequelize-typescript';

@Table({})
export class RejectedLead_0 extends Model<RejectedLead_0> {
  @Column({
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
    type: DataType.UUID,
  })
  id: string;

  @Column({
    type: DataType.SMALLINT,
    comment:
      '0: Accepted, 1: In Process, 2: Rejected, 3: Disbursed, 4: Existing',
  })
  leadStatus: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0: <PERSON> Buddha, 1: <PERSON><PERSON><PERSON><PERSON>, 2: Switch<PERSON>yLoan, 3: LoanTap',
  })
  leadSource: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Salaried, 1: Self employed' })
  jobType: number;

  @Column({ type: DataType.SMALLINT })
  cibilScore: number;

  @Column({ type: DataType.SMALLINT })
  age: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Male, 1: Female' })
  gender: number;

  @Column({ type: DataType.DATEONLY })
  dob: string;

  @Column({ type: DataType.INTEGER, allowNull: false })
  salary: number;

  @Column({ type: DataType.INTEGER })
  pincode: number;

  @Column({ type: DataType.TEXT })
  fullName: string;

  @Column({ type: DataType.TEXT })
  email: string;

  @Column({ type: DataType.TEXT })
  state: string;

  @Column({ type: DataType.TEXT })
  rejectReasons: string;

  @Column({ type: DataType.TEXT })
  phone: string;

  @Column({ type: DataType.TEXT })
  hashPhone: string;

  @Column({ type: DataType.TEXT })
  pan: string;

  @Column({ type: DataType.TEXT })
  hashPAN: string;
}

@Table({})
export class RejectedLead_1 extends Model<RejectedLead_1> {
  @Column({
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
    type: DataType.UUID,
  })
  id: string;

  @Column({
    type: DataType.SMALLINT,
    comment:
      '0: Accepted, 1: In Process, 2: Rejected, 3: Disbursed, 4: Existing',
  })
  leadStatus: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0: Finance Buddha, 1: FintiFi, 2: SwitchMyLoan, 3: LoanTap',
  })
  leadSource: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Salaried, 1: Self employed' })
  jobType: number;

  @Column({ type: DataType.SMALLINT })
  cibilScore: number;

  @Column({ type: DataType.SMALLINT })
  age: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Male, 1: Female' })
  gender: number;

  @Column({ type: DataType.DATEONLY })
  dob: string;

  @Column({ type: DataType.INTEGER, allowNull: false })
  salary: number;

  @Column({ type: DataType.INTEGER })
  pincode: number;

  @Column({ type: DataType.TEXT })
  fullName: string;

  @Column({ type: DataType.TEXT })
  email: string;

  @Column({ type: DataType.TEXT })
  state: string;

  @Column({ type: DataType.TEXT })
  rejectReasons: string;

  @Column({ type: DataType.TEXT })
  phone: string;

  @Column({ type: DataType.TEXT })
  hashPhone: string;

  @Column({ type: DataType.TEXT })
  pan: string;

  @Column({ type: DataType.TEXT })
  hashPAN: string;
}

@Table({})
export class RejectedLead_2 extends Model<RejectedLead_2> {
  @Column({
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
    type: DataType.UUID,
  })
  id: string;

  @Column({
    type: DataType.SMALLINT,
    comment:
      '0: Accepted, 1: In Process, 2: Rejected, 3: Disbursed, 4: Existing',
  })
  leadStatus: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0: Finance Buddha, 1: FintiFi, 2: SwitchMyLoan, 3: LoanTap',
  })
  leadSource: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Salaried, 1: Self employed' })
  jobType: number;

  @Column({ type: DataType.SMALLINT })
  cibilScore: number;

  @Column({ type: DataType.SMALLINT })
  age: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Male, 1: Female' })
  gender: number;

  @Column({ type: DataType.DATEONLY })
  dob: string;

  @Column({ type: DataType.INTEGER, allowNull: false })
  salary: number;

  @Column({ type: DataType.INTEGER })
  pincode: number;

  @Column({ type: DataType.TEXT })
  fullName: string;

  @Column({ type: DataType.TEXT })
  email: string;

  @Column({ type: DataType.TEXT })
  state: string;

  @Column({ type: DataType.TEXT })
  rejectReasons: string;

  @Column({ type: DataType.TEXT })
  phone: string;

  @Column({ type: DataType.TEXT })
  hashPhone: string;

  @Column({ type: DataType.TEXT })
  pan: string;

  @Column({ type: DataType.TEXT })
  hashPAN: string;
}

@Table({})
export class RejectedLead_3 extends Model<RejectedLead_3> {
  @Column({
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
    type: DataType.UUID,
  })
  id: string;

  @Column({
    type: DataType.SMALLINT,
    comment:
      '0: Accepted, 1: In Process, 2: Rejected, 3: Disbursed, 4: Existing',
  })
  leadStatus: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0: Finance Buddha, 1: FintiFi, 2: SwitchMyLoan, 3: LoanTap',
  })
  leadSource: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Salaried, 1: Self employed' })
  jobType: number;

  @Column({ type: DataType.SMALLINT })
  cibilScore: number;

  @Column({ type: DataType.SMALLINT })
  age: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Male, 1: Female' })
  gender: number;

  @Column({ type: DataType.DATEONLY })
  dob: string;

  @Column({ type: DataType.INTEGER, allowNull: false })
  salary: number;

  @Column({ type: DataType.INTEGER })
  pincode: number;

  @Column({ type: DataType.TEXT })
  fullName: string;

  @Column({ type: DataType.TEXT })
  email: string;

  @Column({ type: DataType.TEXT })
  state: string;

  @Column({ type: DataType.TEXT })
  rejectReasons: string;

  @Column({ type: DataType.TEXT })
  phone: string;

  @Column({ type: DataType.TEXT })
  hashPhone: string;

  @Column({ type: DataType.TEXT })
  pan: string;

  @Column({ type: DataType.TEXT })
  hashPAN: string;
}

@Table({})
export class RejectedLead_4 extends Model<RejectedLead_4> {
  @Column({
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
    type: DataType.UUID,
  })
  id: string;

  @Column({
    type: DataType.SMALLINT,
    comment:
      '0: Accepted, 1: In Process, 2: Rejected, 3: Disbursed, 4: Existing',
  })
  leadStatus: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0: Finance Buddha, 1: FintiFi, 2: SwitchMyLoan, 3: LoanTap',
  })
  leadSource: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Salaried, 1: Self employed' })
  jobType: number;

  @Column({ type: DataType.SMALLINT })
  cibilScore: number;

  @Column({ type: DataType.SMALLINT })
  age: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Male, 1: Female' })
  gender: number;

  @Column({ type: DataType.DATEONLY })
  dob: string;

  @Column({ type: DataType.INTEGER, allowNull: false })
  salary: number;

  @Column({ type: DataType.INTEGER })
  pincode: number;

  @Column({ type: DataType.TEXT })
  fullName: string;

  @Column({ type: DataType.TEXT })
  email: string;

  @Column({ type: DataType.TEXT })
  state: string;

  @Column({ type: DataType.TEXT })
  rejectReasons: string;

  @Column({ type: DataType.TEXT })
  phone: string;

  @Column({ type: DataType.TEXT })
  hashPhone: string;

  @Column({ type: DataType.TEXT })
  pan: string;

  @Column({ type: DataType.TEXT })
  hashPAN: string;
}

@Table({})
export class RejectedLead_5 extends Model<RejectedLead_5> {
  @Column({
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
    type: DataType.UUID,
  })
  id: string;

  @Column({
    type: DataType.SMALLINT,
    comment:
      '0: Accepted, 1: In Process, 2: Rejected, 3: Disbursed, 4: Existing',
  })
  leadStatus: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0: Finance Buddha, 1: FintiFi, 2: SwitchMyLoan, 3: LoanTap',
  })
  leadSource: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Salaried, 1: Self employed' })
  jobType: number;

  @Column({ type: DataType.SMALLINT })
  cibilScore: number;

  @Column({ type: DataType.SMALLINT })
  age: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Male, 1: Female' })
  gender: number;

  @Column({ type: DataType.DATEONLY })
  dob: string;

  @Column({ type: DataType.INTEGER, allowNull: false })
  salary: number;

  @Column({ type: DataType.INTEGER })
  pincode: number;

  @Column({ type: DataType.TEXT })
  fullName: string;

  @Column({ type: DataType.TEXT })
  email: string;

  @Column({ type: DataType.TEXT })
  state: string;

  @Column({ type: DataType.TEXT })
  rejectReasons: string;

  @Column({ type: DataType.TEXT })
  phone: string;

  @Column({ type: DataType.TEXT })
  hashPhone: string;

  @Column({ type: DataType.TEXT })
  pan: string;

  @Column({ type: DataType.TEXT })
  hashPAN: string;
}

@Table({})
export class RejectedLead_6 extends Model<RejectedLead_6> {
  @Column({
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
    type: DataType.UUID,
  })
  id: string;

  @Column({
    type: DataType.SMALLINT,
    comment:
      '0: Accepted, 1: In Process, 2: Rejected, 3: Disbursed, 4: Existing',
  })
  leadStatus: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0: Finance Buddha, 1: FintiFi, 2: SwitchMyLoan, 3: LoanTap',
  })
  leadSource: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Salaried, 1: Self employed' })
  jobType: number;

  @Column({ type: DataType.SMALLINT })
  cibilScore: number;

  @Column({ type: DataType.SMALLINT })
  age: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Male, 1: Female' })
  gender: number;

  @Column({ type: DataType.DATEONLY })
  dob: string;

  @Column({ type: DataType.INTEGER, allowNull: false })
  salary: number;

  @Column({ type: DataType.INTEGER })
  pincode: number;

  @Column({ type: DataType.TEXT })
  fullName: string;

  @Column({ type: DataType.TEXT })
  email: string;

  @Column({ type: DataType.TEXT })
  state: string;

  @Column({ type: DataType.TEXT })
  rejectReasons: string;

  @Column({ type: DataType.TEXT })
  phone: string;

  @Column({ type: DataType.TEXT })
  hashPhone: string;

  @Column({ type: DataType.TEXT })
  pan: string;

  @Column({ type: DataType.TEXT })
  hashPAN: string;
}

@Table({})
export class RejectedLead_7 extends Model<RejectedLead_7> {
  @Column({
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
    type: DataType.UUID,
  })
  id: string;

  @Column({
    type: DataType.SMALLINT,
    comment:
      '0: Accepted, 1: In Process, 2: Rejected, 3: Disbursed, 4: Existing',
  })
  leadStatus: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0: Finance Buddha, 1: FintiFi, 2: SwitchMyLoan, 3: LoanTap',
  })
  leadSource: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Salaried, 1: Self employed' })
  jobType: number;

  @Column({ type: DataType.SMALLINT })
  cibilScore: number;

  @Column({ type: DataType.SMALLINT })
  age: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Male, 1: Female' })
  gender: number;

  @Column({ type: DataType.DATEONLY })
  dob: string;

  @Column({ type: DataType.INTEGER, allowNull: false })
  salary: number;

  @Column({ type: DataType.INTEGER })
  pincode: number;

  @Column({ type: DataType.TEXT })
  fullName: string;

  @Column({ type: DataType.TEXT })
  email: string;

  @Column({ type: DataType.TEXT })
  state: string;

  @Column({ type: DataType.TEXT })
  rejectReasons: string;

  @Column({ type: DataType.TEXT })
  phone: string;

  @Column({ type: DataType.TEXT })
  hashPhone: string;

  @Column({ type: DataType.TEXT })
  pan: string;

  @Column({ type: DataType.TEXT })
  hashPAN: string;
}

@Table({})
export class RejectedLead_8 extends Model<RejectedLead_8> {
  @Column({
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
    type: DataType.UUID,
  })
  id: string;

  @Column({
    type: DataType.SMALLINT,
    comment:
      '0: Accepted, 1: In Process, 2: Rejected, 3: Disbursed, 4: Existing',
  })
  leadStatus: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0: Finance Buddha, 1: FintiFi, 2: SwitchMyLoan, 3: LoanTap',
  })
  leadSource: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Salaried, 1: Self employed' })
  jobType: number;

  @Column({ type: DataType.SMALLINT })
  cibilScore: number;

  @Column({ type: DataType.SMALLINT })
  age: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Male, 1: Female' })
  gender: number;

  @Column({ type: DataType.DATEONLY })
  dob: string;

  @Column({ type: DataType.INTEGER, allowNull: false })
  salary: number;

  @Column({ type: DataType.INTEGER })
  pincode: number;

  @Column({ type: DataType.TEXT })
  fullName: string;

  @Column({ type: DataType.TEXT })
  email: string;

  @Column({ type: DataType.TEXT })
  state: string;

  @Column({ type: DataType.TEXT })
  rejectReasons: string;

  @Column({ type: DataType.TEXT })
  phone: string;

  @Column({ type: DataType.TEXT })
  hashPhone: string;

  @Column({ type: DataType.TEXT })
  pan: string;

  @Column({ type: DataType.TEXT })
  hashPAN: string;
}

@Table({})
export class RejectedLead_9 extends Model<RejectedLead_9> {
  @Column({
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
    type: DataType.UUID,
  })
  id: string;

  @Column({
    type: DataType.SMALLINT,
    comment:
      '0: Accepted, 1: In Process, 2: Rejected, 3: Disbursed, 4: Existing',
  })
  leadStatus: number;

  @Column({
    type: DataType.SMALLINT,
    comment: '0: Finance Buddha, 1: FintiFi, 2: SwitchMyLoan, 3: LoanTap',
  })
  leadSource: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Salaried, 1: Self employed' })
  jobType: number;

  @Column({ type: DataType.SMALLINT })
  cibilScore: number;

  @Column({ type: DataType.SMALLINT })
  age: number;

  @Column({ type: DataType.SMALLINT, comment: '0: Male, 1: Female' })
  gender: number;

  @Column({ type: DataType.DATEONLY })
  dob: string;

  @Column({ type: DataType.INTEGER, allowNull: false })
  salary: number;

  @Column({ type: DataType.INTEGER })
  pincode: number;

  @Column({ type: DataType.TEXT })
  fullName: string;

  @Column({ type: DataType.TEXT })
  email: string;

  @Column({ type: DataType.TEXT })
  state: string;

  @Column({ type: DataType.TEXT })
  rejectReasons: string;

  @Column({ type: DataType.TEXT })
  phone: string;

  @Column({ type: DataType.TEXT })
  hashPhone: string;

  @Column({ type: DataType.TEXT })
  pan: string;

  @Column({ type: DataType.TEXT })
  hashPAN: string;
}
