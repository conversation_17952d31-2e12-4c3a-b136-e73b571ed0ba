// Imports
import { Op } from 'sequelize';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import {
  CIBIL_MEMBER_PASS,
  CIBIL_MEMBER_USERID,
  MAX_INQUIRY_PAST_30_DAYS,
  NAME_MISS_MATCH_PER,
  MIN_CIBIL_SCORE,
  MIN_PL_SCORE,
  cibilIdType,
  cibilAccountOwnershipIndicator,
  cibilAccountType,
  cibilAddressCategory,
  cibilCollateralType,
  cibilCreditFacilityStatus,
  cibilOccupationCode,
  cibilPaymentFrequency,
  cibilResidenceCode,
  cibilSuitFiled,
  cibilTelephoneType,
  cibilStateCode,
  GLOBAL_RANGES,
  CIBIL_REDIS_KEY,
  PAGE_LIMIT,
} from 'src/constants/globals';
import { k500Error } from 'src/constants/misc';
import {
  k422ErrorMessage,
  kInternalError,
  kParamMissing,
  kNoDataFound,
  kInvalidParamValue,
} from 'src/constants/responses';
import { validateISODate } from 'src/constants/validation';
import { KYCEntity } from 'src/entities/kyc.entity';
import { registeredUsers } from 'src/entities/user.entity';
import { UserRepository } from 'src/repositories/user.repository';
import { CibilScoreRepository } from 'src/repositories/cibil.score.repository';
import { AddressesRepository } from 'src/repositories/addresses.repository';
import { CryptService } from 'src/utils/crypt.service';
import { TypeService, convertDateInDDMMYYYY } from 'src/utils/type.service';
import { NSModel } from 'src/admin/cibil_score/model/ns.tudf.model';
import { CibilThirdParty } from 'src/thirdParty/cibil/cibil.service';
import { RedisService } from 'src/redis/redis.service';
import { RedisKeys, kMockResponse, shortMonth } from 'src/constants/objects';
import { ValidationService } from 'src/utils/validation.service';
import { LoanRepository } from 'src/repositories/loan.repository';
import { DateService } from 'src/utils/date.service';
import { EnvConfig } from 'src/configs/env.config';
import { loanTransaction } from 'src/entities/loan.entity';
import { CibilScoreEntity } from 'src/entities/cibil.score.entity';
import { EmiEntity } from 'src/entities/emi.entity';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { NUMBERS } from 'src/constants/numbers';
import { kSomthinfWentWrong } from 'src/constants/strings';
import { CommonSharedService } from './common.shared.service';
import { FileService } from 'src/utils/file.service';
import { batchCibilFileCols } from 'src/constants/objects';
import { BatchCibilDataEntity } from 'src/entities/batchCibilData.entity';
import { KYCRepository } from 'src/repositories/kyc.repository';
import { ReportHistoryRepository } from 'src/repositories/reportHistory.repository';
import { BatchCibilFileTrackingEntity } from 'src/entities/batchCibilFileTracking.entity';
import { ErrorContextService } from 'src/utils/error.context.service';
import { ExperianSharedService } from './experian.service';
import { BureauService } from 'src/admin/bureau/bureau.service';
import { CibilScoreArchive } from 'src/entities/cibilScoreArchive.entity';
import { EligibilitySharedService } from './eligibility.shared.service';

@Injectable()
export class CibilService {
  constructor(
    private readonly userRepo: UserRepository,
    private readonly loanRepo: LoanRepository,
    private readonly CibilScoreRepo: CibilScoreRepository,
    private readonly addressRepo: AddressesRepository,
    private readonly reportHistoryRepo: ReportHistoryRepository,
    private readonly kycRepo: KYCRepository,
    private readonly cryptService: CryptService,
    private readonly typeService: TypeService,
    private readonly NSModel: NSModel,
    private readonly cibilThirdParty: CibilThirdParty,
    // Database
    private readonly redis: RedisService,
    private readonly repo: RepositoryManager,
    // Utils
    private readonly validation: ValidationService,
    private readonly dateService: DateService,
    private readonly commonSharedService: CommonSharedService,
    private readonly fileService: FileService,
    private readonly errorContextService: ErrorContextService,
    private readonly experianSharedService: ExperianSharedService,
    @Inject(forwardRef(() => BureauService))
    private readonly bureauService: BureauService,
    @Inject(forwardRef(() => EligibilitySharedService))
    private readonly eligibility: EligibilitySharedService,
  ) {}

  async cibilPersonalLoanScore(body: any) {
    try {
      // Validation -> Parameters
      const userId = body?.userId;
      if (!userId) return kParamMissing('userId');

      let loanId = body?.loanId;
      let cibilForceFetch = body?.cibilForceFetch == 'true';

      let validatePanDetails;

      // Preparation -> Query
      const attributes = [
        'completedLoans',
        'fullName',
        'gender',
        'phone',
        'email',
        'appType',
      ];
      const kycInclude: any = { model: KYCEntity };
      kycInclude.attributes = [
        'id',
        'aadhaarDOB',
        'aadhaarAddress',
        'aadhaarAddressResponse',
        'panCardNumber',
        'panStatus',
        'pincode',
      ];
      const include = [kycInclude];
      const options = { include, where: { id: userId } };
      // Hit -> Query
      const userData = await this.userRepo.getRowWhereData(attributes, options);
      // Validation -> Query data
      if (userData == k500Error) return kInternalError;
      if (!userData) return kNoDataFound;

      const appType = userData?.appType;
      userData.phone = this.cryptService.decryptPhone(userData.phone);

      // Preparation -> Address details
      const cibilAddress = this.typeService.getAadhaarAddress(userData.kycData);
      cibilAddress.address = cibilAddress.address.replace(/\s/g, '');
      const addressLine = this.NSModel.getAddressNameLines(
        cibilAddress.address,
        40,
      );
      const nameLine = this.NSModel.getAddressNameLines(userData.fullName, 26);
      let stateCode = this.NSModel.getStateCode(cibilAddress.state).replace(
        '0602',
        '',
      );

      let aadharDOB = await this.typeService.getDateAsPerAadhaarDOB(
        userData.kycData.aadhaarDOB,
      );

      let cibilDOB = '';
      if (aadharDOB.includes('/')) {
        let parts = aadharDOB.split('/');
        parts = this.fixDatePartsLen(parts);
        cibilDOB = parts[0] + parts[1] + parts[2];
      } else if (aadharDOB.includes('-')) {
        let parts = aadharDOB.split('-');
        parts = this.fixDatePartsLen(parts);
        cibilDOB = parts[2] + parts[1] + parts[0];
      } else if (aadharDOB?.length === 4) cibilDOB = '01' + '01' + aadharDOB;
      else return kInternalError;

      let cibilGender =
        userData.gender.toLocaleUpperCase() == 'MALE' ? '2' : '1';

      const monitoringDate = convertDateInDDMMYYYY(new Date().toJSON());

      if (
        stateCode == '99' ||
        !userData.kycData.pincode ||
        userData.kycData.pincode == '-'
      ) {
        stateCode = '99';
        userData.kycData.pincode = '999999';
      }

      // Preparation -> API
      const cibilReqData = {
        url: 'acquire/credit-assessment/v1/consumer-cir-cv',
        custRefId: loanId,
        sampledata: {
          serviceCode: 'CN1CIR0006', //CN1CAS0013 For PLScore, CN1CAS0012 for NTC, CN1CIR0006 ERS Score
          monitoringDate: monitoringDate,
          consumerInputSubject: {
            tuefHeader: {
              headerType: 'TUEF',
              version: '12',
              memberRefNo: 'MR' + loanId,
              gstStateCode: '01',
              enquiryMemberUserId: CIBIL_MEMBER_USERID,
              enquiryPassword: CIBIL_MEMBER_PASS,
              enquiryPurpose: '05',
              enquiryAmount: '000000001',
              scoreType: '27', //16 for PLScore, 17 For NTC, 27 For ERS Score
              outputFormat: '03',
              responseSize: '1',
              ioMedia: 'CC',
              authenticationMethod: 'L',
            },
            names: [
              {
                index: 'N01',
                firstName: nameLine[0] ?? '',
                middleName: nameLine[2] ?? '',
                lastName: nameLine[1] ?? '',
                birthDate: cibilDOB,
                gender: cibilGender,
              },
            ],
            ids: [
              {
                index: 'I01',
                idNumber: userData.kycData.panCardNumber,
                idType: '01',
              },
            ],
            telephones: [
              {
                index: 'T01',
                telephoneNumber: userData.phone,
                telephoneType: '01',
              },
            ],
            addresses: [
              {
                index: 'A01',
                line1: addressLine[0] ?? addressLine[1] ?? '',
                line2: addressLine[1] ?? addressLine[2] ?? '',
                line3: addressLine[2] ?? addressLine[3] ?? '',
                line4: addressLine[3] ?? '',
                line5: addressLine[4] ?? '',
                stateCode: stateCode,
                pinCode: userData.kycData.pincode,
                addressCategory: '01',
                residenceCode: '01',
              },
            ],
            enquiryAccounts: [
              {
                index: 'I01',
                accountNumber: '',
              },
            ],
          },
        },
      };

      try {
        let existingData;

        await this.bureauService.deleteRedisDataForMultipleUserId(userId);
        await this.bureauService.deleteRedisDataForFV(userId);
        await this.deleteRedisDataForFV(userId); // for fv
        await this.deleteRedisDataForUserDetails(userId); // for customerDetails

        /* Manipulate the cibil response with the help of redis 
        as per testing requirements, Works in DEV, UAT, PROD all environment modes*/
        const mockResponse = await this.getMockResponse(
          userData.kycData.panCardNumber,
        );

        if (mockResponse != null) existingData = mockResponse;
        //if not hitting forcefully  cibil fetch button
        else if (!cibilForceFetch) {
          /* Manipulate the cibil response with the help of postgres 
        as per testing requirements, Works in DEV, UAT, PROD all environment modes*/
          const dbResponse = await this.getDBResponse(userId, loanId);
          if (dbResponse != null) existingData = dbResponse;
          // lsp user
          if (
            userData?.email &&
            (userData.email.includes(
              EnvConfig.emailDomain.companyEmailDomain1,
            ) ||
              userData.email.includes(
                EnvConfig.emailDomain.companyEmailDomain2,
              ))
          ) {
            existingData = kMockResponse;
            existingData.consumerCreditData[0].names[0].name =
              userData.fullName;
            existingData.consumerCreditData[0].names[0].birthDate = cibilDOB;
            existingData.consumerCreditData[0].names[0].gender = cibilGender;
          }
        }
        const getoptions = {
          where: { userId },
          order: [['id', 'DESC']],
        };
        let scoreData: any;
        scoreData = await this.CibilScoreRepo.getRowWhereData(
          ['id', 'loanId', 'status'],
          getoptions,
        );
        if (scoreData == k500Error) return kInternalError;

        //for updating at a time of rekyc
        if (existingData == null && scoreData?.status == '1') {
          cibilForceFetch = true;
        }

        if (scoreData?.loanId !== loanId || cibilForceFetch) {
          const rData = {
            userId,
            loanId,
            type: '1',
          };

          //if cibit data is created again of existing user then it deleted data from redis

          scoreData = await this.CibilScoreRepo.createRowData(rData);
          if (scoreData == k500Error) return kInternalError;
          if (!scoreData) return k422ErrorMessage(kNoDataFound);
        }
        const cibilId = scoreData.id;
        const loanOpts = {
          where: { userId, loanStatus: ['InProcess', 'Accepted'] },
          order: [['id', 'DESC']],
        };
        const loanData = await this.loanRepo.getRowWhereData(['id'], loanOpts);
        if (loanData != k500Error && loanData?.id) {
          const lId = loanData.id;
          await this.loanRepo.updateRowData({ cibilId }, lId);
        }
        // API hit to cibil server
        const result: any =
          existingData != null
            ? existingData
            : await this.cibilThirdParty.CreditVisionScorePersonalLoanScore(
                cibilReqData,
              );

        await this.bureauService.deleteRedisDataForFV(userId);
        await this.bureauService.deleteRedisDataForMultipleUserId(userId);
        await this.deleteRedisDataForFV(userId); // for fv
        await this.deleteRedisDataForUserDetails(userId); // for customerDetails

        // Response failure
        if (result?.message) {
          // Update failed response
          const updatedData: any = {};
          updatedData.status = '3';
          updatedData.responsedata = result;
          updatedData.requestdata = cibilReqData;
          const options = {
            where: {
              id: scoreData.id,
              status: { [Op.ne]: '1' },
            },
          };
          await this.CibilScoreRepo.updateRowWhereData(updatedData, options);
          result.success = false;
          return result;
        } else {
          // Update failed response
          if (result?.controlData?.success == false) {
            const updatedData: any = {};
            updatedData.status = '2';
            updatedData.responsedata = result;
            updatedData.requestdata = cibilReqData;
            await this.CibilScoreRepo.updateRowData(updatedData, scoreData.id);
            result.success = false;
            return result;
          }
          // Update success response
          else if (result?.controlData?.success == true) {
            const scoreDate = result?.consumerCreditData[0].scores[0].scoreDate;
            const fetchDate = scoreDate
              ? this.typeService.strDateToDate(
                  result.consumerCreditData[0]?.scores[0]?.scoreDate,
                )
              : null;
            // Validate name
            const isValidName = await this.validateNameAsPerCibil(
              userData.fullName,
              result.consumerCreditData[0]?.names ?? [],
              appType,
            );
            result.internal_name_check = isValidName;

            const updatedData: any = {};
            updatedData.status = '1';
            const createData = {
              requestdata: cibilReqData,
              id: scoreData.id,
              loanId,
              userId,
            };
            await this.repo.upsert(CibilScoreArchive, createData, {
              conflictFields: ['id'],
            });

            updatedData.responsedata = result;
            if (fetchDate)
              updatedData.fetchDate = this.typeService
                .getGlobalDate(new Date(fetchDate))
                .toJSON();
            updatedData.tuefHeader = result.consumerCreditData[0]?.tuefHeader;
            updatedData.names = result.consumerCreditData[0]?.names;
            updatedData.ids = result.consumerCreditData[0]?.ids;
            updatedData.telephones = result.consumerCreditData[0]?.telephones;
            updatedData.emails = result.consumerCreditData[0]?.emails;
            updatedData.employment = result.consumerCreditData[0]?.employment;
            updatedData.scores = result.consumerCreditData[0]?.scores;
            updatedData.addresses = result.consumerCreditData[0]?.addresses;
            updatedData.enquiries = result.consumerCreditData[0]?.enquiries;

            let totalDelayDays = 0;
            let PLOutstanding = 0;
            let totalOutstanding = 0;
            let PLAccounts = 0;
            let ourOverdueAmount = 0;
            let ourOverdueAccounts = 0;
            let past6MonthDelay = 0;
            let today = new Date();
            today.setMonth(today.getMonth() - 6);
            if (result.consumerCreditData[0]?.accounts?.length > 0) {
              for (
                let index = 0;
                index < result.consumerCreditData[0]?.accounts.length;
                index++
              ) {
                const element = result.consumerCreditData[0]?.accounts[index];

                // CSC 1.0.0 -> Guarantor account should be in exception for all scenarios
                if (element.ownershipIndicator === 3) continue;

                totalOutstanding += element.currentBalance;
                const PLAccountTypeIds = [
                  '00',
                  '05',
                  '06',
                  '09',
                  '24',
                  '40',
                  '41',
                  '43',
                  '45',
                  '51',
                  '52',
                  '53',
                  '54',
                  '55',
                  '56',
                  '57',
                  '58',
                  '59',
                  '60',
                  '61',
                  '69',
                  '71',
                ];
                if (PLAccountTypeIds.includes(element.accountType)) {
                  PLOutstanding += element.currentBalance;
                  PLAccounts++;
                }
                const paymentHistory = await this.decodePaymentHistory(
                  element.paymentHistory,
                );
                element.lastDelayDays = paymentHistory.lastDelayDays;
                element.past6MonDelayDays = 0;
                if (
                  element?.amountOverdue > 0 &&
                  !element?.dateClosed &&
                  (EnvConfig.bureauReportCreds.cibilTudfMemberName !=
                    element?.memberShortName ||
                    element.lastDelayDays > 2)
                ) {
                  totalDelayDays += paymentHistory.lastDelayDays;
                  ourOverdueAmount += element.amountOverdue;
                  ourOverdueAccounts++;
                }
                const dateReported = new Date(
                  this.typeService.strDateToDate(element.dateReported),
                );
                if (dateReported >= today) {
                  const monthDiff = this.typeService.dateDifference(
                    today,
                    dateReported,
                    'Month',
                  );

                  const historyArray =
                    monthDiff === 0
                      ? [paymentHistory.historyArray[0]]
                      : paymentHistory.historyArray.slice(0, monthDiff);

                  for (const entry of historyArray) {
                    const status = parseInt(entry.status);
                    if (!isNaN(status) && status > element.past6MonDelayDays)
                      element.past6MonDelayDays = status;
                  }
                  if (
                    element.past6MonDelayDays > 0 &&
                    (EnvConfig.bureauReportCreds.cibilTudfMemberName !=
                      element?.memberShortName ||
                      element.lastDelayDays > 2 ||
                      element.past6MonDelayDays > element.lastDelayDays)
                  )
                    past6MonthDelay = 1;
                }
              }
            }
            updatedData.accounts = result.consumerCreditData[0]?.accounts;
            updatedData.past6MonthDelay = past6MonthDelay;

            let monthlyIncome: any = 0;
            if (result.consumerCreditData[0]?.employment?.length > 0) {
              for (
                let index = 0;
                index < result.consumerCreditData[0]?.employment.length;
                index++
              ) {
                const element = result.consumerCreditData[0]?.employment[index];
                if (element?.income) {
                  if (element?.incomeFrequency == 'A')
                    monthlyIncome = element.income / 12;
                  else monthlyIncome = element.income;
                  monthlyIncome = isNaN(monthlyIncome)
                    ? 0
                    : parseInt(monthlyIncome);
                }
              }
            }
            updatedData.monthlyIncome = monthlyIncome;
            let panMatch = false;
            let cibilPan;
            if (result.consumerCreditData[0]?.ids?.length > 0) {
              for (
                let index = 0;
                index < result.consumerCreditData[0]?.ids.length;
                index++
              ) {
                const element = result.consumerCreditData[0]?.ids[index];
                if (
                  element?.idType == '01' &&
                  element?.idNumber == userData.kycData.panCardNumber
                ) {
                  panMatch = true;
                  cibilPan = element?.idNumber;
                }
              }
            }

            updatedData.cibilScore =
              result.consumerCreditData[0]?.scores[0]?.score.replace(
                /^0+/,
                '',
              ) ?? -1;
            updatedData.plScore =
              result.consumerCreditData[0]?.scores[1]?.score.replace(
                /^0+/,
                '',
              ) ?? -1;

            if (updatedData?.plScore == '') updatedData.plScore = -1;
            if (
              updatedData?.cibilScore == '' ||
              (updatedData?.cibilScore >= 0 && updatedData?.cibilScore < 300)
            )
              updatedData.cibilScore = -1;

            //setting Cibil score and PL score for every user after cibil hit in redis
            const mode = process.env.MODE;
            let cibilScoreObj: any = {
              cibilScore: parseInt(updatedData?.cibilScore),
              plScore: parseInt(updatedData?.plScore),
            };
            cibilScoreObj = JSON.stringify(cibilScoreObj);
            await this.redis.set(
              `CIBIL_SCORE_DATA_${userId}`,
              cibilScoreObj,
              604800,
            );

            // Calculate oldestInquiry Date
            let oldInquiryDate = result?.consumerCreditData?.[0]?.enquiries;
            // Check if oldInquiryDate is valid (not null/undefined/empty)
            if (Array.isArray(oldInquiryDate) && oldInquiryDate?.length > 0) {
              const updatedOldInquiryDate: any = this.sortCibilArrayByDate(
                oldInquiryDate,
                'enquiryDate',
              );

              oldInquiryDate =
                Array.isArray(updatedOldInquiryDate) &&
                updatedOldInquiryDate.length > 0
                  ? updatedOldInquiryDate[0]?.enquiryDate
                  : null;
            } else {
              oldInquiryDate = null; // Handle null/empty case
            }

            // Calculate oldestOpen Date
            let oldOpenDate = result?.consumerCreditData?.[0]?.accounts;

            // Check if oldOpenDate is valid (not null/undefined/empty)
            if (Array.isArray(oldOpenDate) && oldOpenDate?.length > 0) {
              const updatedOldOpenDate: any = this.sortCibilArrayByDate(
                oldOpenDate,
                'dateOpened',
              );

              oldOpenDate =
                Array.isArray(updatedOldOpenDate) &&
                updatedOldOpenDate.length > 0
                  ? updatedOldOpenDate[0]?.dateOpened
                  : null;
            } else {
              oldOpenDate = null; // Handle null/empty case
            }

            updatedData.totalAccounts =
              result.consumerSummaryData?.accountSummary?.totalAccounts;
            updatedData.currentBalance =
              result.consumerSummaryData?.accountSummary?.currentBalance;
            updatedData.overdueBalance = ourOverdueAmount;
            updatedData.totalOverdueDays = totalDelayDays;
            updatedData.PLOutstanding = PLOutstanding;
            updatedData.PLAccounts = PLAccounts;
            updatedData.totalOutstanding = totalOutstanding;
            updatedData.overdueAccounts = ourOverdueAccounts;
            updatedData.highCreditAmount =
              result.consumerSummaryData?.accountSummary?.highCreditAmount;
            updatedData.zeroBalanceAccounts =
              result.consumerSummaryData?.accountSummary?.zeroBalanceAccounts;
            updatedData.oldestDateOpened =
              this.typeService.cibiDateToDBDate(oldOpenDate);
            updatedData.recentDateOpened = this.typeService.cibiDateToDBDate(
              result.consumerSummaryData?.accountSummary?.recentDateOpened,
            );

            updatedData.totalInquiry =
              result.consumerSummaryData?.inquirySummary?.totalInquiry;
            updatedData.inquiryPast30Days =
              result.consumerSummaryData?.inquirySummary?.inquiryPast30Days;
            updatedData.recentInquiryDate = this.typeService.cibiDateToDBDate(
              result.consumerSummaryData?.inquirySummary?.recentInquiryDate,
            );
            updatedData.oldestInquiryDate =
              this.typeService.cibiDateToDBDate(oldInquiryDate);
            updatedData.inquiryPast12Months =
              result.consumerSummaryData?.inquirySummary?.inquiryPast12Months;
            updatedData.inquiryPast24Months =
              result.consumerSummaryData?.inquirySummary?.inquiryPast24Months;

            // Update cibil data
            let validCibilData = 0;
            if (isValidName == true && panMatch == true) {
              validCibilData = 1;
              // Temporarily stopping the fetching of PAN details from CIBIL to prevent incorrect PAN retrieval
              // if (regPanCard(cibilPan)) {
              //   const kycData = {
              //     userId: userId,
              //     pan: cibilPan,
              //     consentMode: kCibilScore,
              //   };
              //   validatePanDetails = await this.kycService.validatePan(
              //     kycData,
              //     true,
              //   );
              // }
            }
            updatedData.validCibilData = validCibilData;
            const updateResult = await this.CibilScoreRepo.updateRowData(
              updatedData,
              scoreData.id,
            );
            if (updateResult == k500Error) {
              console.log('ERROR CIBIL', scoreData.id, updatedData);
            }

            if (result.consumerCreditData[0]?.addresses?.length > 0)
              await this.addCibilAddresses(
                userId,
                result.consumerCreditData[0]?.addresses,
              );
            result.success = true;
            // Validation -> CSC 1.1.0
            let isExceptionMiplLogic: any = false;
            let UUData = { maybeGoodCibil: 0 };
            const completedLoans = userData.completedLoans ?? 0;
            const isPLExceptionUser =
              completedLoans >= GLOBAL_RANGES.PL_EXCEPTION_MIN_COMPLETED_LOANS;

            // Purpose -> Analysis
            // Not Needed Experian Now
            // try {
            //   if (updatedData.cibilScore != -1 && updatedData.plScore != -1) {
            //     this.experianSharedService
            //       .getExperianDetails({ userId })
            //       .catch((_) => {});
            //   }
            // } catch (error) {}

            // #01 -> No cibil or PL history
            if (updatedData.cibilScore == -1 || updatedData.plScore == -1) {
              // Not Needed Experian Now
              // await this.experianSharedService
              //   .getExperianDetails({ userId })
              //   .catch((error) => {});

              if (updatedData.overdueBalance > 0) {
                return { isLoanDeclined: true, result };
              } else {
                await this.userRepo.updateRowData(UUData, userId);

                // Experiment -> Check pre approval flow
                this.eligibility.checkPreApproval(userId).catch((err) => {
                  console.log({ err });
                });

                return {
                  isLoanDeclined: false,
                  success: true,
                  ...updatedData,
                  UUData,
                  validatePanDetails,
                };
              }
            }

            // #02 -> No personal loans history
            // else if (updatedData.PLAccounts == 0 && !isPLExceptionUser)
            //   return { isLoanDeclined: true, result };
            // #03 -> Amount overdue & Not ideal score
            else if (
              updatedData.overdueBalance > 0 &&
              (+updatedData.cibilScore < GLOBAL_RANGES.MIN_IDEAL_CIBIL_SCORE ||
                (+updatedData.plScore < GLOBAL_RANGES.MIN_IDEAL_PL_SCORE &&
                  !isPLExceptionUser))
            ) {
              return { isLoanDeclined: true, result };
            }

            // #05 -> Ideal Cibil user but very high Enquiry In Past 30 Days
            else if (
              updatedData.inquiryPast30Days > MAX_INQUIRY_PAST_30_DAYS &&
              updatedData.cibilScore < MIN_CIBIL_SCORE &&
              updatedData.plScore < MIN_PL_SCORE &&
              !isPLExceptionUser
            ) {
              return { isLoanDeclined: true, result };
            } else if (
              updatedData.cibilScore < GLOBAL_RANGES.CIBIL_SCORE_BYPASS &&
              updatedData.plScore < GLOBAL_RANGES.PL_SCORE_BYPASS
            ) {
              return { isLoanDeclined: true, result };
            }

            // #05 -> Ideal Cibil user but very high Enquiry In Past 30 Days
            else if (
              updatedData.cibilScore < MIN_CIBIL_SCORE ||
              (updatedData.plScore < MIN_PL_SCORE && !isPLExceptionUser)
            ) {
              return { isLoanDeclined: true, result };
            } else {
              if (!isPLExceptionUser && EnvConfig.nbfcType == '1') {
                if (updatedData.plScore >= 650 && updatedData.plScore < 700) {
                  // New criteria
                  if (updatedData.cibilScore < 700)
                    return { isLoanDeclined: true, result };
                }
                if (
                  updatedData.cibilScore >= 650 &&
                  updatedData.cibilScore < 700
                ) {
                  if (updatedData.plScore < 700)
                    return { isLoanDeclined: true, result };
                }
              }

              if (
                updatedData.cibilScore >= MIN_CIBIL_SCORE &&
                updatedData.plScore >= MIN_PL_SCORE &&
                updatedData.totalOverdueDays == 0
              ) {
                UUData = { maybeGoodCibil: 1 };
              }

              await this.userRepo.updateRowData(UUData, userId);

              // Experiment -> Check pre approval flow
              this.eligibility.checkPreApproval(userId).catch((err) => {
                console.log({ err });
              });

              return {
                isLoanDeclined: false,
                success: true,
                ...updatedData,
                UUData,
                validatePanDetails,
              };
            }
          }
          // Update unexpected response
          else {
            const updatedData: any = {};
            updatedData.status = '4';
            updatedData.responsedata = result;
            await this.CibilScoreRepo.updateRowData(updatedData, scoreData.id);
            result.success = false;
            return result;
          }
        }
      } catch (error) {
        this.errorContextService.throwAndSetCtxErr(error);
        return kInternalError;
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private fixDatePartsLen(parts = []) {
    return parts.map((item) => (item.length === 1 ? '0' + item : item));
  }

  /* Manipulate the cibil response with the help of redis 
        as per testing requirements, Works in DEV, UAT, PROD all environment modes*/
  private async getMockResponse(panNumber) {
    // Check mock service
    let mockService =
      (await this.redis.getKeyDetails(RedisKeys.MOCK_SERVICES)) ?? {};
    if (typeof mockService == 'string') mockService = JSON.parse(mockService);

    const isMockCibilService = mockService.cibil_hard_pull ?? false;
    if (isMockCibilService?.toString() != 'true') return;

    // Get mock data
    let mockResponse =
      (await this.redis.getKeyDetails(RedisKeys.MOCK_CIBIL_DATA)) ?? {};
    if (typeof mockResponse == 'string')
      mockResponse = JSON.parse(mockResponse);
    if (typeof mockResponse == 'string')
      mockResponse = JSON.parse(mockResponse);

    if (mockResponse[panNumber])
      return { ...mockResponse[panNumber], internal_source: 'REDIS' };
    else return;
  }

  /* Manipulate the cibil response with the help of postgres 
        as per testing requirements, Works in DEV, UAT, PROD all environment modes*/
  private async getDBResponse(userId, loanId) {
    const today = new Date();
    today.setDate(today.getDate() - 30); // 30 days ago

    const attributes = ['id', 'responsedata', 'fetchDate', 'loanId'];
    const options = {
      order: [['id', 'DESC']],
      where: {
        userId,
        type: '1',
        status: '1',
      },
    };

    let existingData: any = await this.CibilScoreRepo.getRowWhereData(
      attributes,
      options,
    );
    if (existingData == k500Error) return;
    if (!existingData?.responsedata || !existingData?.fetchDate) return;

    const fetchDate = new Date(existingData.fetchDate);
    // If fetchDate is older than 30 days, return
    if (fetchDate <= today) return;

    let filteredData = null;
    if (existingData.loanId !== loanId) {
      //cibil hit after 30 days condition for new loan
      filteredData = {
        ...existingData.responsedata,
        internal_source: 'POSTGRES',
      };
    } else {
      //cibil hit for same loanId e.g. if fetch cibil button has been hit and then approve bank
      filteredData = {
        ...existingData.responsedata,
      };
    }
    return filteredData;
  }

  // Validate wheater the name is correct or not
  private async validateNameAsPerCibil(
    targetName: string,
    names: any[],
    appType,
  ) {
    for (let index = 0; index < names.length; index++) {
      try {
        const cibilName = (names[0]?.name ?? '')
          .toLowerCase()
          .replace('null', '')
          .trim();
        const response = await this.validation.nameMatch(
          targetName,
          cibilName,
          appType,
        );
        if (response == k500Error) return kInternalError;
        if (response.valid == true) {
          if (response.data >= NAME_MISS_MATCH_PER) return true;
        }
      } catch (error) {}
    }

    return false;
  }

  async updateCibilScoreData() {
    try {
      const attributes = [
        'id',
        'accounts',
        'employment',
        'names',
        'requestdata',
        'ids',
      ];
      const userInclude: any = { model: registeredUsers };
      userInclude.attributes = ['id', 'fullName', 'appType'];
      const include = [userInclude];
      const options = { include, where: { type: '1', status: '1' } };
      const cibilData: any = await this.CibilScoreRepo.getTableWhereData(
        attributes,
        options,
      );
      if (cibilData == k500Error) return kInternalError;
      if (!cibilData) return kNoDataFound;

      cibilData.forEach(async (result) => {
        // Validate name
        const isValidName = await this.validateNameAsPerCibil(
          result.registeredUsers.fullName,
          result?.names ?? [],
          result.registeredUsers.appType,
        );

        /// Update success response
        const updatedData: any = {};

        let totalDelayDays = 0;
        let PLOutstanding = 0;
        let totalOutstanding = 0;
        let PLAccounts = 0;
        if (result.accounts?.length > 0) {
          for (let index = 0; index < result.accounts.length; index++) {
            const element = result.accounts[index];
            totalOutstanding += element.currentBalance;
            if (!['01', '02', '03', '04'].includes(element.accountType)) {
              PLOutstanding += element.currentBalance;
              PLAccounts++;
            }
            const paymentHistory = await this.decodePaymentHistory(
              element.paymentHistory,
            );
            element.lastDelayDays = paymentHistory.lastDelayDays;
            if (element?.amountOverdue > 0)
              totalDelayDays += paymentHistory.lastDelayDays;
          }
        }
        updatedData.accounts = result.accounts;
        updatedData.totalOverdueDays = totalDelayDays;
        updatedData.PLOutstanding = PLOutstanding;
        updatedData.PLAccounts = PLAccounts;
        updatedData.totalOutstanding = totalOutstanding;

        let monthlyIncome: any = 0;
        if (result.employment?.length > 0) {
          for (let index = 0; index < result.employment.length; index++) {
            const element = result.employment[index];
            if (element?.income) {
              if (element?.incomeFrequency == 'A')
                monthlyIncome = element.income / 12;
              else monthlyIncome = element.income;
              monthlyIncome = isNaN(monthlyIncome)
                ? 0
                : parseInt(monthlyIncome);
            }
          }
        }
        updatedData.monthlyIncome = monthlyIncome;

        let panMatch = false;
        if (result.ids?.length > 0) {
          for (let index = 0; index < result.ids.length; index++) {
            const element = result.ids[index];
            if (
              element?.idType == '01' &&
              element?.idNumber ==
                result.requestdata.sampledata.consumerInputSubject.ids[0]
                  .idNumber
            ) {
              panMatch = true;
            }
          }
        }

        // Update cibil data
        let validCibilData = 0;
        if (isValidName == true && panMatch == true) validCibilData = 1;
        updatedData.validCibilData = validCibilData;

        const res = await this.CibilScoreRepo.updateRowData(
          updatedData,
          result.id,
        );
        if (res == k500Error) return kInternalError;
        return true;
      });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async cibilPreScreen(body: any) {
    try {
      const userId = body?.userId;
      if (!userId) return kParamMissing('userId');

      const attributes = ['panName', 'phone'];
      const options = { where: { id: userId } };
      const userData = await this.userRepo.getRowWhereData(attributes, options);
      if (userData == k500Error) return kInternalError;
      if (!userData) return kNoDataFound;
      userData.phone = this.cryptService.decryptPhone(userData.phone);

      const monitoringDate = convertDateInDDMMYYYY(new Date().toJSON());

      const nameLine = this.NSModel.getAddressNameLines(userData.panName, 26);

      const cibilReqData = {
        url: 'digital-onboarding/acquire/v1/prefill',
        custRefId: userId,
        sampledata: {
          serviceCode: 'CN1OPF0001',
          monitoringDate: monitoringDate,
          searchPaths: [
            {
              searchPath: 'phoneNameSearch',
            },
          ],
          consumerInputSubject: {
            tuefHeader: {
              headerType: 'TUEF',
              version: '12',
              memberRefNo: 'MR' + userData.phone,
              gstStateCode: '01',
              enquiryMemberUserId: CIBIL_MEMBER_USERID,
              enquiryPassword: CIBIL_MEMBER_PASS,
              enquiryPurpose: '65',
              enquiryAmount: '000000001',
              scoreType: '08',
              outputFormat: '03',
              responseSize: '1',
              ioMedia: 'CC',
              authenticationMethod: 'L',
            },
            names: [
              {
                index: 'N01',
                firstName: nameLine[0] ?? '',
                middleName: nameLine[2] ?? '',
                lastName: nameLine[1] ?? '',
              },
            ],
            telephones: [
              {
                index: 'T01',
                telephoneNumber: userData.phone,
                telephoneType: '01',
              },
            ],
          },
        },
      };

      /// Add data to cibil score table
      const rData = {
        userId,
        type: '2',
        requestdata: cibilReqData,
      };
      //if cibit data is created again of existing user then it deleted data from redis

      const scoreData = await this.CibilScoreRepo.createRowData(rData);
      if (scoreData == k500Error) return kInternalError;
      if (!scoreData) return k422ErrorMessage(kNoDataFound);
      await this.deleteRedisDataForMultipleUserId(userId);
      await this.deleteRedisDataForFV(userId); // for fv
      await this.deleteRedisDataForUserDetails(userId); // for customerDetails

      try {
        const result: any = await this.cibilThirdParty.onlinePrefill(
          cibilReqData,
        );
        if (result?.message) {
          /// Update error response
          const updatedData: any = {};
          updatedData.status = '3';
          updatedData.responsedata = result;
          await this.CibilScoreRepo.updateRowData(updatedData, scoreData.id);
          return result;
        } else {
          if (result?.controlData?.success == false) {
            /// Update failed response
            const updatedData: any = {};
            updatedData.status = '2';
            updatedData.responsedata = result;
            await this.CibilScoreRepo.updateRowData(updatedData, scoreData.id);
            return result;
          } else if (result?.controlData?.success == true) {
            /// Update success response
            const updatedData: any = {};
            updatedData.status = '1';
            updatedData.responsedata = result;
            updatedData.tuefHeader = result.consumerCreditData[0]?.tuefHeader;
            updatedData.names = result.consumerCreditData[0]?.names;
            updatedData.ids = result.consumerCreditData[0]?.ids;
            updatedData.telephones = result.consumerCreditData[0]?.telephones;
            updatedData.emails = result.consumerCreditData[0]?.emails;
            updatedData.employment = result.consumerCreditData[0]?.employment;
            updatedData.addresses = result.consumerCreditData[0]?.addresses;
            let monthlyIncome: any = 0;
            if (result.consumerCreditData[0]?.employment?.length > 0) {
              result.consumerCreditData[0]?.employment.forEach(
                async (element) => {
                  if (element?.income) {
                    if (element?.incomeFrequency == 'A')
                      monthlyIncome = element.income / 12;
                    else monthlyIncome = element.income;
                    monthlyIncome = isNaN(monthlyIncome)
                      ? 0
                      : parseInt(monthlyIncome);
                  }
                },
              );
            }
            updatedData.monthlyIncome = monthlyIncome;
            await this.CibilScoreRepo.updateRowData(updatedData, scoreData.id);
            if (result.consumerCreditData[0]?.addresses?.length > 0)
              await this.addCibilAddresses(
                userId,
                result.consumerCreditData[0]?.addresses,
              );
            return result;
          } else {
            /// Update unknown response
            const updatedData: any = {};
            updatedData.status = '4';
            updatedData.responsedata = result;
            await this.CibilScoreRepo.updateRowData(updatedData, scoreData.id);
            return result;
          }
        }
      } catch (error) {
        this.errorContextService.throwAndSetCtxErr(error);
        return kInternalError;
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async addLeadingZero(number) {
    return number < 10 ? `0${number}` : `${number}`;
  }

  ///CibilDate 26072023 convert to db date 2023-07-26
  async cibiDateToDBDate(cibilDate) {
    if (cibilDate && cibilDate.length == 8) {
      let year = cibilDate.slice(4, 8);
      let month = cibilDate.slice(2, 4);
      let date = cibilDate.slice(0, 2);
      return year + '-' + month + '-' + date;
    } else return cibilDate;
  }

  async addCibilAddresses(userId, addresses) {
    let finalData = [];
    for (let i = 0; i < addresses.length; i++) {
      try {
        const address: any = addresses[i];
        const line1 = address.line1 ?? '';
        const line2 = address.line2 ?? '';
        const line3 = address.line3 ?? '';
        const line4 = address.line4 ?? '';
        const line5 = address.line5 ?? '';
        let addString =
          line1 + ' ' + line2 + ' ' + line3 + ' ' + line4 + ' ' + line5;
        addString = addString.replace(/  /g, '');

        const addData: any = {};
        addData.userId = userId;
        addData.status = '0';
        addData.address = addString;
        addData.type = '13';
        addData.subType = address?.index;
        finalData.push(addData);
      } catch (error) {}
    }
    const addressData = await this.addressRepo.bulkCreate(finalData);
    if (addressData === k500Error) return kInternalError;
    return addressData;
  }

  async decodePaymentHistory(paymentHistoryString) {
    const statusMapping = {
      STD: 'Standard',
      SMA: 'Special Mention Account',
      SUB: 'Substandard',
      DBT: 'Doubtful',
      LSS: 'Loss',
      XXX: 'Not Reported',
    };

    const historyArray = [];
    const last = paymentHistoryString.slice(0, 3);

    let lastDaysPastDue = parseInt(last, 10);
    if (['SMA', 'SUB', 'DBT', 'LSS'].includes(last)) {
      lastDaysPastDue = 999;
    }
    const lastDelayDays = isNaN(lastDaysPastDue) ? 0 : lastDaysPastDue;

    while (paymentHistoryString.length >= 3) {
      const code = paymentHistoryString.slice(0, 3);
      paymentHistoryString = paymentHistoryString.slice(3);

      let status;
      if (statusMapping[code]) {
        status = statusMapping[code];
      } else {
        const daysPastDue = parseInt(code, 10);
        status = isNaN(daysPastDue)
          ? 'Not Reported'
          : `${daysPastDue} Days Past Due`;
      }

      historyArray.push({ status });
    }

    return { lastDelayDays, historyArray };
  }

  async updateLastCibilHardPullLoanId(reqData) {
    // Params validation
    const userId = reqData.userId;
    if (!userId) return kParamMissing('userId');
    const loanId = reqData.loanId;
    if (!loanId) return kParamMissing('loanId');
    let scoreData: any;
    const getoptions = {
      where: { type: '1', userId },
      order: [['id', 'DESC']],
    };
    const attributes = ['id'];
    scoreData = await this.CibilScoreRepo.getRowWhereData(
      attributes,
      getoptions,
    );
    if (scoreData == k500Error) return kInternalError;
    if (!scoreData) return kNoDataFound;

    const options = { where: { id: scoreData.id } };
    const result = await this.CibilScoreRepo.updateRowWhereData(
      { loanId },
      options,
    );
    if (result == k500Error) return kInternalError;
    const loanOpts = {
      where: { userId, loanStatus: ['InProcess', 'Accepted'] },
      order: [['id', 'DESC']],
    };
    const loanData = await this.loanRepo.getRowWhereData(['id'], loanOpts);
    const lId = loanData?.id;
    if (loanData != k500Error && lId && lId == loanId)
      await this.loanRepo.updateRowData({ cibilId: scoreData.id }, lId);

    return {};
  }

  // start region Get User CibilScore Data
  async funGetUserCibilScoreData(reqData) {
    try {
      const userId = reqData.userId;
      if (!userId) return kParamMissing('userId');

      const id = reqData.id;
      const loanId = reqData?.loanId;
      let scoreData: any = {};
      let getDataFromRedis: any;

      // temp redis comment due to issue
      getDataFromRedis = await this.getRedisDataForFV(userId);
      scoreData = getDataFromRedis;

      if (!getDataFromRedis || getDataFromRedis?.status != '1') {
        const attributes = [
          'status',
          'cibilScore',
          'plScore',
          'totalAccounts',
          'overdueAccounts',
          'zeroBalanceAccounts',
          'highCreditAmount',
          'currentBalance',
          'overdueBalance',
          'totalOverdueDays',
          'PLOutstanding',
          'totalOutstanding',
          'monthlyIncome',
          'totalInquiry',
          'inquiryPast30Days',
          'inquiryPast12Months',
          'inquiryPast24Months',
          'recentDateOpened',
          'oldestDateOpened',
          'recentInquiryDate',
          'oldestInquiryDate',
          'PLAccounts',
        ];

        let idWhere = ``;
        if (id) idWhere += `AND \"id\" = '${id}'`;
        let loanIdWhere = ``;
        if (loanId) loanIdWhere += `AND \"loanId\" = '${loanId}'`;

        const rawQuery = `SELECT "${attributes.join('","')}"
        FROM "CibilScoreEntities"
        WHERE "type" = '1' AND "userId" = '${userId}'
        ${idWhere} ${loanIdWhere}
        ORDER BY "id" DESC  
        LIMIT 1`;
        const outputList = await this.repo.injectRawQuery(
          CibilScoreEntity,
          rawQuery,
          { source: 'REPLICA' },
        );
        if (outputList === k500Error) throw new Error();

        scoreData = outputList[0];
        if (!scoreData) return {};
        await this.setRedisDataForFV(userId, scoreData);
      }
      if (reqData?.forRouteDetails && scoreData?.status) {
        const newScore: any = {};
        newScore.cibilScore = scoreData.cibilScore ?? '-';
        newScore.plScore = scoreData.plScore ?? '-';
        return newScore;
      }
      if (scoreData?.status) {
        scoreData.cibilScore = scoreData.cibilScore ?? '-';
        scoreData.plScore = scoreData.plScore ?? '-';
        scoreData.totalAccounts = scoreData.totalAccounts ?? '-';
        scoreData.overdueAccounts = scoreData.overdueAccounts ?? '-';
        scoreData.zeroBalanceAccounts = scoreData.zeroBalanceAccounts ?? '-';
        scoreData.highCreditAmount = scoreData.highCreditAmount ?? '-';
        scoreData.currentBalance = scoreData.currentBalance ?? '-';
        scoreData.overdueBalance = scoreData.overdueBalance ?? '-';
        scoreData.totalOverdueDays = scoreData.totalOverdueDays ?? '-';
        scoreData.PLOutstanding = scoreData.PLOutstanding ?? '-';
        scoreData.totalOutstanding = scoreData.totalOutstanding ?? '-';
        scoreData.monthlyIncome = scoreData.monthlyIncome ?? '-';
        scoreData.totalInquiry = scoreData.totalInquiry ?? '-';
        scoreData.inquiryPast30Days = scoreData.inquiryPast30Days ?? '-';
        scoreData.inquiryPast12Months = scoreData.inquiryPast12Months ?? '-';
        scoreData.inquiryPast24Months = scoreData.inquiryPast24Months ?? '-';
        scoreData.PLAccounts = scoreData.PLAccounts ?? '-';

        scoreData.recentDateOpened = scoreData.recentDateOpened
          ? this.typeService.dateToJsonStr(
              scoreData.recentDateOpened,
              'DD/MM/YYYY',
            )
          : '-';
        scoreData.oldestDateOpened = scoreData.oldestDateOpened
          ? this.typeService.dateToJsonStr(
              scoreData.oldestDateOpened,
              'DD/MM/YYYY',
            )
          : '-';
        scoreData.recentInquiryDate = scoreData.recentInquiryDate
          ? this.typeService.dateToJsonStr(
              scoreData.recentInquiryDate,
              'DD/MM/YYYY',
            )
          : '-';
        scoreData.oldestInquiryDate = scoreData.oldestInquiryDate
          ? this.typeService.dateToJsonStr(
              scoreData.oldestInquiryDate,
              'DD/MM/YYYY',
            )
          : '-';
      }

      return scoreData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async getAllCibilData(userId, type) {
    try {
      let outputList: any;
      // temp redis comment due to issue
      let getDataFromRedis = await this.getRedisDataForMultipleUserId(userId);
      outputList = getDataFromRedis;
      if (!getDataFromRedis || getDataFromRedis[0]?.status != '1') {
        const rawQuery = `SELECT "cibilScore", "id", "plScore", "responsedata", "scores", "status" 
        FROM "CibilScoreEntities" AS "cibil"
        WHERE "userId" = '${userId}'
        ORDER BY "id" DESC;`;

        outputList = await this.repo.injectRawQuery(
          CibilScoreEntity,
          rawQuery,
          {
            source: 'REPLICA',
          },
        );
        if (outputList == k500Error) throw new Error();
        await this.setRedisDataForMultipleUserId(userId, outputList);
      }
      const dates = [];
      const scores = [];
      for (let i = 0; i < outputList.length; i++) {
        const currentData = outputList[i];
        if (
          currentData?.responsedata?.internal_source != 'POSTGRES' &&
          currentData?.scores
        ) {
          const date = currentData?.scores[0]?.scoreDate;
          const formattedDate =
            date.slice(0, 2) + '/' + date.slice(2, 4) + '/' + date.slice(4);
          dates.push({
            date: formattedDate,
            id: currentData.id,
          });
          if (scores.length < 2)
            scores.push({
              cibilScore: currentData.cibilScore,
              plScore: currentData.plScore,
            });
        }
      }
      if (!type && scores.length === 2) {
        const diffInCibil = scores[0]?.cibilScore - scores[1]?.cibilScore;
        const diffInPl = scores[0]?.plScore - scores[1]?.plScore;

        return {
          diffInCibil: diffInCibil ? diffInCibil : 0,
          diffInPl: diffInPl ? diffInPl : 0,
        };
      } else if (type) {
        return dates;
      } else return;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // get Cibil History Data for chart
  async getCibilHistoryData(userId, appType) {
    try {
      if (!userId) return kParamMissing('userId');
      if (!appType) return kParamMissing('appType');
      let cibilDifference: any = 0;
      let cibilDiffColor: any = null;

      const dataFromRedis = await this.getRedisDataForUserDetails(userId);
      if (
        dataFromRedis &&
        dataFromRedis?.cibilScore != '-' &&
        dataFromRedis?.cibilScore != null &&
        dataFromRedis.diffInCibil
      ) {
        cibilDifference =
          dataFromRedis.diffInCibil > 0
            ? dataFromRedis.diffInCibil
            : -dataFromRedis.diffInCibil;
        cibilDiffColor =
          dataFromRedis.diffInCibil > 0 ? '0xFFF1FFE6' : '0xFFFFEFF1';
      }
      let cibilAttributes = [
        'cibilScore',
        'plScore',
        'accounts',
        'enquiries',
        'totalAccounts',
        'overdueAccounts',
        'zeroBalanceAccounts',
        'PLAccounts',
        'scores',
        'responsedata',
        'id',
      ];

      const cibilInclude: any = {
        required: false,
        model: CibilScoreEntity,
        attributes: cibilAttributes,
        where: { type: '1', status: '1' },
        order: [['id', 'DESC']],
      };
      const emiInclude: any = {
        required: false,
        model: EmiEntity,
        attributes: [
          'emi_date',
          'loanId',
          'payment_due_status',
          'payment_status',
          'id',
        ],
      };

      const loanInclude: any = {
        required: false,
        model: loanTransaction,
        attributes: ['id'],
        order: [['id', 'DESC']],
        include: [emiInclude],
      };

      const include = [loanInclude, cibilInclude];
      let options = { where: { id: userId }, limit: 4, include };
      let attributes = ['id'];

      let scoreData: any = await this.userRepo.getRowWhereData(
        attributes,
        options,
      );

      if (scoreData == k500Error) return kInternalError;
      scoreData?.cibilList.sort((a, b) => b.id - a.id);
      const scoreHistory = {
        yMax: 900,
        yMin: 999,
        chartData: [],
        chartInterval: 50,
      };

      const maxScoreMap = new Map();

      for (let i = 0; i < scoreData?.cibilList.length; i++) {
        const currentData = scoreData?.cibilList[i];
        if (!currentData?.cibilScore) continue;

        const date = currentData?.scores[0]?.scoreDate;
        if (!date) continue;

        let month = parseInt(date.slice(2, 4), 10);
        let year = parseInt(date.slice(6, 8), 10);
        let fullYear = parseInt(date.slice(4, 8), 10);
        let day = parseInt(date.slice(0, 2), 10);

        if (month < 1 || month > 12) continue;

        const monthName = shortMonth[month - 1];
        const key = `${monthName}-${year}`;

        const dateTimestamp = new Date(`${fullYear}-${month}-${day}`).getTime();

        if (
          !maxScoreMap.has(key) ||
          maxScoreMap.get(key).dateTimestamp < dateTimestamp
        ) {
          maxScoreMap.set(key, {
            xAxis: `${monthName} ${year}`,
            yAxis: currentData.cibilScore,
            monthNumber: month,
            yearNumber: fullYear,
            dateTimestamp: dateTimestamp,
          });
        }
      }

      scoreHistory.chartData = Array.from(maxScoreMap.values());

      if (scoreHistory.chartData.length > 0) {
        scoreHistory.yMin = Math.min(
          ...scoreHistory.chartData.map((data) => data.yAxis),
        );
      }

      scoreHistory.chartData.sort((a, b) => {
        if (a.yearNumber === b.yearNumber) {
          return a.monthNumber - b.monthNumber;
        }
        return a.yearNumber - b.yearNumber;
      });
      if (scoreHistory.chartData.length > 4) {
        scoreHistory.chartData = scoreHistory.chartData.slice(
          scoreHistory.chartData.length - 4,
          scoreHistory.chartData.length,
        );
      }
      scoreHistory.chartData.forEach((data) => {
        delete data.monthNumber;
        delete data.yearNumber;
        delete data.dateTimestamp;
      });

      scoreHistory.yMin = Math.floor((scoreHistory.yMin - 100) / 100) * 100;
      scoreData?.loanData.sort((a, b) => b.id - a.id);
      scoreData?.loanData[0]?.emiData?.sort((a, b) => a.id - b.id);

      let scoreAlert = {
        title: 'Boost Your Score!',
        subTitle: 'Boost your CIBIL Score with timely payments!',
        imageUrl:
          'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/startup_90715201.png',
        cardColor: '0xffD1E8D1',
        dotColor: '0xff008000',
      };

      if (scoreData?.loanData[0]?.emiData) {
        for (let i = 0; i < scoreData?.loanData[0]?.emiData.length; i++) {
          const currentData = scoreData?.loanData[0]?.emiData[i];

          if (currentData?.payment_status === '1') {
            scoreAlert = {
              title: 'Great News!',
              subTitle: 'You have made your repayments on-time. ',
              imageUrl:
                'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/Group38058.png',
              cardColor: '0xffD1E8D1',
              dotColor: '0xff008000',
            };
          } else if (currentData?.payment_due_status === '1') {
            scoreAlert = {
              title: 'You are late!',
              subTitle: 'Please pay on-time to maintain your CIBIL Score',
              imageUrl:
                'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/caution-sign_86374741.png',
              cardColor: '0xffFFCCCC',
              dotColor: '0xff008000',
            };
          }
        }
      }
      scoreData = scoreData?.cibilList[0];

      let totalAccounts = 0;
      let closedAccounts = 0;

      scoreData.accounts.forEach((ele) => {
        totalAccounts++;
        if (ele?.dateClosed) closedAccounts++;
      });

      let latestCibilDate = scoreData?.scores[0]?.scoreDate;
      latestCibilDate = this.typeService.cibiDateToDisplayDate(latestCibilDate);
      latestCibilDate = latestCibilDate.split('/');
      latestCibilDate = `${String(latestCibilDate[0])} ${
        shortMonth[Number(latestCibilDate[1]) - 1]
      } ${String(latestCibilDate[2])}`;

      let remark = '';
      let colorCode = '';
      const score = scoreData?.cibilScore;
      if (score < 700) {
        remark = 'Poor';
        colorCode = '0xffFF4E60';
      } else if (score <= 749) {
        remark = 'Average';
        colorCode = '0xffFF8616';
      } else if (score <= 764) {
        remark = 'Good';
        colorCode = '0xffFCCA4E';
      } else if (score <= 830) {
        remark = 'Very Good';
        colorCode = '0xff91C71F';
      } else {
        remark = 'Excellent';
        colorCode = '0xff2BC432';
      }

      const otherDetails = [];
      otherDetails.push({
        title: 'Accounts',
        desc: 'Check your all the account details',
        type: 0,
        icon: 'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/Accounts.png',
        data: [
          {
            title: 'Active accounts',
            value: totalAccounts - closedAccounts,
          },
          {
            title: 'Overdue accounts',
            value: scoreData?.overdueAccounts ?? 0,
          },
          {
            title: 'Closed accounts',
            value: closedAccounts ?? 0,
          },
          {
            title: 'Total accounts',
            value: totalAccounts ?? 0,
          },
        ],
      });
      scoreData?.enquiries?.forEach((element) => {
        if (element?.enquiryDate) {
          element.Date = this.typeService.cibiDateToDisplayDate(
            element?.enquiryDate,
          );
          element.Date = element.Date.split('/');
          element.Date = `${String(element.Date[0])} ${
            shortMonth[Number(element.Date[1]) - 1]
          } ${String(element.Date[2])}`;
        }

        if (element?.enquiryPurpose)
          element.Purpose = cibilAccountType[element?.enquiryPurpose];

        if (element?.enquiryAmount)
          element.Amount =
            ' ₹ ' +
            this.typeService.amountNumberWithCommas(element?.enquiryAmount);
        delete element?.enquiryPurpose;
        delete element?.enquiryDate;
        delete element?.enquiryAmount;
        delete element?.memberShortName;
        delete element?.index;
      });
      otherDetails.push({
        title: 'Enquiries',
        desc: 'Check your past enquiries',
        type: 1,
        icon: 'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/Enquiries.png',
        data: scoreData?.enquiries,
      });
      if (appType == 0) {
        otherDetails.map((ele) => {
          if (ele?.type == 1)
            ele.icon =
              'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/enquiries1.svg';
          if (ele?.type == 0)
            ele.icon =
              'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Nov-2024/accounts1.svg';
        });
      }

      return {
        cibilScore: scoreData?.cibilScore,
        latestCibilDate,
        color: colorCode,
        remark,
        infoText: 'View CIBIL Score Range',
        scoreAlert,
        scoreHistory,
        otherDetails,
        cibilTrack:
          cibilDifference !== 0
            ? {
                icon:
                  dataFromRedis.diffInCibil > 0
                    ? 'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF%2FNov-2024%2FCIBIL_UP.svg'
                    : 'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF%2FNov-2024%2FCIBIL_DOWN.svg',
                title: ` ${
                  dataFromRedis.diffInCibil > 0 ? 'Up' : 'Down'
                } ${Math.abs(dataFromRedis.diffInCibil)} ${
                  Math.abs(dataFromRedis.diffInCibil) === 1 ? 'point' : 'points'
                } #*since last update*#`,
                color: cibilDiffColor,
              }
            : null,
        cibilFactor: [
          'Paying loans and credit card bills on time improves your score.',
          'A balanced mix and long credit history improve your score.',
          'Too many loan or credit applications hurt your score.',
        ],
        chartLegends: {
          btmSheetHeader: {
            title: 'CIBIL Score Range',
            ...(appType == 0 && {
              description: 'Generally accepted in the industry',
              icon: 'https://objectstorage.ap-mumbai-1.oraclecloud.com/n/bmpjqd11lgyv/b/7a94fa68ad069b8956afc061f4cf42da/o/BACKEND_STATIC_STUFF/Apr-2025/1745323852228.svg',
            }),
          },
          data: [
            {
              range: '831-900',
              remark: 'Excellent',
              color: '0xff2BC432',
            },
            {
              range: '765-830',
              remark: 'Very Good',
              color: '0xff91C71F',
            },
            {
              range: '750-764',
              remark: 'Good',
              color: '0xffFCCA4E',
            },
            {
              range: '700-749',
              remark: 'Average',
              color: '0xffFF8616',
            },
            {
              range: '300-699',
              remark: 'Poor',
              color: '0xffFF4E60',
            },
          ],
        },
      };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#endregion

  async getRedisDataForIdWise(userId, id) {
    const key = `${userId}_${id}_${CIBIL_REDIS_KEY.FOR_ID_WISE_DATA}`;
    let redisData = await this.redis.get(key);
    return redisData ? JSON.parse(redisData) : null;
  }

  async setRedisDataForIdWise(userId, id, data) {
    const key = `${userId}_${id}_${CIBIL_REDIS_KEY.FOR_ID_WISE_DATA}`;
    await this.redis.set(
      key,
      JSON.stringify(data),
      NUMBERS.FIVE_DAYS_IN_SECONDS,
    );
  }

  async deleteRedisDataForIdWise(userId, id) {
    const key = `${userId}_${id}_${CIBIL_REDIS_KEY.FOR_ID_WISE_DATA}`;
    await this.redis.del(key);
  }

  async getRedisDataForMultipleUserId(userId) {
    let key = `${userId}_${CIBIL_REDIS_KEY.FOR_MULTIPLE_DATA}`;
    let redisData = await this.redis.get(key);
    redisData = redisData ? JSON.parse(redisData) : null;
    return redisData;
  }

  async setRedisDataForMultipleUserId(userId, data) {
    let key = `${userId}_${CIBIL_REDIS_KEY.FOR_MULTIPLE_DATA}`;
    data = JSON.stringify(data);
    await this.redis.set(key, data, NUMBERS.FIVE_DAYS_IN_SECONDS);
  }

  async deleteRedisDataForMultipleUserId(userId) {
    let key = `${userId}_${CIBIL_REDIS_KEY.FOR_MULTIPLE_DATA}`;
    await this.redis.del(key);
  }

  async getRedisDataForFV(userId) {
    let key = `${userId}_${CIBIL_REDIS_KEY.FOR_FV_DATA}`;
    let redisData = await this.redis.get(key);
    redisData = redisData ? JSON.parse(redisData) : null;
    return redisData;
  }

  async setRedisDataForFV(userId, data) {
    let key = `${userId}_${CIBIL_REDIS_KEY.FOR_FV_DATA}`;
    data = JSON.stringify(data);
    await this.redis.set(key, data, NUMBERS.FIVE_DAYS_IN_SECONDS);
  }

  async deleteRedisDataForFV(userId) {
    let key = `${userId}_${CIBIL_REDIS_KEY.FOR_FV_DATA}`;
    await this.redis.del(key);
  }

  async getRedisDataForUserDetails(userId) {
    let key = `${userId}_${CIBIL_REDIS_KEY.FOR_SINGLE_DATA}`;
    let redisData = await this.redis.get(key);
    redisData = redisData ? JSON.parse(redisData) : null;
    return redisData;
  }

  async setRedisDataForUserDetails(userId, data) {
    let key = `${userId}_${CIBIL_REDIS_KEY.FOR_SINGLE_DATA}`;
    data = JSON.stringify(data);
    await this.redis.set(key, data, NUMBERS.FIVE_DAYS_IN_SECONDS);
  }

  async deleteRedisDataForUserDetails(userId) {
    let key = `${userId}_${CIBIL_REDIS_KEY.FOR_SINGLE_DATA}`;
    await this.redis.del(key);
  }

  async getLatestCibilData(reqData) {
    const userId = reqData?.userId;
    if (!userId) return kParamMissing('userId');

    // check if redis has data already
    // temp redis comment due to issue
    const dataFromRedis = await this.getRedisDataForUserDetails(userId);
    if (
      dataFromRedis &&
      dataFromRedis?.cibilScore != '-' &&
      dataFromRedis?.cibilScore != null
    )
      return dataFromRedis;

    const attributes = [
      'loanId',
      'status',
      'cibilScore',
      'plScore',
      'overdueAccounts',
      'overdueBalance',
      'totalOverdueDays',
      'PLOutstanding',
      'totalOutstanding',
      'inquiryPast30Days',
      'PLAccounts',
      'scores',
      'responsedata',
      'accounts',
    ];

    const rawQuery = `SELECT "${attributes.join('","')}"
        FROM "CibilScoreEntities"
        WHERE "userId" = '${userId}' AND "status" != '0' 
        ORDER BY "id" DESC;`;

    const outputList = await this.repo.injectRawQuery(
      CibilScoreEntity,
      rawQuery,
      {
        source: 'REPLICA',
      },
    );
    if (outputList == k500Error) throw new Error();
    if (outputList.length == 0) return {};
    const preparedData = this.prepareCibilData(outputList[0]);
    const scores = [];
    // we want last two cibil fetched data to get diff of last two cibil and pl
    for (let i = 0; i < outputList.length; i++) {
      if (scores.length == 2) break;
      const currentData = outputList[i];
      if (
        currentData?.responsedata?.internal_source != 'POSTGRES' &&
        currentData?.scores
      ) {
        scores.push({
          cibilScore: currentData.cibilScore,
          plScore: currentData.plScore,
        });
      }
    }
    if (scores.length == 2) {
      const diffInCibil = scores[0]?.cibilScore - scores[1]?.cibilScore;
      const diffInPl = scores[0]?.plScore - scores[1]?.plScore;
      preparedData.diffInCibil = diffInCibil ? diffInCibil : 0;
      preparedData.diffInPl = diffInPl ? diffInPl : 0;
    }
    await this.setRedisDataForUserDetails(userId, preparedData);
    return preparedData;
  }

  prepareCibilData(scoreData, type?) {
    // prepare Cibil Account Details
    if (type == 'consumerCreditDetails') {
      let accountDetails: any = {};
      accountDetails.ids = scoreData.ids ?? [];
      accountDetails.names = scoreData.names ?? [];
      accountDetails.scores = scoreData.scores ?? [];
      accountDetails.enquiries = scoreData.enquiries ?? [];
      accountDetails.telephones = scoreData.telephones ?? [];
      accountDetails.employment = scoreData.employment ?? [];
      accountDetails.addresses = scoreData.addresses ?? [];
      accountDetails.accounts = scoreData.accounts ?? [];

      accountDetails.ids.forEach((element) => {
        if (element?.idType) element.idType = cibilIdType[element.idType];
      });

      accountDetails.names.forEach((element) => {
        if (element?.birthDate)
          element.birthDate = this.typeService.cibiDateToDisplayDate(
            element.birthDate,
          );
        if (element.gender == '1') {
          element.gender = 'Female';
        } else if (element.gender == '2') {
          element.gender = 'Male';
        } else if (element.gender == '3') {
          element.gender = 'Transgender';
        }
      });

      accountDetails.scores.forEach((element) => {
        element.score = element.score.replace(/^0+/, '') ?? -1;
        if (element?.scoreDate)
          element.scoreDate = this.typeService.cibiDateToDisplayDate(
            element.scoreDate,
          );
      });

      accountDetails.enquiries.forEach((element) => {
        if (element?.enquiryDate)
          element.enquiryDate = this.typeService.cibiDateToDisplayDate(
            element.enquiryDate,
          );
        if (element?.enquiryPurpose)
          element.enquiryPurpose = cibilAccountType[element.enquiryPurpose];
      });

      accountDetails.telephones.forEach((element) => {
        if (element?.telephoneType)
          element.telephoneType = cibilTelephoneType[element.telephoneType];
      });

      accountDetails.employment.forEach((element) => {
        if (element?.dateReported)
          element.dateReported = this.typeService.cibiDateToDisplayDate(
            element.dateReported,
          );
        if (element?.accountType)
          element.accountType = cibilAccountType[element.accountType];
        if (element?.occupationCode)
          element.occupationCode = cibilOccupationCode[element.occupationCode];
      });

      accountDetails.addresses.forEach((element) => {
        element.address = `${element?.line1 ? element?.line1 + ', ' : ''}${
          element?.line2 ? element?.line2 + ', ' : ''
        }${element?.line3 ? element?.line3 + ', ' : ''}${
          element?.line4 ? element?.line4 + ', ' : ''
        }${element?.line5 ? element?.line5 : ''}`;

        if (element?.addressCategory)
          element.addressCategory =
            cibilAddressCategory[element.addressCategory];
        if (element?.residenceCode)
          element.residenceCode = cibilResidenceCode[element.residenceCode];
        if (element?.dateReported)
          element.dateReported = this.typeService.cibiDateToDisplayDate(
            element.dateReported,
          );
        if (element?.stateCode)
          element.stateCode = cibilStateCode[element.stateCode];
        delete element.line1;
        delete element.line2;
        delete element.line3;
        delete element.line4;
        delete element.line5;
      });

      accountDetails.accounts.forEach((element) => {
        if (element?.dateReported)
          element.dateReported = this.typeService.cibiDateToDisplayDate(
            element.dateReported,
          );
        if (element?.dateOpened)
          element.dateOpened = this.typeService.cibiDateToDisplayDate(
            element.dateOpened,
          );
        if (element?.paymentEndDate)
          element.paymentEndDate = this.typeService.cibiDateToDisplayDate(
            element.paymentEndDate,
          );
        if (element?.paymentStartDate)
          element.paymentStartDate = this.typeService.cibiDateToDisplayDate(
            element.paymentStartDate,
          );
        if (element?.dateClosed)
          element.dateClosed = this.typeService.cibiDateToDisplayDate(
            element.dateClosed,
          );
        if (element?.lastPaymentDate)
          element.lastPaymentDate = this.typeService.cibiDateToDisplayDate(
            element.lastPaymentDate,
          );
        if (element?.accountType)
          element.accountType = cibilAccountType[element.accountType];
        if (element?.paymentFrequency)
          element.paymentFrequency =
            cibilPaymentFrequency[element.paymentFrequency];
        if (element?.ownershipIndicator)
          element.ownershipIndicator =
            cibilAccountOwnershipIndicator[element.ownershipIndicator];
        if (element?.collateralType)
          element.collateralType = cibilCollateralType[element.collateralType];
        if (element?.suitFiled)
          element.suitFiled = cibilSuitFiled[element.suitFiled];
        if (element?.creditFacilityStatus)
          element.creditFacilityStatus =
            cibilCreditFacilityStatus[element.creditFacilityStatus];

        element.memberShortName = element.memberShortName ?? '-';
        element.accountNumber = element.accountNumber ?? '-';
        element.accountType = element.accountType ?? '-';
        element.ownershipIndicator = element.ownershipIndicator ?? '-';
        element.dateOpened = element.dateOpened ?? '-';
        element.lastPaymentDate = element.lastPaymentDate ?? '-';
        if (
          EnvConfig.bureauReportCreds.cibilTudfMemberName ==
            element?.memberShortName &&
          element.lastDelayDays <= 2
        ) {
          element.amountOverdue = '-';
          element.paymentHistory = element.paymentHistory
            ? '000' + element?.paymentHistory?.slice(3)
            : '-';
          element.past6MonDelayDays = element.past6MonDelayDays
            ? element.past6MonDelayDays - element.lastDelayDays
            : '-';
          element.lastDelayDays = '-';
        } else {
          element.amountOverdue = element.amountOverdue ?? '-';
          element.paymentHistory = element.paymentHistory ?? '-';
          element.past6MonDelayDays = element.past6MonDelayDays ?? '-';
          element.lastDelayDays = element.lastDelayDays ?? '-';
        }
        element.dateReported = element.dateReported ?? '-';
        element.currentBalance = element.currentBalance ?? '-';
        element.paymentEndDate = element.paymentEndDate ?? '-';
        element.highCreditAmount = element.highCreditAmount ?? '-';
        element.paymentFrequency = element.paymentFrequency ?? '-';
        element.paymentStartDate = element.paymentStartDate ?? '-';
        element.dateClosed = element.dateClosed ?? '-';
        element.collateralType = element.collateralType ?? '-';
        element.cashLimit = element.cashLimit ?? '-';
        element.creditLimit = element.creditLimit ?? '-';
        element.actualPaymentAmount = element.actualPaymentAmount ?? '-';
        element.suitFiled = element.suitFiled ?? '-';
        element.creditFacilityStatus = element.creditFacilityStatus ?? '-';
        element.collateralValue = element.collateralValue ?? '-';
        element.interestRate = element.interestRate ?? '-';
        element.paymentTenure = element.paymentTenure ?? '-';
        element.emiAmount = element.emiAmount ?? '-';
        element.woAmountTotal = element.woAmountTotal ?? '-';
        element.woAmountPrincipal = element.woAmountPrincipal ?? '-';
        element.settlementAmount = element.settlementAmount ?? '-';
      });
      accountDetails.emails = scoreData?.emails ?? [];
      return accountDetails;
    }

    // Prepare Cibil Data
    let cibilDetails: any = {};
    if (type == 'cibilDetails')
      cibilDetails.bureauScore = scoreData.cibilScore ?? '-';
    else cibilDetails.cibilScore = scoreData.cibilScore ?? '-';

    cibilDetails.plScore = scoreData.plScore ?? '-';
    cibilDetails.totalAccounts = scoreData.totalAccounts ?? '-';
    cibilDetails.overdueAccounts = scoreData.overdueAccounts ?? '-';
    cibilDetails.zeroBalanceAccounts = scoreData.zeroBalanceAccounts ?? '-';
    cibilDetails.highCreditAmount = scoreData.highCreditAmount ?? '-';
    cibilDetails.currentBalance = scoreData.currentBalance ?? '-';
    cibilDetails.overdueBalance = scoreData.overdueBalance ?? '-';
    cibilDetails.totalOverdueDays = scoreData.totalOverdueDays ?? '-';
    cibilDetails.PLOutstanding = scoreData.PLOutstanding ?? '-';
    cibilDetails.totalOutstanding = scoreData.totalOutstanding ?? '-';
    cibilDetails.monthlyIncome = scoreData.monthlyIncome ?? '-';
    cibilDetails.totalInquiry = scoreData.totalInquiry ?? '-';
    cibilDetails.inquiryPast30Days = scoreData.inquiryPast30Days ?? '-';
    cibilDetails.inquiryPast12Months = scoreData.inquiryPast12Months ?? '-';
    cibilDetails.inquiryPast24Months = scoreData.inquiryPast24Months ?? '-';
    cibilDetails.PLAccounts = scoreData.PLAccounts ?? '-';
    scoreData.cibilEmiSum = 0;

    ///calculate emi amount sum
    let cibilEmiSum = 0;
    if (scoreData?.accounts && Array.isArray(scoreData?.accounts)) {
      scoreData?.accounts.forEach((value: any) => {
        value.dateClosed = value?.dateClosed ?? '-';
        value.emiAmont = Number(value?.emiAmount ?? 0);
        if (value?.dateClosed == '-' && value?.emiAmount != 0) {
          const emi = value?.emiAmount;
          if (!isNaN(emi)) cibilEmiSum += emi;
        }
      });
    }
    scoreData.cibilEmiSum = cibilEmiSum;

    cibilDetails.recentDateOpened = scoreData.recentDateOpened
      ? this.typeService.dateToJsonStr(scoreData.recentDateOpened, 'DD/MM/YYYY')
      : '-';
    cibilDetails.oldestDateOpened = scoreData.oldestDateOpened
      ? this.typeService.dateToJsonStr(scoreData.oldestDateOpened, 'DD/MM/YYYY')
      : '-';
    cibilDetails.recentInquiryDate = scoreData.recentInquiryDate
      ? this.typeService.dateToJsonStr(
          scoreData.recentInquiryDate,
          'DD/MM/YYYY',
        )
      : '-';
    cibilDetails.oldestInquiryDate = scoreData.oldestInquiryDate
      ? this.typeService.dateToJsonStr(
          scoreData.oldestInquiryDate,
          'DD/MM/YYYY',
        )
      : '-';
    if (type == 'cibilDetails') return cibilDetails;

    scoreData = { ...scoreData, ...cibilDetails };

    let isCibilFetchButton = false;
    const scoreDateStr = scoreData?.responsedata?.consumerCreditData
      ? scoreData?.responsedata?.consumerCreditData[0]?.scores[0]?.scoreDate
      : null;
    if (scoreDateStr) {
      const day = parseInt(scoreDateStr.substr(0, 2), 10);
      const month = parseInt(scoreDateStr.substr(2, 2), 10) - 1; // Months are 0-indexed
      const year = parseInt(scoreDateStr.substr(4, 4), 10);

      let scoreDate = new Date(year, month, day);
      scoreDate = this.typeService.getGlobalDate(scoreDate);
      const todaydate = new Date();
      const cibilFetchDateObj =
        this.dateService.dateToReadableFormat(scoreDate);

      //date at which cibil fetched
      scoreData.cibilFetchDate = cibilFetchDateObj
        ? `${cibilFetchDateObj?.readableStr}`
        : '-';
      const diffInDays = this.typeService.differenceInDays(
        todaydate,
        scoreDate,
      );
      let nextCibilFetchDate = scoreDate;
      nextCibilFetchDate.setDate(nextCibilFetchDate.getDate() + 90);
      scoreData.nextCibilFetchDate =
        this.typeService.dateToJsonStr(nextCibilFetchDate) ?? '-';
      //giving manual cibil fetch button option after 30 days of cibil hit
      if (diffInDays > 30) isCibilFetchButton = true;
      scoreData.isCibilFetchButton = isCibilFetchButton;
    }
    // not needed this fields for latest cibil data
    if (!type) {
      delete scoreData?.accounts;
      delete scoreData?.scores;
      delete scoreData?.responsedata;
      delete scoreData?.totalAccounts;
      delete scoreData?.zeroBalanceAccounts;
      delete scoreData?.highCreditAmount;
      delete scoreData?.currentBalance;
      delete scoreData?.monthlyIncome;
      delete scoreData?.totalInquiry;
      delete scoreData?.inquiryPast12Months;
      delete scoreData?.inquiryPast24Months;
      delete scoreData?.recentDateOpened;
      delete scoreData?.oldestDateOpened;
      delete scoreData?.recentInquiryDate;
      delete scoreData?.oldestInquiryDate;
    }
    return scoreData;
  }

  async getAllCibilDataUserWise(reqData) {
    const userId = reqData.userId;
    if (!userId) return kParamMissing('userId');

    // check if redis has data already
    // temp redis comment due to issue
    const dataFromRedis = await this.getRedisDataForMultipleUserId(userId);
    if (dataFromRedis) return dataFromRedis;

    const attributes = [
      'id',
      'loanId',
      'status',
      'cibilScore',
      'plScore',
      'totalAccounts',
      'overdueAccounts',
      'zeroBalanceAccounts',
      'highCreditAmount',
      'currentBalance',
      'overdueBalance',
      'totalOverdueDays',
      'PLOutstanding',
      'totalOutstanding',
      'monthlyIncome',
      'totalInquiry',
      'inquiryPast30Days',
      'inquiryPast12Months',
      'inquiryPast24Months',
      'recentDateOpened',
      'oldestDateOpened',
      'recentInquiryDate',
      'oldestInquiryDate',
      'PLAccounts',
      'scores',
      'responsedata',
    ];

    const rawQuery = `SELECT "${attributes.join('","')}"
        FROM "CibilScoreEntities"
        WHERE "type" = '1' AND "status" = '1' AND "userId" = '${userId}'
        ORDER BY "id" DESC;`;
    const outputList = await this.repo.injectRawQuery(
      CibilScoreEntity,
      rawQuery,
      {
        source: 'REPLICA',
      },
    );
    const result = [];
    if (outputList === k500Error) throw new Error();
    for (let i = 0; i < outputList.length; i++) {
      const currentData = outputList[i];
      // no internal_source means did not fetched cibil data and got cibil data from internal_source
      if (
        currentData?.responsedata?.internal_source != 'POSTGRES' &&
        currentData?.scores
      ) {
        const date = currentData?.scores[0]?.scoreDate;
        // DD/MM/YYYY format
        const formattedDate =
          date.slice(0, 2) + '/' + date.slice(2, 4) + '/' + date.slice(4);
        const cibilData = this.prepareCibilData(currentData, 'cibilDetails');
        result.push({
          date: formattedDate,
          id: currentData.id,
          data: cibilData,
        });
      }
    }

    // Prepare difference of cibil data between cibil fetched dates
    for (let i = result.length - 2; i >= 0; i--) {
      let currentEle = result[i].data;
      let previousEle = result[i + 1].data;

      currentEle.diffInCibil = this.calculatePercentage(
        currentEle?.cibilScore ?? 0,
        previousEle?.cibilScore ?? 0,
      );
      currentEle.diffInPl = this.calculatePercentage(
        currentEle?.plScore ?? 0,
        previousEle?.plScore ?? 0,
      );
      currentEle.diffInTotalAccounts =
        (currentEle?.totalAccounts ?? 0) - (previousEle?.totalAccounts ?? 0);
      currentEle.diffinOverdueAccounts =
        (currentEle?.overdueAccounts ?? 0) -
        (previousEle?.overdueAccounts ?? 0);
      currentEle.diffInZeroBalanceAccounts =
        (currentEle?.zeroBalanceAccounts ?? 0) -
        (previousEle?.zeroBalanceAccounts ?? 0);
      currentEle.diffInHighCreditAmount =
        (currentEle?.highCreditAmount ?? 0) -
        (previousEle?.highCreditAmount ?? 0);
      currentEle.diffInOverdueBalance =
        (currentEle?.overdueBalance ?? 0) - (previousEle?.overdueBalance ?? 0);
      currentEle.diffInTotalOutstandingBalance =
        (currentEle?.totalOutstanding ?? 0) -
        (previousEle?.totalOutstanding ?? 0);
      currentEle.diffInPLoutstanding =
        (currentEle?.PLOutstanding ?? 0) - (previousEle?.PLOutstanding ?? 0);
      currentEle.diffInPlAccounts =
        (currentEle?.PLAccounts ?? 0) - (previousEle?.PLAccounts ?? 0);
      currentEle.diffInDelaydays =
        (currentEle?.totalOverdueDays ?? 0) -
        (previousEle?.totalOverdueDays ?? 0);
      currentEle.diffInTotalInquiry =
        (currentEle?.totalInquiry ?? 0) - (previousEle?.totalInquiry ?? 0);
    }
    // set prepared data in redis
    if (result.length > 0)
      await this.setRedisDataForMultipleUserId(userId, result);
    return result;
  }

  async getCibilDataIdWise(reqData) {
    const id = reqData?.id;

    const attributes = [
      'id',
      'names',
      'ids',
      'telephones',
      'emails',
      'employment',
      'scores',
      'addresses',
      'accounts',
      'enquiries',
    ];

    const rawQuery = `SELECT "${attributes.join('","')}"
        FROM "CibilScoreEntities"
        WHERE "id" = '${id}';`;

    const outputList = await this.repo.injectRawQuery(
      CibilScoreEntity,
      rawQuery,
      {
        source: 'REPLICA',
      },
    );
    if (outputList == k500Error) throw new Error();
    if (outputList.length == 0) return kSomthinfWentWrong;

    const result = this.prepareCibilData(
      outputList[0],
      'consumerCreditDetails',
    );
    return result;
  }

  calculatePercentage(current: number, previous: number) {
    // calculate percentage difference between any two number
    const difference = current - previous;

    const percentageDifference = (difference / previous) * 100;
    return +percentageDifference.toFixed(2);
  }

  sortCibilArrayByDate(data, sortBy) {
    data.sort((a, b) => {
      const valueA = a[sortBy];
      const valueB = b[sortBy];

      // If both values are valid, proceed with comparison
      if (valueA && valueB) {
        const dateA =
          valueA.substring(4) + valueA.substring(2, 4) + valueA.substring(0, 2);
        const dateB =
          valueB.substring(4) + valueB.substring(2, 4) + valueB.substring(0, 2);

        return dateA.localeCompare(dateB);
      }
      // If only one is undefined, decide how to handle the undefined/null case
      if (!valueA && valueB)
        return 1; // Push entries with undefined/null to the bottom
      else if (valueA && !valueB) return -1; // Push entries with undefined/null to the bottom
      return 0;
    });
    return data;
  }

  async migrateOldestOpenandEnquiryDate() {
    const rawQuery = `SELECT 
    "cibil"."id",
    "cibil"."oldestInquiryDate",
    "cibil"."oldestDateOpened",
    "cibil"."createdAt",
    "cibil"."accounts",
    "cibil"."enquiries"
     FROM 
     public."CibilScoreEntities" AS "cibil"
     WHERE "cibil"."accounts" is not null and  "cibil"."enquiries" is not null 
     and "cibil"."cibilScore"!='-1' and
     "cibil"."oldestInquiryDate" = "cibil"."oldestDateOpened" and "cibil"."status"='1' 
      order by "cibil"."createdAt" desc`;

    const outputList = await this.repo.injectRawQuery(
      CibilScoreEntity,
      rawQuery,
    );
    if (outputList == k500Error) throw new Error();

    for (let i = 0; i < outputList.length; i++) {
      const updatedData: any = {};
      const tempObj: any = {};
      const currentElement = outputList[i];
      let oldInquiryDate = currentElement?.enquiries ?? [];
      const updatedOldInquiryDate: any = this.sortCibilArrayByDate(
        oldInquiryDate,
        'enquiryDate',
      );
      oldInquiryDate =
        Array.isArray(updatedOldInquiryDate) && updatedOldInquiryDate.length > 0
          ? updatedOldInquiryDate[0]?.enquiryDate
          : null;
      tempObj.oldestInquiryDate =
        this.typeService.cibiDateToDBDate(oldInquiryDate);

      // calculate oldestOpen Date
      let oldOpenDate = currentElement?.accounts ?? [];
      const updatedOldOpenDate: any = this.sortCibilArrayByDate(
        oldOpenDate,
        'dateOpened',
      );

      oldOpenDate =
        Array.isArray(updatedOldOpenDate) && updatedOldOpenDate.length > 0
          ? updatedOldOpenDate[0]?.dateOpened
          : null;
      tempObj.oldestDateOpened = this.typeService.cibiDateToDBDate(oldOpenDate);

      if (currentElement.oldestInquiryDate != tempObj.oldestInquiryDate) {
        updatedData.oldestInquiryDate = tempObj.oldestInquiryDate;
      }
      if (currentElement.oldestInquiryDate != tempObj.oldestDateOpened) {
        updatedData.oldestDateOpened = tempObj.oldestDateOpened;
      }
      console.log({ id: currentElement.id });
      console.log(updatedData);
      await this.repo.updateRowData(
        CibilScoreEntity,
        updatedData,
        currentElement.id,
        true,
      );
    }

    return true;
  }

  async insertBatchCibilDataFile(reqData) {
    const { file, body } = reqData;
    let batchOutputDate = body.batchOutputDate;
    let submissionDate = body.submissionDate;
    const adminId = body.adminId;
    if (!file) return kParamMissing('file');
    if (!batchOutputDate) return kParamMissing('batchOutputDate');
    if (!submissionDate) return kParamMissing('submissionDate');
    if (!adminId) return kParamMissing('adminId');
    if (!validateISODate(batchOutputDate))
      return kInvalidParamValue('batchOutputDate');
    if (!validateISODate(submissionDate))
      return kInvalidParamValue('submissionDate');

    const fileName = file.filename;
    const originalFileName = file.originalname;
    if (
      !(
        originalFileName?.includes('E491') || originalFileName?.includes('E528')
      )
    )
      return k422ErrorMessage('Please provide valid 3 or 5 token type file');

    batchOutputDate = this.typeService.getGlobalDate(batchOutputDate);
    submissionDate = this.typeService.getGlobalDate(submissionDate);
    const checkFileExist = await this.repo.getCountsWhere(
      BatchCibilFileTrackingEntity,
      {
        where: {
          fileName: originalFileName,
        },
      },
    );
    if (checkFileExist == k500Error) return kInternalError;
    if (checkFileExist > 0)
      return k422ErrorMessage(
        'File is already uploaded, please upload a new file',
      );

    const cibilFile: any = await this.fileService.excelToArray(
      fileName,
      batchCibilFileCols,
      true,
    );
    if (cibilFile.message) return cibilFile;
    let cibilDataFromFile = cibilFile.finalData;
    let cibilFileColumnsName = cibilFile.columnName;
    cibilFileColumnsName = new Set(cibilFileColumnsName);
    const batchCibilFileColsName = Object.values(batchCibilFileCols);
    const IsvalidColumnsFile = batchCibilFileColsName.every((value) =>
      cibilFileColumnsName.has(value),
    );
    if (!IsvalidColumnsFile)
      return k422ErrorMessage('Please provide valid file');

    cibilDataFromFile = cibilDataFromFile.filter(
      (el) => Object.keys(el)?.length,
    );
    if (!cibilDataFromFile.length)
      return k422ErrorMessage('Please provide valid not empty file');

    const fileURL = await this.fileService.uploadFile(
      file?.path,
      'BatchCibilInput',
      fileName.endsWith('csv') ? 'csv' : 'xlsx',
    );
    if (fileURL.message) return fileURL;

    const insertFileDetails = {
      fileName: originalFileName,
      tokenType: originalFileName?.includes('E491') ? '5' : '3',
      numberOfUser: cibilDataFromFile.length,
      url: fileURL,
      submissionDate,
      batchOutputDate,
      uploadedBy: adminId,
    };

    const insertFileData = await this.repo.createRowData(
      BatchCibilFileTrackingEntity,
      insertFileDetails,
    );
    if (insertFileData == k500Error) return kInternalError;

    const uniqueIdArr = [
      ...new Set(cibilDataFromFile.map((el) => el.memberId?.slice(2))),
    ];
    const userData = await this.userRepo.getTableWhereData(
      ['id', 'fullName', 'uniqueId', 'kycId', 'phone'],
      { where: { uniqueId: uniqueIdArr } },
    );

    if (userData == k500Error) return kInternalError;
    const kycIdArr = [...new Set(userData.map((el) => el.kycId))];

    const kycData = await this.kycRepo.getTableWhereData(
      ['id', 'panCardNumber'],
      { where: { id: kycIdArr } },
    );
    if (kycData == k500Error) return kInternalError;

    const cibilData: any = [];
    for (let i = 0; i < cibilDataFromFile.length; i++) {
      const ele = cibilDataFromFile[i];
      const uniqueId = ele.memberId?.slice(2);
      const tempObj: any = {
        memberId: uniqueId,
        cibilScore: ele.cibilScore,
        plScore: ele.plScore,
        salaryBand: ele.salaryBand,
        securedOverdueAmount: ele.securedOverdueAmount,
        unsecuredOverdueAmount: ele.unsecuredOverdueAmount,
        fileId: insertFileData.id,
      };
      const user = userData.find((user) => user.uniqueId == uniqueId);
      if (user) {
        tempObj.userId = user.id;
        tempObj.fullName = user.fullName;
        tempObj.phone = user.phone;
        if (user.kycId) {
          const kyc = kycData.find(
            (el) => el.id == user.kycId && el.panCardNumber,
          );
          if (kyc)
            tempObj.panNumber = await this.cryptService.encryptText(
              kyc.panCardNumber,
            );
        }
      }
      cibilData.push(tempObj);
    }

    const insertedData = await this.repo.bulkCreate(
      BatchCibilDataEntity,
      cibilData,
    );
    if (insertedData == k500Error) return kInternalError;
    return true;
  }

  async getBatchCibilData(reqData) {
    const startDate = reqData.startDate;
    const endDate = reqData.endDate;
    const adminId = reqData.adminId;
    const isDownload = reqData.isDownload?.toString() == 'true';
    if (!adminId) return kParamMissing('adminId');
    if (!startDate) return kParamMissing('startDate');
    if (!endDate) return kParamMissing('endDate');

    const cibilData = await this.repo.getTableWhereData(
      BatchCibilDataEntity,
      null,
      {
        where: {
          createdAt: {
            [Op.gte]: this.typeService.getUTCDate(startDate),
            [Op.lte]: this.typeService.getUTCDate(endDate),
          },
        },
        order: [['id', 'DESC']],
      },
    );

    if (cibilData == k500Error) return kInternalError;

    const finalizedData: any = [];
    for (let i = 0; i < cibilData.length; i++) {
      const ele = cibilData[i];
      const tempObj: any = {
        userId: ele.userId ?? '-',
        fullName: ele.fullName ?? '-',
        phone: '-',
        panNumber: '-',
        memberId: ele.memberId ?? '-',
        cibilScore: ele.cibilScore ?? '-',
        plScore: ele.plScore ?? '-',
        salaryBand: ele.salaryBand ?? '-',
        securedOverdueAmount: ele.securedOverdueAmount ?? '-',
        unsecuredOverdueAmount: ele.unsecuredOverdueAmount ?? '-',
      };
      if (ele.phone) {
        const decryptPhone = this.cryptService.decryptPhone(ele.phone);
        if (decryptPhone != k500Error) tempObj.phone = decryptPhone;
      }
      if (ele.panNumber) {
        const decryptPan = await this.cryptService.decryptText(ele.panNumber);
        if (decryptPan) tempObj.panNumber = decryptPan;
      }
      finalizedData.push(tempObj);
    }

    if (isDownload) {
      const rawExcelData = {
        sheets: 'Batch-Cibil-User-Data',
        data: finalizedData,
        sheetName: 'Batch_Cibil_User_Data.xlsx',
      };
      const url: any = await this.fileService.objectToExcelURL(rawExcelData);
      if (url?.message) return url;
      await this.reportHistoryRepo.create({
        adminId,
        fromDate: startDate,
        toDate: endDate,
        downloadUrl: url,
        reportName: 'Batch Cibil',
        status: '1',
        apiUrl: 'shared/getBatchCibilData',
      });
      return { url };
    }
    return finalizedData;
  }

  async getBatchFileCibilData(reqData) {
    const startDate = reqData.startDate;
    const endDate = reqData.endDate;
    const adminId = reqData.adminId;
    const page = reqData.page ?? 1;
    const pageSize = reqData.pageSize ?? PAGE_LIMIT;
    const offset = (page - 1) * pageSize;
    if (!adminId) return kParamMissing('adminId');
    if (!startDate) return kParamMissing('startDate');
    if (!endDate) return kParamMissing('endDate');

    const cibilFileData = await this.repo.getTableCountWhereData(
      BatchCibilFileTrackingEntity,
      null,
      {
        where: { submissionDate: { [Op.gte]: startDate, [Op.lte]: endDate } },
        limit: pageSize,
        offset,
        order: [['id', 'DESC']],
      },
    );
    if (cibilFileData == k500Error) return kInternalError;
    const finalizedData: any = { count: cibilFileData.count };
    await this.commonSharedService.getAdminData(adminId);
    finalizedData.rows = cibilFileData.rows.map((el) => ({
      'File Name': el.fileName,
      'Submission Date': this.typeService.getDateFormated(el.submissionDate),
      'Token Type': el.tokenType,
      'Number Of Users': el.numberOfUser,
      'Output Date': this.typeService.getDateFormated(el.batchOutputDate),
      'Uploaded By':
        this.commonSharedService.allAdminData?.find(
          (admin) => admin.id == el.uploadedBy,
        )?.fullName ?? '-',
      'Uploaded Date': this.typeService.getDateFormated(el.createdAt),
      'View Doc': el.url,
    }));
    return finalizedData;
  }
}
