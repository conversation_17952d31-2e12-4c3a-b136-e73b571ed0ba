import { Inject, Injectable } from '@nestjs/common';
import { MAIL_TRACKER_REPOSITORY } from 'src/constants/entities';
import { k500Error } from 'src/constants/misc';
import { MailTrackerEntity } from 'src/entities/mail.tracker.entity';
import { RepositoryManager } from './repository.manager';
import { AlertSharedService } from 'src/shared/alert.service';

@Injectable()
export class MailTrackerRepository {
  constructor(
    @Inject(MAIL_TRACKER_REPOSITORY)
    private readonly repository: typeof MailTrackerEntity,
    private readonly repoManager: RepositoryManager,
    private readonly alertSharedService: AlertSharedService,
  ) {}

  async getRowWhereData(
    category: string,
    attributes: any[],
    options: any,
    isForce = false,
  ) {
    return await this.alertSharedService.getRowWhereData(
      category,
      attributes,
      options,
      isForce,
    );
  }

  async getTableWhereData(
    category: string,
    attributes: any,
    options: any,
    isForce = false,
  ) {
    try {
      return await this.alertSharedService.getTableWhereData(
        category,
        attributes,
        options,
        isForce,
      );
    } catch (error) {
      return k500Error;
    }
  }

  async create(category: string, data: any) {
    return await this.alertSharedService.createRowData(category, data);
  }

  async updateRowData(category: string, updateData, id: any, userId: any) {
    try {
      return await this.alertSharedService.updateRowData(
        category,
        updateData,
        id,
        false,
        userId,
      );
    } catch (error) {
      return k500Error;
    }
  }

  async updateRowWhereData(
    category: string,
    updateData,
    options: any,
    isForce = false,
  ) {
    try {
      return await this.alertSharedService.updateRowWhereData(
        category,
        updateData,
        options,
        isForce,
      );
    } catch (error) {
      return k500Error;
    }
  }

  async getCountsWhere(category: string, options, isForce = false) {
    try {
      return await this.alertSharedService.getCountsWhere(
        category,
        options,
        isForce,
      );
    } catch (error) {
      return k500Error;
    }
  }

  async deleteWhereData(
    category: string,
    options: any,
    restricted = true,
    isForce = false,
  ) {
    try {
      return await this.alertSharedService.deleteWhereData(
        category,
        options,
        restricted,
        isForce,
      );
    } catch (error) {
      return k500Error;
    }
  }
}
