// Imports
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { k500Error } from 'src/constants/misc';
import { KYCEntity } from 'src/entities/kyc.entity';
import { UserRepository } from 'src/repositories/user.repository';
import { AwsService } from 'src/thirdParty/awsServices/aws.service';
import { UserSelfieRepository } from 'src/repositories/user.selfie.repository';
import { Op, Sequelize } from 'sequelize';
import { AdminRepository } from 'src/repositories/admin.repository';
import { RedisService } from 'src/redis/redis.service';
import { CryptService } from 'src/utils/crypt.service';
import { TemplateRepository } from 'src/repositories/template.repository';
import { PurposeRepository } from 'src/repositories/purpose.repository';
import { EmployementDegignationRepository } from 'src/repositories/degignation.repository';
import { EmployementSectoreRepository } from 'src/repositories/sector.repository';
import { BankingEntity } from 'src/entities/banking.entity';
import { employmentDetails } from 'src/entities/employment.entity';
import { registeredUsers } from 'src/entities/user.entity';
import { LoanRepository } from 'src/repositories/loan.repository';
import { EmploymentRepository } from 'src/repositories/employment.repository';
import {
  GlobalServices,
  SYSTEM_ADMIN_ID,
  gIsPROD,
  AADHARE_LAT_LONG_RADIUS,
  UAT_PHONE_NUMBER,
  LOGOUTTIME,
  BELOW_OUTSTANDING_AMOUNT,
  templateDesign,
} from 'src/constants/globals';
import {
  KLOANCHANGEABLE,
  KLOANREFUND,
  kCompleted,
  kErrorMsgs,
  kInsurance,
  kInsuranceRelationshipCode,
  kMiscRedisKeys,
  kSomthinfWentWrong,
  kMaskAccount,
  kMaskPan,
  kMaskEmail,
  kMaskPhone,
} from 'src/constants/strings';
import { StaticConfigRepository } from 'src/repositories/static.config.repository';
import {
  k422ErrorMessage,
  kInternalError,
  kParamMissing,
} from 'src/constants/responses';
import { ManualVerifiedWorkEmailRepository } from 'src/repositories/manualVerifiedWorkEmail.repository';
import { EmiEntity } from 'src/entities/emi.entity';
import { LegalCollectionRepository } from 'src/repositories/legal.collection.repository';
import {
  kAppNotificationIcon,
  kMiscActionType,
  kMiscReqUrl,
  kMiscSubType,
  kSettingReqUrl,
  kSettingSubType,
  UserStage,
  REDIS_KEY,
} from 'src/constants/objects';
import { KYCRepository } from 'src/repositories/kyc.repository';
import { PredictionEntity } from 'src/entities/prediction.entity';
import { TypeService } from 'src/utils/type.service';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { WhatsAppService } from 'src/thirdParty/whatsApp/whatsApp.service';
import { MasterEntity } from 'src/entities/master.entity';
import { EMIRepository } from 'src/repositories/emi.repository';
import { TransactionEntity } from 'src/entities/transaction.entity';
import * as fs from 'fs';
import {
  PY_BACKEND_BASE_URL_LSP,
  PY_BACKEND_BASE_URL_NBFC,
} from 'src/constants/network';
import { ActiveLoanAddressesEntity } from 'src/entities/activeLoanAddress.entity';
import { ValidationService } from 'src/utils/validation.service';
import { HashPhoneEntity } from 'src/entities/hashPhone.entity';
import { ThirdPartyServiceRepo } from 'src/repositories/thirdParty.service.repo';
import { ThirdPartyProviderRepo } from 'src/repositories/thirdpartyService.provider.repo';
import { NUMBERS } from 'src/constants/numbers';
import { ErrorContextService } from 'src/utils/error.context.service';
import { StaticConfigEntity } from 'src/entities/static.config.entity';
import { EnvConfig } from 'src/configs/env.config';
import { HypothecationEntity } from 'src/entities/hypothecation.entity';
import { employmentDesignation } from 'src/entities/designation.entity';
import { Department } from 'src/entities/department.entity';
import { TemplateEntity } from 'src/entities/template.entity';
import { ElephantService } from 'src/thirdParty/elephant/elephant.service';
import { ChangeLogsRepository } from 'src/repositories/changeLogs.repository';
import { loanTransaction } from 'src/entities/loan.entity';
import { AdminEligibilityEntity } from 'src/entities/adminEligibility.entity';
import { CrmReasonEntity } from 'src/entities/crm.reasons.entity';
import { crmTitle } from 'src/entities/crmTitle.entity';
import { crmStatus } from 'src/entities/crmStatus.entity';
import { AdminRoleRepository } from 'src/repositories/admin_role.repository';
import { AdminSubRoleModuleRepository } from 'src/repositories/admin_sub_role_module.repository';
let gActiveCollectionExecutives = [];

@Injectable()
export class CommonSharedService {
  allLoanPurpose: any[];
  allAdminData: any[] = [];
  allDesignationData: any[];
  allSectorData: any[];
  constructor(
    private readonly userRepo: UserRepository,
    private readonly awsService: AwsService,
    private readonly selfieRepo: UserSelfieRepository,
    private readonly adminRepo: AdminRepository,
    private readonly typeService: TypeService,
    private readonly redisService: RedisService,
    private readonly cryptService: CryptService,
    private readonly staticConfig: StaticConfigRepository,
    private readonly templateRepo: TemplateRepository,
    private readonly purposeRepo: PurposeRepository,
    private readonly designationRepo: EmployementDegignationRepository,
    private readonly sectorRepo: EmployementSectoreRepository,
    private readonly loanRepo: LoanRepository,
    private readonly manualWorkMailRepo: ManualVerifiedWorkEmailRepository,
    private readonly legalCollectionRepo: LegalCollectionRepository,
    private readonly empRepo: EmploymentRepository,
    private readonly KYCRepo: KYCRepository,
    private readonly changeLogsRepo: ChangeLogsRepository,
    private readonly adminRoles: AdminRoleRepository,
    private readonly adminSubModelRepo: AdminSubRoleModuleRepository,
    // Database
    private readonly repoManager: RepositoryManager,
    private readonly whatsAppService: WhatsAppService,
    private readonly validation: ValidationService,
    // Repositories
    private readonly emiRepo: EMIRepository,
    @Inject(forwardRef(() => ThirdPartyServiceRepo))
    private readonly thirdPartyServiceRepo: ThirdPartyServiceRepo,
    @Inject(forwardRef(() => ThirdPartyProviderRepo))
    private readonly thirdPartyServiceProvider: ThirdPartyProviderRepo,
    private readonly errorContextService: ErrorContextService,
    private readonly elephantService: ElephantService,
  ) {
    this.getAdminData(1);
    this.getSectorData(1);
    this.getDesignationData(1);
  }

  //#region validate with aadhare profile image return selfie status
  async validateWithAadhareImage(userId, statusData) {
    try {
      const aadhaar = statusData?.aadhaar ?? -1;
      const selfie = statusData?.selfie ?? -1;
      if (aadhaar == 1 || aadhaar == 3) {
        /// kyc include
        const kycInclude: any = { model: KYCEntity };
        kycInclude.attributes = ['aadhaarFront', 'profileImage'];
        const include = [kycInclude];
        const options = { where: { id: userId }, include };
        const att = ['id', 'selfieId', 'isRedProfile'];
        const userData = await this.userRepo.getRowWhereData(att, options);
        if (!userData || userData === k500Error) return 5;
        const kycData = userData?.kycData;
        const aadhaarImg = kycData?.profileImage ?? kycData?.aadhaarFront ?? '';
        const isRedProfile = (userData?.isRedProfile ?? 0) === 2;

        const checkExitsData = await this.getSelfieData({
          userId,
          callFnFrom: '3',
        });

        if (checkExitsData === k500Error) return kInternalError;
        const selfieImg =
          checkExitsData?.tempImage ?? checkExitsData?.image ?? '';
        if (!aadhaarImg || !selfieImg) return 5;
        /// compare image to aws
        const data = { imageA: selfieImg, imageB: aadhaarImg };
        const result = await this.awsService.compareImages(data);
        // update selfie data
        const verifiedDate = new Date().toJSON();
        const response = result?.message ? '' : JSON.stringify(result);
        const updatedData: any = {
          response,
          status: '0',
          verifiedDate,
        };
        if (
          checkExitsData?.details?.selfieFromRetry === true &&
          !isRedProfile
        ) {
          // if (!checkExitsData?.tempImage) updatedData.tempImage = selfieImg;
          await this.selfieRepo.updateRowData(updatedData, userData.selfieId);
          await this.redisService.del(`SELFIE_DATA_${userId}`);
          await this.redisService.del(`APP_USER_PROFILE_DATA_${userId}`);

          return 0;
        }
        if (result?.isMatched) {
          if (selfie == 2) updatedData.status = '0';
          else {
            updatedData.status = '1';
            updatedData.image = selfieImg;
          }
        }

        /// if old defulter then approved
        if (isRedProfile) updatedData.status = '1';
        await this.selfieRepo.updateRowData(updatedData, userData.selfieId);
        await this.redisService.del(`SELFIE_DATA_${userId}`);
        await this.redisService.del(`APP_USER_PROFILE_DATA_${userId}`);

        if (isRedProfile) return 1;
        if (result?.isMatched) {
          if (selfie == 2) return 0;
          else return 1;
        } else return 0;
      } else return 5;
    } catch (error) {
      return 5;
    }
  }

  // clear admin jwt details
  async clearAdminJwtDetails(adminId) {
    try {
      const key = `ADMIN_JWTDETAILS`;
      const rawData = await this.redisService.getKeyDetails(key);
      if (rawData) {
        let jsonData = JSON.parse(rawData);
        jsonData = jsonData.filter((entry) => entry.id !== parseInt(adminId));
        await this.redisService.updateKeyDetails(key, JSON.stringify(jsonData));
      }
      return true;
    } catch (error) {
      return false;
    }
  }

  async validatedToken(token) {
    try {
      let jsonData = [];
      const key = `ADMIN_JWTDETAILS`;
      const rawData = await this.redisService.getKeyDetails(key);
      if (rawData) jsonData = JSON.parse(rawData);

      const currDate = new Date();

      // Check if the token is in the JSON file and if it's valid
      let tokenData = jsonData.find((entry) => entry.jwtToken === token);
      if (tokenData) {
        const expiryDate = new Date(tokenData.jwtTokenExpireDate);
        if (expiryDate.getTime() > currDate.getTime())
          return { id: tokenData.id, roleId: tokenData.roleId };
      }
      const att = ['id', 'roleId', 'jwtDetails'];
      const where = { jwtDetails: { [Op.iRegexp]: token }, isActive: '1' };
      const result = await this.adminRepo.getRoweData(att, { where });
      if (!result || result === k500Error) return false;
      const jwt = result.jwtDetails;
      if (jwt) {
        const find = JSON.parse(jwt).find((f) => f.jwtToken === token);
        if (find) {
          const currDate = new Date();
          const expiryDate = new Date(find.jwtTokenExpireDate);
          if (expiryDate.getTime() > currDate.getTime()) {
            const newTokenData = {
              id: result.id,
              roleId: result.roleId,
              jwtToken: token,
              jwtTokenExpireDate: find.jwtTokenExpireDate,
            };

            // Store the new token data in the JSON file
            const index = jsonData.findIndex(
              (token) => token.id === newTokenData.id,
            );
            if (index !== -1) jsonData[index] = newTokenData;
            else jsonData.push(newTokenData);

            await this.redisService.updateKeyDetails(
              key,
              JSON.stringify(jsonData),
            );

            return { id: result.id, roleId: result.roleId };
          }
        }
      }
      return false;
    } catch (error) {
      return false;
    }
  }
  //#endregion

  getPythonBaseUrl(appType = 0) {
    return appType == 1 ? PY_BACKEND_BASE_URL_NBFC : PY_BACKEND_BASE_URL_LSP;
  }
  async validateRights(email, token, type?) {
    try {
      const data = await this.checkNValidateToken(email, token);
      if (data.isExpired) return false;
      if (type == KLOANCHANGEABLE) {
        if (data.adminData && data.adminData.changeableData) {
          return true;
        }
      } else if (type == KLOANREFUND) {
        if (data.adminData && data.adminData.isRefund) {
          return true;
        }
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  async checkNValidateToken(email, token) {
    try {
      const attributes = [
        'id',
        'fullName',
        'roleId',
        'email',
        'password',
        'phone',
        'jwtDetails',
        'thirdPartyViews',
        'changeableData',
        'isRefund',
      ];
      const allAdmins = await this.adminRepo.getTableWhereData(attributes, {});

      let checkUser;
      for (let index = 0; index < allAdmins.length; index++) {
        try {
          const adminData = allAdmins[index];

          adminData.email = await this.cryptService.decryptText(
            adminData.email,
          );

          if (adminData.email == email.toLowerCase()) {
            checkUser = adminData;
            break;
          }
        } catch (error) {}
      }
      let currJwt;
      let isTokenExpired = true;
      if (checkUser.jwtDetails) {
        JSON.parse(checkUser.jwtDetails).map((singleData) => {
          if (token == singleData.jwtToken) {
            currJwt = singleData;
          }
        });
        if (currJwt) {
          const currDate = new Date();
          const expiryDate = new Date(currJwt.jwtTokenExpireDate);
          isTokenExpired = expiryDate.getTime() < currDate.getTime();
        } else {
          isTokenExpired = true;
        }
      }
      return { isExpired: isTokenExpired, adminData: checkUser };
    } catch (error) {
      return { isExpired: false };
    }
  }

  async getServiceName(serviceName) {
    const info = await this.redisService.get(serviceName);
    return info ?? GlobalServices[serviceName];
  }

  //#region addOrUpdateStaticConfig
  async addOrUpdateStaticConfig(body) {
    try {
      if (!gIsPROD) {
        let data = body?.value;
        const type = body?.type;
        if (!type || !data) return kParamMissing();
        if (typeof data === 'object' && !data.length) data = [data];
        else if (typeof data != 'object') data = [data];
        const att = ['id'];
        const options = { where: { type } };
        const find = await this.staticConfig.getRowWhereData(att, options);
        let id;
        if (find?.id) id = find.id;
        if (id) {
          const updateData = { data };
          const update = await this.staticConfig.updateRowData(updateData, id);
          if (!update || update === k500Error) return kInternalError;
        } else {
          const createData = { type, data };
          const create = await this.staticConfig.create(createData);
          if (!create || create === k500Error) return kInternalError;
        }
      } else return k422ErrorMessage(kSomthinfWentWrong);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region refresh insurance array
  async refreshInsuranceArray(planId?) {
    try {
      const options = {
        where: { type: [kInsurance, kInsuranceRelationshipCode] },
      };
      if (planId == true) options.where.type = ['INSURANCE_PLAN_ID'];
      const att = ['id', 'type', 'data'];
      let filter = await this.staticConfig.getTableWhereData(att, options);
      if (!filter || filter === k500Error) return kInternalError;
      let filterObj: any = {};
      for (let i = 0; i < filter.length; i++) {
        try {
          const ele = filter[i];
          Object.assign(filterObj, JSON.parse(ele.data));
        } catch (error) {}
      }
      return { ...filterObj };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#endregion
  async checkManualWorkMail(
    workMailData,
    empData,
    updateData,
    approveById = SYSTEM_ADMIN_ID,
  ) {
    try {
      const workMail = workMailData.email;
      const domain = workMail.split('@')[1];
      const companyName = (empData?.companyName ?? '').toLowerCase().trim();
      if (!companyName) return false;
      let url = (empData?.companyUrl ?? '_URL').trim();
      if (!url) url = '_URL';
      const where = {
        domain,
        isActive: true,
        approveById: { [Op.ne]: null },
        companyName,
      };
      const checkExits = await this.manualWorkMailRepo.getRowWhereData(
        ['id', 'domain', 'url'],
        { where },
      );
      if (approveById != SYSTEM_ADMIN_ID) {
        if (
          (!checkExits || checkExits == k500Error) &&
          (updateData.status == '1' || updateData.status == '3')
        ) {
          const create = {
            domain,
            url,
            isActive: true,
            approveById,
            companyName,
          };
          await this.manualWorkMailRepo.create(create);
          return true;
        } else if (!checkExits || checkExits == k500Error) return false;
        else {
          const findURL = checkExits?.url ?? '_URL';
          if (findURL == '_URL' && findURL != url) {
            const update = { url, approveById };
            await this.manualWorkMailRepo.update(update, { id: checkExits.id });
          }
        }
      }
      if (!checkExits || checkExits == k500Error) return false;
      return true;
    } catch (error) {
      return false;
    }
  }

  //get transactions failed reason
  getFailedReason(response) {
    try {
      const failResponse = JSON.parse(response);
      let reason = '-';
      if (failResponse) {
        if (failResponse?.adNotPlaced === true) {
          reason = failResponse?.errorMsg ?? '-';
        } else if (failResponse.reason === 'INTERNAL_SERVER_ERROR') {
          reason = '-';
        } else {
          reason =
            failResponse?.error_message === 'NA'
              ? '-'
              : failResponse?.payment?.failureReason ??
                failResponse?.failureReason ??
                failResponse?.reason ??
                failResponse?.error_description ??
                '-';
        }
      }
      return reason;
    } catch (error) {
      return '-';
    }
  }

  async getRejectReasonTemplate(rejectReason) {
    try {
      if (!rejectReason) return {};
      const template = await this.templateRepo.getRowWhereData(['content'], {
        where: { title: rejectReason },
      });
      if (template === k500Error) return kInternalError;
      return template;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async getEligibleInterestRate(reqData) {
    // Params validation
    const userId = reqData?.userId;
    if (!userId) return kParamMissing('userId');

    const options = {
      useMaster: false,
      where: { userId },
      order: [['id', 'desc']],
    };

    const loanData = await this.loanRepo.getRowWhereData(
      ['id', 'interestRate'],
      options,
    );
    if (loanData == k500Error) throw new Error();
    let interestRate = loanData?.interestRate;
    const needDelayTag = reqData?.needDelayTag == true;
    const checkBanner = reqData?.checkBanner == true;
    // Update user data
    // await this.userRepo.updateRowData({ interestRate }, userId);

    // Returns the interest rate details with warrnings
    let delayInterestRate;

    if (needDelayTag) return { interestRate, delayInterestRate };
    if (checkBanner) return { interestRate, getBanner: false };

    return interestRate;
  }

  //#region this fun call for get company detials
  async getTansactionQueryParams(
    loanId,
    isCheckAddional = false,
    getData = false,
    accountNumber = '',
    bankCode?,
    skipEndDate = false,
  ) {
    try {
      if (!loanId) return kParamMissing('loanId');
      /// banking inclued
      const attributes = ['accountNumber', 'bank'];
      if (isCheckAddional) attributes.push('additionalAccountNumber');
      if (getData) attributes.push('salaryDate');
      const bankingModel = { model: BankingEntity, attributes };
      /// user inclued
      const include = [bankingModel];
      const options = { useMaster: false, where: { id: loanId }, include };
      const att = [
        'id',
        'userId',
        'loanStatus',
        'loan_disbursement_date',
        'completedLoan',
        'fullName',
      ];
      const result = await this.loanRepo.getRowWhereData(att, options);
      if (result === k500Error) return kInternalError;
      let loanStatus = result?.loanStatus?.toLowerCase();
      const empAtt = ['companyName', 'salary'];
      const empOptions = {
        where: { userId: result.userId },
        useMaster: false,
      };
      const employeeData = await this.empRepo.getRowWhereData(
        empAtt,
        empOptions,
      );
      if (employeeData === k500Error) return kInternalError;
      const where = {
        id: { [Op.lt]: result?.id },
        loanStatus: 'Complete',
        userId: result.userId,
      };
      const lastLoan =
        result?.completedLoan > 0
          ? await this.loanRepo.getRowWhereData(att, {
              where,
              order: [['id', 'desc']],
            })
          : {};
      let url = '';
      try {
        const bank = result?.bankingData;
        const emp = employeeData ?? {};
        const name = result?.fullName ?? '';
        let salaryAccountId = bank?.accountNumber ?? accountNumber ?? '';
        if (accountNumber) salaryAccountId = accountNumber ?? salaryAccountId;
        const additionalAccountId = bank?.additionalAccountNumber ?? '';
        const companyName = encodeURIComponent(emp?.companyName ?? '');
        const salary = +(emp?.salary ?? 0);
        const endDate = result?.loan_disbursement_date ?? '';
        const ecsCheckDate = lastLoan?.loan_disbursement_date ?? '';
        if (!salaryAccountId) return kInternalError;
        if (salaryAccountId)
          url +=
            '?callerApi=TransactionQuery&salaryAccountId=' + salaryAccountId;
        if (additionalAccountId && isCheckAddional)
          url += '&additionalAccountId=' + additionalAccountId;
        if (companyName) url += '&companyName=' + companyName;
        if (salary) url += '&salary=' + salary;
        if (endDate && !skipEndDate) url += '&endDate=' + endDate;
        if (ecsCheckDate) url += '&ecsCheckDate=' + ecsCheckDate;
        if (name) url += '&name=' + name;
        if (bank?.bank ?? bankCode)
          url += '&bankCode=' + (bank?.bank ?? bankCode);
        if (loanStatus) url += '&loanStatus=' + loanStatus;
      } catch (error) {}
      if (url) url = encodeURI(url);
      if (getData) return { ...result, url };
      return url;
    } catch (error) {
      console.log({ error });
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region
  async getLoanPurpose(purposeId: number) {
    try {
      if (this.allLoanPurpose?.length > 0) {
        const findData = this.allLoanPurpose.find(
          (item) => item.id === purposeId,
        );
        if (findData) return findData;
        else this.allLoanPurpose = [];
      } else {
        const data: any = await this.purposeRepo.getTableWhereData(
          ['id', 'purposeName'],
          {},
        );
        if (data == k500Error) this.allLoanPurpose = [];
        this.allLoanPurpose = data;
        const findData = this.allLoanPurpose.find(
          (item) => item.id === purposeId,
        );
        if (findData) return findData;
      }
    } catch (error) {
      this.allLoanPurpose = [];
    }
  }
  //#endregion

  //#region get admin Data from adminId
  async getAdminData(filterByKey: number | string) {
    try {
      if (filterByKey == SYSTEM_ADMIN_ID || !filterByKey)
        return { id: filterByKey, fullName: 'System' };
      if (this.allAdminData.length > 0) {
        const findData = this.allAdminData.find(
          (item) =>
            item.id == filterByKey ||
            item.email == filterByKey ||
            item.companyPhone == filterByKey ||
            item.phone == filterByKey ||
            item.fullName == filterByKey,
        );
        if (findData) return findData;
        else this.allAdminData = [];
      } else {
        const data = await this.adminRepo.getTableWhereData(
          [
            'id',
            'fullName',
            'email',
            'phone',
            'companyPhone',
            'departmentId',
            'otherData',
            'isActive',
            'roleId',
          ],
          {},
        );

        if (!data || data == k500Error) this.allAdminData = [];
        else {
          for (let i = 0; i < data.length; i++) {
            data[i].email = await this.cryptService.decryptText(data[i].email);
            data[i].phone = await this.cryptService.decryptText(data[i].phone);
            data[i].companyPhone = await this.cryptService.decryptText(
              data[i].companyPhone,
            );
          }
          this.allAdminData = data;
          const findData = this.allAdminData.find(
            (item) =>
              item.id == filterByKey ||
              item.email == filterByKey ||
              item.companyPhone == filterByKey ||
              item.phone == filterByKey ||
              item.fullName == filterByKey,
          );
          if (findData) return findData;
        }
      }
    } catch (error) {
      this.allAdminData = [];
    }
  }
  //#endregion

  async getDesignationData(filterByKey: any) {
    try {
      if (this.allDesignationData?.length > 0) {
        const findData = this.allDesignationData.find(
          (item) =>
            item.id == filterByKey || item.designationName == filterByKey,
        );
        if (findData) return findData;
        else this.allDesignationData = [];
      } else {
        const data = await this.designationRepo.getTableWhereData(
          ['id', 'designationName', 'designationStatusVerified'],
          {},
        );
        if (data == k500Error) this.allDesignationData = [];
        this.allDesignationData = data;
        const findData = this.allDesignationData.find(
          (item) =>
            item.id == filterByKey || item.designationName == filterByKey,
        );
        if (findData) return findData;
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }

  async getSectorData(filterByKey: any) {
    try {
      if (this.allSectorData?.length > 0) {
        const findData = this.allSectorData.find(
          (item) => item.id == filterByKey || item.sectorName == filterByKey,
        );
        if (findData) return findData;
        else this.allSectorData = [];
      } else {
        const data = await this.sectorRepo.getTableWhereData(
          ['id', 'sectorName', 'sectorStatusVerified'],
          {},
        );
        if (data == k500Error) this.allSectorData = [];
        this.allSectorData = data;
        const findData = this.allSectorData.find(
          (item) => item.id == filterByKey || item.sectorName == filterByKey,
        );
        if (findData) return findData;
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return k500Error;
    }
  }

  //get legal notice by loanId
  async getLegalDataByLoanId(loanId) {
    const attr = [
      'id',
      'sentType',
      'createdAt',
      'type',
      'dates',
      'subType',
      'caseDetails',
    ];
    const options: any = {
      useMaster: false,
      where: {
        loanId,
        otherDetails: {
          isAssign: { [Op.or]: [-1, null] },
          isHistory: { [Op.or]: [-1, null] },
        },
      },
      order: [['id', 'DESC']],
    };
    const legalData = await this.legalCollectionRepo.getRowWhereData(
      attr,
      options,
    );
    if (legalData === k500Error) throw new Error();
    return legalData;
  }

  //collection executive
  async getCollectionExecutive(adminId) {
    let adminList = [];
    if (adminId != -1) adminList.push(adminId);
    else {
      if (gActiveCollectionExecutives.length != 0)
        return gActiveCollectionExecutives;

      const attributes = ['followerId'];
      const options = {
        group: ['followerId'],
        where: { followerId: { [Op.ne]: null } },
      };
      const followerList = await this.loanRepo.getTableWhereData(
        attributes,
        options,
      );
      adminList = followerList.map((el) => el.followerId);

      let totalAdmins = await this.adminRepo.getTableWhereData(
        ['isActive', 'id'],
        { where: { id: adminList } },
      );
      totalAdmins = totalAdmins.filter((el) => el.isActive == '1');
      gActiveCollectionExecutives = totalAdmins.map((el) => el.id);
      return gActiveCollectionExecutives;
    }

    return adminList;
  }

  stageNumberToStr(stageNumber) {
    switch (stageNumber) {
      case UserStage.PHONE_VERIFICATION:
        return 'PHONE_VERIFICATION';

      case UserStage.BASIC_DETAILS:
        return 'BASIC_DETAILS';

      case UserStage.SELFIE:
        return 'SELFIE';

      case UserStage.NOT_ELIGIBLE:
        return 'NOT_ELIGIBLE';

      case UserStage.PIN:
        return 'PIN';

      case UserStage.AADHAAR:
        return 'AADHAAR';

      case UserStage.EMPLOYMENT:
        return 'EMPLOYMENT';

      case UserStage.BANKING:
        return 'BANKING';

      case UserStage.RESIDENCE:
        return 'RESIDENCE';

      case UserStage.LOAN_ACCEPT:
        return 'LOAN_ACCEPT';

      case UserStage.CONTACT:
        return 'CONTACT';

      case UserStage.PAN:
        return 'PAN';

      case UserStage.FINAL_VERIFICATION:
        return 'FINAL_VERIFICATION';

      case UserStage.MANDATE:
        return 'MANDATE';

      case UserStage.ESIGN:
        return 'ESIGN';

      case UserStage.DISBURSEMENT:
        return 'DISBURSEMENT';

      case UserStage.REPAYMENT:
        return 'REPAYMENT';

      case UserStage.EXPRESS_REAPPLY:
        return 'EXPRESS REAPPLY';

      case UserStage.REAPPLY:
        return 'REAPPLY';

      default:
        return '-';
    }
  }

  // verify AadhaarLatLong
  async verifyAadhaarLatLong(userId, isData = false) {
    try {
      // find company name of user according to userId
      let attribute = ['companyName'];
      const options = { where: { userId } };
      const data = await this.empRepo.getRowWhereData(attribute, options);
      const companyName = data?.companyName;
      if (!companyName) return { status: false, enum: 'COMPANY_NAME_NOT_FIND' };
      //find user same company
      const result: any = await this.getSameCompanyUsers(companyName);
      if (result?.status || result?.enum) return result;
      if (result.length === 0) return { status: true };
      //if userid find in  getSameCompanyUsers() then match AadhaarLatLong under 5km
      return await this.matchAadhaarLatLong(userId, result, isData);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // get user of same company Name
  async getSameCompanyUsers(companyName) {
    try {
      const attributes = ['userId'];
      const empOptions = { where: { companyName } };
      const companyListUser = await this.empRepo.getTableWhereData(
        attributes,
        empOptions,
      );
      if (!companyListUser || companyListUser == k500Error)
        return { status: false, enum: 'COMPANY_NAME_NOT_FIND' };

      const ids = [...new Set(companyListUser.map((item) => item.userId))];
      if (ids.length === 0) return { status: true };

      //get active user according to loan status active
      return await this.getActiveUsers(ids);
    } catch (error) {
      return { status: false, enum: 'COMPANY_NAME_NOT_FIND' };
    }
  }

  //get user which loan status active
  async getActiveUsers(userId) {
    try {
      const att = ['userId', 'id'];
      const options = { where: { userId, loanStatus: 'Active' } };
      const result = await this.loanRepo.getTableWhereData(att, options);
      if (!result || result == k500Error)
        return { status: false, enum: 'PROBLEM_IN_ACTIVE' };
      return result;
    } catch (error) {
      return { status: false, enum: 'PROBLEM_IN_ACTIVE' };
    }
  }

  //check user between 5km
  async matchAadhaarLatLong(userId, Ids, isData) {
    try {
      const activeUserId = [...new Set(Ids.map((item) => item.userId))];
      const att = ['aadhaarLatLongPoint'];
      const options = { where: { userId } };

      const result = await this.KYCRepo.getRowWhereData(att, options);

      const aadhaarLatLongPoint = result?.aadhaarLatLongPoint ?? '-';
      if (!result || result == k500Error || aadhaarLatLongPoint === '-')
        return { status: false, enum: 'AADHAR_LAT_LONG_NOT_FOUND' };

      const userLat = aadhaarLatLongPoint['x'];
      const userLng = aadhaarLatLongPoint['y'];
      let kycOptions: any = {
        where: {
          userId: activeUserId,
          aadhaarStatus: '1',
          [Op.and]: [
            Sequelize.literal(`ACOS(
              SIN(RADIANS(${userLat})) * SIN(RADIANS("KYCEntity"."aadhaarLatLongPoint"[0])) +
              COS(RADIANS(${userLat})) * COS(RADIANS("KYCEntity"."aadhaarLatLongPoint"[0])) *
              COS(RADIANS("KYCEntity"."aadhaarLatLongPoint"[1]) - RADIANS(${userLng}))
          ) * 3958.8 <=${AADHARE_LAT_LONG_RADIUS}`),
          ],
        },
      };

      const users = await this.KYCRepo.getTableWhereData(
        ['id', 'userId'],
        kycOptions,
      );
      if (users === k500Error)
        return { status: false, enum: 'AADHAR_LAT_LONG_NOT_FOUND' };
      const userList = users.map((el) => el?.userId);
      // get exact aadhaar address users
      const matchAadhaarAddressUserIds = await this.matchExactAadhaarAddress(
        userId,
      );
      if (matchAadhaarAddressUserIds?.status == false)
        return matchAadhaarAddressUserIds;
      const exactAddressUsersLength =
        matchAadhaarAddressUserIds.exactMatchUsers.length;
      if (exactAddressUsersLength > 0) {
        const finalData = {};
        finalData['status'] = false;
        finalData['enum'] = 'LAT_LONG_FOUND_WITH_EXACT_ADDRESS';
        finalData['users'] = users.length ?? 0;
        finalData['exactAddressUsersList'] =
          matchAadhaarAddressUserIds.exactMatchUsers;
        finalData['exactAddressUsers'] = exactAddressUsersLength;
        if (users.length > 0) finalData['userList'] = userList;
        return finalData;
      }

      if (users.length == 0) {
        return {
          status: true,
          enum: 'LAT_LONG_NOT_FOUND_IN_ACTIVE_LOAN',
          users: users.length ?? 0,
        };
      }
      return {
        status: false,
        enum: 'LAT_LONG_FOUND_IN_ACTIVE_LOAN',
        users: users.length ?? 0,
        userList,
      };
    } catch (error) {
      return { status: false, enum: 'AADHAR_LAT_LONG_NOT_FOUND' };
    }
  }

  // get aadhaar location exact or within 5km
  async findAadhaarLocationUsers(userId: string, type: string) {
    const predictionInclude = {
      model: PredictionEntity,
      attribute: ['id', 'reason'],
    };

    if (type == 'nearest')
      predictionInclude['where'] = { aadhaarLatLong: false };

    const att = ['userId', 'id'];
    const options: any = { where: { userId }, order: [['id', 'DESC']] };
    options.include = [predictionInclude];
    const loanData = await this.loanRepo.getRowWhereData(att, options);
    if (loanData === k500Error) throw new Error();
    if (!loanData) return {};

    let predictReasonData = loanData?.predictionData?.reason;
    if (!predictReasonData) return {};
    predictReasonData = JSON.parse(predictReasonData);

    const exactMatchUsers = predictReasonData?.exactMatchAddressUsers ?? [];

    let exactMatchUsersPercentage =
      predictReasonData?.exactMatchAddressPercentage ?? [];

    let exactMatchAddressUserIds = exactMatchUsers;

    const oldUsers = exactMatchUsers[0]?.userId ? false : true;

    if (!oldUsers) {
      exactMatchAddressUserIds = exactMatchUsers.map((user) => user.userId);
      exactMatchUsersPercentage = exactMatchUsers.map(
        (user) => user.matchedPercentage,
      );
    }

    const userIds =
      type == 'nearest'
        ? predictReasonData?.matchLatLongUsers
        : exactMatchAddressUserIds;

    if (!userIds) return {};
    // Get matched users data
    const userMatchData = await this.funCollectData(userIds);
    if (userMatchData === k500Error) return kInternalError;

    const finalData = [];

    for (let i = 0; i < userMatchData.length; i++) {
      try {
        const element = userMatchData[i];
        const exist = finalData.find(
          (data) => data['User Id'] === element.userId,
        );
        if (exist) continue;
        let loanStatus = element?.loanStatus ?? '-';
        const emi = element.emiData.find(
          (emi) => emi.payment_due_status === '1',
        );
        // if (exactMatchUsers[0]?.userId) {
        //   const userDetails = exactMatchUsers.find(
        //     (user) => user.userId == element.userId,
        //   );
        // }
        const userIdIndex = userIds.findIndex((user) => user == element.userId);

        if (emi) loanStatus = 'Delay';

        const fullName = element?.registeredUsers?.fullName ?? '-';

        const phoneNumber =
          this.cryptService.decryptPhone(element?.registeredUsers?.phone) ??
          '-';

        const aadhaarAddress = this.typeService.getAadhaarAddress(
          element?.registeredUsers?.kycData,
        );

        const penalty_days = this.typeService.getOverDueDay(element?.emiData);

        const temp = {};
        temp['User Id'] = element?.userId ?? '-';
        temp['Loan ID'] = element?.id ?? '-';
        temp['Name'] = fullName;
        temp['Days of delay'] = penalty_days ?? 0;
        temp['Aadhaar address'] = aadhaarAddress?.address ?? '-';
        temp['Phone number'] = phoneNumber;
        temp['Loan Status'] = loanStatus;
        if (type != 'nearest')
          temp['Similarity %'] = exactMatchUsersPercentage[userIdIndex];
        finalData.push(temp);
      } catch (error) {}
    }
    return finalData;
  }

  //#region Get Aadhaar location exact or within 5km
  private async funCollectData(userIds) {
    try {
      const userMatchData = await this.funLoanData(userIds);
      if (!userMatchData || userMatchData == k500Error) return k500Error;

      // Extract loan id(s) from data
      const loanIdArr = userMatchData.map((loan) => loan.id);
      // Get EMI data based on loan id(s)
      const EMIData = await this.funEMIData(loanIdArr);
      if (!EMIData || EMIData == k500Error) return k500Error;

      // Extract user id(s) from data
      const userIdArr = userMatchData.map((loan) => loan.userId);
      // Get KYC Data based on array of user id(s)
      let KYCData = await this.funKYCData(userIdArr);
      if (!KYCData || KYCData == k500Error) return k500Error;

      // Prepare db response suitable for processing this API response
      const response = userMatchData.map((data) => {
        const { fullName, phone, ...restLoanData } = data;
        const { userId, ...restKYCData } = KYCData.find(
          (el) => el.userId == data?.userId,
        );
        const filteredEMIData = EMIData.filter(
          (el) => el.loanId == data?.id,
        ).map(({ loanId, ...rest }) => rest);
        return {
          ...restLoanData,
          registeredUsers: {
            id: data?.userId,
            fullName: fullName,
            phone: phone,
            kycData: restKYCData,
          },
          emiData: filteredEMIData,
        };
      });
      return response;
    } catch (error) {
      return k500Error;
    }
  }
  //#endregion

  //#region Get Aadhaar location exact or within 5km
  private async funLoanData(userIds) {
    userIds = [...new Set(userIds)];

    const attributes = ['id', 'userId', 'loanStatus', 'fullName', 'phone'];
    const options = {
      where: {
        userId: userIds,
        loanStatus: { [Op.in]: ['Active', 'Complete'] },
      },
      useMaster: false,
    };
    return await this.repoManager.getTableWhereData(
      loanTransaction,
      attributes,
      options,
    );
  }
  //#endregion
  //#region Get Aadhaar location exact or within 5km
  private async funEMIData(loanId) {
    const attributes = [
      'loanId',
      'emi_date',
      'payment_due_status',
      'penalty_days',
      'penalty_update_date',
    ];
    const options = { where: { loanId }, useMaster: false };
    return await this.repoManager.getTableWhereData(
      EmiEntity,
      attributes,
      options,
    );
  }
  //#endregion

  //#region Get Aadhaar location exact or within 5km
  private async funKYCData(userIds) {
    const attributes = [
      'id',
      'userId',
      'aadhaarAddressResponse',
      'aadhaarAddress',
    ];
    const options = {
      where: { userId: userIds },
      order: [['id', 'DESC']],
      useMaster: false,
    };
    return await this.repoManager.getTableWhereData(
      KYCEntity,
      attributes,
      options,
    );
  }
  //#endregion

  getCalenderDataForEMI(emiDate) {
    try {
      const calenderData: any = {};
      if (emiDate) {
        const today = new Date();
        const currentMonth = today.getMonth();
        const date = new Date();
        date.setDate(emiDate);
        // EMI date falls this month
        if (date.getMonth() == currentMonth) {
          // Date is in past
          if (today.getDate() >= date.getDate()) {
            date.setMonth(date.getMonth() + 1);
          } else {
            const diffInDays = this.typeService.dateDifference(date, today);
            // EMI should start from minimum 13 days of gap from today
            if (diffInDays <= 12) {
              date.setMonth(date.getMonth() + 1);
            }
          }
        }
        calenderData.month = date.getMonth();
        calenderData.year = date.getFullYear();
      }
      return calenderData;
    } catch (error) {
      return {};
    }
  }

  async sendDisbursementLimitRaisedWhatsAppMsg() {
    try {
      const att = ['userId'];
      const masterInclude = {
        model: MasterEntity,
        attributes: ['loanId'],
        where: {
          'status.loan': 2,
        },
      };
      const options = {
        where: {
          [Op.and]: [
            { loanStatus: { [Op.ne]: 'Active' } },
            { userReasonDecline: 'Need more loan amount' },
          ],
        },
        group: ['userId'],
      };
      const users = await this.loanRepo.getTableWhereData(att, options);
      const userIdsArray = users.map((user) => user.userId);
      const dataAtt = [
        'id',
        'fullName',
        'phone',
        'email',
        'appType',
        'hashPhone',
      ];
      const userOptions = gIsPROD
        ? {
            where: { id: { [Op.in]: userIdsArray } },
            include: [masterInclude],
          }
        : {
            where: { id: { [Op.in]: userIdsArray } },
            include: [masterInclude],
            limit: 2,
          };
      const userDetails = await this.userRepo.getTableWhereDataWithCounts(
        dataAtt,
        userOptions,
      );

      const hashPhones = userDetails.rows.map((item) => item?.hashPhone);
      const nonWhatsAppHashPhone =
        await this.whatsAppService.getNonWhatsAppUsers(hashPhones);

      for (const user of userDetails.rows) {
        if (nonWhatsAppHashPhone?.includes(user?.hashPhone)) continue;
        const number = this.cryptService.decryptPhone(user?.phone);
        const whatsappOptions = {
          number,
          userId: user?.id,
          customerName: user?.fullName,
          title: 'disbursement limit upgraded',
          email: user?.email,
          amount: '1 Lakh',
          loanId: user?.masterData?.loanId,
          appType: user?.appType,
        };
        this.whatsAppService.sendWhatsAppMessageMicroService(whatsappOptions);
      }

      return { numberOfUsers: userDetails.count };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // calculation of loan repayment for move user assign to collection
  async calcuLoanRepayment(loanId, needData = false) {
    const tranInclude = {
      model: TransactionEntity,
      attributes: [
        'id',
        'loanId',
        'emiId',
        'principalAmount',
        'interestAmount',
        'transactionId',
        'type',
        'status',
        'paidAmount',
      ],

      where: { status: kCompleted },
      required: false,
    };
    const options = {
      where: { loanId, payment_status: '0', payment_due_status: '1' },
      include: [tranInclude],
    };
    const emiList = await this.emiRepo.getTableWhereData(
      [
        'id',
        'principalCovered',
        'paid_principal',
        'totalPenalty',
        'interestCalculate',
        'pay_type',
      ],
      options,
    );

    if (emiList == k500Error) throw new Error();
    if (!emiList.length) return false;
    let totalPaidAmount = 0;
    let totalEmiAmount = 0;
    for (let i = 0; i < emiList.length; i++) {
      try {
        const ele = emiList[i];
        let transactionData = ele?.transactionData;
        transactionData = await this.filterTransActionData(transactionData);
        transactionData.forEach((el) => {
          totalPaidAmount +=
            (el?.principalAmount ?? 0) + (el?.interestAmount ?? 0);
        });
        totalEmiAmount +=
          (ele?.principalCovered ?? 0) + (ele?.interestCalculate ?? 0);
      } catch (error) {}
    }
    let paidPercantage = (totalPaidAmount / totalEmiAmount) * 100;
    if (needData) return { paidPercantage, totalPaidAmount, totalEmiAmount };
    //if principle and interest total remanining <5000 then case assing to collection
    let remAmount = this.typeService.manageAmount(
      totalEmiAmount - totalPaidAmount,
    );
    if (remAmount < BELOW_OUTSTANDING_AMOUNT) return true;
    else return false;
  }
  async filterTransActionData(transData) {
    try {
      const refundData = transData.find(
        (el) => el?.type == 'REFUND' && el?.status == 'COMPLETED',
      );
      if (refundData) {
        transData.sort((a, b) => b.id - a.id);
        let extraPayment;
        for (let index = 0; index < transData.length; index++) {
          try {
            const element = transData[index];
            if (
              element.emiId &&
              element.emiId == refundData.emiId &&
              Math.abs(element.paidAmount + refundData?.paidAmount) < 10 &&
              element.type !== 'REFUND' &&
              element.status == 'COMPLETED'
            ) {
              extraPayment = element;
              break;
            }
          } catch (error) {}
        }

        transData = transData.filter(
          (el) =>
            el?.type !== 'REFUND' &&
            extraPayment?.transactionId !== el?.transactionId,
        );
      }
      transData.sort((a, b) => a.id - b.id);
      return transData;
    } catch (error) {}
  }

  async updateActivityTime(adminId) {
    let adminData = await this.redisService.get('ADMIN_LIST');
    adminData = await JSON.parse(adminData);
    if (adminId in adminData) {
      adminData[adminId].lastActivityTime = new Date();
    }
    const result = await this.redisService.set(
      'ADMIN_LIST',
      JSON.stringify(adminData),
    );
    return result;
  }

  async adminAutoLogout() {
    try {
      let adminData = await this.redisService.get('ADMIN_LIST');
      adminData = await JSON.parse(adminData);
      const adminIds = [];
      for (const key in adminData) {
        if (adminData[key].isLogin == 1) {
          const timeDiff = await this.typeService.dateDifference(
            new Date(adminData[key].lastActivityTime),
            new Date(),
            'Minutes',
          );
          if (timeDiff >= LOGOUTTIME) {
            adminData[key].isLogin = 0;
            adminData[key].lastActivityTime = null;
            adminIds.push(key);
          }
        }
      }

      ///update admin data
      const update = {
        jwtDetails: null,
        isLogin: 0,
      };
      const admins = await this.adminRepo.updateRowData(update, adminIds);
      if (!admins || admins === k500Error) return kInternalError;

      await this.redisService.set('ADMIN_LIST', JSON.stringify(adminData));

      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // Checks user's bankcode and ifsc code combination
  checkBankCode(bankCode, ifscDetails) {
    try {
      let targetBankCode = ifscDetails.BANKCODE ?? ifscDetails.bankCode;
      if (targetBankCode == 'GSCB') {
        let ifsc: string = ifscDetails.IFSC ?? ifscDetails.ifsc;
        if (ifsc.startsWith('GSCB0BKD')) {
          targetBankCode = ifsc?.toUpperCase()?.slice(0, -3);
        }
      }

      let receivedCode = '';
      switch (targetBankCode) {
        // DEUTSCHE BANK
        case 'DEUT':
          receivedCode = 'DEUTSCHE_BANK';
          break;

        // AU small finance bank
        case 'AUBL':
          receivedCode = 'AU_SMALL_FINANCE_BANK';
          break;

        case 'FINF':
          receivedCode = 'AU_SMALL_FINANCE_BANK';
          break;

        // DBS bank
        case 'DBSS':
          receivedCode = 'DBS';
          break;

        // AXIS Bank
        case 'UTIB':
          receivedCode = 'AXIS';
          break;

        // Bank of baroda bank
        case 'BARB':
          receivedCode = 'BANK_OF_BARODA';
          break;

        // Canara bank
        case 'CNRB':
          receivedCode = 'CANARA';
          break;

        case 'CBIN':
          receivedCode = 'CENTRAL_BANK';
          break;

        // City union bank
        case 'CIUB':
          receivedCode = 'CITY_UNION';
          break;

        // Federal bank
        case 'FDRL':
          receivedCode = 'FEDERAL';
          break;

        // ICICI Bank
        case 'ICIC':
          receivedCode = 'ICICI';
          break;

        // Indian overseas bank
        case 'IOBA':
          receivedCode = 'INDIAN_OVERSEAS';
          break;

        // IndusInd Bank
        case 'INDB':
          receivedCode = 'INDUSIND';
          break;

        // IDBI Bank
        case 'IBKL':
          receivedCode = 'IDBI';
          break;

        // IDFC Bank
        case 'IDFB':
          receivedCode = 'IDFC';
          break;

        // HDFC Bank
        case 'HDFC':
          receivedCode = 'HDFC';
          break;

        // Punjab national bank
        case 'PUNB':
          receivedCode = 'PNB';
          break;

        // State bank of india
        case 'SBIN':
          receivedCode = 'SBI';
          break;

        // Union bank of india
        case 'UBIN':
          receivedCode = 'UNION_BANK';
          break;

        // YES Bank
        case 'YESB':
          receivedCode = 'YES';
          break;

        // KOTAK Bank
        case 'KKBK':
          receivedCode = 'KOTAK';
          break;

        // KARNATAKA Bank
        case 'KARB':
          receivedCode = 'KARNATAKA';
          break;

        // Bank of India
        case 'BKID':
          receivedCode = 'BOI';
          break;

        // Indian Bank
        case 'IDIB':
          receivedCode = 'INDIAN_BANK';
          break;

        // Andhra Pragathi Grameena Bank
        case 'APGB':
          receivedCode = 'ANDHRA_PRAGATHI_GRAMEENA';
          break;

        // NSDL Payments Bank
        case 'NSPB':
          receivedCode = 'NSDL';
          break;

        // HSBC Bank
        case 'HSBC':
          receivedCode = 'HSBC';
          break;

        case 'SCBL':
          receivedCode = 'STANDARD_CHARTERED';
          break;

        case 'MAHB':
          receivedCode = 'BANK_OF_MAHARASHTRA';
          break;

        case 'KVBL':
          receivedCode = 'KARUR_VYASA';
          break;

        case 'PSIB':
          receivedCode = 'PUNJAB_SIND';
          break;

        case 'SIBL':
          receivedCode = 'SOUTH_INDIAN';
          break;

        case 'UCBA':
          receivedCode = 'UCO_BANK';
          break;

        case 'BDBL':
          receivedCode = 'BANDHAN';
          break;

        case 'SURY':
          receivedCode = 'SURYODAY_SMALL_FINANCE';
          break;

        case 'ESMF':
          receivedCode = 'ESAF_SMALL_FINANCE_BANK';
          break;

        case 'TMBL':
          receivedCode = 'TAMILNAD_MERCANTILE';
          break;

        case 'CSBK':
          receivedCode = 'CSB';
          break;

        case 'DLXB':
          receivedCode = 'DHANLAXMI';
          break;

        case 'JSFB':
          receivedCode = 'JANA_SMALL_FINANCE_BANK';
          break;

        case 'USFB':
          receivedCode = 'UJJIVAN_SMALL_FINANCE_BANK';
          break;

        case 'UTKS':
          receivedCode = 'UTKARSH_SMALL_FINANCE';
          break;

        case 'CLBL':
          receivedCode = 'CAPITAL_SMALL_FINANCE';
          break;

        case 'APGV':
          receivedCode = 'AP_GRAMEENA_VIKAS';
          break;

        case 'UTGX':
          receivedCode = 'UTTARAKHAND_GRAMIN';
          break;

        case 'DGBX':
          receivedCode = 'TELANGANA_GRAMEENA';
          break;

        case 'CRGB':
          receivedCode = 'CHHATTISGARH_RAJYA_GRAMIN';
          break;

        case 'MAHG':
          receivedCode = 'MAHARASHTRA_GRAMIN';
          break;

        case 'GSCB':
          receivedCode = 'AHMEDABAD_DISTRICT_CO_OP';
          break;

        case 'MERX':
          receivedCode = 'MEGHALAYA_RURAL';
          break;

        case 'TGCX':
          receivedCode = 'TAMLUK_GHATAL_CENTRAL_CO_OP_BK';
          break;

        case 'MZRX':
          receivedCode = 'MIZORAM_RURAL';
          break;

        case 'RMGB':
          receivedCode = 'RAJASTHAN_MARUDHARA_GRAMIN';
          break;

        case 'KVGB':
          receivedCode = 'KARNATAKA_VIKAS_GRAMEENA';
          break;

        case 'GSCB':
          receivedCode = 'GUJARAT_STATE_CO_OP';
          break;

        case 'GSCB0BKD':
          receivedCode = 'BANASKANTHA_DCC';
          break;

        case 'SGBA':
          receivedCode = 'SAURASHTRA_GRAMIN';
          break;

        case 'PANX':
          receivedCode = 'PDC_BANK';
          break;

        case 'COSB':
          receivedCode = 'COSMOS';
          break;

        case 'SRCB':
          receivedCode = 'SARASWAT_BANK';
          break;

        case 'VARA':
          receivedCode = 'VARACHHA_CO_OPERATIVE';
          break;

        case 'RATN':
          receivedCode = 'RBL';
          break;

        default:
          break;
      }

      if (bankCode == null) return receivedCode;
      if (receivedCode != bankCode)
        return k422ErrorMessage(
          `Kindly provide valid ifsc code for ${bankCode}`,
        );
      return {};
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async getRiskCategoryByScoreData(reqData) {
    try {
      const masterData = reqData.masterData;
      const userAge = this.typeService.getAgeFromAadhar(reqData.aadhaarDOB);
      const salaryInfo = await this.getSalaryRange(
        masterData.otherInfo.salaryInfo,
      );
      const ageRange = await this.getAgeRange(userAge);
      const vehicleInfo = Array.isArray(masterData?.otherInfo?.vehicleInfo)
        ? masterData.otherInfo.vehicleInfo
            .map((item) => item.replace(/\D+/g, '').trim() + '-wheeler')
            .join(', ')
        : masterData.otherInfo.vehicleInfo.replace(/\D+/g, '').trim() +
          ' Wheeler';

      const maritalStatus = masterData.otherInfo.maritalInfo;

      const liveStateInfo = reqData?.liveState
        ? await this.typeService.capitalizeFirstLetter(reqData.liveState)
        : 0;

      const aadhaarStateInfo = await this.typeService.capitalizeFirstLetter(
        reqData.aadhaarState,
      );
      const data = fs.readFileSync('scores.json', 'utf8');
      const scoreData = JSON.parse(data);
      const liveStateScore = scoreData.liveState[liveStateInfo] ?? 0;
      const aadhaarStateScore = scoreData.aadhaarState[aadhaarStateInfo] ?? 0;
      const similarStateScoreInfo =
        liveStateScore === aadhaarStateScore ? 'Yes' : 'No';
      const vehicleScore = scoreData.vehicleInfo[vehicleInfo] ?? 0;
      const employmentScore =
        scoreData.employmentInfo[masterData.otherInfo.employmentInfo] ?? 0;
      const educationScore =
        scoreData.educationInfo[masterData.otherInfo.educationInfo] ?? 0;
      const residentialScore =
        scoreData.residentialInfo[masterData.otherInfo.residentialInfo] ?? 0;
      const salaryScore = scoreData.salaryRange[salaryInfo] ?? 0;
      const ageScore =
        maritalStatus === 'Single'
          ? scoreData.singleAge[ageRange] ?? 0
          : scoreData.marriedAge[ageRange] ?? 0;
      const similarStateScore =
        scoreData.similarState[similarStateScoreInfo] ?? 0;

      const scorePoint =
        liveStateScore +
        aadhaarStateScore +
        vehicleScore +
        employmentScore +
        educationScore +
        residentialScore +
        salaryScore +
        ageScore +
        similarStateScore;

      await this.repoManager.updateRowData(
        registeredUsers,
        { categoryScore: scorePoint },
        reqData.userId,
      );

      return {};
    } catch (error) {
      console.log({ error }, reqData);
    }
  }

  private async getSalaryRange(salary) {
    const salaryRanges = {
      '<20k': [0, 20000],
      '20k-30k': [20000, 30001],
      '31k-40k': [30001, 40001],
      '41k-50k': [40001, 50001],
      '51k-60k': [50001, 60001],
      '61k-70k': [60001, 70001],
      '71k-80k': [70001, 80001],
      '81k-1L': [80001, 100001],
      '>1L': [100001, Infinity],
    };
    let salaryRange = '';
    for (const range in salaryRanges) {
      const [min, max] = salaryRanges[range];
      if (salary >= min && salary < max) {
        salaryRange = range;
      }
    }
    return salaryRange;
  }

  private async getAgeRange(userAge) {
    const ageRanges = {
      '21-25': [21, 26],
      '26-30': [26, 31],
      '31-35': [31, 36],
      '36-40': [36, 41],
      '>40': [41, Infinity],
    };
    let ageRange = '';
    for (const range in ageRanges) {
      const [min, max] = ageRanges[range];
      if (userAge >= min && userAge < max) {
        ageRange = range;
      }
    }
    return ageRange;
  }

  async getEmailTemplatePath(
    template,
    appType?: number,
    userId?: string,
    loanId?: number,
  ) {
    try {
      if (appType != null) {
        if (template.includes('##templateDesignNumber##')) {
          template = template.replace(/##templateDesignNumber##/g, appType);
        }
        return template;
      } else {
        let appType;
        if (templateDesign == '1') {
          if (loanId) {
            const loanAtr = ['appType'];
            const loanOptions = {
              where: {
                id: loanId,
              },
            };
            const loanData = await this.loanRepo.getRowWhereData(
              loanAtr,
              loanOptions,
            );
            if (loanData === k500Error) return kInternalError;
            appType = loanData?.appType;
          } else {
            const userAtr = ['appType'];
            const userOptions = {
              where: {
                id: userId,
              },
            };
            const userData = await this.userRepo.getRowWhereData(
              userAtr,
              userOptions,
            );
            if (userData === k500Error) return kInternalError;
            appType = userData?.appType;
          }
        }
        if (templateDesign == '0') appType = 0;
        if (template.includes('##appType##')) {
          template = template.replace(/##appType##/g, appType);
        }
        return template;
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // Get UserId From Phone
  async funGetUserIdFromPhone(reqData) {
    try {
      const phone = reqData?.phone;
      const phonenumber = [];
      for (let i = 0; i < phone.length; i++) {
        try {
          const ele = phone[i].toString();
          const encPhone = this.cryptService.getMD5Hash(ele);
          phonenumber.push(encPhone);
        } catch (error) {}
      }
      const hashPhoneData = await this.repoManager.getTableWhereData(
        HashPhoneEntity,
        ['userId'],
        { where: { hashPhone: phonenumber } },
      );
      if (hashPhoneData === k500Error) throw new Error();

      const userIds = [...new Set(hashPhoneData.map((ele) => ele?.userId))];
      return userIds;
    } catch (error) {}
  }

  async matchExactAadhaarAddress(userId) {
    const kycData = await this.KYCRepo.getRowWhereData(
      ['aadhaarLatLong', 'aadhaarAddress', 'maskedAadhaar'],
      {
        where: { userId },
      },
    );
    const aadhaarPoints = kycData?.aadhaarLatLong ?? '-';
    const aadhaarLocation = kycData?.aadhaarAddress ?? '-';
    if (
      !kycData ||
      kycData == k500Error ||
      aadhaarPoints == '-' ||
      aadhaarLocation == '-'
    )
      return { status: false, enum: 'AADHAR_LAT_LONG_NOT_FOUND' };
    const latLong = JSON.parse(aadhaarPoints);
    const address = JSON.parse(aadhaarLocation);
    const mergedAddress = Object.values(address).filter(Boolean).join(' ');

    ///old query
    // const options = {
    //   where: {
    //     [Op.and]: [
    //       Sequelize.literal(`ACOS(
    //         SIN(RADIANS(${latLong.lat})) * SIN(RADIANS("ActiveLoanAddressesEntity"."aadhaarLatLong"[0])) +
    //         COS(RADIANS(${latLong.lat})) * COS(RADIANS("ActiveLoanAddressesEntity"."aadhaarLatLong"[0])) *
    //         COS(RADIANS("ActiveLoanAddressesEntity"."aadhaarLatLong"[1]) - RADIANS(${latLong.lng}))
    //     ) * 3958.8 <=${AADHARE_LAT_LONG_RADIUS}`),
    //     ],
    //     isActive: true,
    //     userId: { [Op.ne]: userId },
    //   },
    // };
    const options = {
      where: {
        [Op.and]: [
          Sequelize.literal(` ACOS(
  LEAST(GREATEST(
      SIN(RADIANS(${latLong.lat})) * SIN(RADIANS("aadhaarLatLong"[0])) +
      COS(RADIANS(${latLong.lat})) * COS(RADIANS("aadhaarLatLong"[0])) *
      COS(RADIANS("aadhaarLatLong"[1]) - RADIANS(${latLong.lng}))
  , -1), 1)
) * 3958.8<=${AADHARE_LAT_LONG_RADIUS}`),
        ],
        isActive: true,
        userId: { [Op.ne]: userId },
      },
    };
    const attributes = ['userId', 'aadhaarAddress', 'aadhaarLatLong'];
    const data = await this.repoManager.getTableWhereData(
      ActiveLoanAddressesEntity,
      attributes,
      options,
    );
    if (!data || data == k500Error) throw new Error();

    const exactMatchUsers = [];
    for (let userData of data) {
      const userAddress = userData.aadhaarAddress;
      const percentage = this.validation.getTextProbability(
        userAddress,
        mergedAddress,
      );
      if (percentage == 100) {
        exactMatchUsers.push({
          userId: userData.userId,
          matchedPercentage: percentage,
        });
      }
    }
    return {
      exactMatchUsers,
    };
  }

  async getNecessaryList() {
    try {
      // Get sectors
      const sectorAtt = ['id', 'sectorName'];
      const sectorOps = { where: { sectorStatusVerified: '1' } };
      const sectors: any = this.sectorRepo.getTableWhereData(
        sectorAtt,
        sectorOps,
      );
      if (sectors === k500Error) return kInternalError;

      // Get designations
      const designationsAtt = ['id', 'designationName'];
      const designationsOps = { where: { designationStatusVerified: '1' } };
      const designations = await this.designationRepo.getTableWhereData(
        designationsAtt,
        designationsOps,
      );
      if (designations === k500Error) return kInternalError;

      const topCompanies = await this.getTop10VerifiedCompanies();
      if (topCompanies.message) return topCompanies;

      return { designations, sectors, topCompanies };
    } catch (error) {
      return kInternalError;
    }
  }

  private async getTop10VerifiedCompanies() {
    try {
      const opt = {
        where: { companyVerification: { [Op.or]: ['1', '3'] } },
        group: [Sequelize.fn('lower', Sequelize.col('companyName'))],
        order: [['count', 'DESC']],
        limit: 10,
      };
      const attr: any = [
        [Sequelize.fn('lower', Sequelize.col('companyName')), 'companyName'],
        [Sequelize.fn('COUNT', Sequelize.col('companyName')), 'count'],
      ];
      const empData = await this.empRepo.getTableWhereData(attr, opt);
      if (empData === k500Error) return kInternalError;
      const companyList = empData.map(
        (el) =>
          el?.companyName.charAt(0).toUpperCase() + el?.companyName.slice(1),
      );
      return companyList.sort();
    } catch (error) {
      return kInternalError;
    }
  }
  async getActivePaymentServices() {
    let redisData = await this.redisService.get('PAYMENT_MODE');
    if (redisData) return JSON.parse(redisData);
    let activeServices = [];

    if (redisData == null) {
      // Getting IDs of ThirdParty Services
      let activeIds = await this.thirdPartyServiceRepo.getRowWhereData(
        ['activeProviderIds'],
        { where: { name: 'PAYMENT_MODE' } },
      );
      if (activeIds == k500Error) return kInternalError;

      // Getting Name of Third Party Services
      let activeProviders =
        await this.thirdPartyServiceProvider.getTableWhereData(['name'], {
          where: { id: activeIds.activeProviderIds },
        });
      if (activeProviders == k500Error) return kInternalError;

      // Mapping Only Name of Services
      activeServices = activeProviders.map((provider) => provider.name);
      await this.redisService.set(
        'PAYMENT_MODE',
        JSON.stringify(activeServices),
      );

      return activeServices;
    }
  }

  exceptionMiplLogic(data) {
    const { cibilScore, accounts, plScore, overdueBalance, inquiryPast30Days } =
      data;
    const isLoanAccount = [];
    const overduePast3Months = accounts?.filter((el) => {
      const dateReported =
        this.typeService.strDateToDate(el?.dateReported) ?? '-';

      const threeMonthsAgo = new Date();
      threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
      const isWithinLast3Months =
        new Date(dateReported).getTime() > threeMonthsAgo.getTime();

      if (
        el.accountType !== '7' &&
        el.accountType !== '07' &&
        ![null, undefined].includes(el.lastPaymentDate)
      ) {
        isLoanAccount.push(el.accountType);
      }

      if (isWithinLast3Months && el.lastDelayDays > 0) {
        return el;
      }
    });

    if (
      ((cibilScore >= 0 && cibilScore < 700) ||
        (plScore >= 0 && plScore < 700)) &&
      inquiryPast30Days <= 2 &&
      overdueBalance == 0 &&
      overduePast3Months.length == 0 &&
      isLoanAccount.length > 0
    ) {
      return true;
    } else return false;
  }

  async getCompanyActivityRedis(companyName) {
    const data = await this.redisService.get(
      'COMPANY_ACTIVITY_' + this.cryptService.getMD5Hash(companyName),
    );
    return JSON.parse(data);
  }
  //#endregion

  //#region set company activity
  async setCompanyActivityRedis(companyName, data) {
    await this.redisService.set(
      'COMPANY_ACTIVITY_' + this.cryptService.getMD5Hash(companyName),
      JSON.stringify(data),
      NUMBERS.FIVE_HOURS_IN_SECONDS,
    );
  }
  async getStaticConfigData(type?: string, isRefresh = false) {
    const key = 'STATIC_CONFIG_DATA';

    if (!isRefresh) {
      const cachedData = await this.redisService.get(key);
      if (cachedData) {
        const parsedData = JSON.parse(cachedData);
        return type
          ? parsedData.find((item: any) => item.type === type)
          : parsedData;
      }
    }

    const staticConfig = await this.repoManager.getTableWhereData(
      StaticConfigEntity,
      ['data', 'type'],
      {},
    );

    if (staticConfig === k500Error) throw new Error();

    await this.redisService.set(
      key,
      JSON.stringify(staticConfig),
      NUMBERS.SEVEN_DAYS_IN_SECONDS,
    );

    return type
      ? staticConfig.find((item: any) => item.type === type)
      : staticConfig;
  }

  getAppNotificationIcons(appType, key) {
    return (
      kAppNotificationIcon[key]?.[appType] ||
      EnvConfig.gCloudAssets.notificationIcon[appType]
    );
  }

  //#region common shared function for get loan purpose
  async fetchLoanPurpose(id?: number, isRefresh = false) {
    let purpose: any;
    if (!isRefresh) {
      purpose = await this.redisService.get('LOAN_PURPOSE_DETAILS');
    }
    if (!isRefresh && purpose) {
      purpose = JSON.parse(purpose);
      return id ? purpose.find((item: any) => item.id === id) : purpose;
    } else {
      purpose = await this.purposeRepo.getTableWhereData(
        ['purposeName', 'id'],
        {},
      );
      if (purpose == k500Error) throw new Error();
      await this.redisService.set(
        'LOAN_PURPOSE_DETAILS',
        JSON.stringify(purpose),
        NUMBERS.SEVEN_DAYS_IN_SECONDS,
      );
      return id ? purpose.find((item: any) => item.id === id) : purpose;
    }
  }
  //#endregion

  //#region common shared function for get department data
  async getDepartment(id?: number, isRefresh = false) {
    let department: any;
    if (!isRefresh) {
      department = await this.redisService.get('DEPARTMENT_DATA');
    }
    if (!isRefresh && department) {
      department = JSON.parse(department);
      return id ? department.find((item: any) => item.id === id) : department;
    } else {
      department = await this.repoManager.getTableWhereData(
        Department,
        ['department', 'id'],
        {},
      );
      if (department == k500Error) throw new Error();
      await this.redisService.set(
        'DEPARTMENT_DATA',
        JSON.stringify(department),
        NUMBERS.SEVEN_DAYS_IN_SECONDS,
      );
      return id ? department.find((item: any) => item.id === id) : department;
    }
  }

  //#region Stores employee's PAN data who are not allowed to move further for loan process (step: e-Sign).
  async employeeEligibilityList() {
    const attributes = ['hash_pan'];
    const options = {
      where: {
        is_eligible: false,
      },
    };
    const response = await this.repoManager.getTableWhereData(
      AdminEligibilityEntity,
      attributes,
      options,
    );
    if (response == k500Error) throw new Error();
    const PAN_LIST = response.map((item) => item.hash_pan);
    await this.redisService.set(
      'EMPLOYEE_ELIGIBILITY_ESIGN',
      JSON.stringify(PAN_LIST),
      NUMBERS.THIRTY_DAYS_IN_SECONDS,
    );
  }

  //#region common shared function for get loan purpose
  async fetchDesignation(isRefresh = false) {
    let designation: any;
    if (!isRefresh) {
      designation = await this.redisService.get('DESIGNATION_DETAILS');
    }
    if (!isRefresh && designation) return JSON.parse(designation);
    else {
      designation = await this.repoManager.getTableWhereData(
        employmentDesignation,
        ['designationName', 'id'],
        {},
      );
      if (designation == k500Error) throw new Error();
      await this.redisService.set(
        'DESIGNATION_DETAILS',
        JSON.stringify(designation),
        NUMBERS.SEVEN_DAYS_IN_SECONDS,
      );
      return designation;
    }
  }
  //#endregion

  //#region common shared function for get loan purpose
  async fetchHypothecationLenders(isRefresh = false) {
    let lenders: any;
    if (!isRefresh) {
      lenders = await this.redisService.get('HYPOTHICATION_LENDERS');
    }
    if (!isRefresh && lenders) return JSON.parse(lenders);
    else {
      lenders = await this.repoManager.getTableWhereData(
        HypothecationEntity,
        [
          'id',
          'lenderName',
          'loanAmount',
          'hypothecatedAmount',
          'minLoanAmount',
          'outStandingAmount',
          'maxLoanAmount',
          'hypothecatedpercentage',
          'dayOfEMI',
          'loanTenure',
          'approvedPrincipalAmount',
          'emiAmount',
          'repaymentPercentage',
          'max_dpd',
          'interestAmount',
          'adminId',
          'repaymentSchedule',
          'lenderDoc',
          'createdAt',
          'updatedAt',
        ],
        {},
      );
      if (lenders == k500Error) throw new Error();
      await this.redisService.set(
        'HYPOTHICATION_LENDERS',
        JSON.stringify(lenders),
        NUMBERS.SEVEN_DAYS_IN_SECONDS,
      );
      return lenders;
    }
  }
  //#endregion

  async getSelfieData(reqData) {
    const userId = reqData.userId;
    if (!userId) return kParamMissing('userId');
    const isRefresh = reqData?.isRefresh;
    //make a key for redis
    const key = `SELFIE_DATA_${userId}`;
    // get data from redis
    let selfieData: any = await this.redisService.get(key);
    if (selfieData && !isRefresh) {
      selfieData = JSON.parse(selfieData);
      if ([1, 3].includes(+selfieData?.status)) {
        return selfieData;
      }
    }

    // get data from db
    selfieData = await this.selfieRepo.getRowWhereData(
      [
        'id',
        'userId',
        'image',
        'tempImage',
        'adminId',
        'status',
        'verifiedDate',
        'rejectReason',
        'response',
        'extraData',
        'details',
        'createdAt',
      ],
      {
        where: { userId },
        order: [['id', 'DESC']],
      },
    );
    if (selfieData == k500Error) return kInternalError;
    await this.redisService.incrSortedSetMember(
      'SELFIE_QUERY_COUNT',
      userId + '_' + reqData?.callFnFrom,
    );
    if (!selfieData) return {};
    await this.redisService.set(
      key,
      JSON.stringify(selfieData),
      NUMBERS.SEVEN_DAYS_IN_SECONDS,
    );
    return selfieData;
  }

  async getKycData(reqData) {
    const userId = reqData.userId;
    if (!userId) return kParamMissing('userId');
    const isRefresh = reqData?.isRefresh;
    //make a key for redis
    const key = `KYC_DATA_${userId}`;
    // get data from redis
    let kycData: any = await this.redisService.get(key);
    if (kycData && !isRefresh) {
      kycData = JSON.parse(kycData);
      if ([1, 3].includes(+kycData?.aadhaarStatus)) {
        return kycData;
      }
    }

    kycData = await this.KYCRepo.getRowWhereData(
      [
        'id',
        'maskedAadhaar',
        'maskedPan',
        'panCardNumber',
        'aadhaarAddress',
        'aadhaarStatus',
        'aadhaarDOB',
        'panStatus',
        'updatedAt',
        'profileImage',
        'createdAt',
        'aadhaarNo',
        'aadhaarReferenceId',
        'aadhaarFront',
        'aadhaarBack',
        'pan',
        'aadhaarVerifiedAdmin',
        'panUploadedAdmin',
        'panVerifiedAdmin',
        'attemptData',
        'maskedPan',
        'aadhaarState',
        'aadhaarResponse',
        'aadhaarAddressResponse',
      ],
      {
        where: { userId },
        order: [['id', 'DESC']],
      },
    );
    if (kycData == k500Error) return kInternalError;
    await this.redisService.incrSortedSetMember(
      'KYC_QUERY_COUNT',
      userId + '_' + reqData?.callFnFrom,
    );
    if (!kycData) return {};
    await this.redisService.set(
      key,
      JSON.stringify(kycData),
      NUMBERS.SEVEN_DAYS_IN_SECONDS,
    );

    return kycData;
  }

  //#region common shared function for smsObject from smsId
  async getSMSFromId(id, isRefresh = false) {
    let sms: any;
    if (!isRefresh) {
      sms = await this.redisService.get('SMS_TEMPLATES_IDS');
    }
    if (!isRefresh && sms) {
      sms = JSON.parse(sms);
      return id
        ? sms.find(
            (item: any) => item.templateId == id || item.lspTemplateId == id,
          )
        : sms;
    } else {
      sms = await this.repoManager.getTableWhereData(
        TemplateEntity,
        ['content', 'id', 'templateId', 'lspTemplateId'],
        {
          where: {
            isActive: true,
          },
        },
      );
      if (sms == k500Error) throw new Error();
      await this.redisService.set(
        'SMS_TEMPLATES_IDS',
        JSON.stringify(sms),
        NUMBERS.SEVEN_DAYS_IN_SECONDS,
      );
      return id
        ? sms.find(
            (item: any) => item.templateId == id || item.lspTemplateId == id,
          )
        : sms;
    }
  }
  //#endregion

  //#region Manage user data for admin chat by providing user id
  async funManageChatExtraData(id, isRefresh = false) {
    if (!id) return;
    const key = `CHAT_EXTRA_DATA_${id}`;
    let chatExtraData;
    if (!isRefresh) chatExtraData = await this.redisService.get(key);
    if (chatExtraData) return JSON.parse(chatExtraData);
    const data = await this.funGetUserData(id);
    await this.redisService.set(
      key,
      JSON.stringify(data),
      NUMBERS.THIRTY_DAYS_IN_SECONDS,
    );
    return data;
  }
  //#endregion

  //#region Get user data for admin chat by providing user id
  private async funGetUserData(userId) {
    /// Fetch user data
    const userData = await this.repoManager.getRowWhereData(
      registeredUsers,
      [
        'id',
        'isBlacklist',
        'stage',
        'masterId',
        'kycId',
        'lastLoanId',
        'completedLoans',
        'loanStatus',
      ],
      { where: { id: userId } },
    );

    /// Check for repeat user using completed loans count
    const repeatUser = +userData?.completedLoans > 0;

    /// Check if user is blocked
    const isBlockedUsers = userData?.isBlacklist == 1 ? true : false;

    /// If user is not blocked, then check for cool off
    let isCoolOffUser = false;
    if (!isBlockedUsers) {
      const masterData = await this.repoManager.getRowWhereData(
        MasterEntity,
        ['id', 'coolOffData'],
        { where: { id: userData?.masterId } },
      );
      let coolOffEndsOn = masterData?.coolOffData?.coolOffEndsOn;
      if (masterData?.id && coolOffEndsOn) {
        const todayDate = this.typeService.getGlobalDate(new Date());
        coolOffEndsOn = this.typeService.getGlobalDate(coolOffEndsOn);
        isCoolOffUser = coolOffEndsOn > todayDate;
      }
    }

    /// Check if user is on-time or defaulter
    let loanStep = userData?.stage ?? null;
    const loanStatus = userData?.loanStatus ?? null;

    /// Repayment = 17, Defaulter = 18, On-time = 1, Defaulter = 3
    if ([17, 18].includes(loanStep) && loanStatus != 2) {
      if (loanStatus == 1) loanStep = 17;
      if (loanStatus == 3) loanStep = 18;
    }

    return {
      repeatUser,
      isBlockedUsers,
      isCoolOffUser,
      loanStep,
      loanId: userData?.lastLoanId ?? null, /// For search by loan id.
    };
  }
  //#endregion

  //#region Save MiscData to the MiscHistoryEntity....
  async formatMiscData(reqUrl: string, id?: string) {
    let actionMap: any;
    if (kMiscReqUrl?.includes(reqUrl)) {
      // Mapping for request types....
      actionMap = new Map([
        [
          'createuserpermission',
          {
            subType: kMiscSubType.permissionList,
            action: kMiscActionType.insert,
          },
        ],
        [
          'edituserpermission',
          {
            subType: kMiscSubType.permissionList,
            action: kMiscActionType.update,
            redisKey: kMiscRedisKeys.permissions,
          },
        ],
        [
          'addservice',
          {
            subType: kMiscSubType.serviceKey,
            action: kMiscActionType.insert,
          },
        ],
        [
          'updateservice',
          {
            subType: kMiscSubType.serviceKey,
            action: kMiscActionType.update,
            redisKey: kMiscRedisKeys.services,
          },
        ],
        [
          'addproviders',
          {
            subType: kMiscSubType.serviceProvider,
            action: kMiscActionType.insert,
          },
        ],
        [
          'updatenewallbanklist',
          {
            subType: kMiscSubType.bankService,
            action: kMiscActionType.update,
            redisKey: kMiscRedisKeys.banks,
          },
        ],
        [
          'updateconfigs',
          {
            subType: kMiscSubType.configEntity,
            action: kMiscActionType.update,
            redisKey: kMiscRedisKeys.configs,
          },
        ],
        [
          'checktransactionids',
          {
            subType: kMiscSubType.transactionVerification,
            action: kMiscActionType.insert,
          },
        ],
        [
          'insertbatchcibildatafile',
          {
            subType: kMiscSubType.batchData,
            action: kMiscActionType.insert,
          },
        ],
      ]);
    } else if (kSettingReqUrl?.includes(reqUrl)) {
      // Mapping for request types....
      actionMap = new Map([
        [
          'removeblacklistcompanies',
          {
            subType: kSettingSubType?.companyBlacklist,
            action: kMiscActionType.delete,
          },
        ],
        [
          'blacklistcompanies',
          {
            subType: kSettingSubType.companyBlacklist,
            action: kMiscActionType.insert,
          },
        ],
        [
          'addqualityparameter',
          {
            subType: kSettingSubType.qualityParameter,
            action: kMiscActionType.insert,
          },
        ],
      ]);
    }
    reqUrl = reqUrl.toLowerCase();
    // Identify matching request type....
    for (const [key, value] of actionMap) {
      if (reqUrl?.includes(key)) {
        let oldData = '';

        // Fetch old data if Redis key is defined....
        if (value?.redisKey) {
          let storedData = await this.redisService.get(value.redisKey);
          storedData =
            storedData && typeof storedData === 'string'
              ? JSON.parse(storedData)
              : [];

          // If data should be filtered by ID
          if (Array.isArray(storedData) && storedData?.length != 0 && id)
            oldData = storedData.find((item) => item.id == id) || '-';
        }
        return {
          subType: value.subType,
          action: value.action,
          oldData: oldData,
        };
      }
    }

    // If no match is found....
    return null;
  }

  parseJsonData(data) {
    if (typeof data === 'string' && data !== '') {
      try {
        return JSON.parse(data) ?? '-';
      } catch (error) {
        throw new Error();
      }
    }
    return '';
  }

  //#region Fetch Prediction Data by Loan IDs
  async fetchPredictionData(loanIds) {
    const attributes = [
      'id',
      'loanId',
      'automationDetails',
      'categorizationDetails',
      'ml_approval',
      'categorizationTag',
      'CFLScore',
    ];
    const options = {
      where: { loanId: loanIds },
      order: [['id', 'DESC']],
      required: false,
      useMaster: false,
    };

    const predictionData = await this.repoManager.getTableWhereData(
      PredictionEntity,
      attributes,
      options,
    );
    if (predictionData === k500Error)
      throw new Error(kErrorMsgs.INTERNAL_SERVER_ERROR);
    return predictionData;
  }
  //#endregion

  //#region
  formatSlackMesseageTable(slackData) {
    try {
      const { headers, callData } = slackData;
      const rows = callData.map((call) => [
        ...headers.map((header) => String(call[header])),
      ]);
      const columnWidths = headers.map((header, i) => {
        return Math.max(header.length, ...rows.map((row) => row[i].length));
      });
      const formatRow = (row) => {
        return row.map((cell, i) => cell.padEnd(columnWidths[i])).join(' | ');
      };
      const table = [
        formatRow(headers),
        '-'.repeat(columnWidths.reduce((a, b) => a + b + 3, -3)),
        ...rows.map(formatRow),
      ].join('\n');
      return `\`\`\`\n${table.trim()}\n\`\`\``;
    } catch (error) {
      console.log('error', error);
      return '';
    }
  }
  //#endregion

  //#region
  async getCRMReasons(isRefresh = false) {
    try {
      let crmReasons;
      if (!isRefresh) {
        crmReasons = await this.redisService.get('CRM_REASONS');
        if (crmReasons) return JSON.parse(crmReasons);
      }

      const data = await this.repoManager.getTableWhereData(
        CrmReasonEntity,
        ['id', 'reason'],
        {},
      );
      if (data === k500Error) return [];

      this.redisService.set(
        'CRM_REASONS',
        JSON.stringify(data),
        NUMBERS.SEVEN_DAYS_IN_SECONDS,
      );
      return data;
    } catch (error) {
      return [];
    }
  }
  //#endregion

  //#region
  async getCRMTitles(departmentId, isRefresh = false) {
    try {
      let crmTitles;
      if (!isRefresh) {
        crmTitles = await this.redisService.get('CRM_TITLES');

        if (crmTitles) {
          const data = JSON.parse(crmTitles);
          return data.filter((ele) => {
            return ele.departmentId == departmentId;
          });
        }
      }

      const data = await this.repoManager.getTableWhereData(
        crmTitle,
        [
          'id',
          'title',
          'isAmount',
          'isDate',
          'isReference',
          'isReason',
          'isSettlement',
          'departmentId',
        ],
        {},
      );
      if (data === k500Error) return [];

      await this.redisService.set(
        'CRM_TITLES',
        JSON.stringify(data),
        NUMBERS.SEVEN_DAYS_IN_SECONDS,
      );

      return data.filter((ele) => {
        return ele.departmentId == departmentId;
      });
    } catch (error) {
      return [];
    }
  }
  //#endregion

  //#region
  async getCRMStatus(isRefresh = false) {
    try {
      let crmStatusData;
      if (!isRefresh) {
        crmStatusData = await this.redisService.get('CRM_STATUS');
        if (crmStatusData) return JSON.parse(crmStatusData);
      }

      const data = await this.repoManager.getTableWhereData(
        crmStatus,
        ['id', 'status'],
        {},
      );

      if (data === k500Error) return [];

      await this.redisService.set(
        'CRM_STATUS',
        JSON.stringify(data),
        NUMBERS.SEVEN_DAYS_IN_SECONDS,
      );

      return data;
    } catch (error) {
      return [];
    }
  }
  //#endregion

  //#region
  async getAllAdminData(isRefresh = false) {
    try {
      if (!isRefresh && this.allAdminData?.length) return this.allAdminData;

      const data = await this.adminRepo.getTableWhereData(
        [
          'id',
          'fullName',
          'email',
          'phone',
          'companyPhone',
          'departmentId',
          'otherData',
          'isActive',
          'roleId',
        ],
        {},
      );

      if (!data || data == k500Error) this.allAdminData = [];
      else {
        for (let i = 0; i < data.length; i++) {
          data[i].email = await this.cryptService.decryptText(data[i].email);
          data[i].phone = await this.cryptService.decryptText(data[i].phone);
          data[i].companyPhone = await this.cryptService.decryptText(
            data[i].companyPhone,
          );
        }
      }
      this.allAdminData = data;
      return data;
    } catch (error) {
      return [];
    }
  }
  //#endregion
  async getRoleDetails(roleId, isRefresh = false) {
    try {
      if (!roleId) return;

      const key = REDIS_KEY.ADMIN_ROLES;

      let adminRoleData = [];

      const redisAdminRole = await this.redisService.get(key);

      if (redisAdminRole && !isRefresh) {
        adminRoleData = JSON.parse(redisAdminRole);
        if (!Array.isArray(adminRoleData)) return;
        return (adminRoleData = adminRoleData.find(
          (role) => role.id === roleId,
        ));
      }
      adminRoleData = await this.adminRoles.getTableWhereData(
        ['id', 'title', 'isActive', 'adminId'],
        {},
      );

      if (!Array.isArray(adminRoleData)) return;
      await this.redisService.set(key, JSON.stringify(adminRoleData));
      return adminRoleData.find((role) => role.id === roleId);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return;
    }
  }

  //#region get stage name from stage number
  getStageFromId(stage) {
    if (!stage) return;
    return Object.keys(UserStage).find((key) => UserStage[key] === stage);
  }
  //#endregion

  async findMaskRole(adminId) {
    const adminRoleId: any = (await this.getAdminData(adminId))?.roleId;
    const attSub = ['id', 'access_list', 'title'];
    const type = [kMaskEmail, kMaskPhone, kMaskPan, kMaskAccount];
    const options = { where: { title: type } };
    const sub = await this.adminSubModelRepo.getTableWhereData(attSub, options);
    if (sub === k500Error) throw new Error();
    if (!sub || sub.length === 0) return [];
    const filteredSub = sub.filter((ele) => {
      const access_list = ele?.access_list ?? [];
      return access_list.includes(adminRoleId);
    });

    const titles = filteredSub.map((item) => item.title);

    // return {
    //   isMaskPhone: !titles.includes(kMaskPhone),
    //   isDisbursementAccMask: !titles.includes(kMaskAccount),
    //   isMaskEmail: !titles.includes(kMaskEmail),
    //   isMaskPan: !titles.includes(kMaskPan),
    // };
    return {
      isMaskPhone: false,
      isDisbursementAccMask: false,
      isMaskEmail: false,
      isMaskPan: false,
    };
  }
}
