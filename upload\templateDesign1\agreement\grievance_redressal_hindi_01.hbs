<html>
<header>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Lexend&family=Lexend+Deca:wght@200&display=swap"
    rel="stylesheet" />
  <style>
    .body {
      font-family: "Lexend";
    }
  </style>
</header>

<body style="padding: 0; margin: 0">
  <table style="
        width: 100%;
        margin: auto;
        border-collapse: collapse;
        font-family: Lexend;
      ">
    <tbody>
      <tr>
        <td>
          <table style="
                max-width: 725px;
                width: 100%;
                margin: auto;
                border-collapse: collapse;
                font-family: Lexend;
              ">
            <tbody>
              <tr>
                <td style="height: 80px">
                  <div style="margin-top: 18px; margin-bottom: 8px">
                    <img width="150px" height="40px" src="{{nbfcLogo}}" />
                  </div>
                </td>
                <td style="padding: 0 0px 0 0; width: 400px">
                  <div style="
                        font-size: 12px;
                        text-align: end;
                        margin: 5px 0 0 0;
                      ">
                    {{nbfcAddress}}
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </td>
      </tr>
      <tr>
        <td style="background-color: #e02d42; height: 2px; margin-top: -2px"></td>
      </tr>
      <tr>
        <td>
          <div style="
                padding: 10px 30px;
                font-size: 10px;
                display: flex;
                justify-content: space-between;
                align-items: center;
              ">
            <div>
              {{#cIf showLendingPartner}}
              <div style="
                    background-color: #ffe9ec;
                    padding: 4px 12px;
                    border-radius: 20px;
                    font-size: 12px;
                    font-weight: 500;
                    font-family: Lexend;
                    display: inline-block;
                  ">
                Lending partner: {{appName}}
              </div>
              {{/cIf}}
            </div>
          </div>
        </td>
      </tr>
      <tr style="font-size: 12px; margin: 40px 30px 15px 30px; display: flex">
        <td>
          <div style="text-align: center">
            <strong> शिकायत निवारण प्रणाली </strong>
          </div>
          <p>
            {{nbfcName}} में, हम मानते हैं कि एक उत्कृष्ट ग्राहक अनुभव प्रदान
            करना हमारे व्यवसाय की सफलता के लिए महत्वपूर्ण है। हम यह सुनिश्चित
            करने के लिए प्रतिबद्ध हैं कि हमारे ग्राहक सभी संपर्क बिंदुओं पर
            बेहतरीन सेवा प्राप्त करें।
          </p>
          <div>
            <strong>1. उद्देश्य</strong>
          </div>
          <p>
            ग्राहक की प्रतिक्रिया, जिसमें शिकायतें भी शामिल हैं, हमारी सेवा
            सुधार रणनीति का एक महत्वपूर्ण घटक है। यह नीति शिकायतों को एक
            संरचित शिकायत निवारण ढांचे के माध्यम से प्रबंधित करने के हमारे
            दृष्टिकोण को रेखांकित करती है। इसमें एक समीक्षा तंत्र शामिल है
            जिसे भविष्य में समान मुद्दों के उत्पन्न होने की संभावना को कम करने
            के लिए तैयार किया गया है।
          </p>
          <p>
            <strong>शिकायत निवारण नीति निम्नलिखित सिद्धांतों का पालन करती है:
            </strong>
          </p>
          <div>
            <ul type="disc">
              <li>सभी ग्राहकों के साथ समान व्यवहार।</li>
              <br />
              <li>शिकायतों का शिष्टता और तत्परता से निवारण।</li>
              <br />
              <li>
                असंतोष की स्थिति में उत्पन्न होने वाले विकल्पों और ग्राहक
                अधिकारों के बारे में स्पष्ट संवाद।
              </li>
              <br />
              <li>
                कर्मचारी ईमानदारी और निष्पक्षता से कार्य करते हैं, ग्राहकों के
                हितों को प्राथमिकता देते हैं।
              </li>
              <br />
              <li>ग्राहक शिकायतों को संभालने के लिए आंतरिक व्यवस्था ।</li>
              <br />
              <li>
                कंपनी ने समय पर शिकायतों के समाधान को सुविधाजनक बनाने के लिए
                एक अत्याधुनिक LOS & CRM प्रणाली में निवेश किया है।
              </li>
              <br />
            </ul>
          </div>
          <p>
            यह प्रणाली शिकायतों को ट्रैक करती है, प्रश्न के प्रकार के आधार पर
            निर्धारित कार्य निष्पादन समय (TATs) का पालन करती है और
            पूर्वनिर्धारित शिकायत वृद्धि प्रणाली (Escalation Matrix) के अनुसार
            मुद्दों को उच्च स्तर पर ले जाती है।
          </p>
          <p>
            एक बार शिकायत हमारे सिस्टम में रिकॉर्ड हो जाने के बाद, ग्राहक सेवा
            टीम इसे 30 दिनों के भीतर ग्राहक की संतुष्टि तक हल करने के लिए काम
            करेगी। हम जहां उपयुक्त हो, वहां उपयुक्त वैकल्पिक समाधान प्रदान
            करने के लिए हर प्रयास करेंगे।
          </p>
          <p>
            यदि ग्राहक समाधान से असंतुष्ट रहता है, तो वह नीचे उल्लिखित शिकायत
            निवारण तंत्र का उपयोग करके मुद्दे को बढ़ा सकता है।
          </p>
        </td>
      </tr>
      <!-- 2 -->
      <tr style="font-size: 12px; margin: 15px 30px 15px 30px; display: flex">
        <td>
          <div>
            <strong>2. समय सीमा </strong>
          </div>
          <p>
            सभी शिकायतों के लिए 30 दिनों का मानक समाधान समय निर्धारित किया गया
            है, जो जांच की जटिलता पर निर्भर करता है। शिकायतों की प्राप्ति पर
            उनका स्वीकृति किया जाता है, और ग्राहक को समाधान प्रक्रिया में किसी
            भी देरी की सूचना दी जाती है।
          </p>
        </td>
      </tr>
      <!-- 2 -->
      <!-- 3 -->
      <tr style="font-size: 12px; margin: 15px 30px 15px 30px; display: flex">
        <td>
          <div>
            <strong>3. समीक्षा और निगरानी </strong>
          </div>
          <p>
            हम शिकायतों की निगरानी, TATs और शिकायतों के प्रकार की नियमित
            समीक्षा करते हैं ताकि प्रक्रिया में किसी भी अंतराल को पहचानकर उसे
            हल किया जा सके और उभरती प्रवृत्तियों का समाधान किया जा सके।
          </p>
        </td>
      </tr>
      <!-- 3 -->
    </tbody>
  </table>
  <div style="
        font-size: 10px;
        bottom: 5px;
        position: fixed;
        right: 0;
        padding: 10px 30px 5px 0px;
        text-align: right;
      ">
    <b>पृष्ठ 1</b>
  </div>
</body>

</html>