import { Transform, Type } from 'class-transformer';
import { IsNotEmpty, IsNumber, IsOptional, IsUUID } from 'class-validator';
import { UUID } from 'crypto';

export class LoanTimelineDto {
  @IsNumber()
  @IsNotEmpty()
  @Type(() => Number)
  loanId: number;

  @IsNotEmpty()
  @IsUUID()
  userId: UUID;

  @IsOptional()
  @Transform((o) => o.value === 'true' || o.value === true)
  isRefresh: boolean;
}

export class LoanHistoryDto{
  @IsNotEmpty()
  @IsUUID()
  userId: UUID;
}