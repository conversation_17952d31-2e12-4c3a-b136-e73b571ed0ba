//Profile
export const ACTIVATION_REPOSITORY = 'ACTIVATION_REPOSITORY';
export const USER_REPOSITORY = 'USER_REPOSITORY';
export const FLOW_REPOSITORY = 'FLOW_REPOSITORY';
export const USER_LOAN_DECLINE_REPOSITORY = 'USER_LOAN_DECLINE_REPOSITORY';
export const LOCATION_REPOSITORY = 'LOCATION_REPOSITORY';
export const GOOGLE_COORDINATES_REPOSITORY = 'GOOGLE_COORDINATES_REPOSITORY';
export const USERHISTORY_REPOSITORY = 'USERHISTORY_REPOSITORY';
export const KYC_REPOSITORY = 'KYC_REPOSITORY';
export const DEPARTMENT_REPOSITORY = 'DEPARTMENT_REPOSITORY';
export const PURPOSE_REPOSITORY = 'PURPOSE_REPOSITORY';
export const CONFIGS_REPOSITORY = 'CONFIGS_REPOSITORY';
export const LEGAL_CONSIGNMENT_REPOSITORY = 'LEGAL_CONSIGNMENT_REPOSITORY';
export const LEGAL_DOC_REPOSITORY = 'LEGAL_DOC_REPOSITORY';
export const CONFIGURATION_REPOSITORY = 'CONFIGURATION_REPOSITORY';

//Employment
export const EMPLOYEEDETAILS_REPOSITORY = 'EMPLOYEEDETAILS_REPOSITORY';
export const EMPLOYEMENT_SECTOR_ENTITY = 'EMPLOYEMENT_SECTOR_ENTITY';
export const DEGIGNATION_REPOSITORY = 'DEGIGNATION_REPOSITORY';
export const EMPLOYEMENT_TYPE_ENTITY = 'EMPLOYEMENT_TYPE_ENTITY';
export const EMPLOYEEDETAILS_HISTORY_REPOSITORY =
  'EMPLOYEEDETAILS_HISTORY_REPOSITORY';
export const SALARY_SLIP_REPOSITORY = 'SALARY_SLIP_REPOSITORY';
export const WORK_EMAIL_REPOSITORY = 'WORK_EMAIL_REPOSITORY';
export const BLACKLIST_COMPANIES_REPOSITORY = 'BLACKLIST_COMPANIES_REPOSITORY';

//Loan
export const LOANTRANSACTION_REPOSITORY = 'LOANTRANSACTION_REPOSITORY';

export const RESIDENCE_REPOSITORY = 'RESIDANT_REPOSITORY';
//EMI
export const EMIENTITY_REPOSITORY = 'EMIENTITY_REPOSITORY';
export const PAYMENT_ENTITY_REPOSITORY = 'PAYMENT_ENTITY_REPOSITORY';
export const DEFAULTERCONTACT_ENTITY_REPOSITORY =
  'DEFAULTERCONTACT_ENTITY_REPOSITORY';

//DISRBURSEMENT
export const DISBURSEMENTENTITY_REPOSITORY = 'DISBURSEMENTENTITY_REPOSITORY';
export const DISBURSEMENT_FAILURE_ENTITY_REPOSITORY =
  'DISBURSEMENT_FAILURE_ENTITY_REPOSITORY';

//Media
export const CHAT_DOCUMENTENTITY_REPOSITORY = 'CHAT_DOCUMENTENTITY_REPOSITORY';

//NetBanking
export const USERNETBANKINGDETAILES_REPOSITORY =
  'USERNETBANKINGDETAILES_REPOSITORY';
export const BANKING_REPOSITORY = 'BANKING_REPOSITORY';

// NetHistory
export const NETHISTORY_REPOSITORY = 'NETHISTORY_REPOSITORY';

//Legal notice
export const LEGAL_NOTICE_REPOSITORY = 'LEGAL_NOTICE_REPOSITORY';

// Bank
export const TOTAL_BANK_REPOSITORY = 'TOTAL_BANK_REPOSITORY';

//Scoring
export const SCOREGRADE_REPOSITORY = 'SCOREGRADE_REPOSITORY';

//Admin
export const ADMIN_REPOSITORY = 'ADMIN_REPOSITORY';
export const API_LOGGER_REPOSITORY = 'API_LOGGER_REPOSITORY';
export const CHANGE_LOGS_REPOSITORY = 'CHANGE_LOGS_REPOSITORY';
export const ADMINROLEMODULE_REPOSITORY = 'ADMINROLEMODULE_REPOSITORY';
export const ADMINROLE_REPOSITORY = 'ADMINROLE_REPOSITORY';
export const ADMIN_SUB_ROLE_MODULE_REPOSITORY =
  'ADMIN_SUB_ROLE_MODULE_REPOSITORY';
export const ACCESS_OF_LIST_REPOSITORY = 'ACCESS_OF_LIST_REPOSITORY';
export const ACCESS_OF_ROLE_REPOSITORY = 'ACCESS_OF_ROLE_REPOSITORY';
export const API_ACCESS_LIST_REPOSITORY = 'API_ACCESS_LIST_REPOSITORY';

//Contacts
export const CONTACT_DETAILS_REPOSITORY = 'CONTACT_DETAILS_REPOSITORY';

//Mandate
export const MANDATE_REPOSITORY = 'MANDATE_REPOSITORY';
export const SUBSCRIPTION_ENTITY = 'SUBSCRIPTION_ENTITY';

//CAMS
export const CAMS_ENTITY = 'CAMS_ENTITY';

// Exotel -> 3rd Party
export const EXOTEL_CALL_HISTORY_ENTITY = 'EXOTEL_CALL_HISTORY_ENTITY';
export const EXOTEL_CATEGORY_ENTITY = 'EXOTEL_CATEGORY_ENTITY';

//ESign
export const ESIGN_REPOSITORY = 'ESIGN_REPOSITORY';
export const STAMP_REPOSITORY = 'STAMP_REPOSITORY';

// crm
export const CRMACTIVITY_REPOSITORY = 'CRMACTIVITY_REPOSITORY';
export const CRMTITLE_REPOSITORY = 'CRMTITLE_REPOSITORY';
export const CRMDISPOSITION_REPOSITORY = 'CRMDISPOSITION_REPOSITORY';

//Transactions
export const AUTOPAY_REPOSITORY = 'AUTOPAY_REPOSITORY';
export const RAZORPAYMENT_REPOSITORY = 'RAZORPAYMENT_REPOSITORY';
export const TRANSACTION_REPOSITORY = 'TRANSACTION_REPOSITORY';
export const TRACK_ERROR_METRICS = 'TRACK_ERROR_METRICS';
export const TRACK_STEP_METRICS = 'TRACK_STEP_METRICS';
export const TRACK_STEP_CATEGORY = 'TRACK_STEP_CATEGORY';
export const TRACK_USERS_ATTEMPTS = 'TRACK_USERS_ATTEMPTS';

// manual verified company
export const MANUAL_VERIFIED_COMPANY_REPOSITORY =
  'MANUAL_VERIFIED_COMPANY_REPOSITORY';
export const MANUAL_VERIFIED_WORK_EMAIL_REPOSITORY =
  'MANUAL_VERIFIED_WORK_EMAIL_REPOSITORY';

// Devices
export const DEVICE_REPOSITORY = 'DEVICE_REPOSITORY';
export const DEVICESIM_REPOSITORY = 'DEVICESIM_REPOSITORY';
export const DEVICE_INFO_AND_INSTALL_APP_REPOSITORY =
  'DEVICE_INFO_AND_INSTALL_APP_REPOSITORY';
export const INSTALL_APP_REPOSITORY = 'INSTALL_APP_REPOSITORY';

// users block history
export const BLOCK_USER_HISTORY_REPOSITORY = 'BLOCK_USER_HISTORY_REPOSITORY';

export const USER_LOG_TRACKER_REPOSITORY = 'USER_LOG_TRACKER_REPOSITORY';

//Eligibility
export const PREDICTION_REPOSITORY = 'PREDICTION_REPOSITORY';

//Test
export const TEST_REPOSITORY = 'TEST_REPOSITORY';

export const PREDICTION_HISTORY_REPOSITORY = 'PREDICTION_HISTORY_REPOSITORY';
export const REMINDER_ENTITY = 'REMINDER_ENTITY';
//  References
export const REFERENCES_REPOSITORY = 'REFERENCES_REPOSITORY';
export const BYPASS_REPOSITORY = 'BYPASS_REPOSITORY';

// missmatch name
export const MISSMATCH_LOGS_REPOSITORY = 'MISSMATCH_LOGS_REPOSITORY';

//  unique contact
export const UNIQUE_CONTACT_REPOSITORY = 'UNIQUE_CONTACT_REPOSITORY';
export const UNIQUE_CONTACT_LOG_REPOSITORY = 'UNIQUE_CONTACT_LOG_REPOSITORY';
export const CONTACT_LOG_REPOSITORY = 'CONTACT_LOG_REPOSITORY';

//  users flow data
export const USERS_FLOW_DATA_REPOSITORY = 'USERS_FLOW_DATA_REPOSITORY';

//  ADDRESSES_REPOSITORY
export const ADDRESSES_REPOSITORY = 'ADDRESSES_REPOSITORY';

//  refund
export const REFUND_REPOSITORY = 'REFUND_REPOSITORY';

//sent notification traking
export const SENTNOTIFICATION_REPOSITORY = 'SENTNOTIFICATION_REPOSITORY';

//  mail tracker repository
export const MAIL_TRACKER_REPOSITORY = 'MAIL_TRACKER_REPOSITORY';

// user selfie
export const USER_SELFIE_REPOSITORY = 'USER_SELFIE_REPOSITORY';

//  static config data
export const STATIC_CONFIG_REPOSITORY = 'STATIC_CONFIG_REPOSITORY';

// state eligibility
export const STATE_ELIGIBILITY_REPOSITORY = 'STATE_ELIGIBILITY_REPOSITORY';

// Exotel call history
export const EXOTEL_CALL_HISTORY_REPOSITORY = 'EXOTEL_CALL_HISTORY_REPOSITORY';

export const COOL_PERIOD_REPORSITORY = 'COOL_PERIOD_REPORSITORY';
export const TEMPLATE_ENTITY = 'TEMPLATE_ENTITY';

// user permission
export const USER_PERMISSION_REPOSITORY = 'USER_PERMISSION_REPOSITORY';
export const USER_ACTIVITY_REPOSITORY = 'USER_ACTIVITY_REPOSITORY';
export const REASON_REPOSITORY = 'REASON_REPOSITORY';
export const CRMSTATUS_REPOSITORY = 'CRMSTATUS_REPOSITORY';

/// augmount
export const AUGMONT_TRANSACTION_REPOSITORY = 'AUGMONT_TRANSACTION_REPOSITORY';

export const REPORT_LIST_REPOSITORY = 'REPORT_LIST_REPOSITORY';
export const THIRDPARTY_SERVICE_REPOSITORY = 'THIRDPARTY_SERVICE_REPOSITORY';
export const THIRDPARTY_PROVIDER_REPOSITORY = 'THIRDPARTY_PROVIDER_REPOSITORY';

///cibil
export const CIBIL_REPOSITORY = 'CIBIL_REPOSITORY';
export const CIBIL_TRIGGER_REPOSITORY = 'CIBIL_TRIGGER_REPOSITORY';
export const CIBIL_SCORE_REPOSITORY = 'CIBIL_SCORE_REPOSITORY';
export const CRM_REASON_REPOSITORY = 'CRM_REASON_REPOSITORY';

// User
export const MASTER_REPOSITORY = 'MASTER_REPOSITORY';
export const DOWNLOAD_APP_TRACK = 'DOWNLOAD_APP_TRACK';
export const DELETE_USER = 'DELETE_USER';
export const COMPANY_REPOSITORY = 'GOOGLE_REPOSITORY';

// Banking
export const BRANCHES_REPOSITORY = 'BRANCHES_REPOSITORY';
export const BANKS_REPOSITORY = 'BANKS_REPOSITORY';
export const LEGAL_COLLECTION = 'LEGAL_COLLECTION_REPOSITORY';
export const AA_REPOSITORY = 'AA_REPOSITORY';

/// defaulter
export const DEFAULTER_ONLINE_ENTITY_REPOSITORY =
  'DEFAULTER_ONLINE_ENTITY_REPOSITORY';
export const COLLECTION_PERFORMANCE_REPORT = 'COLLECTION_PERFORMANCE_REPORT';
export const PROMO_CODE_ENTITY_REPOSITORY = 'PROMO_CODE_ENTITY_REPOSITORY';

//health Data
//Other Permission Data
export const HEALTH_DATA_ENTITY_REPOSITORY = 'HEALTH_DATA_ENTITY_REPOSITORY';
export const OTHER_PERMISSION_DATA_ENTITY_REPOSITORY =
  'OTHER_PERMISSION_DATA_ENTITY_REPOSITORY';

export const INSURANCE_REPOSITORY = 'INSURANCE_REPOSITORY';
export const CRON_TRAKING_REPOSITORY = 'CRON_TRAKING_REPOSITORY';

// quality parameters repository
export const QUALITY_PARAMETERS_REPOSITORY = 'QUALITY_PARAMETERS_REPOSITORY';

// Third party
export const RESPONSE_REPOSITORY = 'RESPONSE_REPOSITORY';
//report  repository
export const REPORT_HISTORY_REPOSITORY = 'REPORT_HISTORY_REPOSITORY';
export const SETTLEMENT_REPOSITORY = 'SETTLEMENT_REPOSITORY';

export const METRICS_REPOSITORY = 'METRICS_REPOSITORY';
export const REQUEST_SUPPORT_REPOSITORY = 'REQUEST_SUPPORT_REPOSITORY';

// Ip Master Repository
export const IP_MASTER_REPOSITORY = 'IP_MASTER_REPOSITORY';

// finvu Repository
export const FINVU_REPOSITORY = 'FINVU_REPOSITORY';

//RBI Guidelines repository
export const RBI_GUIDELINE_REPOSITORY = 'RBI_GUIDELINE_REPOSITORY';

//Whatsapp Message Repository
export const WHATSAPP_MESSAGE_REPOSITORY = 'WHATSAPP_MESSAGE_REPOSITORY';
//Tally Principal Interest Reversal Repository
export const TALLY_IP_REVERSAL_REPOSITORY = 'TALLY_IP_REVERSAL_REPOSITORY';

//Lead Traacking File repository
export const LEAD_TRACKING_FILE_REPOSITORY = 'LEAD_TRACKING_FILE_REPOSITORY';

//Lead Tracking repository
export const LEAD_TRACKING_REPOSITORY = 'LEAD_TRACKING_REPOSITORY';

//Lead tracking Ip repository
export const LEADS_TRACKING_IP_REPOSITORY = 'LEADS_TRACKING_IP_REPOSITORY';
export const USER_SMS_REPOSITORY = 'USER_SMS_REPOSITORY';
