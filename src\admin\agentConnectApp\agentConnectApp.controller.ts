// Imports
import { Body, Controller, Get, Post, Query, Res } from '@nestjs/common';
import { kInternalError, kSuccessData } from 'src/constants/responses';
import { AgentConnectAppService } from './agentConnectApp.service';
import { ErrorContextService } from 'src/utils/error.context.service';

@Controller('admin/agentConnectApp')
export class AgentConnectAppController {
  constructor(
    private readonly service: AgentConnectAppService,
    private readonly errorContextService: ErrorContextService,
  ) {}

  @Post('sendOtp')
  async funSentOtp(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.sendOtp(body);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Post('logIn')
  async funLogIn(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.logIn(body);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Post('createCallHistory')
  async funCreateCallHistory(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.createCallHistory(body);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Post('uploadCallRecording')
  async funUploadCallRecording(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.uploadCallRecording(body);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Get('getCustomerLatestCallLog')
  async funGetCallHistoryData(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.getCustomerLatestCallLog(query);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Get('getCustomerCallHistory')
  async funGetCustomerCallHistory(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.getCustomerCallHistory(query);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Get('getSuggestedCustomers')
  async funGetSuggestedCustomers(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.getSuggestedCustomers(query);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Get('getFavoriteCustomers')
  async funGetFavoriteCustomers(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.getFavoriteCustomers(query);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Post('updateCustomerFavStatus')
  async funUpdateFavStatus(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.updateCustomerFavStatus(body);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Post('handleLogout')
  async funHandleLogout(@Body() body, @Res() res) {
    try {
      const data: any = await this.service.handleLogout(body);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Post('sendCollectionCallCategoryReportToSlack')
  async funSendColletionCallSummeryReportOnSlack(@Res() res) {
    try {
      const data: any =
        await this.service.sendCollectionCallCategoryReportToSlack();
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Post('sendCallPerformanceReportToSlack')
  async funSendCallSummeryReportOnSlack(@Res() res) {
    try {
      const data: any = await this.service.sendCallPerformanceReportToSlack();
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Get('getUserDataUsingPhone')
  async funGetUserDataUsingPhone(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.getUserDataUsingPhone(query);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }
}
