import {
  Column,
  DataType,
  Model,
  Table,
} from 'sequelize-typescript';
@Table({})
export class HypothecationHistoryEntity extends Model<HypothecationHistoryEntity> {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    allowNull: false,
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  loanId: number;

  @Column({
    type: DataType.SMALLINT,
    allowNull: false,
  })
  lenderId: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  assigned_date: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  unassigned_date: Date;
}
