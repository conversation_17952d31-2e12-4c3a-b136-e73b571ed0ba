// Imports
import {
  Table,
  Model,
  Column,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { registeredUsers } from './user.entity';

@Table({})
export class UserLoginTrackerEntity extends Model<UserLoginTrackerEntity> {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    allowNull: false,
  })
  id: number;

  @ForeignKey(() => registeredUsers)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId: string;

  @BelongsTo(() => registeredUsers)
  userData: registeredUsers;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  ip: string;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  reqBody: Object;
}
