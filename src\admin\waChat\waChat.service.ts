import { Injectable } from '@nestjs/common';
import { Op } from 'sequelize';
import {
  GLOBAL_RANGES,
  LSP_APP_LINK,
  minPerDayInt,
  NBFC_APP_LINK,
  SYSTEM_ADMIN_ID,
} from 'src/constants/globals';
import { k500Error } from 'src/constants/misc';
import { CLOUD_FOLDER_PATH, MICRO_ALERT_TOPIC } from 'src/constants/objects';
import { kInternalError } from 'src/constants/responses';
import {
  disbursementLimitUpgradedTitle,
  disbursementTitle,
  eligibleForLowerPerTitle,
  kCoolOffPeriodOverTitle,
  kLoanAcceptFeedback,
  kNBFC,
  kNoDataFound,
  kSignUpEligibility,
  kycInWATitle,
  kycStuckTitle,
  loanDeclinedTitle,
  paymentFailedTitle,
  paymentReminderTitle,
  paymentSuccessfulTitle,
  selfieStuckTitle,
  userStuckTitle,
} from 'src/constants/strings';
import { disbursementEntity } from 'src/entities/disbursement.entity';
import { esignEntity } from 'src/entities/esign.entity';
import { loanTransaction } from 'src/entities/loan.entity';
import { registeredUsers } from 'src/entities/user.entity';
import { KafkaService } from 'src/microservice/kafka/kafka.service';
import { EMIRepository } from 'src/repositories/emi.repository';
import { LoanRepository } from 'src/repositories/loan.repository';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { UserRepository } from 'src/repositories/user.repository';
import { CryptService } from 'src/utils/crypt.service';
import { FileService } from 'src/utils/file.service';
import { TypeService } from 'src/utils/type.service';

@Injectable()
export class WAChatService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly repoManager: RepositoryManager,
    private readonly typeService: TypeService,
    private readonly cryptService: CryptService,
    private readonly emiRepository: EMIRepository,
    private readonly loanRepo: LoanRepository,
    private readonly kafkaService: KafkaService,
    private readonly fileService: FileService,
  ) {}

  async getValueOfTemplate(reqData) {
    const { templateName, userId } = reqData;

    const maxLoanAmount = this.typeService.amountNumberWithCommas(
      GLOBAL_RANGES.MAX_LOAN_AMOUNT,
    );

    const loanAmountUpTo = maxLoanAmount ?? '--';
    const nbfcName = kNBFC ?? '--';
    const interestRate = `${minPerDayInt}%`;

    switch (templateName) {
      // disbursementLimitUpgradedTitle
      case disbursementLimitUpgradedTitle: {
        let { loanData, userData, payload } = await this.funGetLoanAndUserData(
          userId,
          templateName,
        );
        const fullName = payload?.customerName ?? '--';
        const values =
          loanData.appType == 0
            ? [fullName, loanAmountUpTo, nbfcName]
            : [fullName, loanAmountUpTo];

        payload.loanAmountUpTo = loanAmountUpTo;
        if (loanData.appType == 0) {
          payload.nbfcName = nbfcName;
        }

        return { values, payload };
      }

      // userStuckTitle
      case userStuckTitle: {
        let { loanData, userData, payload } = await this.funGetLoanAndUserData(
          userId,
          templateName,
        );
        const fullName = payload?.customerName ?? '--';
        const values =
          loanData.appType == 0
            ? [fullName, loanAmountUpTo, nbfcName]
            : [fullName, loanAmountUpTo];

        payload.loanAmountUpTo = loanAmountUpTo;
        if (loanData.appType == 0) {
          payload.nbfcName = nbfcName;
        }

        return { values, payload };
      }
      // eligibleForLowerPerTitle
      case eligibleForLowerPerTitle: {
        let { loanData, userData, payload } = await this.funGetLoanAndUserData(
          userId,
          templateName,
        );
        const fullName = payload?.customerName ?? '--';
        const values = [fullName, interestRate, loanAmountUpTo];

        payload.interestRate = interestRate;
        payload.loanAmountUpTo = loanAmountUpTo;

        return { values, payload };
      }

      // kCoolOffPeriodOverTitle
      case kCoolOffPeriodOverTitle: {
        let { loanData, userData, payload } = await this.funGetLoanAndUserData(
          userId,
          templateName,
        );
        const fullName = payload?.customerName ?? '--';
        const values =
          loanData.appType == 0 ? [fullName, nbfcName] : [fullName];

        if (loanData.appType == 0) {
          payload.nbfcName = nbfcName;
        }

        return { values, payload };
      }

      // kSignUpEligibility
      case kSignUpEligibility: {
        let { loanData, userData, payload } = await this.funGetLoanAndUserData(
          userId,
          templateName,
        );
        const fullName = payload?.customerName ?? '--';
        return { payload };
      }

      // paymentReminderTitle
      case paymentReminderTitle: {
        let { loanData, userData, payload } = await this.funGetLoanAndUserData(
          userId,
          templateName,
        );
        const fullName = payload?.customerName ?? '--';
        const data = await this.getPaymentReminderTempPayload(
          userData,
          loanData,
        );
        if (loanData.appType == 0) {
          payload.nbfcName = nbfcName;
        }

        if (data == false) return { errorText: 'Not eligible' };
        const amount = data.amount ?? '--';
        const date = data.date ?? '--';
        payload.amount = amount;
        payload.date = date;

        const values =
          loanData.appType == 0
            ? [fullName, amount, date, nbfcName]
            : [fullName, amount, date];
        return { values, payload };
      }

      case disbursementTitle: {
        let { loanData, userData, payload, errorText } =
          await this.funGetLoanAndUserData(userId, templateName);

        if (errorText) return { errorText };
        const fullName = payload?.customerName ?? '--';
        const disbursedAmount = payload?.disbursedAmount;
        const disburseDate = payload?.disburseDate;
        const transactionId = payload?.transactionId;
        const values =
          payload.appType == 0
            ? [fullName, disbursedAmount, disburseDate, transactionId, nbfcName]
            : [fullName, disbursedAmount, disburseDate, transactionId];
        return { values, payload };
      }

      // case kLoanAcceptFeedback: {
      //   let { loanData, userData, payload } =
      //     await this.funGetLoanAndUserData(userId, templateName);

      //   const fullName = payload?.customerName ?? '--';
      //   return { payload };
      // }

      case kycInWATitle: {
        let { loanData, userData, payload } = await this.funGetLoanAndUserData(
          userId,
          templateName,
        );
        return { payload };
      }
      case kycStuckTitle: {
        let { loanData, userData, payload } = await this.funGetLoanAndUserData(
          userId,
          templateName,
        );

        payload.appLink = payload?.appType == 0 ? LSP_APP_LINK : NBFC_APP_LINK;
        const appLink = payload.appLink;
        const fullName = payload?.customerName ?? '--';
        const values = [fullName, appLink];
        return { payload, values };
      }
      case loanDeclinedTitle: {
        let { loanData, userData, payload } = await this.funGetLoanAndUserData(
          userId,
          templateName,
        );

        const fullName = payload?.customerName ?? '--';
        const values = [fullName];
        return { values, payload };
      }
      // case paymentFailedTitle: {
      //   let { loanData, userData, payload } =
      //     await this.funGetLoanAndUserData(userId, templateName);

      //   const fullName = payload?.customerName ?? '--';
      //   return { payload };
      // }

      // case paymentSuccessfulTitle: {
      //   let { loanData, userData, payload } =
      //     await this.funGetLoanAndUserData(userId, templateName);

      //   const fullName = payload?.customerName ?? '--';
      //   return { payload };
      // }
      case selfieStuckTitle: {
        let { loanData, userData, payload } = await this.funGetLoanAndUserData(
          userId,
          templateName,
        );

        payload.appLink = payload?.appType == 0 ? LSP_APP_LINK : NBFC_APP_LINK;
        const appLink = payload.appLink;
        const fullName = payload?.customerName ?? '--';
        const values = [fullName, appLink];
        return { payload, values };
      }

      default: {
        let { loanData, userData, payload } = await this.funGetLoanAndUserData(
          userId,
          templateName,
        );

        return { payload };
      }
    }
  }

  async funGetLoanAndUserData(userId, templateName) {
    // disbursementTitle
    if ([disbursementTitle].includes(templateName)) {
      return await this.getDisbursementTempPayload(userId, templateName);
    }
    // if (
    //   [
    //     disbursementLimitUpgradedTitle,
    //     userStuckTitle,
    //     eligibleForLowerPerTitle,
    //     kCoolOffPeriodOverTitle,
    //     kSignUpEligibility,
    //     paymentReminderTitle,
    //   ].includes(templateName)
    // ) {
    const userOption = {
      where: { id: userId },
    };

    const userData = await this.userRepository.getRowWhereData(
      ['fullName', 'email', 'phone', 'appType', 'id'],
      userOption,
    );
    if (userData == k500Error) throw new Error();

    const loanOpt = {
      where: { userId },
      order: [['id', 'desc']],
    };
    const loanData = await this.repoManager.getRowWhereData(
      loanTransaction,
      ['id', 'appType', 'userId', 'loanStatus'],
      loanOpt,
    );

    if (loanData == k500Error) throw new Error();

    const phone = this.cryptService.decryptPhone(userData?.phone);
    let payload: any = {
      title: templateName,
      appType: loanData?.appType ?? userData?.appType,
      userId: userData?.id,
      loanId: loanData?.loanId,
      number: phone,
      customerName: userData?.fullName,
      email: userData?.email,
    };

    return { loanData, userData, payload, errorText: null };
    // }
  }

  async getPaymentReminderTempPayload(userData, loanData) {
    let tomorrowDate = new Date();
    tomorrowDate.setDate(tomorrowDate.getDate() + 1);
    tomorrowDate = this.typeService.getGlobalDate(tomorrowDate);

    const attributes = [
      'id',
      'payment_due_status',
      'payment_status',
      'userId',
      'loanId',
      'emi_date',
      'emi_amount',
      'payment_done_date',
      'penalty_days',
    ];

    let options = {
      where: {
        userId: userData.id,
        loanId: loanData.id,
        emi_date: { [Op.eq]: tomorrowDate.toJSON() },
        payment_status: '0',
      },
    };
    //get all tomorrow emi users data
    const emiData = await this.emiRepository.getRowWhereData(
      attributes,
      options,
    );

    if (!emiData || emiData == k500Error || emiData?.length === 0) return false;

    const obj = {
      amount: this.typeService.amountNumberWithCommas(
        Math.floor(+emiData?.emi_amount),
      ),
      date: this.typeService.dateToFormatStr(emiData?.emi_date),
    };

    return obj;
  }

  async getDisbursementTempPayload(userId, templateName) {
    const userInclude = {
      model: registeredUsers,
      attributes: ['id', 'fullName', 'email', 'phone'],
    };
    const eSignInclude = {
      model: esignEntity,
      attributes: ['id', 'signed_document_upload'],
    };
    const disInclude = {
      model: disbursementEntity,
      attributes: [
        'id',
        'createdAt',
        'amount',
        'updatedAt',
        'status',
        'utr',
        'account_number',
        'bank_name',
      ],
      where: { status: 'processed' },
    };

    const loanData: any = await this.loanRepo.getRowWhereData(
      ['id', 'createdAt', 'loan_disbursement_date', 'esign_id', 'appType'],
      {
        where: {
          userId: userId,
          loanStatus: 'Active',
        },
        include: [disInclude, userInclude, eSignInclude],
      },
    );

    if (!loanData)
      return {
        payload: null,
        loanData: null,
        userData: null,
        errorText: 'Not eligible',
      };

    const adminId = loanData?.adminId ?? SYSTEM_ADMIN_ID;
    const loanId = loanData?.id;
    const appType = loanData?.appType;
    const loan_disbursement_date = loanData?.loan_disbursement_date;
    const amount = this.typeService.amountNumberWithCommas(
      loanData?.disbursementData[0]?.amount / 100,
    );
    const transactionId = loanData?.disbursementData[0]?.utr;
    const user_Id = loanData?.registeredUsers?.id;
    const customerName = loanData?.registeredUsers?.fullName;
    const email = loanData?.registeredUsers?.email;
    let number = this.cryptService.decryptPhone(
      loanData?.registeredUsers?.phone,
    );

    const agreementURL = loanData?.eSignData?.signed_document_upload;
    const payload = {
      customerName,
      email,
      number,
      disbursedAmount: amount,
      transactionId: transactionId,
      disburseDate: this.typeService.dateToFormatStr(loan_disbursement_date),
      loanId,
      userId: user_Id,
      agreementURL,
      adminId,
      title: templateName,
      requestData: 'disbursement',
      appType,
    };
    return { payload, loanData, userData: loanData?.registeredUsers };
  }

  async userChatList(reqData) {
    return await this.kafkaService.send(
      MICRO_ALERT_TOPIC.USER_CHAT_LIST,
      reqData,
    );
  }

  async getUserChat(reqData) {
    return await this.kafkaService.send(
      MICRO_ALERT_TOPIC.GET_USER_CHAT,
      reqData,
    );
  }

  async readMessage(reqData) {
    return await this.kafkaService.send(
      MICRO_ALERT_TOPIC.READ_MESSAGE,
      reqData,
    );
  }

  async adminWASendMessage(reqData) {
    return await this.kafkaService.send(
      MICRO_ALERT_TOPIC.ADMIN_WA_SEND_MESSAGE,
      reqData,
    );
  }

  async sendTemplateMsg(reqData) {
    return await this.kafkaService.send(
      MICRO_ALERT_TOPIC.SEND_TEMPLATE_MSG,
      reqData,
    );
  }

  async getAllTemplates(reqData) {
    return await this.kafkaService.send(
      MICRO_ALERT_TOPIC.GET_ALL_TEMPLATES,
      reqData,
    );
  }

  async getTemplateByName(reqData) {
    const res = await this.kafkaService.send(
      MICRO_ALERT_TOPIC.GET_TEMPLATE_BY_NAME,
      reqData,
    );
    if (res?.data == 'Not eligible')
      return { data: {}, message: res.data, valid: false };
    return res;
  }

  async fileToUrl(reqData) {
    const { url, extension, headers } = reqData;
    const filePath = await this.fileService.urlToBuffer(url, true, extension, {
      headers,
    });

    const fileUrl = await this.fileService.uploadFile(
      filePath,
      CLOUD_FOLDER_PATH.whatsApp,
      extension,
    );

    return fileUrl;
  }
}
