import {
  Controller,
  Post,
  Get,
  Res,
  Body,
  UseInterceptors,
  Query,
  UploadedFiles,
  UploadedFile,
} from '@nestjs/common';
import { kInternalError, kSuccessData } from 'src/constants/responses';
import { MarketingService } from './marketing.service';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { AdminRedisSyncService } from '../admin/AdminRedisSync.service';
import { kUploadFileObj } from 'src/constants/objects';
import { ErrorContextService } from 'src/utils/error.context.service';
import { PromoCodeService } from 'src/shared/promo.code.service';
@Controller('admin/marketing')
export class MarketingController {
  constructor(
    private readonly service: MarketingService,
    private readonly creditAnalyst: AdminRedisSyncService,
    private readonly errorContextService: ErrorContextService,
    private readonly promoCodeService: PromoCodeService,
  ) {}

  @Post('manageBanner')
  @UseInterceptors(FilesInterceptor('files'))
  async funManageBanner(@UploadedFiles() files, @Body() body, @Res() res) {
    try {
      const data: any = await this.service.funManageBanner({ files, body });
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Post('storeBanners')
  async storeBanners(@Res() res) {
    try {
      const data: any = await this.creditAnalyst.storeBanners();
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Get('getBannersData')
  async funGetBannersData(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.funGetBannersData(query);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }
  @Get('getBulkWhatsAppData')
  async funGetBulkWhatsAppData(@Query() query, @Res() res) {
    try {
      const data: any = await this.service.getBulkWhatsAppData(query);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }
  @Post('sendBulkWhatsAppData')
  @UseInterceptors(FileInterceptor('file', kUploadFileObj()))
  async funSendBulkWhatsAppData(
    @UploadedFile() file,
    @Body() body,
    @Res() res,
  ) {
    try {
      body.file = file;
      const data: any = await this.service.sendBulkWhatsAppData({ body, file });
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
    }
  }
  @Post('updateDiscountOffer')
  @UseInterceptors(FileInterceptor('file', kUploadFileObj()))
  async funUpdateDiscountOffer(@Body() body, @Res() res, @UploadedFile() file) {
    try {
      body.file = file;
      const data: any = await this.promoCodeService.updateDiscountOffer(body);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }

  @Get('getPromoCodeData')
  async funGetPromoCodeData(@Query() query, @Res() res) {
    try {
      const data: any = await this.promoCodeService.getPromoCodeData(query);
      if (data?.message) return res.json(data);
      return res.json({ ...kSuccessData, data });
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return res.json(kInternalError);
    }
  }
}
