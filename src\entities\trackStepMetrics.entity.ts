import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { TrackStepCategory } from './trackStepCategory.entity';

@Table({})
export class TrackStepMetrics extends Model<TrackStepMetrics> {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    allowNull: false,
  })
  id: number;

  @ForeignKey(() => TrackStepCategory)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  stepId: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  totalErrors: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  totalAttempts: number;
}
