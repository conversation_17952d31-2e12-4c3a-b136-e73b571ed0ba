// Imports
import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { EnvConfig } from 'src/configs/env.config';
import {
  Frames,
  FramesSchema,
} from 'src/entities/frames_schemas/frames.schema';
import { MongoQueryService } from './mongodb/mongo.query.service';
import { RepositoryManager } from 'src/repositories/repository.manager';

@Module({
  imports: [
    // Connection -> MongoDB
    MongooseModule.forRoot(
      `mongodb://${EnvConfig.database.mongodb.host}:${EnvConfig.database.mongodb.port}/${EnvConfig.database.mongodb.mongoFramesDBname}`,
      {
        user: EnvConfig.database.mongodb.username,
        pass: EnvConfig.database.mongodb.password,
        authSource: 'admin',
        connectionName: EnvConfig.database.mongodb.mongoFramesDBname,
      },
    ),
    // Injection -> MongoDB Schema
    MongooseModule.forFeature(
      [{ name: Frames.name, schema: FramesSchema }],
      EnvConfig.database.mongodb.mongoFramesDBname,
    ),
  ],
  controllers: [],
  providers: [MongoQueryService, RepositoryManager],
  exports: [MongooseModule, MongoQueryService],
})
export class MongoFramesModule {}
