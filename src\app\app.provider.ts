// Imports
import { APP_INTERCEPTOR } from '@nestjs/core';
import { EnvConfig } from 'src/configs/env.config';
import {
  AA_REPOSITORY,
  ACCESS_OF_LIST_REPOSITORY,
  ACCESS_OF_ROLE_REPOSITORY,
  ADDRESSES_REPOSITORY,
  ADMINROL<PERSON><PERSON>ULE_REPOSITORY,
  ADMI<PERSON><PERSON>E_REPOSITORY,
  ADMIN_REPOSITORY,
  ADMIN_SUB_ROLE_MODULE_REPOSITORY,
  API_ACCESS_LIST_REPOSITORY,
  API_LOGGER_REPOSITORY,
  BANKING_REPOSITORY,
  BANKS_REPOSITORY,
  B<PERSON><PERSON><PERSON><PERSON><PERSON>T_COMPANIES_REPOSITORY,
  BLOCK_USER_HISTORY_REPOSITORY,
  BRANCHES_REPOSITORY,
  CHANGE_LOGS_REPOSITORY,
  CHAT_DOCUMENTENTITY_REPOSITORY,
  CIBIL_R<PERSON>OSITORY,
  CIBIL_SCORE_REPOSITORY,
  <PERSON><PERSON><PERSON>_TRIGGER_REP<PERSON>ITORY,
  COLLECTION_PERFORMANCE_REPORT,
  COMPANY_REPOSITORY,
  CONFIGS_REPOSITORY,
  CONFIGURATION_REPOSITORY,
  CONTACT_DETAILS_REPOSITORY,
  CONTACT_LOG_REPOSITORY,
  CRMACTIVITY_REPOSITORY,
  CRMDISPOSITION_REPOSITORY,
  CRMSTATUS_REPOSITORY,
  CRMTITLE_REPOSITORY,
  CRM_REASON_REPOSITORY,
  CRON_TRAKING_REPOSITORY,
  DEFAULTER_ONLINE_ENTITY_REPOSITORY,
  DEGIGNATION_REPOSITORY,
  DELETE_USER,
  DEPARTMENT_REPOSITORY,
  DEVICESIM_REPOSITORY,
  DEVICE_INFO_AND_INSTALL_APP_REPOSITORY,
  DEVICE_REPOSITORY,
  DISBURSEMENTENTITY_REPOSITORY,
  DOWNLOAD_APP_TRACK,
  EMIENTITY_REPOSITORY,
  EMPLOYEEDETAILS_HISTORY_REPOSITORY,
  EMPLOYEEDETAILS_REPOSITORY,
  EMPLOYEMENT_SECTOR_ENTITY,
  EMPLOYEMENT_TYPE_ENTITY,
  ESIGN_REPOSITORY,
  EXOTEL_CALL_HISTORY_ENTITY,
  EXOTEL_CATEGORY_ENTITY,
  FINVU_REPOSITORY,
  FLOW_REPOSITORY,
  GOOGLE_COORDINATES_REPOSITORY,
  HEALTH_DATA_ENTITY_REPOSITORY,
  INSTALL_APP_REPOSITORY,
  INSURANCE_REPOSITORY,
  IP_MASTER_REPOSITORY,
  KYC_REPOSITORY,
  LEADS_TRACKING_IP_REPOSITORY,
  LEAD_TRACKING_FILE_REPOSITORY,
  LEAD_TRACKING_REPOSITORY,
  LEGAL_COLLECTION,
  LEGAL_CONSIGNMENT_REPOSITORY,
  LEGAL_NOTICE_REPOSITORY,
  LOANTRANSACTION_REPOSITORY,
  LOCATION_REPOSITORY,
  MAIL_TRACKER_REPOSITORY,
  MANDATE_REPOSITORY,
  MANUAL_VERIFIED_COMPANY_REPOSITORY,
  MANUAL_VERIFIED_WORK_EMAIL_REPOSITORY,
  MASTER_REPOSITORY,
  METRICS_REPOSITORY,
  MISSMATCH_LOGS_REPOSITORY,
  OTHER_PERMISSION_DATA_ENTITY_REPOSITORY,
  PREDICTION_REPOSITORY,
  PROMO_CODE_ENTITY_REPOSITORY,
  PURPOSE_REPOSITORY,
  QUALITY_PARAMETERS_REPOSITORY,
  RBI_GUIDELINE_REPOSITORY,
  REASON_REPOSITORY,
  REPORT_HISTORY_REPOSITORY,
  REPORT_LIST_REPOSITORY,
  REQUEST_SUPPORT_REPOSITORY,
  RESPONSE_REPOSITORY,
  SALARY_SLIP_REPOSITORY,
  SETTLEMENT_REPOSITORY,
  STAMP_REPOSITORY,
  STATE_ELIGIBILITY_REPOSITORY,
  STATIC_CONFIG_REPOSITORY,
  SUBSCRIPTION_ENTITY,
  TALLY_IP_REVERSAL_REPOSITORY,
  TEMPLATE_ENTITY,
  TEST_REPOSITORY,
  THIRDPARTY_PROVIDER_REPOSITORY,
  THIRDPARTY_SERVICE_REPOSITORY,
  TOTAL_BANK_REPOSITORY,
  TRACK_ERROR_METRICS,
  TRACK_STEP_CATEGORY,
  TRACK_STEP_METRICS,
  TRACK_USERS_ATTEMPTS,
  TRANSACTION_REPOSITORY,
  UNIQUE_CONTACT_LOG_REPOSITORY,
  UNIQUE_CONTACT_REPOSITORY,
  USERHISTORY_REPOSITORY,
  USERNETBANKINGDETAILES_REPOSITORY,
  USERS_FLOW_DATA_REPOSITORY,
  USER_ACTIVITY_REPOSITORY,
  USER_LOAN_DECLINE_REPOSITORY,
  USER_LOG_TRACKER_REPOSITORY,
  USER_PERMISSION_REPOSITORY,
  USER_REPOSITORY,
  USER_SELFIE_REPOSITORY,
  USER_SMS_REPOSITORY,
  WHATSAPP_MESSAGE_REPOSITORY,
  WORK_EMAIL_REPOSITORY,
} from 'src/constants/entities';
import { AccessOfListEntity } from 'src/entities/access_of_list.entity';
import { AccessOfRoleEntity } from 'src/entities/access_of_role.entity';
import { AddressesEntity } from 'src/entities/addresses.entity';
import { admin } from 'src/entities/admin.entity';
import { AdminRole } from 'src/entities/admin.role.entity';
import { AAEntity } from 'src/entities/aggregator.entity';
import { APIAccessListEntity } from 'src/entities/api.list.entity';
import { APILogsEntity } from 'src/entities/apilog.entity';
import { BankList } from 'src/entities/bank.entity';
import { BankingEntity } from 'src/entities/banking.entity';
import { banks } from 'src/entities/banks.entity';
import { BlacklistCompanyEntity } from 'src/entities/blacklistCompanies.entity';
import { BlockUserHistoryEntity } from 'src/entities/block.user.history.entity';
import { branches } from 'src/entities/branches.entity';
import { ChangeLogsEntity } from 'src/entities/change.logs.entity';
import { CIBILEntity } from 'src/entities/cibil.entity';
import { CibilScoreEntity } from 'src/entities/cibil.score.entity';
import { cibilTriggerEntity } from 'src/entities/cibilTrigger.entity';
import { CollectionPerformanceReport } from 'src/entities/collectionPerformanceReport.entity';
import { GoogleCompanyResultEntity } from 'src/entities/company.entity';
import { Configuration } from 'src/entities/configuration.entity';
import { contactDetailEntity } from 'src/entities/contact.entity';
import { ContactLogEntity } from 'src/entities/contact.log.entity';
import { crmActivity } from 'src/entities/crm.entity';
import { CrmReasonEntity } from 'src/entities/crm.reasons.entity';
import { crmDisposition } from 'src/entities/crmDisposition.entity';
import { crmStatus } from 'src/entities/crmStatus.entity';
import { crmTitle } from 'src/entities/crmTitle.entity';
import { CronTrakingEntity } from 'src/entities/cron.track.entity';
import { DefaulterOnlineEntity } from 'src/entities/defaulterOnline.entity';
import { Department } from 'src/entities/department.entity';
import { employmentDesignation } from 'src/entities/designation.entity';
import { device } from 'src/entities/device.entity';
import { DeviceInfoAndInstallAppEntity } from 'src/entities/device.info.entity';
import { DeviceSIM } from 'src/entities/deviceSIM.entity';
import { disbursementEntity } from 'src/entities/disbursement.entity';
import { DownloaAppTrack } from 'src/entities/downloads.app.entity';
import { EmiEntity } from 'src/entities/emi.entity';
import { employmentDetails } from 'src/entities/employment.entity';
import { EmploymentHistoryDetailsEntity } from 'src/entities/employment.history.entity';
import { employmentType } from 'src/entities/employment.type';
import { esignEntity } from 'src/entities/esign.entity';
import { ExotelCallHistory } from 'src/entities/ExotelCallHistory.entity';
import { ExotelCategoryEntity } from 'src/entities/exotelCategory.entity';
import { FinvuEntity } from 'src/entities/finvu.entity';
import { FlowEntity } from 'src/entities/flow.entity';
import { GoogleCoordinatesEntity } from 'src/entities/googleCoordinates.entity';
import { HealthDataEntity } from 'src/entities/healthData.entity';
import { InstallAppEntity } from 'src/entities/InstallApp.entities';
import { InsuranceEntity } from 'src/entities/insurance.entity';
import { ipMasterEntity } from 'src/entities/ipMaster.entity';
import { KYCEntity } from 'src/entities/kyc.entity';
import { LeadTrackingEntity } from 'src/entities/leadTracking.entity';
import { LeadTrackingFileEntity } from 'src/entities/leadTrackingFile.entity';
import { LeadTrackingIp } from 'src/entities/leadTrackingIp.entities';
import { LegalCollectionEntity } from 'src/entities/legal.collection.entity';
import { LegalConsigment } from 'src/entities/legal.consignment.entity';
import { userLoanDecline } from 'src/entities/loan.decline.entity';
import { loanTransaction } from 'src/entities/loan.entity';
import { loanPurpose } from 'src/entities/loan.purpose.entity';
import { LocationEntity } from 'src/entities/location.entity';
import { MailTrackerEntity } from 'src/entities/mail.tracker.entity';
import { mandateEntity } from 'src/entities/mandate.entity';
import { ManualVerifiedCompanyEntity } from 'src/entities/manual.verified.company.entity';
import { ManualVerifiedWorkEmailEntity } from 'src/entities/manualVerifiedWorkEmail.entity';
import { MasterEntity } from 'src/entities/master.entity';
import { ChatDocumentEntity } from 'src/entities/media.entity';
import { MetricsEntity } from 'src/entities/metrics.entity';
import { missMatchLogs } from 'src/entities/missMatchLogs.entity';
import { userNetBankingDetails } from 'src/entities/netBanking.entity';
import { LegalNoticeEntity } from 'src/entities/notice.entity';
import { OtherPermissionDataEntity } from 'src/entities/otherPermissionData.entity';
import { PredictionEntity } from 'src/entities/prediction.entity';
import { PromoCodeEntity } from 'src/entities/promocode.entity';
import { QualityParameterEntity } from 'src/entities/qualityParameter.entity';
import { RBIGuidelineEntity } from 'src/entities/rbiGuideline.entity';
import { ReasonsEntity } from 'src/entities/Reasons.entity';
import { ReportListEntity } from 'src/entities/report.list.entity';
import { ReportHistoryEntity } from 'src/entities/reportHistory.entity';
import { RequestSupportEntity } from 'src/entities/request_support.entity';
import { ResponseEntity } from 'src/entities/response.entity';
import { AdminRoleModuleEntity } from 'src/entities/role.module.entity';
import { AdminSubRoleModuleEntity } from 'src/entities/role.sub.module.entity';
import { SalarySlipEntity } from 'src/entities/salarySlip.entity';
import { employmentSector } from 'src/entities/sector.entity';
import { SettlementEntity } from 'src/entities/settlement.entity';
import { stamp } from 'src/entities/stamp.entity';
import { StateEligibilty } from 'src/entities/stateEligibility.entity';
import { StaticConfigEntity } from 'src/entities/static.config.entity';
import { SubScriptionEntity } from 'src/entities/subscription.entity';
import { TallyIPReversal } from 'src/entities/tallyIPReversal.entity';
import { TemplateEntity } from 'src/entities/template.entity';
import { TestEntity } from 'src/entities/test.entity';
import { ThirdPartyServiceEntities } from 'src/entities/thirdParty.service.entities';
import { ThirdPartyProvider } from 'src/entities/thirdpartyProviders.entities';
import { TrackErrorMetrics } from 'src/entities/trackErrorsMetrics.entity';
import { TrackStepCategory } from 'src/entities/trackStepCategory.entity';
import { TrackStepMetrics } from 'src/entities/trackStepMetrics.entity';
import { TrackUserAttempts } from 'src/entities/trackUserAttempts.entity';
import { TransactionEntity } from 'src/entities/transaction.entity';
import { uniqueContactEntity } from 'src/entities/unique.contact.entity';
import { UniqueContactLogEntity } from 'src/entities/unique.contact.log.entity';
import { UserActivityEntity } from 'src/entities/user.activity.entity';
import { registeredUsers } from 'src/entities/user.entity';
import { UsersFlowDataEntity } from 'src/entities/user.flow.entity';
import { userHistory } from 'src/entities/user.history.entity';
import { UserSelfieEntity } from 'src/entities/user.selfie.entity';
import { UserDelete } from 'src/entities/userDelete.entity';
import { UserLogTracker } from 'src/entities/userLogTracker.entity';
import { UserPermissionEntity } from 'src/entities/userPermission.entity';
import { userSmsEntity } from 'src/entities/userSms.entity';
import { whatsappMSGEntity } from 'src/entities/whatsappMSGEntity';
import { WorkMailEntity } from 'src/entities/workMail.entity';
import { EncryptionInterceptor } from 'src/intercept/resHandler';

export const AppProvider = [
  {
    provide: 'REDIS_CONNECTION_CONFIG_OPTIONS',
    useValue: {
      host: EnvConfig.database.redis.host,
      port: EnvConfig.database.redis.port,
      auth_pass: EnvConfig.database.redis.auth_pass,
    },
  },
  // Banking
  { provide: AA_REPOSITORY, useValue: AAEntity },

  { provide: APP_INTERCEPTOR, useValue: EncryptionInterceptor },

  //Profile
  {
    provide: USER_REPOSITORY,
    useValue: registeredUsers,
  },
  {
    provide: CONFIGURATION_REPOSITORY,
    useValue: Configuration,
  },
  {
    provide: LOCATION_REPOSITORY,
    useValue: LocationEntity,
  },
  {
    provide: GOOGLE_COORDINATES_REPOSITORY,
    useValue: GoogleCoordinatesEntity,
  },
  {
    provide: USERHISTORY_REPOSITORY,
    useValue: userHistory,
  },
  {
    provide: FLOW_REPOSITORY,
    useValue: FlowEntity,
  },

  //Employment
  {
    provide: EMPLOYEEDETAILS_REPOSITORY,
    useValue: employmentDetails,
  },
  {
    provide: EMPLOYEEDETAILS_HISTORY_REPOSITORY,
    useValue: EmploymentHistoryDetailsEntity,
  },
  {
    provide: EMPLOYEMENT_SECTOR_ENTITY,
    useValue: employmentSector,
  },
  {
    provide: SALARY_SLIP_REPOSITORY,
    useValue: SalarySlipEntity,
  },
  {
    provide: WORK_EMAIL_REPOSITORY,
    useValue: WorkMailEntity,
  },
  {
    provide: BLACKLIST_COMPANIES_REPOSITORY,
    useValue: BlacklistCompanyEntity,
  },

  //Loan
  {
    provide: LOANTRANSACTION_REPOSITORY,
    useValue: loanTransaction,
  },

  //EMI
  {
    provide: EMIENTITY_REPOSITORY,
    useValue: EmiEntity,
  },

  //Disbursement
  {
    provide: DISBURSEMENTENTITY_REPOSITORY,
    useValue: disbursementEntity,
  },

  //Media
  {
    provide: CHAT_DOCUMENTENTITY_REPOSITORY,
    useValue: ChatDocumentEntity,
  },

  //NetBanking
  {
    provide: USERNETBANKINGDETAILES_REPOSITORY,
    useValue: userNetBankingDetails,
  },
  {
    provide: BANKING_REPOSITORY,
    useValue: BankingEntity,
  },

  //Legal
  {
    provide: LEGAL_NOTICE_REPOSITORY,
    useValue: LegalNoticeEntity,
  },

  //Bank
  {
    provide: TOTAL_BANK_REPOSITORY,
    useValue: BankList,
  },

  //Admin
  {
    provide: ADMIN_REPOSITORY,
    useValue: admin,
  },
  {
    provide: API_LOGGER_REPOSITORY,
    useValue: APILogsEntity,
  },
  {
    provide: ADMINROLE_REPOSITORY,
    useValue: AdminRole,
  },
  {
    provide: CRMACTIVITY_REPOSITORY,
    useValue: crmActivity,
  },
  {
    provide: CRMTITLE_REPOSITORY,
    useValue: crmTitle,
  },
  { provide: CRMDISPOSITION_REPOSITORY, useValue: crmDisposition },
  {
    provide: ADMINROLEMODULE_REPOSITORY,
    useValue: AdminRoleModuleEntity,
  },
  {
    provide: CHANGE_LOGS_REPOSITORY,
    useValue: ChangeLogsEntity,
  },
  {
    provide: ADMIN_SUB_ROLE_MODULE_REPOSITORY,
    useValue: AdminSubRoleModuleEntity,
  },
  {
    provide: ACCESS_OF_LIST_REPOSITORY,
    useValue: AccessOfListEntity,
  },
  {
    provide: ACCESS_OF_ROLE_REPOSITORY,
    useValue: AccessOfRoleEntity,
  },
  {
    provide: API_ACCESS_LIST_REPOSITORY,
    useValue: APIAccessListEntity,
  },

  //Contacts
  {
    provide: CONTACT_DETAILS_REPOSITORY,
    useValue: contactDetailEntity,
  },
  {
    provide: CONTACT_LOG_REPOSITORY,
    useValue: ContactLogEntity,
  },
  {
    provide: UNIQUE_CONTACT_LOG_REPOSITORY,
    useValue: UniqueContactLogEntity,
  },
  //Mandate
  {
    provide: MANDATE_REPOSITORY,
    useValue: mandateEntity,
  },
  {
    provide: SUBSCRIPTION_ENTITY,
    useValue: SubScriptionEntity,
  },

  // Exotel -> 3rd Party
  {
    provide: EXOTEL_CALL_HISTORY_ENTITY,
    useValue: ExotelCallHistory,
  },
  {
    provide: EXOTEL_CATEGORY_ENTITY,
    useValue: ExotelCategoryEntity,
  },

  //ESign
  {
    provide: ESIGN_REPOSITORY,
    useValue: esignEntity,
  },
  {
    provide: STAMP_REPOSITORY,
    useValue: stamp,
  },
  {
    provide: STATE_ELIGIBILITY_REPOSITORY,
    useValue: StateEligibilty,
  },
  //Transactions
  {
    provide: TRANSACTION_REPOSITORY,
    useValue: TransactionEntity,
  },
  //  manual verified company
  {
    provide: MANUAL_VERIFIED_COMPANY_REPOSITORY,
    useValue: ManualVerifiedCompanyEntity,
  },
  {
    provide: MANUAL_VERIFIED_WORK_EMAIL_REPOSITORY,
    useValue: ManualVerifiedWorkEmailEntity,
  },
  //  device
  {
    provide: DEVICE_REPOSITORY,
    useValue: device,
  },
  {
    provide: DEVICESIM_REPOSITORY,
    useValue: DeviceSIM,
  },
  {
    provide: DELETE_USER,
    useValue: UserDelete,
  },
  {
    provide: DEVICE_INFO_AND_INSTALL_APP_REPOSITORY,
    useValue: DeviceInfoAndInstallAppEntity,
  },
  {
    provide: INSTALL_APP_REPOSITORY,
    useValue: InstallAppEntity,
  },
  {
    provide: BLOCK_USER_HISTORY_REPOSITORY,
    useValue: BlockUserHistoryEntity,
  },
  {
    provide: USER_LOG_TRACKER_REPOSITORY,
    useValue: UserLogTracker,
  },

  //Eligibility
  {
    provide: PREDICTION_REPOSITORY,
    useValue: PredictionEntity,
  },

  //Test
  {
    provide: TEST_REPOSITORY,
    useValue: TestEntity,
  },
  // {
  //   provide: REFERENCES_REPOSITORY,
  //   useValue: ReferencesEntity,
  // },
  { provide: MISSMATCH_LOGS_REPOSITORY, useValue: missMatchLogs },
  {
    provide: UNIQUE_CONTACT_REPOSITORY,
    useValue: uniqueContactEntity,
  },
  {
    provide: USERS_FLOW_DATA_REPOSITORY,
    useValue: UsersFlowDataEntity,
  },
  {
    provide: ADDRESSES_REPOSITORY,
    useValue: AddressesEntity,
  },
  {
    provide: KYC_REPOSITORY,
    useValue: KYCEntity,
  },
  {
    provide: DEPARTMENT_REPOSITORY,
    useValue: Department,
  },
  {
    provide: DEGIGNATION_REPOSITORY,
    useValue: employmentDesignation,
  },
  {
    provide: EMPLOYEMENT_TYPE_ENTITY,
    useValue: employmentType,
  },
  {
    provide: PURPOSE_REPOSITORY,
    useValue: loanPurpose,
  },
  {
    provide: CONFIGS_REPOSITORY,
    useValue: Configuration,
  },
  {
    provide: LEGAL_CONSIGNMENT_REPOSITORY,
    useValue: LegalConsigment,
  },
  {
    provide: MAIL_TRACKER_REPOSITORY,
    useValue: MailTrackerEntity,
  },
  {
    provide: USER_SELFIE_REPOSITORY,
    useValue: UserSelfieEntity,
  },
  {
    provide: STATIC_CONFIG_REPOSITORY,
    useValue: StaticConfigEntity,
  },

  {
    provide: USER_LOAN_DECLINE_REPOSITORY,
    useValue: userLoanDecline,
  },
  { provide: TEMPLATE_ENTITY, useValue: TemplateEntity },

  // user permission
  {
    provide: USER_PERMISSION_REPOSITORY,
    useValue: UserPermissionEntity,
  },
  // user activity
  {
    provide: USER_ACTIVITY_REPOSITORY,
    useValue: UserActivityEntity,
  },
  {
    provide: REASON_REPOSITORY,
    useValue: ReasonsEntity,
  },
  {
    provide: CRMSTATUS_REPOSITORY,
    useValue: crmStatus,
  },

  {
    provide: REPORT_LIST_REPOSITORY,
    useValue: ReportListEntity,
  },
  {
    provide: THIRDPARTY_PROVIDER_REPOSITORY,
    useValue: ThirdPartyProvider,
  },
  {
    provide: THIRDPARTY_SERVICE_REPOSITORY,
    useValue: ThirdPartyServiceEntities,
  },
  /// cibil
  {
    provide: CIBIL_REPOSITORY,
    useValue: CIBILEntity,
  },
  {
    provide: CIBIL_TRIGGER_REPOSITORY,
    useValue: cibilTriggerEntity,
  },
  {
    provide: CIBIL_SCORE_REPOSITORY,
    useValue: CibilScoreEntity,
  },
  {
    provide: CRM_REASON_REPOSITORY,
    useValue: CrmReasonEntity,
  },
  {
    provide: BANKS_REPOSITORY,
    useValue: banks,
  },
  { provide: COMPANY_REPOSITORY, useValue: GoogleCompanyResultEntity },

  // User
  {
    provide: MASTER_REPOSITORY,
    useValue: MasterEntity,
  },
  {
    provide: DOWNLOAD_APP_TRACK,
    useValue: DownloaAppTrack,
  },

  {
    provide: CRMDISPOSITION_REPOSITORY,
    useValue: crmDisposition,
  },
  // Bank
  {
    provide: BRANCHES_REPOSITORY,
    useValue: branches,
  },
  {
    provide: BANKS_REPOSITORY,
    useValue: banks,
  },
  {
    provide: DEFAULTER_ONLINE_ENTITY_REPOSITORY,
    useValue: DefaulterOnlineEntity,
  },
  {
    provide: PROMO_CODE_ENTITY_REPOSITORY,
    useValue: PromoCodeEntity,
  },
  {
    provide: LEGAL_COLLECTION,
    useValue: LegalCollectionEntity,
  },
  // health data
  {
    provide: HEALTH_DATA_ENTITY_REPOSITORY,
    useValue: HealthDataEntity,
  },
  //Other Permission Data
  {
    provide: OTHER_PERMISSION_DATA_ENTITY_REPOSITORY,
    useValue: OtherPermissionDataEntity,
  },
  {
    provide: INSURANCE_REPOSITORY,
    useValue: InsuranceEntity,
  },
  {
    provide: CRON_TRAKING_REPOSITORY,
    useValue: CronTrakingEntity,
  },
  {
    provide: QUALITY_PARAMETERS_REPOSITORY,
    useValue: QualityParameterEntity,
  },
  // Third party
  {
    provide: RESPONSE_REPOSITORY,
    useValue: ResponseEntity,
  },
  // Report
  {
    provide: REPORT_HISTORY_REPOSITORY,
    useValue: ReportHistoryEntity,
  },
  {
    provide: COLLECTION_PERFORMANCE_REPORT,
    useValue: CollectionPerformanceReport,
  },
  //razorpay and cashfree settlement
  {
    provide: SETTLEMENT_REPOSITORY,
    useValue: SettlementEntity,
  },
  {
    provide: METRICS_REPOSITORY,
    useValue: MetricsEntity,
  },
  // Ip master
  {
    provide: IP_MASTER_REPOSITORY,
    useValue: ipMasterEntity,
  },
  // Request Support
  {
    provide: REQUEST_SUPPORT_REPOSITORY,
    useValue: RequestSupportEntity,
  },
  // finvu
  {
    provide: FINVU_REPOSITORY,
    useValue: FinvuEntity,
  },
  //RBI Guideline
  {
    provide: RBI_GUIDELINE_REPOSITORY,
    useValue: RBIGuidelineEntity,
  },
  //Whatsapp MSG
  {
    provide: WHATSAPP_MESSAGE_REPOSITORY,
    useValue: whatsappMSGEntity,
  },
  // Tally IP Reversal
  {
    provide: TALLY_IP_REVERSAL_REPOSITORY,
    useValue: TallyIPReversal,
  },

  {
    provide: TRACK_ERROR_METRICS,
    useValue: TrackErrorMetrics,
  },
  {
    provide: TRACK_STEP_METRICS,
    useValue: TrackStepMetrics,
  },
  {
    provide: TRACK_STEP_CATEGORY,
    useValue: TrackStepCategory,
  },
  //Lead Tracking File
  {
    provide: LEAD_TRACKING_FILE_REPOSITORY,
    useValue: LeadTrackingFileEntity,
  },

  //Lead Tracking
  {
    provide: LEAD_TRACKING_REPOSITORY,
    useValue: LeadTrackingEntity,
  },

  //Lead tracking ip
  {
    provide: LEADS_TRACKING_IP_REPOSITORY,
    useValue: LeadTrackingIp,
  },

  {
    provide: TRACK_USERS_ATTEMPTS,
    useValue: TrackUserAttempts,
  },

  {
    provide: USER_SMS_REPOSITORY,
    useValue: userSmsEntity,
  },
];
