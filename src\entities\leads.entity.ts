// Imports
import { Table, Column, Model, DataType } from 'sequelize-typescript';

@Table({ timestamps: false })
export class LeadEntitiy extends Model<LeadEntitiy> {
    @Column({
        type: DataType.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
    })
    id: number;

    @Column({
        type: DataType.UUID,
        allowNull: true,
    })
    userId: string;

    @Column({
        type: DataType.SMALLINT,
        comment: '1 -> GOOD BANK USER',
        allowNull: false,
    })
    type: any;

    @Column({
        type: DataType.JSONB,
        defaultValue: {},
        allowNull: true,
    })
    data: any;
}
