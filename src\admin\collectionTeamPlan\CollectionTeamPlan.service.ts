import { Injectable } from '@nestjs/common';
import { Op } from 'sequelize';
import { COLLECTION_PERFORMANCE_CYCLE_DATE } from 'src/constants/globals';
import { k500Error } from 'src/constants/misc';
import { kCollectionTeamRedisKeys, REDIS_KEY } from 'src/constants/objects';
import {
  k422ErrorMessage,
  kParamsMissing,
  kWrongDetails,
} from 'src/constants/responses';
import { ChangeLogsEntity } from 'src/entities/change.logs.entity';
import { CollectionTeamPlanEntity } from 'src/entities/colletionTeamPlan.entity';
import { RedisService } from 'src/redis/redis.service';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { DateService } from 'src/utils/date.service';
import { TypeService } from 'src/utils/type.service';
import { AdminRedisSyncService } from '../admin/AdminRedisSync.service';

@Injectable()
export class CollectionTeamPlanService {
  constructor(
    private readonly commonSharedService: CommonSharedService,
    private readonly adminRedisSyncService: AdminRedisSyncService,
    private readonly dateService: DateService,
    private readonly typeService: TypeService,
    private readonly redisService: RedisService,

    //Repository
    private readonly repoManager: RepositoryManager,
  ) {}

  async createTeam(reqData) {
    let name = reqData?.name;
    const updatedBy = reqData?.updatedBy;
    const employees = reqData?.employees;
    const minDelay = parseInt(reqData?.minDelay);
    const maxDelay = parseInt(reqData?.maxDelay);
    if (!name || !updatedBy || !employees) return kParamsMissing;
    if (
      typeof name != 'string' ||
      !name?.trim() ||
      isNaN(minDelay) ||
      isNaN(maxDelay) ||
      minDelay > maxDelay ||
      minDelay < -1 ||
      maxDelay < -1 ||
      (maxDelay == -1 && minDelay == -1) ||
      !Array.isArray(employees) ||
      !employees?.length
    )
      return kWrongDetails;
    name = name?.trim();

    const data = {
      name,
      minDelay,
      maxDelay,
      employees,
      updatedBy,
      isActive: true,
    };

    const isTeamExist: any = await this.checkTeamExistWithGivenPeriod(data);
    if (isTeamExist?.message) return isTeamExist;

    const isEmployeeExist: any = await this.checkEmployeeExistInOtherTeam(data);
    if (isEmployeeExist?.message) return isEmployeeExist;

    const createdData = await this.repoManager.createRowData(
      CollectionTeamPlanEntity,
      data,
    );
    if (createdData == k500Error) throw new Error();

    const logsData = {
      type: 'Collection Team Plan',
      subType: 'Create Collection Team',
      oldData: '',
      newData: JSON.stringify(createdData),
      adminId: updatedBy,
    };
    await this.repoManager.createRowData(ChangeLogsEntity, logsData);
    return true;
  }

  async updateTeam(reqData) {
    const teamId = reqData?.teamId;
    const updatedBy = reqData?.updatedBy;
    const employees = reqData?.employees ?? [];
    const isActive = reqData?.isActive === true;
    if (!teamId || !updatedBy || !employees) return kParamsMissing;
    if (isActive && (!Array.isArray(employees) || !employees?.length))
      return kWrongDetails;
    const teamData = await this.repoManager.getRowWhereData(
      CollectionTeamPlanEntity,
      ['id', 'minDelay', 'maxDelay', 'employees', 'isActive'],
      { where: { id: teamId } },
    );
    if (!teamData || teamData == k500Error) throw new Error();

    if (isActive) {
      const isTeamExist: any = await this.checkTeamExistWithGivenPeriod({
        minDelay: teamData?.minDelay,
        maxDelay: teamData?.maxDelay,
        teamId: teamData?.id,
      });
      if (isTeamExist?.message) return isTeamExist;

      const isEmployeeExist: any = await this.checkEmployeeExistInOtherTeam({
        teamId,
        employees: employees?.length ? employees : teamData?.employees,
      });
      if (isEmployeeExist?.message) return isEmployeeExist;
    }

    const updateData: any = { updatedBy };
    if (isActive && employees?.length) updateData.employees = employees;
    if (isActive != teamData.isActive) updateData.isActive = isActive;

    const updatedDetails = await this.repoManager.updateRowData(
      CollectionTeamPlanEntity,
      updateData,
      teamId,
    );

    if (updatedDetails == k500Error) throw new Error();

    const logsData = {
      type: 'Collection Team Plan',
      subType: 'Update Collection Team',
      oldData: JSON.stringify(teamData),
      newData: JSON.stringify(updateData),
      adminId: updatedBy,
    };
    await this.repoManager.createRowData(ChangeLogsEntity, logsData);
    return true;
  }

  async checkTeamExistWithGivenPeriod(reqData) {
    const { minDelay, maxDelay, teamId } = reqData;

    const where: any = {
      minDelay,
      maxDelay,
      isActive: true,
    };
    if (teamId) where.id = { [Op.ne]: teamId };
    const existData = await this.repoManager.getRowWhereData(
      CollectionTeamPlanEntity,
      ['id'],
      { where },
    );
    if (existData == k500Error) throw new Error();
    if (existData)
      return k422ErrorMessage(
        'Team already exist with given minDelay and maxDelay',
      );
    return {};
  }

  async checkEmployeeExistInOtherTeam(reqData) {
    const { employees, teamId } = reqData;
    const where: any = {
      employees: {
        [Op.overlap]: employees,
      },
      isActive: true,
    };
    if (teamId) where.id = { [Op.ne]: teamId };
    const isEmployeeExistInOtherTeam = await this.repoManager.getRowWhereData(
      CollectionTeamPlanEntity,
      ['id'],
      { where },
    );

    if (isEmployeeExistInOtherTeam == k500Error) throw new Error();
    if (isEmployeeExistInOtherTeam)
      return k422ErrorMessage(
        'Any of the selected employees already exist in another team, remove them first',
      );
    return {};
  }

  async getAllTeam() {
    let allTeamData = await this.redisService.get(
      kCollectionTeamRedisKeys.teamPlanData,
    );
    if (!allTeamData) allTeamData = [];
    else allTeamData = JSON.parse(allTeamData);

    const formattedData = [];
    let tempDataObj = {};
    for (const ele of allTeamData) {
      let teamName = ele?.name;
      let startPoint = '';
      let endPoint = '';
      if (ele?.minDelay == -1) startPoint = 'Upcoming';
      else if (ele?.minDelay == 0) startPoint = 'OnTime';
      else startPoint = `${ele?.minDelay}`;

      if (ele?.maxDelay == -1) endPoint = 'Upcoming';
      else if (ele?.maxDelay == 0) endPoint = 'OnTime';
      else endPoint = `${ele?.maxDelay}`;

      if (
        ele?.minDelay == ele?.maxDelay &&
        (ele?.minDelay == 0 || ele?.minDelay == -1)
      )
        teamName = teamName + ` (${startPoint})`;
      else if (
        ele?.maxDelay == ele?.minDelay &&
        ele?.minDelay != -1 &&
        ele?.minDelay != 0
      )
        teamName = teamName + ` (${+startPoint} DPD)`;
      else if (ele?.maxDelay == -1 && ele?.minDelay != -1 && ele?.minDelay != 0)
        teamName = teamName + ` (${+startPoint - 1}+ DPD)`;
      else teamName = teamName + ` (${startPoint} - ${endPoint} DPD)`;
      
      const currentMember = [];
      for (const employee of ele?.employees) {
        const adminData = await this.commonSharedService.getAdminData(employee);
        if (adminData?.fullName) currentMember.push(adminData?.fullName);
      }

      tempDataObj = {
        id: ele?.id,
        'Team Name': teamName,
        'Current Member': currentMember,
        'Last Updated By':
          (await this.commonSharedService.getAdminData(ele?.updatedBy))
            ?.fullName ?? '-',
        'Last Updated At':
          (await this.dateService.formatDate(ele?.updatedAt)) ?? '-',
      };
      formattedData.push(tempDataObj);
    }
    return formattedData;
  }

  async syncTeamData(reqData?) {
    const isForcefully = reqData?.isForcefully === true;
    let cycleStartDate = await this.redisService.get(
      kCollectionTeamRedisKeys.cycleDate,
    );
    if (!cycleStartDate) cycleStartDate = COLLECTION_PERFORMANCE_CYCLE_DATE;
    const isTodayIsCycleStartDate =
      +cycleStartDate == this.typeService.getGlobalDate(new Date()).getDate();

    if (!isTodayIsCycleStartDate && !isForcefully) return true;

    const allTeamData = await this.repoManager.getTableWhereData(
      CollectionTeamPlanEntity,
      [
        'id',
        'minDelay',
        'maxDelay',
        'updatedBy',
        'name',
        'employees',
        'updatedAt',
      ],
      {
        where: { isActive: true },
        order: [['id', 'ASC']],
      },
    );
    if (allTeamData == k500Error) throw new Error();

    await this.redisService.set(
      kCollectionTeamRedisKeys.teamPlanData,
      JSON.stringify(allTeamData),
    );
    return true;
  }

  async getCollectionAdminWithTeam() {
    let adminRoleData = await this.redisService.get(REDIS_KEY.ADMIN_ROLES);
    if (!adminRoleData) {
      await this.adminRedisSyncService.storeAllAdminsOnRedis();
      adminRoleData = await this.redisService.get(REDIS_KEY.ADMIN_ROLES);
    }
    if (adminRoleData) adminRoleData = JSON.parse(adminRoleData);
    else throw new Error();
    const collectionRole = adminRoleData.find(
      (ele) => ele.title == 'collection' && ele.isActive == '1',
    );
    if (!collectionRole) throw new Error();
    let allAdmins = await this.redisService.get(REDIS_KEY.ADMINS_DATA);
    if (!allAdmins) {
      await this.adminRedisSyncService.storeAllAdminsOnRedis();
      allAdmins = await this.redisService.get(REDIS_KEY.ADMINS_DATA);
    }
    if (allAdmins) allAdmins = JSON.parse(allAdmins);
    else throw new Error();

    const collectionAdmins = allAdmins.filter(
      (ele) => ele.roleId == collectionRole?.id && ele.isActive == '1',
    );
    if (!collectionAdmins?.length) return [];

    const allTeamData = await this.repoManager.getTableWhereData(
      CollectionTeamPlanEntity,
      ['id', 'name', 'employees'],
      {
        where: { isActive: true },
        order: [['id', 'ASC']],
      },
    );
    if (allTeamData == k500Error) throw new Error();

    const workingEmployees = allTeamData?.map((ele) => ele?.employees)?.flat();
    const adminWithTeam = [];
    let tempObj = {};
    for (const ele of allTeamData) {
      const employees = ele?.employees;
      for (let idx = 0; idx < employees?.length; idx++) {
        tempObj = {
          id: employees[idx],
          fullName:
            (await this.commonSharedService.getAdminData(employees[idx]))
              ?.fullName ?? '-',
          teamId: ele?.id ?? '',
          teamName: ele?.name ?? '',
        };

        adminWithTeam.push(tempObj);
      }
    }

    for (const ele of collectionAdmins) {
      if (workingEmployees.includes(ele?.id)) continue;
      tempObj = {
        id: ele?.id,
        fullName: ele?.fullName ?? '-',
        teamId: '',
        teamName: '',
      };
      adminWithTeam.push(tempObj);
    }
    return adminWithTeam;
  }
}
