// Imports
import { Table, Column, Model, DataType } from 'sequelize-typescript';

@Table({ timestamps: true })
export class DynamicEntity extends Model<DynamicEntity> {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    allowNull: false,
  })
  id: number;

  @Column({
    type: DataType.UUID,
    allowNull: true,
  })
  userId: string;

  @Column({
    type: DataType.JSONB,
    defaultValue: {},
    allowNull: true,
  })
  data: any;
}
